from django.db import models
from users.models import Student

class DocumentGroup(models.Model):
    user = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='document_groups')
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['-created_at']

class Document(models.Model):
    PROCESSING_STATUS = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ]

    user = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='documents')
    group = models.ForeignKey(DocumentGroup, on_delete=models.SET_NULL, null=True, blank=True, related_name='documents')
    title = models.Char<PERSON>ield(max_length=255)
    file = models.FileField(upload_to='documents/')
    uploaded_at = models.DateTimeField(auto_now_add=True)
    processing_status = models.CharField(max_length=20, choices=PROCESSING_STATUS, default='pending')
    error_message = models.TextField(null=True, blank=True)
    blueprint = models.TextField(null=True, blank=True, help_text="Blueprint for the document")

    def __str__(self):
        return f"{self.title} ({self.get_processing_status_display()})"

class DocumentEmbedding(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='embeddings')
    text_chunk = models.TextField(help_text="The text segment this embedding represents")
    embedding = models.JSONField(help_text="Vector embedding of the text chunk")
    chunk_number = models.IntegerField(help_text="Order of this chunk in the document")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['chunk_number']
        indexes = [
            models.Index(fields=['document', 'chunk_number']),
        ]

    def __str__(self):
        return f"{self.document.title} - Chunk {self.chunk_number}"

class Flashcard(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE)
    front = models.TextField()
    back = models.TextField()

class Flowchart(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE)
    mermaid_code = models.TextField(help_text="Store Mermaid.js flowchart code")

class Quiz(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE)
    question = models.TextField()
    answer = models.TextField()

class BlueprintTopics(models.Model):
    document = models.ForeignKey(Document, on_delete=models.CASCADE, related_name='topics')
    title = models.CharField(max_length=255, help_text="Title of the topic")
    weightage = models.DecimalField(max_digits=5, decimal_places=2, help_text="Weightage of the topic in percentile")
    content = models.ManyToManyField(DocumentEmbedding, related_name='topics', help_text="Relevant document embeddings for this topic")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Blueprint Topic"
        verbose_name_plural = "Blueprint Topics"
        ordering = ['-weightage']

    def __str__(self):
        return f"{self.title} ({self.weightage}%)"