(()=>{var e={};e.id=9492,e.ids=[9492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23843:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32222:(e,r,t)=>{Promise.resolve().then(t.bind(t,83066)),Promise.resolve().then(t.bind(t,6931))},33873:e=>{"use strict";e.exports=require("path")},37915:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},52651:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>m,routeModule:()=>u,tree:()=>l});var n=t(65239),o=t(48088),s=t(88170),i=t.n(s),d=t(30893),a={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>d[e]);t.d(r,a);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,m=[],p={require:t,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},58014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>a});var n=t(37413);t(82704);var o=t(61421),s=t.n(o),i=t(83066),d=t(6931);let a={title:"Cognimosity",description:"Learning platform",generator:"v0.dev"};function l({children:e}){return(0,n.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,n.jsx)("body",{className:s().className,children:(0,n.jsxs)(i.ThemeProvider,{defaultTheme:"dark",enableSystem:!0,disableTransitionOnChange:!0,children:[e,(0,n.jsx)(d.Toaster,{})]})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72390:(e,r,t)=>{Promise.resolve().then(t.bind(t,92892)),Promise.resolve().then(t.bind(t,52581))},82704:()=>{},83066:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>o});var n=t(12907);let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\components\\theme-provider.tsx","ThemeProvider");(0,n.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\components\\theme-provider.tsx","useTheme")},92892:(e,r,t)=>{"use strict";t.d(r,{D:()=>d,ThemeProvider:()=>i});var n=t(60687),o=t(43210);let s=(0,o.createContext)({theme:"system",setTheme:()=>null});function i({children:e,defaultTheme:r="system",storageKey:t="ui-theme",enableSystem:i=!0,disableTransitionOnChange:d=!1,...a}){let[l,m]=(0,o.useState)(r);return(0,n.jsx)(s.Provider,{...a,value:{theme:l,setTheme:e=>{m(e)}},children:e})}let d=()=>{let e=(0,o.useContext)(s);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[6171],()=>t(52651));module.exports=n})();