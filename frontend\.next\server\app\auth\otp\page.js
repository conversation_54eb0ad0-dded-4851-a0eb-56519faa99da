(()=>{var e={};e.id=9463,e.ids=[9463],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28195:(e,t,r)=>{Promise.resolve().then(r.bind(r,93440))},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30930:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(60687),a=r(43210),i=r(24934),n=r(68988),o=r(55192),l=r(16189),d=r(30474),c=r(59556),u=r(52581);function p(){let e=(0,l.useRouter)(),[t,r]=(0,a.useState)(Array(6).fill("")),p=(0,a.useRef)([]),[f,m]=(0,a.useState)(60),[x,g]=(0,a.useState)(!1),[h,v]=(0,a.useState)(!1),[b,y]=(0,a.useState)(""),j=(e,s)=>{if(!/^\d*$/.test(s))return;let a=[...t];if(a[e]=s.substring(0,1),r(a),s&&e<5&&p.current[e+1]){let t=p.current[e+1];t&&t.focus()}},w=(e,s)=>{if("ArrowRight"===s.key&&e<5){let t=p.current[e+1];t&&t.focus()}else if("ArrowLeft"===s.key&&e>0){let t=p.current[e-1];t&&t.focus()}if("Backspace"===s.key){if(t[e]){let s=[...t];s[e]="",r(s)}else if(e>0){let t=p.current[e-1];t&&t.focus()}}},N=e=>{e.preventDefault();let s=e.clipboardData.getData("text/plain").trim();if(!/^\d+$/.test(s))return;let a=[...t];for(let e=0;e<Math.min(s.length,6);e++)a[e]=s[e];r(a),s.length<6&&p.current[s.length]&&p.current[s.length].focus()},P=async()=>{if(0===f){g(!0);try{let t=localStorage.getItem("username");if(!t){u.o.error("No username found. Please register again."),e.push("/auth");return}await c.authApi.register({email:b,username:t,password:"",confirm_password:"",first_name:"",last_name:""}),m(60),u.o.success("OTP resent successfully!")}catch(e){u.o.error(e.response?.data?.message||"Failed to resend OTP. Please try again.")}finally{g(!1)}}},_=async()=>{let r=t.join("");if(6!==r.length){u.o.error("Please enter a valid OTP");return}v(!0);try{await c.authApi.verifyOTP({email:b,otp:r});try{if(!localStorage.getItem("username")){u.o.error("No username found. Please register again."),e.push("/auth");return}u.o.success("Email verified successfully! Please sign in with your credentials."),e.push("/auth")}catch(t){u.o.error("Account verified but failed to sign in automatically. Please sign in manually."),e.push("/auth")}}catch(e){u.o.error(e.response?.data?.message||"Failed to verify OTP. Please try again.")}finally{v(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsx)("div",{className:"flex justify-center mb-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative h-10 w-10",children:(0,s.jsx)(d.default,{src:"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png",alt:"Cognimosity Logo",fill:!0,className:"object-contain"})}),(0,s.jsx)("span",{className:"text-2xl font-bold",children:"Cognimosity"})]})}),(0,s.jsxs)(o.Zp,{className:"border-border bg-card",children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{children:"OTP Verification"}),(0,s.jsx)(o.BT,{children:"We've sent a verification code to your email. Please enter the code below."})]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsx)("div",{className:"flex justify-between mb-6",children:t.map((e,t)=>(0,s.jsx)(n.p,{ref:e=>{p.current[t]=e},type:"text",inputMode:"numeric",maxLength:1,value:e,onChange:e=>j(t,e.target.value),onKeyDown:e=>w(t,e),onPaste:0===t?N:void 0,className:"w-12 h-12 text-center text-lg"},t))}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:"Didn't receive the code?"}),(0,s.jsx)(i.$,{variant:"link",className:`p-0 h-auto text-sm ${0===f?"text-purple-500":"text-muted-foreground cursor-not-allowed"}`,onClick:P,disabled:f>0||x,children:x?"Resending...":f>0?`Resend in ${(e=>{let t=Math.floor(e/60);return`${t}:${(e%60).toString().padStart(2,"0")}`})(f)}`:"Resend OTP"})]})]}),(0,s.jsx)(o.wL,{children:(0,s.jsx)(i.$,{className:"w-full bg-purple-600 hover:bg-purple-700",onClick:_,disabled:6!==t.join("").length||h,children:h?"Verifying...":"Verify"})})]})]})})}},33873:e=>{"use strict";e.exports=require("path")},42267:(e,t,r)=>{Promise.resolve().then(r.bind(r,30930))},47269:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["auth",{children:["otp",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,93440)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\auth\\otp\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\auth\\otp\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/otp/page",pathname:"/auth/otp",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55192:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>u});var s=r(60687),a=r(43210),i=r(96241);let n=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let o=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68988:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687),a=r(43210),i=r(96241);let n=a.forwardRef(({className:e,type:t,...r},a)=>(0,s.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...r}));n.displayName="Input"},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\auth\\\\otp\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\auth\\otp\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6171,3955,1060,8946],()=>r(47269));module.exports=s})();