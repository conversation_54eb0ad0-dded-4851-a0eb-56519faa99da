(()=>{var e={};e.id=8365,e.ids=[8365],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3492:(e,s,t)=>{Promise.resolve().then(t.bind(t,31370))},5685:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d={children:["",{children:["auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,31370)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\auth\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\auth\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/page",pathname:"/auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8509:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(60687),a=t(43210),i=t(24934),n=t(68988),l=t(10403),o=t(55192),d=t(85910),c=t(16189),u=t(30474);let p=(0,t(62688).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var m=t(13861),x=t(59556),f=t(52581);function h(){let e=(0,c.useRouter)(),[s,t]=(0,a.useState)(!1),[h,g]=(0,a.useState)(!1),[v,j]=(0,a.useState)(!1),[b,y]=(0,a.useState)(""),[w,N]=(0,a.useState)(""),[C,k]=(0,a.useState)(""),[R,q]=(0,a.useState)(""),[_,I]=(0,a.useState)(""),[P,S]=(0,a.useState)(""),[D,F]=(0,a.useState)(""),[A,E]=(0,a.useState)(""),M=async s=>{s.preventDefault(),j(!0);try{let s=await x.authApi.signIn({username:b,password:w});localStorage.setItem("token",s.token),localStorage.setItem("username",b),localStorage.setItem("isLoggedIn","true"),f.o.success("Successfully signed in!"),e.push("/")}catch(e){f.o.error(e.response?.data?.message||"Failed to sign in. Please try again.")}finally{j(!1)}},U=async s=>{if(s.preventDefault(),_!==P){f.o.error("Passwords do not match");return}j(!0);try{await x.authApi.register({username:C,email:R,password:_,confirm_password:P,first_name:D,last_name:A}),localStorage.setItem("email",R),localStorage.setItem("username",C),f.o.success("Registration successful! Please verify your email."),e.push("/auth/otp")}catch(e){f.o.error(e.response?.data?.message||"Failed to register. Please try again.")}finally{j(!1)}},B=async()=>{f.o.info("Google Sign In coming soon!")};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsx)("div",{className:"flex justify-center mb-8",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"relative h-10 w-10",children:(0,r.jsx)(u.default,{src:"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png",alt:"Cognimosity Logo",fill:!0,className:"object-contain"})}),(0,r.jsx)("span",{className:"text-2xl font-bold",children:"Cognimosity"})]})}),(0,r.jsxs)(d.tU,{defaultValue:"signin",className:"w-full",children:[(0,r.jsxs)(d.j7,{className:"grid w-full grid-cols-2 mb-6",children:[(0,r.jsx)(d.Xi,{value:"signin",className:"text-base",children:"Sign In"}),(0,r.jsx)(d.Xi,{value:"register",className:"text-base",children:"Register"})]}),(0,r.jsx)(d.av,{value:"signin",children:(0,r.jsxs)(o.Zp,{className:"border-border bg-card",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Sign In"}),(0,r.jsx)(o.BT,{children:"Enter your credentials to access your account"})]}),(0,r.jsxs)("form",{onSubmit:M,children:[(0,r.jsxs)(o.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"signin-username",children:"Email"}),(0,r.jsx)(n.p,{id:"signin-username",type:"email",placeholder:"Enter your email",value:b,onChange:e=>y(e.target.value),required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"signin-password",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.p,{id:"signin-password",type:s?"text":"password",placeholder:"Enter your password",value:w,onChange:e=>N(e.target.value),required:!0}),(0,r.jsx)(i.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8",onClick:()=>t(!s),children:s?(0,r.jsx)(p,{className:"h-4 w-4"}):(0,r.jsx)(m.A,{className:"h-4 w-4"})})]})]}),(0,r.jsx)(i.$,{variant:"link",className:"p-0 h-auto text-sm text-purple-500",onClick:()=>console.log("Forgot password"),children:"Forgot password?"})]}),(0,r.jsxs)(o.wL,{className:"flex flex-col gap-4",children:[(0,r.jsx)(i.$,{type:"submit",className:"w-full bg-purple-600 hover:bg-purple-700",disabled:v,children:v?"Signing in...":"Sign In"}),(0,r.jsxs)("div",{className:"relative w-full",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("span",{className:"w-full border-t border-border"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,r.jsx)("span",{className:"bg-card px-2 text-muted-foreground",children:"Or continue with"})})]}),(0,r.jsxs)(i.$,{type:"button",variant:"outline",className:"w-full",onClick:B,children:[(0,r.jsxs)("svg",{className:"mr-2 h-4 w-4",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),(0,r.jsx)("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),(0,r.jsx)("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),(0,r.jsx)("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),"Google"]})]})]})]})}),(0,r.jsx)(d.av,{value:"register",children:(0,r.jsxs)(o.Zp,{className:"border-border bg-card",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{children:"Create an Account"}),(0,r.jsx)(o.BT,{children:"Enter your details to create a new account"})]}),(0,r.jsxs)("form",{onSubmit:U,children:[(0,r.jsxs)(o.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"first-name",children:"First Name"}),(0,r.jsx)(n.p,{id:"first-name",type:"text",placeholder:"John",value:D,onChange:e=>F(e.target.value),required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"last-name",children:"Last Name"}),(0,r.jsx)(n.p,{id:"last-name",type:"text",placeholder:"Doe",value:A,onChange:e=>E(e.target.value),required:!0})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"username",children:"Username"}),(0,r.jsx)(n.p,{id:"username",type:"text",placeholder:"johndoe",value:C,onChange:e=>k(e.target.value),required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"email",children:"Email"}),(0,r.jsx)(n.p,{id:"email",type:"email",placeholder:"<EMAIL>",value:R,onChange:e=>q(e.target.value),required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"password",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.p,{id:"password",type:s?"text":"password",placeholder:"Create a password",value:_,onChange:e=>I(e.target.value),required:!0}),(0,r.jsx)(i.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8",onClick:()=>t(!s),children:s?(0,r.jsx)(p,{className:"h-4 w-4"}):(0,r.jsx)(m.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(l.J,{htmlFor:"confirm-password",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.p,{id:"confirm-password",type:h?"text":"password",placeholder:"Confirm your password",value:P,onChange:e=>S(e.target.value),required:!0}),(0,r.jsx)(i.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8",onClick:()=>g(!h),children:h?(0,r.jsx)(p,{className:"h-4 w-4"}):(0,r.jsx)(m.A,{className:"h-4 w-4"})})]})]})]}),(0,r.jsx)(o.wL,{children:(0,r.jsx)(i.$,{type:"submit",className:"w-full bg-purple-600 hover:bg-purple-700",disabled:v,children:v?"Registering...":"Register"})})]})]})})]})]})})}},10403:(e,s,t)=>{"use strict";t.d(s,{J:()=>c});var r=t(60687),a=t(43210),i=t(14163),n=a.forwardRef((e,s)=>(0,r.jsx)(i.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=t(24224),o=t(96241);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(n,{ref:t,className:(0,o.cn)(d(),e),...s}));c.displayName=n.displayName},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31370:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\auth\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\auth\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},55146:(e,s,t)=>{"use strict";t.d(s,{B8:()=>I,UC:()=>S,bL:()=>_,l9:()=>P});var r=t(43210),a=t(70569),i=t(11273),n=t(72942),l=t(46059),o=t(14163),d=t(43),c=t(65551),u=t(96963),p=t(60687),m="Tabs",[x,f]=(0,i.A)(m,[n.RG]),h=(0,n.RG)(),[g,v]=x(m),j=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,onValueChange:a,defaultValue:i,orientation:n="horizontal",dir:l,activationMode:m="automatic",...x}=e,f=(0,d.jH)(l),[h,v]=(0,c.i)({prop:r,onChange:a,defaultProp:i});return(0,p.jsx)(g,{scope:t,baseId:(0,u.B)(),value:h,onValueChange:v,orientation:n,dir:f,activationMode:m,children:(0,p.jsx)(o.sG.div,{dir:f,"data-orientation":n,...x,ref:s})})});j.displayName=m;var b="TabsList",y=r.forwardRef((e,s)=>{let{__scopeTabs:t,loop:r=!0,...a}=e,i=v(b,t),l=h(t);return(0,p.jsx)(n.bL,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:r,children:(0,p.jsx)(o.sG.div,{role:"tablist","aria-orientation":i.orientation,...a,ref:s})})});y.displayName=b;var w="TabsTrigger",N=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,disabled:i=!1,...l}=e,d=v(w,t),c=h(t),u=R(d.baseId,r),m=q(d.baseId,r),x=r===d.value;return(0,p.jsx)(n.q7,{asChild:!0,...c,focusable:!i,active:x,children:(0,p.jsx)(o.sG.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":m,"data-state":x?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...l,ref:s,onMouseDown:(0,a.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(r)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(r)}),onFocus:(0,a.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;x||i||!e||d.onValueChange(r)})})})});N.displayName=w;var C="TabsContent",k=r.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,forceMount:i,children:n,...d}=e,c=v(C,t),u=R(c.baseId,a),m=q(c.baseId,a),x=a===c.value,f=r.useRef(x);return r.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(l.C,{present:i||x,children:({present:t})=>(0,p.jsx)(o.sG.div,{"data-state":x?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!t,id:m,tabIndex:0,...d,ref:s,style:{...e.style,animationDuration:f.current?"0s":void 0},children:t&&n})})});function R(e,s){return`${e}-trigger-${s}`}function q(e,s){return`${e}-content-${s}`}k.displayName=C;var _=j,I=y,P=N,S=k},55192:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>l,wL:()=>u});var r=t(60687),a=t(43210),i=t(96241);let n=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));n.displayName="Card";let l=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s}));u.displayName="CardFooter"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68988:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var r=t(60687),a=t(43210),i=t(96241);let n=a.forwardRef(({className:e,type:s,...t},a)=>(0,r.jsx)("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));n.displayName="Input"},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84932:(e,s,t)=>{Promise.resolve().then(t.bind(t,8509))},85910:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>l});var r=t(60687),a=t(43210),i=t(55146),n=t(96241);let l=i.bL,o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.B8,{ref:t,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));o.displayName=i.B8.displayName;let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.l9,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=i.l9.displayName;let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.UC,{ref:t,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));c.displayName=i.UC.displayName},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[6171,3955,1060,9085,8946],()=>t(5685));module.exports=r})();