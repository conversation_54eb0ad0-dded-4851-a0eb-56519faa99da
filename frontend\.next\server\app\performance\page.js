(()=>{var e={};e.id=3859,e.ids=[3859],e.modules={900:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\performance\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\performance\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4058:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(60687),o=t(55366),n=t(38115);function i(){return(0,s.jsx)(n.b,{children:(0,s.jsx)("div",{className:"min-h-screen bg-background",children:(0,s.jsx)(o.Z,{})})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40613:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>l,tree:()=>u});var s=t(65239),o=t(48088),n=t(88170),i=t.n(n),a=t(30893),p={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>a[e]);t.d(r,p);let u={children:["",{children:["performance",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,900)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\performance\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\performance\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},l=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/performance/page",pathname:"/performance",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},51738:(e,r,t)=>{Promise.resolve().then(t.bind(t,900))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91906:(e,r,t)=>{Promise.resolve().then(t.bind(t,4058))},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6171,3955,1060,9085,8757,9485,8946,5287,4184],()=>t(40613));module.exports=s})();