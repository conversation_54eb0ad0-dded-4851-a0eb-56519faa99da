/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/quiz/page";
exports.ids = ["app/quiz/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"71cbb6d16935\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam92YW5cXE9uZURyaXZlXFxEZXNrdG9wXFxjb2duaW1vc2l0eV9VSVxcbGF0ZXN0IHJlcG9cXGNvZ25pX2FwaVxcZnJvbnRlbmRcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MWNiYjZkMTY5MzVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"Cognimosity\",\n    description: \"Learning platform\",\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                defaultTheme: \"dark\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_3__.Toaster, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQ3NCO0FBS2hCQTtBQUhxRDtBQUMzQjtBQUl6QixNQUFNRyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7SUFDWEMsV0FBVztBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyx3QkFBd0I7a0JBQ3RDLDRFQUFDQztZQUFLQyxXQUFXYiwySkFBZTtzQkFDOUIsNEVBQUNDLHFFQUFhQTtnQkFBQ2EsY0FBYTtnQkFBT0MsWUFBWTtnQkFBQ0MseUJBQXlCOztvQkFDdEVSO2tDQUNELDhEQUFDTiwyQ0FBT0E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtsQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqb3ZhblxcT25lRHJpdmVcXERlc2t0b3BcXGNvZ25pbW9zaXR5X1VJXFxsYXRlc3QgcmVwb1xcY29nbmlfYXBpXFxmcm9udGVuZFxcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIFJlYWN0IGZyb20gXCJyZWFjdFwiXHJcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIlxyXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCJcclxuaW1wb3J0IHsgVGhlbWVQcm92aWRlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXJcIlxyXG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSBcInNvbm5lclwiXHJcblxyXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSlcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YSA9IHtcclxuICB0aXRsZTogXCJDb2duaW1vc2l0eVwiLFxyXG4gIGRlc2NyaXB0aW9uOiBcIkxlYXJuaW5nIHBsYXRmb3JtXCIsXHJcbiAgICBnZW5lcmF0b3I6ICd2MC5kZXYnXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xyXG4gIGNoaWxkcmVuLFxyXG59OiB7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxyXG59KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxodG1sIGxhbmc9XCJlblwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cclxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxyXG4gICAgICAgIDxUaGVtZVByb3ZpZGVyIGRlZmF1bHRUaGVtZT1cImRhcmtcIiBlbmFibGVTeXN0ZW0gZGlzYWJsZVRyYW5zaXRpb25PbkNoYW5nZT5cclxuICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgIDxUb2FzdGVyIC8+XHJcbiAgICAgICAgPC9UaGVtZVByb3ZpZGVyPlxyXG4gICAgICA8L2JvZHk+XHJcbiAgICA8L2h0bWw+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJpbnRlciIsIlRoZW1lUHJvdmlkZXIiLCJUb2FzdGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiZ2VuZXJhdG9yIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJib2R5IiwiY2xhc3NOYW1lIiwiZGVmYXVsdFRoZW1lIiwiZW5hYmxlU3lzdGVtIiwiZGlzYWJsZVRyYW5zaXRpb25PbkNoYW5nZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/quiz/page.tsx":
/*!***************************!*\
  !*** ./app/quiz/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\quiz\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\components\\theme-provider.tsx",
"ThemeProvider",
);const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\components\\theme-provider.tsx",
"useTheme",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fquiz%2Fpage&page=%2Fquiz%2Fpage&appPaths=%2Fquiz%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjovan%5COneDrive%5CDesktop%5Ccognimosity_UI%5Clatest%20repo%5Ccogni_api%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjovan%5COneDrive%5CDesktop%5Ccognimosity_UI%5Clatest%20repo%5Ccogni_api%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fquiz%2Fpage&page=%2Fquiz%2Fpage&appPaths=%2Fquiz%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjovan%5COneDrive%5CDesktop%5Ccognimosity_UI%5Clatest%20repo%5Ccogni_api%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjovan%5COneDrive%5CDesktop%5Ccognimosity_UI%5Clatest%20repo%5Ccogni_api%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/quiz/page.tsx */ \"(rsc)/./app/quiz/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'quiz',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/quiz/page\",\n        pathname: \"/quiz\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fquiz%2Fpage&page=%2Fquiz%2Fpage&appPaths=%2Fquiz%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjovan%5COneDrive%5CDesktop%5Ccognimosity_UI%5Clatest%20repo%5Ccogni_api%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjovan%5COneDrive%5CDesktop%5Ccognimosity_UI%5Clatest%20repo%5Ccogni_api%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(rsc)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(rsc)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cquiz%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cquiz%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/quiz/page.tsx */ \"(rsc)/./app/quiz/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2pvdmFuJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDY29nbmltb3NpdHlfVUklNUMlNUNsYXRlc3QlMjByZXBvJTVDJTVDY29nbmlfYXBpJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNxdWl6JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUErSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcam92YW5cXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxjb2duaW1vc2l0eV9VSVxcXFxsYXRlc3QgcmVwb1xcXFxjb2duaV9hcGlcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXHF1aXpcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cquiz%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/quiz/page.tsx":
/*!***************************!*\
  !*** ./app/quiz/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QuizPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Crown_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Crown!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Crown_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Crown!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _components_quiz_interface__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/quiz-interface */ \"(ssr)/./components/quiz-interface.tsx\");\n/* harmony import */ var _components_layout_with_sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout-with-sidebar */ \"(ssr)/./components/layout-with-sidebar.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/theme-provider */ \"(ssr)/./components/theme-provider.tsx\");\n/* harmony import */ var _hooks_use_document_time__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-document-time */ \"(ssr)/./hooks/use-document-time.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction QuizPage() {\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { theme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const documentId = searchParams.get(\"documentId\") ? parseInt(searchParams.get(\"documentId\")) : undefined;\n    // Document time tracking with quiz pause/resume\n    const { startQuiz, endQuiz, isActive, isPaused } = (0,_hooks_use_document_time__WEBPACK_IMPORTED_MODULE_6__.useDocumentTime)({\n        documentId: documentId,\n        enabled: !!documentId,\n        isProcessingComplete: true,\n        onQuizStart: {\n            \"QuizPage.useDocumentTime\": ()=>console.log('Quiz started - timer paused')\n        }[\"QuizPage.useDocumentTime\"],\n        onQuizEnd: {\n            \"QuizPage.useDocumentTime\": ()=>console.log('Quiz ended - timer resumed')\n        }[\"QuizPage.useDocumentTime\"]\n    });\n    const handleBackToDocument = ()=>{\n        if (documentId) {\n            router.push(`/process?documentId=${documentId}`);\n        } else {\n            router.push(\"/\");\n        }\n    };\n    if (!documentId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_with_sidebar__WEBPACK_IMPORTED_MODULE_4__.LayoutWithSidebar, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-[calc(100vh-65px)] flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `rounded-lg p-8 text-center ${theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-800\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"No Document Selected\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground mb-4\",\n                            children: \"Please select a document to take a quiz.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>router.push(\"/\"),\n                            className: \"bg-purple-600 hover:bg-purple-700\",\n                            children: \"Go to Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_with_sidebar__WEBPACK_IMPORTED_MODULE_4__.LayoutWithSidebar, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-[calc(100vh-65px)] flex flex-col bg-background text-foreground\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b border-border p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: handleBackToDocument,\n                                        className: \"gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Crown_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Back to Document\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold\",\n                                        children: \"Quiz\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>router.push(\"/subscription\"),\n                                    className: \"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Crown_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Upgrade\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_quiz_interface__WEBPACK_IMPORTED_MODULE_3__.QuizInterface, {\n                        documentId: documentId,\n                        onQuizStart: startQuiz,\n                        onQuizEnd: endQuiz\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcXVpei9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFHNEQ7QUFDYjtBQUNBO0FBQ1k7QUFDUztBQUNkO0FBQ0s7QUFFNUMsU0FBU1M7SUFDdEIsTUFBTUMsZUFBZVYsZ0VBQWVBO0lBQ3BDLE1BQU1XLFNBQVNWLDBEQUFTQTtJQUN4QixNQUFNLEVBQUVXLEtBQUssRUFBRSxHQUFHTCxvRUFBUUE7SUFDMUIsTUFBTU0sYUFBYUgsYUFBYUksR0FBRyxDQUFDLGdCQUFnQkMsU0FBU0wsYUFBYUksR0FBRyxDQUFDLGlCQUFrQkU7SUFFaEcsZ0RBQWdEO0lBQ2hELE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxPQUFPLEVBQUVDLFFBQVEsRUFBRUMsUUFBUSxFQUFFLEdBQUdaLHlFQUFlQSxDQUFDO1FBQ2pFSyxZQUFZQTtRQUNaUSxTQUFTLENBQUMsQ0FBQ1I7UUFDWFMsc0JBQXNCO1FBQ3RCQyxXQUFXO3dDQUFFLElBQU1DLFFBQVFDLEdBQUcsQ0FBQzs7UUFDL0JDLFNBQVM7d0NBQUUsSUFBTUYsUUFBUUMsR0FBRyxDQUFDOztJQUMvQjtJQUVBLE1BQU1FLHVCQUF1QjtRQUMzQixJQUFJZCxZQUFZO1lBQ2RGLE9BQU9pQixJQUFJLENBQUMsQ0FBQyxvQkFBb0IsRUFBRWYsWUFBWTtRQUNqRCxPQUFPO1lBQ0xGLE9BQU9pQixJQUFJLENBQUM7UUFDZDtJQUNGO0lBRUEsSUFBSSxDQUFDZixZQUFZO1FBQ2YscUJBQ0UsOERBQUNQLDhFQUFpQkE7c0JBQ2hCLDRFQUFDdUI7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFXLENBQUMsMkJBQTJCLEVBQUVsQixVQUFVLFVBQVUsaUNBQWlDLGtCQUFrQjs7c0NBQ25ILDhEQUFDbUI7NEJBQUdELFdBQVU7c0NBQTZCOzs7Ozs7c0NBQzNDLDhEQUFDRTs0QkFBRUYsV0FBVTtzQ0FBNkI7Ozs7OztzQ0FHMUMsOERBQUM1Qix5REFBTUE7NEJBQUMrQixTQUFTLElBQU10QixPQUFPaUIsSUFBSSxDQUFDOzRCQUFNRSxXQUFVO3NDQUFvQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU9qRztJQUVBLHFCQUNFLDhEQUFDeEIsOEVBQWlCQTtrQkFDaEIsNEVBQUN1QjtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDNUIseURBQU1BO3dDQUNMZ0MsU0FBUTt3Q0FDUkMsTUFBSzt3Q0FDTEYsU0FBU047d0NBQ1RHLFdBQVU7OzBEQUVWLDhEQUFDM0IsMkZBQVNBO2dEQUFDMkIsV0FBVTs7Ozs7OzRDQUFZOzs7Ozs7O2tEQUduQyw4REFBQ007d0NBQUdOLFdBQVU7a0RBQXdCOzs7Ozs7Ozs7Ozs7MENBR3hDLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQzVCLHlEQUFNQTtvQ0FDTCtCLFNBQVMsSUFBTXRCLE9BQU9pQixJQUFJLENBQUM7b0NBQzNCRSxXQUFVO29DQUNWSyxNQUFLOztzREFFTCw4REFBQy9CLDJGQUFLQTs0Q0FBQzBCLFdBQVU7Ozs7Ozt3Q0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQVExQyw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUN6QixxRUFBYUE7d0JBQ1pRLFlBQVlBO3dCQUNaVSxhQUFhTjt3QkFDYlMsV0FBV1I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNdkIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam92YW5cXE9uZURyaXZlXFxEZXNrdG9wXFxjb2duaW1vc2l0eV9VSVxcbGF0ZXN0IHJlcG9cXGNvZ25pX2FwaVxcZnJvbnRlbmRcXGFwcFxccXVpelxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyB1c2VTZWFyY2hQYXJhbXMsIHVzZVJvdXRlciB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIlxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxuaW1wb3J0IHsgQXJyb3dMZWZ0LCBDcm93biB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IHsgUXVpekludGVyZmFjZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvcXVpei1pbnRlcmZhY2VcIlxuaW1wb3J0IHsgTGF5b3V0V2l0aFNpZGViYXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL2xheW91dC13aXRoLXNpZGViYXJcIlxuaW1wb3J0IHsgdXNlVGhlbWUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyXCJcbmltcG9ydCB7IHVzZURvY3VtZW50VGltZSB9IGZyb20gXCJAL2hvb2tzL3VzZS1kb2N1bWVudC10aW1lXCJcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUXVpelBhZ2UoKSB7XG4gIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIGNvbnN0IHsgdGhlbWUgfSA9IHVzZVRoZW1lKClcbiAgY29uc3QgZG9jdW1lbnRJZCA9IHNlYXJjaFBhcmFtcy5nZXQoXCJkb2N1bWVudElkXCIpID8gcGFyc2VJbnQoc2VhcmNoUGFyYW1zLmdldChcImRvY3VtZW50SWRcIikhKSA6IHVuZGVmaW5lZFxuXG4gIC8vIERvY3VtZW50IHRpbWUgdHJhY2tpbmcgd2l0aCBxdWl6IHBhdXNlL3Jlc3VtZVxuICBjb25zdCB7IHN0YXJ0UXVpeiwgZW5kUXVpeiwgaXNBY3RpdmUsIGlzUGF1c2VkIH0gPSB1c2VEb2N1bWVudFRpbWUoe1xuICAgIGRvY3VtZW50SWQ6IGRvY3VtZW50SWQsXG4gICAgZW5hYmxlZDogISFkb2N1bWVudElkLFxuICAgIGlzUHJvY2Vzc2luZ0NvbXBsZXRlOiB0cnVlLCAvLyBRdWl6IHBhZ2UgYXNzdW1lcyBkb2N1bWVudCBpcyBhbHJlYWR5IHByb2Nlc3NlZFxuICAgIG9uUXVpelN0YXJ0OiAoKSA9PiBjb25zb2xlLmxvZygnUXVpeiBzdGFydGVkIC0gdGltZXIgcGF1c2VkJyksXG4gICAgb25RdWl6RW5kOiAoKSA9PiBjb25zb2xlLmxvZygnUXVpeiBlbmRlZCAtIHRpbWVyIHJlc3VtZWQnKVxuICB9KTtcblxuICBjb25zdCBoYW5kbGVCYWNrVG9Eb2N1bWVudCA9ICgpID0+IHtcbiAgICBpZiAoZG9jdW1lbnRJZCkge1xuICAgICAgcm91dGVyLnB1c2goYC9wcm9jZXNzP2RvY3VtZW50SWQ9JHtkb2N1bWVudElkfWApXG4gICAgfSBlbHNlIHtcbiAgICAgIHJvdXRlci5wdXNoKFwiL1wiKVxuICAgIH1cbiAgfVxuXG4gIGlmICghZG9jdW1lbnRJZCkge1xuICAgIHJldHVybiAoXG4gICAgICA8TGF5b3V0V2l0aFNpZGViYXI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1bY2FsYygxMDB2aC02NXB4KV0gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHJvdW5kZWQtbGcgcC04IHRleHQtY2VudGVyICR7dGhlbWUgPT09IFwibGlnaHRcIiA/IFwiYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ibGFja1wiIDogXCJiZy1uZXV0cmFsLTgwMFwifWB9PlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi00XCI+Tm8gRG9jdW1lbnQgU2VsZWN0ZWQ8L2gyPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTRcIj5cbiAgICAgICAgICAgICAgUGxlYXNlIHNlbGVjdCBhIGRvY3VtZW50IHRvIHRha2UgYSBxdWl6LlxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaChcIi9cIil9IGNsYXNzTmFtZT1cImJnLXB1cnBsZS02MDAgaG92ZXI6YmctcHVycGxlLTcwMFwiPlxuICAgICAgICAgICAgICBHbyB0byBIb21lXG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0xheW91dFdpdGhTaWRlYmFyPlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPExheW91dFdpdGhTaWRlYmFyPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLVtjYWxjKDEwMHZoLTY1cHgpXSBmbGV4IGZsZXgtY29sIGJnLWJhY2tncm91bmQgdGV4dC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLWJvcmRlciBwLTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUJhY2tUb0RvY3VtZW50fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdhcC0yXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxBcnJvd0xlZnQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgQmFjayB0byBEb2N1bWVudFxuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZFwiPlF1aXo8L2gxPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKFwiL3N1YnNjcmlwdGlvblwiKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTYwMCB0by1waW5rLTYwMCBob3Zlcjpmcm9tLXB1cnBsZS03MDAgaG92ZXI6dG8tcGluay03MDAgdGV4dC13aGl0ZSBib3JkZXItMFwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxDcm93biBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgIFVwZ3JhZGVcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFF1aXogQ29udGVudCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgPFF1aXpJbnRlcmZhY2UgXG4gICAgICAgICAgICBkb2N1bWVudElkPXtkb2N1bWVudElkfSBcbiAgICAgICAgICAgIG9uUXVpelN0YXJ0PXtzdGFydFF1aXp9IFxuICAgICAgICAgICAgb25RdWl6RW5kPXtlbmRRdWl6fVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9MYXlvdXRXaXRoU2lkZWJhcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVNlYXJjaFBhcmFtcyIsInVzZVJvdXRlciIsIkJ1dHRvbiIsIkFycm93TGVmdCIsIkNyb3duIiwiUXVpekludGVyZmFjZSIsIkxheW91dFdpdGhTaWRlYmFyIiwidXNlVGhlbWUiLCJ1c2VEb2N1bWVudFRpbWUiLCJRdWl6UGFnZSIsInNlYXJjaFBhcmFtcyIsInJvdXRlciIsInRoZW1lIiwiZG9jdW1lbnRJZCIsImdldCIsInBhcnNlSW50IiwidW5kZWZpbmVkIiwic3RhcnRRdWl6IiwiZW5kUXVpeiIsImlzQWN0aXZlIiwiaXNQYXVzZWQiLCJlbmFibGVkIiwiaXNQcm9jZXNzaW5nQ29tcGxldGUiLCJvblF1aXpTdGFydCIsImNvbnNvbGUiLCJsb2ciLCJvblF1aXpFbmQiLCJoYW5kbGVCYWNrVG9Eb2N1bWVudCIsInB1c2giLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsInAiLCJvbkNsaWNrIiwidmFyaWFudCIsInNpemUiLCJoMSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/quiz/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout-with-sidebar.tsx":
/*!********************************************!*\
  !*** ./components/layout-with-sidebar.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayoutWithSidebar: () => (/* binding */ LayoutWithSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/sidebar */ \"(ssr)/./components/sidebar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/theme-provider */ \"(ssr)/./components/theme-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ LayoutWithSidebar auto */ \n\n\n\n\n\nfunction LayoutWithSidebar({ children }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { theme, setTheme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LayoutWithSidebar.useEffect\": ()=>{\n            // Check if user is logged in from localStorage or cookies\n            const userLoggedIn = localStorage.getItem(\"isLoggedIn\") === \"true\";\n            const storedUsername = localStorage.getItem(\"username\");\n            if (userLoggedIn && storedUsername) {\n                setIsLoggedIn(true);\n                setUsername(storedUsername);\n            }\n        }\n    }[\"LayoutWithSidebar.useEffect\"], []);\n    const handleLogout = ()=>{\n        // In a real app, you would call your logout API here\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"username\");\n        setIsLoggedIn(false);\n        setUsername(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-background text-foreground overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {\n                isOpen: sidebarOpen,\n                setIsOpen: setSidebarOpen,\n                isLoggedIn: isLoggedIn,\n                username: username,\n                onLogout: handleLogout\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"h-10 w-10 rounded-full\",\n                                onClick: ()=>setSidebarOpen(true),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"h-10 w-10 rounded-full\",\n                                    onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                    children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 35\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 65\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2xheW91dC13aXRoLXNpZGViYXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUkyQztBQUNHO0FBQ0M7QUFDRDtBQUNRO0FBTS9DLFNBQVNRLGtCQUFrQixFQUFFQyxRQUFRLEVBQTBCO0lBQ3BFLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHWCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLEVBQUVZLEtBQUssRUFBRUMsUUFBUSxFQUFFLEdBQUdOLG9FQUFRQTtJQUNwQyxNQUFNLENBQUNPLFlBQVlDLGNBQWMsR0FBR2YsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDZ0IsVUFBVUMsWUFBWSxHQUFHakIsK0NBQVFBLENBQUM7SUFFekNDLGdEQUFTQTt1Q0FBQztZQUNSLDBEQUEwRDtZQUMxRCxNQUFNaUIsZUFBZUMsYUFBYUMsT0FBTyxDQUFDLGtCQUFrQjtZQUM1RCxNQUFNQyxpQkFBaUJGLGFBQWFDLE9BQU8sQ0FBQztZQUU1QyxJQUFJRixnQkFBZ0JHLGdCQUFnQjtnQkFDbENOLGNBQWM7Z0JBQ2RFLFlBQVlJO1lBQ2Q7UUFDRjtzQ0FBRyxFQUFFO0lBRUwsTUFBTUMsZUFBZTtRQUNuQixxREFBcUQ7UUFDckRILGFBQWFJLFVBQVUsQ0FBQztRQUN4QkosYUFBYUksVUFBVSxDQUFDO1FBQ3hCUixjQUFjO1FBQ2RFLFlBQVk7SUFDZDtJQUVBLHFCQUNFLDhEQUFDTztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ3ZCLHdEQUFPQTtnQkFDTndCLFFBQVFoQjtnQkFDUmlCLFdBQVdoQjtnQkFDWEcsWUFBWUE7Z0JBQ1pFLFVBQVVBO2dCQUNWWSxVQUFVTjs7Ozs7OzBCQUlaLDhEQUFDRTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ3RCLHlEQUFNQTtnQ0FBQzBCLFNBQVE7Z0NBQVFDLE1BQUs7Z0NBQU9MLFdBQVU7Z0NBQXlCTSxTQUFTLElBQU1wQixlQUFlOzBDQUNuRyw0RUFBQ1AseUZBQUlBO29DQUFDcUIsV0FBVTs7Ozs7Ozs7Ozs7MENBR2xCLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ3RCLHlEQUFNQTtvQ0FDTDBCLFNBQVE7b0NBQ1JDLE1BQUs7b0NBQ0xMLFdBQVU7b0NBQ1ZNLFNBQVMsSUFBTWxCLFNBQVNELFVBQVUsU0FBUyxVQUFVOzhDQUVwREEsVUFBVSx1QkFBUyw4REFBQ1AseUZBQUdBO3dDQUFDb0IsV0FBVTs7Ozs7NkRBQWUsOERBQUNuQix5RkFBSUE7d0NBQUNtQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQUl2RWhCOzs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam92YW5cXE9uZURyaXZlXFxEZXNrdG9wXFxjb2duaW1vc2l0eV9VSVxcbGF0ZXN0IHJlcG9cXGNvZ25pX2FwaVxcZnJvbnRlbmRcXGNvbXBvbmVudHNcXGxheW91dC13aXRoLXNpZGViYXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXHJcblxyXG5pbXBvcnQgdHlwZSBSZWFjdCBmcm9tIFwicmVhY3RcIlxyXG5cclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiXHJcbmltcG9ydCB7IFNpZGViYXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3NpZGViYXJcIlxyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXHJcbmltcG9ydCB7IE1lbnUsIFN1biwgTW9vbiB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxyXG5pbXBvcnQgeyB1c2VUaGVtZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXJcIlxyXG5cclxuaW50ZXJmYWNlIExheW91dFdpdGhTaWRlYmFyUHJvcHMge1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIExheW91dFdpdGhTaWRlYmFyKHsgY2hpbGRyZW4gfTogTGF5b3V0V2l0aFNpZGViYXJQcm9wcykge1xyXG4gIGNvbnN0IFtzaWRlYmFyT3Blbiwgc2V0U2lkZWJhck9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgY29uc3QgeyB0aGVtZSwgc2V0VGhlbWUgfSA9IHVzZVRoZW1lKClcclxuICBjb25zdCBbaXNMb2dnZWRJbiwgc2V0SXNMb2dnZWRJbl0gPSB1c2VTdGF0ZShmYWxzZSlcclxuICBjb25zdCBbdXNlcm5hbWUsIHNldFVzZXJuYW1lXSA9IHVzZVN0YXRlKFwiXCIpXHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyBDaGVjayBpZiB1c2VyIGlzIGxvZ2dlZCBpbiBmcm9tIGxvY2FsU3RvcmFnZSBvciBjb29raWVzXHJcbiAgICBjb25zdCB1c2VyTG9nZ2VkSW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImlzTG9nZ2VkSW5cIikgPT09IFwidHJ1ZVwiXHJcbiAgICBjb25zdCBzdG9yZWRVc2VybmFtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwidXNlcm5hbWVcIilcclxuXHJcbiAgICBpZiAodXNlckxvZ2dlZEluICYmIHN0b3JlZFVzZXJuYW1lKSB7XHJcbiAgICAgIHNldElzTG9nZ2VkSW4odHJ1ZSlcclxuICAgICAgc2V0VXNlcm5hbWUoc3RvcmVkVXNlcm5hbWUpXHJcbiAgICB9XHJcbiAgfSwgW10pXHJcblxyXG4gIGNvbnN0IGhhbmRsZUxvZ291dCA9ICgpID0+IHtcclxuICAgIC8vIEluIGEgcmVhbCBhcHAsIHlvdSB3b3VsZCBjYWxsIHlvdXIgbG9nb3V0IEFQSSBoZXJlXHJcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShcImlzTG9nZ2VkSW5cIilcclxuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKFwidXNlcm5hbWVcIilcclxuICAgIHNldElzTG9nZ2VkSW4oZmFsc2UpXHJcbiAgICBzZXRVc2VybmFtZShcIlwiKVxyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLXNjcmVlbiBiZy1iYWNrZ3JvdW5kIHRleHQtZm9yZWdyb3VuZCBvdmVyZmxvdy1oaWRkZW5cIj5cclxuICAgICAgey8qIFNpZGViYXIgKi99XHJcbiAgICAgIDxTaWRlYmFyXHJcbiAgICAgICAgaXNPcGVuPXtzaWRlYmFyT3Blbn1cclxuICAgICAgICBzZXRJc09wZW49e3NldFNpZGViYXJPcGVufVxyXG4gICAgICAgIGlzTG9nZ2VkSW49e2lzTG9nZ2VkSW59XHJcbiAgICAgICAgdXNlcm5hbWU9e3VzZXJuYW1lfVxyXG4gICAgICAgIG9uTG9nb3V0PXtoYW5kbGVMb2dvdXR9XHJcbiAgICAgIC8+XHJcblxyXG4gICAgICB7LyogTWFpbiBDb250ZW50ICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy1hdXRvXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJpY29uXCIgY2xhc3NOYW1lPVwiaC0xMCB3LTEwIHJvdW5kZWQtZnVsbFwiIG9uQ2xpY2s9eygpID0+IHNldFNpZGViYXJPcGVuKHRydWUpfT5cclxuICAgICAgICAgICAgPE1lbnUgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XHJcbiAgICAgICAgICA8L0J1dHRvbj5cclxuXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxyXG4gICAgICAgICAgICAgIHNpemU9XCJpY29uXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTEwIHctMTAgcm91bmRlZC1mdWxsXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRUaGVtZSh0aGVtZSA9PT0gXCJkYXJrXCIgPyBcImxpZ2h0XCIgOiBcImRhcmtcIil9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7dGhlbWUgPT09IFwiZGFya1wiID8gPFN1biBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4gOiA8TW9vbiBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz59XHJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJTaWRlYmFyIiwiQnV0dG9uIiwiTWVudSIsIlN1biIsIk1vb24iLCJ1c2VUaGVtZSIsIkxheW91dFdpdGhTaWRlYmFyIiwiY2hpbGRyZW4iLCJzaWRlYmFyT3BlbiIsInNldFNpZGViYXJPcGVuIiwidGhlbWUiLCJzZXRUaGVtZSIsImlzTG9nZ2VkSW4iLCJzZXRJc0xvZ2dlZEluIiwidXNlcm5hbWUiLCJzZXRVc2VybmFtZSIsInVzZXJMb2dnZWRJbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJzdG9yZWRVc2VybmFtZSIsImhhbmRsZUxvZ291dCIsInJlbW92ZUl0ZW0iLCJkaXYiLCJjbGFzc05hbWUiLCJpc09wZW4iLCJzZXRJc09wZW4iLCJvbkxvZ291dCIsInZhcmlhbnQiLCJzaXplIiwib25DbGljayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/layout-with-sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/quiz-interface.tsx":
/*!***************************************!*\
  !*** ./components/quiz-interface.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuizInterface: () => (/* binding */ QuizInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/progress */ \"(ssr)/./components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,RotateCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,RotateCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,RotateCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(ssr)/./components/ui/scroll-area.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_quiz_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/quiz-utils */ \"(ssr)/./lib/quiz-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ QuizInterface auto */ \n\n\n\n\n\n\n\n\n\n\nfunction QuizInterface({ documentId, onQuizStart, onQuizEnd }) {\n    const [questions, setQuestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentQuestionIndex, setCurrentQuestionIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [userAnswers, setUserAnswers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isQuizStarted, setIsQuizStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isQuizCompleted, setIsQuizCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [startTime, setStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [timeElapsed, setTimeElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [generating, setGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Time tracking integration for pausing/resuming study time during quiz\n    // Using props passed from parent component\n    // Timer effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuizInterface.useEffect\": ()=>{\n            let interval;\n            if (isQuizStarted && !isQuizCompleted) {\n                interval = setInterval({\n                    \"QuizInterface.useEffect\": ()=>{\n                        setTimeElapsed(Date.now() - startTime);\n                    }\n                }[\"QuizInterface.useEffect\"], 1000);\n            }\n            return ({\n                \"QuizInterface.useEffect\": ()=>clearInterval(interval)\n            })[\"QuizInterface.useEffect\"];\n        }\n    }[\"QuizInterface.useEffect\"], [\n        isQuizStarted,\n        isQuizCompleted,\n        startTime\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuizInterface.useEffect\": ()=>{\n            if (documentId) {\n                loadQuiz();\n            }\n        }\n    }[\"QuizInterface.useEffect\"], [\n        documentId\n    ]);\n    const formatTime = (milliseconds)=>{\n        const seconds = Math.floor(milliseconds / 1000);\n        const minutes = Math.floor(seconds / 60);\n        const remainingSeconds = seconds % 60;\n        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n    };\n    const loadQuiz = async ()=>{\n        if (!documentId) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.toast)({\n                title: \"Error\",\n                description: \"No document selected for quiz\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setGenerating(true);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.quizApi.getOrGenerateQuiz(documentId);\n            if (response.quizzes && response.quizzes.length > 0) {\n                setQuestions(response.quizzes);\n                setUserAnswers(new Array(response.quizzes.length).fill(''));\n                if (response.generated) {\n                    (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.toast)({\n                        title: \"Success\",\n                        description: \"Quiz generated successfully!\"\n                    });\n                } else {\n                    (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.toast)({\n                        title: \"Quiz Loaded\",\n                        description: \"Existing quiz loaded successfully!\"\n                    });\n                }\n            } else {\n                throw new Error('No quiz data received');\n            }\n        } catch (error) {\n            console.error('Error loading/generating quiz:', error);\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.toast)({\n                title: \"Error\",\n                description: \"Failed to load or generate quiz. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setGenerating(false);\n        }\n    };\n    const generateQuiz = async ()=>{\n        if (!documentId) {\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.toast)({\n                title: \"Error\",\n                description: \"No document selected for quiz generation\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setGenerating(true);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.quizApi.generateQuiz(documentId, 5);\n            if (response.quizzes && response.quizzes.length > 0) {\n                setQuestions(response.quizzes);\n                setUserAnswers(new Array(response.quizzes.length).fill(''));\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.toast)({\n                    title: \"Success\",\n                    description: \"New quiz generated successfully!\"\n                });\n            } else {\n                throw new Error('No quiz data received');\n            }\n        } catch (error) {\n            console.error('Error generating quiz:', error);\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.toast)({\n                title: \"Error\",\n                description: \"Failed to generate quiz. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setGenerating(false);\n        }\n    };\n    const startQuiz = async ()=>{\n        setIsQuizStarted(true);\n        setStartTime(Date.now());\n        setTimeElapsed(0);\n        // Pause time tracking when quiz starts\n        try {\n            onQuizStart?.();\n        } catch (error) {\n            console.error('Failed to pause time tracking for quiz:', error);\n        }\n    };\n    const handleAnswerChange = (answer)=>{\n        const newAnswers = [\n            ...userAnswers\n        ];\n        newAnswers[currentQuestionIndex] = answer;\n        setUserAnswers(newAnswers);\n    };\n    const nextQuestion = ()=>{\n        if (currentQuestionIndex < questions.length - 1) {\n            setCurrentQuestionIndex(currentQuestionIndex + 1);\n        }\n    };\n    const previousQuestion = ()=>{\n        if (currentQuestionIndex > 0) {\n            setCurrentQuestionIndex(currentQuestionIndex - 1);\n        }\n    };\n    const calculateScore = ()=>{\n        return (0,_lib_quiz_utils__WEBPACK_IMPORTED_MODULE_9__.calculateQuizScore)(questions, userAnswers);\n    };\n    const submitQuiz = async ()=>{\n        setLoading(true);\n        const finalScore = calculateScore();\n        const timeTakenSeconds = Math.floor(timeElapsed / 1000);\n        setScore(finalScore);\n        setIsQuizCompleted(true);\n        // Resume time tracking when quiz ends\n        try {\n            onQuizEnd?.();\n        } catch (error) {\n            console.error('Failed to resume time tracking after quiz:', error);\n        }\n        // Save performance to backend\n        try {\n            const user = JSON.parse(localStorage.getItem('user') || '{}');\n            if (user.id && documentId) {\n                await _lib_api__WEBPACK_IMPORTED_MODULE_7__.performanceApi.createPerformance({\n                    student: user.id,\n                    document: documentId,\n                    quiz_score: finalScore,\n                    time_taken: timeTakenSeconds,\n                    remarks: `Quiz completed with ${Math.round(finalScore)}% score in ${formatTime(timeElapsed)}`\n                });\n                (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.toast)({\n                    title: \"Quiz Completed!\",\n                    description: `Your score: ${Math.round(finalScore)}% - Performance saved successfully`\n                });\n            }\n        } catch (error) {\n            console.error('Error saving performance:', error);\n            (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.toast)({\n                title: \"Quiz Completed!\",\n                description: `Your score: ${Math.round(finalScore)}% - Note: Performance could not be saved`,\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetQuiz = ()=>{\n        setCurrentQuestionIndex(0);\n        setUserAnswers(new Array(questions.length).fill(''));\n        setIsQuizStarted(false);\n        setIsQuizCompleted(false);\n        setStartTime(0);\n        setTimeElapsed(0);\n        setScore(0);\n    };\n    if (questions.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                className: \"flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        children: \"Quiz\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: !documentId ? \"Select a document to view or generate a quiz\" : generating ? \"Loading quiz questions...\" : \"Generate a quiz based on your document content to test your understanding.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    generating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: loadQuiz,\n                                                disabled: !documentId,\n                                                className: \"w-full\",\n                                                variant: \"outline\",\n                                                children: \"Load Quiz\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: generateQuiz,\n                                                disabled: !documentId,\n                                                className: \"w-full\",\n                                                children: \"Generate New Quiz\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    !documentId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mt-2\",\n                                        children: \"Please select a document to generate a quiz.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this);\n    }\n    if (isQuizCompleted) {\n        const performanceLevel = (0,_lib_quiz_utils__WEBPACK_IMPORTED_MODULE_9__.getPerformanceLevel)(score);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                className: \"flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Quiz Completed!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        className: performanceLevel.color,\n                                        children: [\n                                            performanceLevel.level,\n                                            \" - \",\n                                            performanceLevel.description\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: `text-2xl font-bold ${performanceLevel.color}`,\n                                                        children: [\n                                                            Math.round(score),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"Your Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: formatTime(timeElapsed)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"Time Taken\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: resetQuiz,\n                                                variant: \"outline\",\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Retake Quiz\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: generateQuiz,\n                                                className: \"flex-1\",\n                                                children: \"Generate New Quiz\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                lineNumber: 293,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n            lineNumber: 292,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n            className: \"flex-1\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-mono\",\n                                        children: formatTime(timeElapsed)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: [\n                                    \"Question \",\n                                    currentQuestionIndex + 1,\n                                    \" of \",\n                                    questions.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_5__.Progress, {\n                        value: (currentQuestionIndex + 1) / questions.length * 100\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, this),\n                    !isQuizStarted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        children: \"Ready to Start?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: [\n                                            \"You have \",\n                                            questions.length,\n                                            \" questions to answer. Take your time and good luck!\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: startQuiz,\n                                    className: \"w-full\",\n                                    children: \"Start Quiz\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    children: [\n                                        \"Question \",\n                                        currentQuestionIndex + 1\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg\",\n                                        children: questions[currentQuestionIndex].question\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        placeholder: \"Type your answer here...\",\n                                        value: userAnswers[currentQuestionIndex],\n                                        onChange: (e)=>handleAnswerChange(e.target.value),\n                                        className: \"min-h-[100px]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: previousQuestion,\n                                                disabled: currentQuestionIndex === 0,\n                                                variant: \"outline\",\n                                                children: \"Previous\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, this),\n                                            currentQuestionIndex === questions.length - 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: submitQuiz,\n                                                disabled: loading || !userAnswers[currentQuestionIndex].trim(),\n                                                children: loading ? \"Submitting...\" : \"Submit Quiz\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                onClick: nextQuestion,\n                                                disabled: !userAnswers[currentQuestionIndex].trim(),\n                                                children: \"Next\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n            lineNumber: 335,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\quiz-interface.tsx\",\n        lineNumber: 334,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/quiz-interface.tsx\n");

/***/ }),

/***/ "(ssr)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cuboid.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/theme-provider */ \"(ssr)/./components/theme-provider.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\nfunction Sidebar({ isOpen, setIsOpen, isLoggedIn = false, username = \"\", onLogout }) {\n    const { theme, setTheme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // Determine text color based on theme\n    const textColor = theme === \"dark\" ? \"text-neutral-300\" : \"text-neutral-800\";\n    const iconColor = theme === \"dark\" ? \"text-neutral-400\" : \"text-neutral-600\";\n    const goToHome = ()=>{\n        router.push(\"/\");\n        setIsOpen(false);\n    };\n    const handleSignIn = ()=>{\n        router.push(\"/auth\");\n        setIsOpen(false);\n    };\n    const handleLogout = ()=>{\n        if (onLogout) {\n            onLogout();\n        }\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 0.5\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    className: \"fixed inset-0 bg-black z-40 md:hidden\",\n                    onClick: ()=>setIsOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    x: \"-100%\"\n                },\n                animate: {\n                    x: isOpen ? 0 : \"-100%\"\n                },\n                transition: {\n                    duration: 0.3,\n                    ease: \"easeInOut\"\n                },\n                className: `fixed top-0 left-0 h-full w-64 border-r flex flex-col z-50 ${theme === \"light\" ? \"border-black bg-white\" : \"border-border bg-background\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 hover:text-purple-500\",\n                                onClick: goToHome,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-6 w-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                            alt: \"Cognimosity Logo\",\n                                            fill: true,\n                                            className: \"object-contain\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold\",\n                                        children: \"Cognimosity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"h-8 w-8\",\n                                onClick: ()=>setIsOpen(false),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"ghost\",\n                            className: `w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`,\n                            onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                            children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: `h-4 w-4 mr-2 ${iconColor}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Light Mode\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: `h-4 w-4 mr-2 ${iconColor}`\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Dark Mode\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-full px-4 py-2 ${textColor} font-medium`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"History\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pl-6 space-y-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-neutral-500 py-2\",\n                                    children: \"No recent history\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-full px-4 py-2 ${textColor} font-medium`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Spaces\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pl-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: `w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: `h-4 w-4 mr-2 ${iconColor}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"My Space\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outline\",\n                                        className: `w-full justify-start gap-2 border-dashed border-neutral-700 text-sm ${textColor} hover:border-purple-500 hover:text-purple-500`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add space\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-full px-4 py-2 ${textColor} font-medium`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Help & Tools\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pl-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: `w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: `h-4 w-4 mr-2 ${iconColor}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Feedback\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: `w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: `h-4 w-4 mr-2 ${iconColor}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Quick Guide\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: `w-full justify-start px-4 py-2 text-sm ${textColor} hover:text-purple-500`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: `h-4 w-4 mr-2 ${iconColor}`\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Invite & Earn\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-auto p-3 space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"outline\",\n                                className: `w-full justify-center text-purple-500 hover:bg-purple-500/10 ${theme === \"light\" ? \"border-purple-500\" : \"border-purple-500\"}`,\n                                children: \"Upgrade\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `rounded-md p-2 text-center ${theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\"}`,\n                                children: isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    className: `w-full justify-center text-sm ${textColor} hover:text-purple-500`,\n                                    onClick: handleLogout,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: `h-4 w-4 mr-2 ${iconColor}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Log out (\",\n                                        username,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    className: `w-full justify-center text-sm ${textColor} hover:text-purple-500`,\n                                    onClick: handleSignIn,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: `h-4 w-4 mr-2 ${iconColor}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Sign in / Sign up\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst initialState = {\n    theme: \"system\",\n    setTheme: ()=>null\n};\nconst ThemeProviderContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(initialState);\nfunction ThemeProvider({ children, defaultTheme = \"system\", storageKey = \"ui-theme\", enableSystem = true, disableTransitionOnChange = false, ...props }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const root = window.document.documentElement;\n            const savedTheme = localStorage.getItem(storageKey);\n            if (savedTheme) {\n                setTheme(savedTheme);\n            } else if (enableSystem) {\n                const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n                setTheme(systemTheme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        storageKey,\n        enableSystem\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const root = window.document.documentElement;\n            if (disableTransitionOnChange) {\n                root.classList.add(\"transition-none\");\n                window.setTimeout({\n                    \"ThemeProvider.useEffect\": ()=>{\n                        root.classList.remove(\"transition-none\");\n                    }\n                }[\"ThemeProvider.useEffect\"], 0);\n            }\n            if (theme === \"system\" && enableSystem) {\n                const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n                root.classList.remove(\"light\", \"dark\");\n                root.classList.add(systemTheme);\n                return;\n            }\n            root.classList.remove(\"light\", \"dark\");\n            root.classList.add(theme);\n            localStorage.setItem(storageKey, theme);\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        disableTransitionOnChange,\n        enableSystem,\n        storageKey\n    ]);\n    const value = {\n        theme,\n        setTheme: (theme)=>{\n            setTheme(theme);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeProviderContext.Provider, {\n        ...props,\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeProviderContext);\n    if (context === undefined) throw new Error(\"useTheme must be used within a ThemeProvider\");\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2NhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLE1BQU1FLHFCQUFPRiw2Q0FBZ0IsQ0FHM0IsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUNYLDREQUNBRztRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxLQUFLTSxXQUFXLEdBQUc7QUFFbkIsTUFBTUMsMkJBQWFULDZDQUFnQixDQUdqQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEYsV0FBV0gsOENBQUVBLENBQUMsaUNBQWlDRztRQUM5QyxHQUFHQyxLQUFLOzs7Ozs7QUFHYkksV0FBV0QsV0FBVyxHQUFHO0FBRXpCLE1BQU1FLDBCQUFZViw2Q0FBZ0IsQ0FHaEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUNYLHNEQUNBRztRQUVELEdBQUdDLEtBQUs7Ozs7OztBQUdiSyxVQUFVRixXQUFXLEdBQUc7QUFFeEIsTUFBTUcsZ0NBQWtCWCw2Q0FBZ0IsQ0FHdEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLGlDQUFpQ0c7UUFDOUMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JNLGdCQUFnQkgsV0FBVyxHQUFHO0FBRTlCLE1BQU1JLDRCQUFjWiw2Q0FBZ0IsQ0FHbEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQUlELEtBQUtBO1FBQUtGLFdBQVdILDhDQUFFQSxDQUFDLFlBQVlHO1FBQWEsR0FBR0MsS0FBSzs7Ozs7O0FBRWhFTyxZQUFZSixXQUFXLEdBQUc7QUFFMUIsTUFBTUssMkJBQWFiLDZDQUFnQixDQUdqQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUMxQiw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEYsV0FBV0gsOENBQUVBLENBQUMsOEJBQThCRztRQUMzQyxHQUFHQyxLQUFLOzs7Ozs7QUFHYlEsV0FBV0wsV0FBVyxHQUFHO0FBRXVEIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpvdmFuXFxPbmVEcml2ZVxcRGVza3RvcFxcY29nbmltb3NpdHlfVUlcXGxhdGVzdCByZXBvXFxjb2duaV9hcGlcXGZyb250ZW5kXFxjb21wb25lbnRzXFx1aVxcY2FyZC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcclxuXHJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcclxuXHJcbmNvbnN0IENhcmQgPSBSZWFjdC5mb3J3YXJkUmVmPFxyXG4gIEhUTUxEaXZFbGVtZW50LFxyXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxyXG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXHJcbiAgPGRpdlxyXG4gICAgcmVmPXtyZWZ9XHJcbiAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICBcInJvdW5kZWQtbGcgYm9yZGVyIGJnLWNhcmQgdGV4dC1jYXJkLWZvcmVncm91bmQgc2hhZG93LXNtXCIsXHJcbiAgICAgIGNsYXNzTmFtZVxyXG4gICAgKX1cclxuICAgIHsuLi5wcm9wc31cclxuICAvPlxyXG4pKVxyXG5DYXJkLmRpc3BsYXlOYW1lID0gXCJDYXJkXCJcclxuXHJcbmNvbnN0IENhcmRIZWFkZXIgPSBSZWFjdC5mb3J3YXJkUmVmPFxyXG4gIEhUTUxEaXZFbGVtZW50LFxyXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxyXG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXHJcbiAgPGRpdlxyXG4gICAgcmVmPXtyZWZ9XHJcbiAgICBjbGFzc05hbWU9e2NuKFwiZmxleCBmbGV4LWNvbCBzcGFjZS15LTEuNSBwLTZcIiwgY2xhc3NOYW1lKX1cclxuICAgIHsuLi5wcm9wc31cclxuICAvPlxyXG4pKVxyXG5DYXJkSGVhZGVyLmRpc3BsYXlOYW1lID0gXCJDYXJkSGVhZGVyXCJcclxuXHJcbmNvbnN0IENhcmRUaXRsZSA9IFJlYWN0LmZvcndhcmRSZWY8XHJcbiAgSFRNTERpdkVsZW1lbnQsXHJcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XHJcbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcclxuICA8ZGl2XHJcbiAgICByZWY9e3JlZn1cclxuICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgIFwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCBsZWFkaW5nLW5vbmUgdHJhY2tpbmctdGlnaHRcIixcclxuICAgICAgY2xhc3NOYW1lXHJcbiAgICApfVxyXG4gICAgey4uLnByb3BzfVxyXG4gIC8+XHJcbikpXHJcbkNhcmRUaXRsZS5kaXNwbGF5TmFtZSA9IFwiQ2FyZFRpdGxlXCJcclxuXHJcbmNvbnN0IENhcmREZXNjcmlwdGlvbiA9IFJlYWN0LmZvcndhcmRSZWY8XHJcbiAgSFRNTERpdkVsZW1lbnQsXHJcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XHJcbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcclxuICA8ZGl2XHJcbiAgICByZWY9e3JlZn1cclxuICAgIGNsYXNzTmFtZT17Y24oXCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiLCBjbGFzc05hbWUpfVxyXG4gICAgey4uLnByb3BzfVxyXG4gIC8+XHJcbikpXHJcbkNhcmREZXNjcmlwdGlvbi5kaXNwbGF5TmFtZSA9IFwiQ2FyZERlc2NyaXB0aW9uXCJcclxuXHJcbmNvbnN0IENhcmRDb250ZW50ID0gUmVhY3QuZm9yd2FyZFJlZjxcclxuICBIVE1MRGl2RWxlbWVudCxcclxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cclxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxyXG4gIDxkaXYgcmVmPXtyZWZ9IGNsYXNzTmFtZT17Y24oXCJwLTYgcHQtMFwiLCBjbGFzc05hbWUpfSB7Li4ucHJvcHN9IC8+XHJcbikpXHJcbkNhcmRDb250ZW50LmRpc3BsYXlOYW1lID0gXCJDYXJkQ29udGVudFwiXHJcblxyXG5jb25zdCBDYXJkRm9vdGVyID0gUmVhY3QuZm9yd2FyZFJlZjxcclxuICBIVE1MRGl2RWxlbWVudCxcclxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cclxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxyXG4gIDxkaXZcclxuICAgIHJlZj17cmVmfVxyXG4gICAgY2xhc3NOYW1lPXtjbihcImZsZXggaXRlbXMtY2VudGVyIHAtNiBwdC0wXCIsIGNsYXNzTmFtZSl9XHJcbiAgICB7Li4ucHJvcHN9XHJcbiAgLz5cclxuKSlcclxuQ2FyZEZvb3Rlci5kaXNwbGF5TmFtZSA9IFwiQ2FyZEZvb3RlclwiXHJcblxyXG5leHBvcnQgeyBDYXJkLCBDYXJkSGVhZGVyLCBDYXJkRm9vdGVyLCBDYXJkVGl0bGUsIENhcmREZXNjcmlwdGlvbiwgQ2FyZENvbnRlbnQgfVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIkNhcmQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJkaXYiLCJkaXNwbGF5TmFtZSIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkQ29udGVudCIsIkNhcmRGb290ZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/progress.tsx":
/*!************************************!*\
  !*** ./components/ui/progress.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Progress: () => (/* binding */ Progress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-progress */ \"(ssr)/./node_modules/@radix-ui/react-progress/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Progress auto */ \n\n\n\nconst Progress = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, value, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative h-4 w-full overflow-hidden rounded-full bg-secondary\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Indicator, {\n            className: \"h-full w-full flex-1 bg-primary transition-all\",\n            style: {\n                transform: `translateX(-${100 - (value || 0)}%)`\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\progress.tsx\",\n            lineNumber: 20,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\progress.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nProgress.displayName = _radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/progress.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/scroll-area.tsx":
/*!***************************************!*\
  !*** ./components/ui/scroll-area.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollBar: () => (/* binding */ ScrollBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-scroll-area */ \"(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ScrollArea,ScrollBar auto */ \n\n\n\nconst ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative overflow-hidden\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                className: \"h-full w-full rounded-[inherit]\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 13,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScrollBar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 14,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.Corner, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\scroll-area.tsx\",\n                lineNumber: 15,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nScrollArea.displayName = \"ScrollArea\";\nconst ScrollBar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = \"vertical\", ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaScrollbar, {\n        ref: ref,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex touch-none select-none transition-colors\", orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent p-[1px]\", orientation === \"horizontal\" && \"h-2.5 border-t border-t-transparent p-[1px]\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollAreaThumb, {\n            className: \"relative flex-1 rounded-full bg-border\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\scroll-area.tsx\",\n            lineNumber: 35,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\scroll-area.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nScrollBar.displayName = \"ScrollBar\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/scroll-area.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/textarea.tsx":
/*!************************************!*\
  !*** ./components/ui/textarea.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n});\nTextarea.displayName = \"Textarea\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3RleHRhcmVhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUseUJBQVdGLDZDQUFnQixDQUcvQixDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzFCLHFCQUNFLDhEQUFDQztRQUNDSCxXQUFXSCw4Q0FBRUEsQ0FDWCxxVEFDQUc7UUFFRkUsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUNBSCxTQUFTTSxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxqb3ZhblxcT25lRHJpdmVcXERlc2t0b3BcXGNvZ25pbW9zaXR5X1VJXFxsYXRlc3QgcmVwb1xcY29nbmlfYXBpXFxmcm9udGVuZFxcY29tcG9uZW50c1xcdWlcXHRleHRhcmVhLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxyXG5cclxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxyXG5cclxuY29uc3QgVGV4dGFyZWEgPSBSZWFjdC5mb3J3YXJkUmVmPFxyXG4gIEhUTUxUZXh0QXJlYUVsZW1lbnQsXHJcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJ0ZXh0YXJlYVwiPlxyXG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDx0ZXh0YXJlYVxyXG4gICAgICBjbGFzc05hbWU9e2NuKFxyXG4gICAgICAgIFwiZmxleCBtaW4taC1bODBweF0gdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy1iYWNrZ3JvdW5kIHB4LTMgcHktMiB0ZXh0LWJhc2UgcmluZy1vZmZzZXQtYmFja2dyb3VuZCBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwIG1kOnRleHQtc21cIixcclxuICAgICAgICBjbGFzc05hbWVcclxuICAgICAgKX1cclxuICAgICAgcmVmPXtyZWZ9XHJcbiAgICAgIHsuLi5wcm9wc31cclxuICAgIC8+XHJcbiAgKVxyXG59KVxyXG5UZXh0YXJlYS5kaXNwbGF5TmFtZSA9IFwiVGV4dGFyZWFcIlxyXG5cclxuZXhwb3J0IHsgVGV4dGFyZWEgfVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIlRleHRhcmVhIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwidGV4dGFyZWEiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-document-time.ts":
/*!************************************!*\
  !*** ./hooks/use-document-time.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDocumentTime: () => (/* binding */ useDocumentTime)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ useDocumentTime auto */ \n\nfunction useDocumentTime({ documentId, enabled = true, isProcessingComplete = false, onQuizStart, onQuizEnd } = {}) {\n    const sessionIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const isActiveRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isPausedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const currentDocumentRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(documentId);\n    const sessionStartTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    // Get the correct API base URL\n    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';\n    const getAuthHeaders = ()=>{\n        const token = localStorage.getItem('token');\n        return {\n            'Content-Type': 'application/json',\n            'Authorization': `Token ${token}`\n        };\n    };\n    const startSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[startSession]\": async ()=>{\n            if (!enabled || !documentId || !isProcessingComplete || isActiveRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                const response = await fetch(`${API_BASE_URL}/users/platform-time/start/`, {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    sessionIdRef.current = data.session_id;\n                    isActiveRef.current = true;\n                    isPausedRef.current = false;\n                    currentDocumentRef.current = documentId;\n                    sessionStartTimeRef.current = new Date() // Record when session started\n                    ;\n                    console.log('Document time session started:', data.session_id, data.message);\n                }\n            } catch (error) {\n                console.error('Error starting document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[startSession]\"], [\n        documentId,\n        enabled,\n        isProcessingComplete\n    ]);\n    const pauseSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[pauseSession]\": async (reason = 'manual')=>{\n            if (!documentId || !isActiveRef.current || isPausedRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                const response = await fetch(`${API_BASE_URL}/users/platform-time/pause/`, {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId,\n                        reason: reason\n                    })\n                });\n                if (response.ok) {\n                    isPausedRef.current = true;\n                    console.log(`Document time session paused: ${reason}`);\n                }\n            } catch (error) {\n                console.error('Error pausing document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[pauseSession]\"], [\n        documentId\n    ]);\n    const resumeSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[resumeSession]\": async ()=>{\n            if (!documentId || !isActiveRef.current || !isPausedRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                const response = await fetch(`${API_BASE_URL}/users/platform-time/resume/`, {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                if (response.ok) {\n                    isPausedRef.current = false;\n                    console.log('Document time session resumed');\n                }\n            } catch (error) {\n                console.error('Error resuming document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[resumeSession]\"], [\n        documentId\n    ]);\n    const endSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[endSession]\": async ()=>{\n            if (!documentId || !isActiveRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                await fetch(`${API_BASE_URL}/users/platform-time/end/`, {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                sessionIdRef.current = null;\n                isActiveRef.current = false;\n                isPausedRef.current = false;\n                sessionStartTimeRef.current = null;\n                console.log('Document time session ended');\n            } catch (error) {\n                console.error('Error ending document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[endSession]\"], [\n        documentId\n    ]);\n    // Quiz control functions\n    const startQuiz = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[startQuiz]\": ()=>{\n            pauseSession('quiz');\n            onQuizStart?.();\n        }\n    }[\"useDocumentTime.useCallback[startQuiz]\"], [\n        pauseSession,\n        onQuizStart\n    ]);\n    const endQuiz = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[endQuiz]\": ()=>{\n            resumeSession();\n            onQuizEnd?.();\n        }\n    }[\"useDocumentTime.useCallback[endQuiz]\"], [\n        resumeSession,\n        onQuizEnd\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useDocumentTime.useEffect\": ()=>{\n            if (!enabled || !documentId || !isProcessingComplete) return;\n            // If document changed, end previous session and start new one\n            if (currentDocumentRef.current !== documentId) {\n                if (isActiveRef.current) {\n                    endSession();\n                }\n                currentDocumentRef.current = documentId;\n            }\n            // Start session only when processing is complete (learning phase begins)\n            startSession();\n            // Handle page unload - end session when user leaves\n            const handleBeforeUnload = {\n                \"useDocumentTime.useEffect.handleBeforeUnload\": ()=>{\n                    endSession();\n                }\n            }[\"useDocumentTime.useEffect.handleBeforeUnload\"];\n            // Handle navigation away from process page - end session\n            const handleRouteChange = {\n                \"useDocumentTime.useEffect.handleRouteChange\": ()=>{\n                    if (window.location.pathname !== '/process') {\n                        endSession();\n                    }\n                }\n            }[\"useDocumentTime.useEffect.handleRouteChange\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            window.addEventListener('popstate', handleRouteChange);\n            return ({\n                \"useDocumentTime.useEffect\": ()=>{\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                    window.removeEventListener('popstate', handleRouteChange);\n                    endSession();\n                }\n            })[\"useDocumentTime.useEffect\"];\n        }\n    }[\"useDocumentTime.useEffect\"], [\n        documentId,\n        enabled,\n        isProcessingComplete,\n        startSession,\n        endSession\n    ]);\n    return {\n        sessionId: sessionIdRef.current,\n        isActive: isActiveRef.current,\n        isPaused: isPausedRef.current,\n        sessionStartTime: sessionStartTimeRef.current,\n        startQuiz,\n        endQuiz,\n        pauseSession,\n        resumeSession,\n        endSession\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-document-time.ts\n");

/***/ }),

/***/ "(ssr)/./hooks/use-toast.ts":
/*!****************************!*\
  !*** ./hooks/use-toast.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ // Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   blueprintApi: () => (/* binding */ blueprintApi),\n/* harmony export */   chapterApi: () => (/* binding */ chapterApi),\n/* harmony export */   chatApi: () => (/* binding */ chatApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   documentApi: () => (/* binding */ documentApi),\n/* harmony export */   flashcardsApi: () => (/* binding */ flashcardsApi),\n/* harmony export */   flowchartApi: () => (/* binding */ flowchartApi),\n/* harmony export */   generateChapters: () => (/* binding */ generateChapters),\n/* harmony export */   generateFlashcards: () => (/* binding */ generateFlashcards),\n/* harmony export */   generateFlowchart: () => (/* binding */ generateFlowchart),\n/* harmony export */   generateQuiz: () => (/* binding */ generateQuiz),\n/* harmony export */   performanceApi: () => (/* binding */ performanceApi),\n/* harmony export */   processBlueprint: () => (/* binding */ processBlueprint),\n/* harmony export */   quizApi: () => (/* binding */ quizApi),\n/* harmony export */   summaryApi: () => (/* binding */ summaryApi),\n/* harmony export */   timeTrackingApi: () => (/* binding */ timeTrackingApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\n// Base URL for API requests\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';\n// Create axios instance with default config\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL\n});\n// Add request interceptor to include auth token\napi.interceptors.request.use((config)=>{\n    const token = localStorage.getItem('token');\n    if (token) {\n        config.headers.Authorization = `Token ${token}`;\n    }\n    return config;\n});\n// Auth API endpoints\nconst authApi = {\n    signIn: async (credentials)=>{\n        try {\n            const response = await api.post('/users/login/', credentials);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    register: async (userData)=>{\n        try {\n            const response = await api.post('/users/register/', userData);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    verifyOTP: async (data)=>{\n        try {\n            const response = await api.post('/users/verify-otp/', data);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    logout: async ()=>{\n        try {\n            const response = await api.post('/users/logout/');\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n};\n// Document API endpoints\nconst documentApi = {\n    // Existing document endpoints\n    uploadDocument: async (file, groupId)=>{\n        const formData = new FormData();\n        formData.append('file', file);\n        if (groupId) {\n            formData.append('group', groupId.toString());\n        }\n        const response = await api.post('/documents/upload/', formData);\n        return response.data;\n    },\n    getDocuments: async (groupId)=>{\n        const params = groupId ? {\n            group: groupId\n        } : {};\n        const response = await api.get('/documents/', {\n            params\n        });\n        return response.data;\n    },\n    // Document group endpoints\n    createGroup: async (data)=>{\n        const response = await api.post('/documents/groups/', data);\n        return response.data;\n    },\n    getGroups: async ()=>{\n        const response = await api.get('/documents/groups/');\n        return response.data;\n    },\n    updateGroup: async (groupId, data)=>{\n        const response = await api.patch(`/documents/groups/${groupId}/`, data);\n        return response.data;\n    },\n    deleteGroup: async (groupId)=>{\n        const response = await api.delete(`/documents/groups/${groupId}/`);\n        return response.data;\n    },\n    // Document management within groups\n    moveDocument: async (documentId, groupId)=>{\n        const response = await api.patch(`/documents/${documentId}/`, {\n            group: groupId\n        });\n        return response.data;\n    },\n    getDocumentStatus: async (documentId)=>{\n        try {\n            const response = await api.get(`/documents/${documentId}/`);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getDocumentDetails: async (documentId)=>{\n        try {\n            const response = await api.get(`/documents/${documentId}/`);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getDocumentFileUrl: (documentId)=>{\n        // Construct the file URL for preview with proper authentication\n        return `${API_BASE_URL}/documents/${documentId}/file/`;\n    },\n    getDocumentFileBlob: async (documentId)=>{\n        try {\n            const response = await api.get(`/documents/${documentId}/file/`, {\n                responseType: 'blob'\n            });\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n};\n// Chat API endpoints\nconst chatApi = {\n    sendMessage: async (message, documentId, model = 'openai')=>{\n        try {\n            const response = await api.post('/chat/message/', {\n                message,\n                document_id: documentId,\n                model\n            });\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getHistory: async ()=>{\n        try {\n            const response = await api.get('/chat/history/');\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n};\n// Performance API endpoints\nconst performanceApi = {\n    createPerformance: async (performanceData)=>{\n        try {\n            const response = await api.post('/users/performance/', performanceData);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getPerformances: async (documentId)=>{\n        try {\n            const url = documentId ? `/users/performance/?document=${documentId}` : '/users/performance/';\n            const response = await api.get(url);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getStudentPerformances: async (studentId)=>{\n        try {\n            const response = await api.get(`/users/performance/?student=${studentId}`);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    },\n    getPerformanceStats: async (documentId)=>{\n        try {\n            const response = await api.get(`/users/performance/?document=${documentId}`);\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n};\n// Blueprint API\nconst blueprintApi = {\n    // Get existing blueprint using DRF endpoint\n    getBlueprint: async (documentId)=>{\n        try {\n            const response = await api.get(`/documents/${documentId}/blueprint/`);\n            return response.data;\n        } catch (error) {\n            console.error('Error getting blueprint:', error);\n            throw error;\n        }\n    },\n    // Upload blueprint file or text using DRF endpoint\n    uploadBlueprint: async (documentId, fileOrText)=>{\n        const formData = new FormData();\n        if (fileOrText instanceof File) {\n            formData.append('file', fileOrText);\n        } else {\n            const blob = new Blob([\n                fileOrText\n            ], {\n                type: 'text/plain'\n            });\n            formData.append('file', new File([\n                blob\n            ], 'blueprint.txt', {\n                type: 'text/plain'\n            }));\n        }\n        try {\n            const response = await api.post(`/documents/${documentId}/blueprint/`, formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            let errorMsg = 'Failed to upload blueprint';\n            // Fix: cast error to any for axios error shape\n            const err = error;\n            if (err.response && err.response.data && err.response.data.error) {\n                errorMsg = err.response.data.error;\n            }\n            throw new Error(errorMsg);\n        }\n    },\n    // Generate new blueprint using FastAPI\n    generateBlueprint: async (documentId, focusAreas, llmModel = 'gemini')=>{\n        try {\n            const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';\n            const token = localStorage.getItem('token');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${fastApiUrl}/generate-blueprint/${documentId}`, {\n                document_id: documentId,\n                llm_model: llmModel,\n                focus_areas: focusAreas\n            }, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error generating blueprint:', error);\n            throw error;\n        }\n    },\n    // Get blueprint topics for a document\n    getBlueprintTopics: async (documentId)=>{\n        try {\n            const response = await api.get(`/documents/topics/by_document/?document_id=${documentId}`);\n            return response.data;\n        } catch (error) {\n            console.error('Error getting blueprint topics:', error);\n            throw error;\n        }\n    },\n    // Get content for a specific blueprint topic by id\n    getBlueprintTopicContent: async (topicId)=>{\n        try {\n            // Use the correct backend endpoint for topic content details\n            const response = await api.get(`/documents/topics/${topicId}/content_details/`);\n            return response.data;\n        } catch (error) {\n            console.error('Error getting topic content:', error);\n            throw error;\n        }\n    }\n};\n// Legacy blueprint function for backward compatibility\nconst processBlueprint = async (documentId, blueprintText, llmModel = 'openai' // can be 'gemini', 'rag', 'openai', etc.\n)=>{\n    try {\n        const response = await api.post(`/process-blueprint/${documentId}/`, {\n            document_id: documentId,\n            blueprint_text: blueprintText,\n            llm_model: llmModel\n        });\n        return response.data;\n    } catch (error) {\n        console.error('Error processing blueprint:', error);\n        throw error;\n    }\n};\n// Flashcards API\nconst flashcardsApi = {\n    // Get flashcards for a document\n    getFlashcards: async (documentId)=>{\n        try {\n            const response = await api.get(`/documents/flashcards/${documentId}/`);\n            return response.data;\n        } catch (error) {\n            console.error('Error getting flashcards:', error);\n            throw error;\n        }\n    },\n    generateFlashcards: async (documentId, numFlashcards = 10, llmModel = 'gemini')=>{\n        try {\n            const response = await api.post(`/api/documents/flashcards/generate/`, {\n                document_id: documentId,\n                num_flashcards: numFlashcards,\n                llm_model: llmModel,\n                force: true // Force regeneration of flashcards\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error generating flashcards:', error);\n            throw error;\n        }\n    },\n    // Generate flashcards using FastAPI (enhanced version)\n    generateFlashcardsEnhanced: async (documentId, numFlashcards = 10, llmModel = 'gemini')=>{\n        try {\n            const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';\n            const token = localStorage.getItem('token');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${fastApiUrl}/generate-flashcards/${documentId}`, {\n                document_id: documentId,\n                llm_model: llmModel,\n                num_flashcards: numFlashcards\n            }, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error generating enhanced flashcards:', error);\n            throw error;\n        }\n    }\n};\n// Flowchart API\nconst flowchartApi = {\n    // Get flowchart for a document\n    getFlowchart: async (documentId)=>{\n        try {\n            const response = await api.get(`/documents/flowcharts/${documentId}/`);\n            return response.data;\n        } catch (error) {\n            console.error('Error getting flowchart:', error);\n            throw error;\n        }\n    },\n    generateFlowchart: async (documentId, llmModel = 'gemini')=>{\n        try {\n            const response = await api.post(`/documents/flowcharts/generate/`, {\n                document_id: documentId,\n                llm_model: llmModel,\n                force: true // Force regeneration of flowchart\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error generating flowchart:', error);\n            throw error;\n        }\n    },\n    // Generate flowchart using FastAPI (enhanced version)\n    generateFlowchartEnhanced: async (documentId, llmModel = 'gemini')=>{\n        try {\n            const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';\n            const token = localStorage.getItem('token');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${fastApiUrl}/generate-flowchart/${documentId}`, {\n                document_id: documentId,\n                llm_model: llmModel\n            }, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error generating enhanced flowchart:', error);\n            throw error;\n        }\n    }\n};\n// Summary API\nconst summaryApi = {\n    // Get existing summary\n    getSummary: async (documentId)=>{\n        try {\n            const response = await api.get(`/summaries/${documentId}/`);\n            return response.data;\n        } catch (error) {\n            console.error('Error getting summary:', error);\n            throw error;\n        }\n    },\n    // Generate new summary using FastAPI\n    generateSummary: async (documentId, summaryType = 'comprehensive', llmModel = 'gemini')=>{\n        try {\n            const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';\n            const token = localStorage.getItem('token');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${fastApiUrl}/generate-summary/${documentId}`, {\n                document_id: documentId,\n                llm_model: llmModel,\n                summary_type: summaryType\n            }, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error generating summary:', error);\n            throw error;\n        }\n    }\n};\n// Quiz API\nconst quizApi = {\n    // Combined GET/POST endpoint - preferred method\n    getOrGenerateQuiz: async (documentId)=>{\n        try {\n            const response = await api.get(`/documents/${documentId}/quiz/`);\n            return response.data;\n        } catch (error) {\n            console.error('Error getting/generating quiz:', error);\n            throw error;\n        }\n    },\n    generateQuiz: async (documentId, numQuestions = 5, llmModel = 'gemini')=>{\n        try {\n            const response = await api.post(`/documents/${documentId}/quiz/`, {\n                num_questions: numQuestions,\n                llm_model: llmModel\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error generating quiz:', error);\n            throw error;\n        }\n    },\n    // Generate quiz using FastAPI (enhanced version)\n    generateQuizEnhanced: async (documentId, numQuestions = 5, llmModel = 'gemini')=>{\n        try {\n            const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';\n            const token = localStorage.getItem('token');\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${fastApiUrl}/generate-quiz/${documentId}`, {\n                document_id: documentId,\n                llm_model: llmModel,\n                num_questions: numQuestions\n            }, {\n                headers: {\n                    'Authorization': `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error generating enhanced quiz:', error);\n            throw error;\n        }\n    },\n    // Legacy method for backward compatibility\n    getQuizzes: async (documentId)=>{\n        try {\n            const response = await api.get(`/quizzes/?document=${documentId}`);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching quizzes:', error);\n            throw error;\n        }\n    },\n    submitQuizAnswer: async (quizId, answers)=>{\n        try {\n            const response = await api.post(`/quizzes/${quizId}/submit/`, {\n                answers\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error submitting quiz:', error);\n            throw error;\n        }\n    }\n};\n// Time Tracking API\nconst timeTrackingApi = {\n    // Start a new document session\n    startSession: async (documentId)=>{\n        try {\n            const response = await api.post('/users/time-tracking/start-session/', {\n                document_id: documentId\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error starting time tracking session:', error);\n            throw error;\n        }\n    },\n    // Update session activity (heartbeat)\n    updateActivity: async (sessionId)=>{\n        try {\n            const response = await api.post('/users/time-tracking/update-activity/', {\n                session_id: sessionId\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error updating session activity:', error);\n            throw error;\n        }\n    },\n    // Pause session (e.g., when starting quiz)\n    pauseSession: async (sessionId, notes)=>{\n        try {\n            const response = await api.post('/users/time-tracking/pause-resume/', {\n                session_id: sessionId,\n                action: 'pause',\n                notes: notes || 'Session paused'\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error pausing session:', error);\n            throw error;\n        }\n    },\n    // Resume session (e.g., after completing quiz)\n    resumeSession: async (sessionId)=>{\n        try {\n            const response = await api.post('/users/time-tracking/pause-resume/', {\n                session_id: sessionId,\n                action: 'resume'\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error resuming session:', error);\n            throw error;\n        }\n    },\n    // End session\n    endSession: async (sessionId)=>{\n        try {\n            const response = await api.post('/users/time-tracking/end-session/', {\n                session_id: sessionId\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error ending session:', error);\n            throw error;\n        }\n    },\n    // Get document time statistics\n    getDocumentStats: async (documentId)=>{\n        try {\n            const response = await api.get(`/users/time-tracking/document/${documentId}/stats/`);\n            return response.data;\n        } catch (error) {\n            console.error('Error getting document stats:', error);\n            throw error;\n        }\n    },\n    // Get user time tracking overview\n    getUserOverview: async ()=>{\n        try {\n            const response = await api.get('/users/time-tracking/overview/');\n            return response.data;\n        } catch (error) {\n            console.error('Error getting user overview:', error);\n            throw error;\n        }\n    },\n    // Cleanup abandoned sessions (admin function)\n    cleanupSessions: async ()=>{\n        try {\n            const response = await api.post('/users/time-tracking/cleanup/');\n            return response.data;\n        } catch (error) {\n            console.error('Error cleaning up sessions:', error);\n            throw error;\n        }\n    }\n};\n// Chapter API\nconst chapterApi = {\n    // Combined GET/POST endpoint - preferred method\n    getOrGenerateChapters: async (documentId)=>{\n        try {\n            const response = await api.get(`/documents/${documentId}/chapters/`);\n            return response.data;\n        } catch (error) {\n            console.error('Error getting/generating chapters:', error);\n            throw error;\n        }\n    },\n    generateChapters: async (documentId, numChapters = 5, llmModel = 'gemini')=>{\n        try {\n            const response = await api.post(`/documents/${documentId}/chapters/`, {\n                num_chapters: numChapters,\n                llm_model: llmModel\n            });\n            return response.data;\n        } catch (error) {\n            console.error('Error generating chapters:', error);\n            throw error;\n        }\n    }\n};\n// Legacy exports for backward compatibility\nconst generateQuiz = quizApi.generateQuiz;\nconst generateFlowchart = flowchartApi.generateFlowchart;\nconst generateFlashcards = flashcardsApi.generateFlashcards;\nconst generateChapters = chapterApi.generateChapters;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/quiz-utils.ts":
/*!***************************!*\
  !*** ./lib/quiz-utils.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateImprovementTrend: () => (/* binding */ calculateImprovementTrend),\n/* harmony export */   calculateQuizScore: () => (/* binding */ calculateQuizScore),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   formatTimeFromSeconds: () => (/* binding */ formatTimeFromSeconds),\n/* harmony export */   generatePerformanceInsights: () => (/* binding */ generatePerformanceInsights),\n/* harmony export */   getPerformanceLevel: () => (/* binding */ getPerformanceLevel)\n/* harmony export */ });\n// Utility functions for quiz functionality\n/**\r\n * Calculate quiz score based on user answers and correct answers\r\n */ function calculateQuizScore(questions, userAnswers) {\n    if (questions.length === 0 || userAnswers.length === 0) return 0;\n    let correctAnswers = 0;\n    questions.forEach((question, index)=>{\n        const userAnswer = userAnswers[index]?.toLowerCase().trim() || '';\n        const correctAnswer = question.answer.toLowerCase().trim();\n        // Simple matching - check if user answer contains key terms from correct answer\n        // or if correct answer contains user answer\n        if (userAnswer && (userAnswer.includes(correctAnswer) || correctAnswer.includes(userAnswer) || calculateSimilarity(userAnswer, correctAnswer) > 0.7)) {\n            correctAnswers++;\n        }\n    });\n    return correctAnswers / questions.length * 100;\n}\n/**\r\n * Calculate similarity between two strings using simple word matching\r\n */ function calculateSimilarity(str1, str2) {\n    const words1 = str1.toLowerCase().split(/\\s+/).filter((word)=>word.length > 2);\n    const words2 = str2.toLowerCase().split(/\\s+/).filter((word)=>word.length > 2);\n    if (words1.length === 0 || words2.length === 0) return 0;\n    const commonWords = words1.filter((word)=>words2.includes(word));\n    return commonWords.length / Math.max(words1.length, words2.length);\n}\n/**\r\n * Format time duration in milliseconds to readable format\r\n */ function formatDuration(milliseconds) {\n    const seconds = Math.floor(milliseconds / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n    if (hours > 0) {\n        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;\n    } else if (minutes > 0) {\n        return `${minutes}m ${seconds % 60}s`;\n    } else {\n        return `${seconds}s`;\n    }\n}\n/**\r\n * Format time duration in seconds to readable format\r\n */ function formatTimeFromSeconds(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    if (minutes > 0) {\n        return `${minutes}m ${remainingSeconds}s`;\n    } else {\n        return `${remainingSeconds}s`;\n    }\n}\n/**\r\n * Get performance level based on score\r\n */ function getPerformanceLevel(score) {\n    if (score >= 90) {\n        return {\n            level: \"Excellent\",\n            color: \"text-green-600\",\n            description: \"Outstanding performance!\"\n        };\n    } else if (score >= 80) {\n        return {\n            level: \"Good\",\n            color: \"text-blue-600\",\n            description: \"Well done!\"\n        };\n    } else if (score >= 70) {\n        return {\n            level: \"Average\",\n            color: \"text-yellow-600\",\n            description: \"Room for improvement\"\n        };\n    } else if (score >= 60) {\n        return {\n            level: \"Below Average\",\n            color: \"text-orange-600\",\n            description: \"Needs more practice\"\n        };\n    } else {\n        return {\n            level: \"Poor\",\n            color: \"text-red-600\",\n            description: \"Requires significant improvement\"\n        };\n    }\n}\n/**\r\n * Calculate improvement trend from performance history\r\n */ function calculateImprovementTrend(scores) {\n    if (scores.length < 2) {\n        return {\n            trend: 'stable',\n            percentage: 0\n        };\n    }\n    const firstHalf = scores.slice(0, Math.floor(scores.length / 2));\n    const secondHalf = scores.slice(Math.floor(scores.length / 2));\n    const firstAvg = firstHalf.reduce((sum, score)=>sum + score, 0) / firstHalf.length;\n    const secondAvg = secondHalf.reduce((sum, score)=>sum + score, 0) / secondHalf.length;\n    const difference = secondAvg - firstAvg;\n    const percentage = Math.abs(difference);\n    if (Math.abs(difference) < 5) {\n        return {\n            trend: 'stable',\n            percentage: 0\n        };\n    } else if (difference > 0) {\n        return {\n            trend: 'improving',\n            percentage\n        };\n    } else {\n        return {\n            trend: 'declining',\n            percentage\n        };\n    }\n}\n/**\r\n * Generate performance insights based on quiz data\r\n */ function generatePerformanceInsights(performances) {\n    const insights = [];\n    if (performances.length === 0) {\n        return [\n            \"Take your first quiz to see performance insights!\"\n        ];\n    }\n    const scores = performances.map((p)=>p.quiz_score);\n    const times = performances.map((p)=>p.time_taken);\n    const avgScore = scores.reduce((sum, score)=>sum + score, 0) / scores.length;\n    const avgTime = times.reduce((sum, time)=>sum + time, 0) / times.length;\n    // Score insights\n    if (avgScore >= 85) {\n        insights.push(\"🎉 Excellent overall performance! Keep up the great work.\");\n    } else if (avgScore >= 70) {\n        insights.push(\"👍 Good performance overall. Focus on areas where you scored lower.\");\n    } else {\n        insights.push(\"📚 Consider reviewing the material more thoroughly before taking quizzes.\");\n    }\n    // Time insights\n    if (avgTime < 300) {\n        insights.push(\"⚡ You're completing quizzes quickly. Make sure you're reading questions carefully.\");\n    } else if (avgTime > 900) {\n        insights.push(\"🐌 You're taking time to think through answers, which is good for accuracy.\");\n    }\n    // Trend insights\n    const trend = calculateImprovementTrend(scores);\n    if (trend.trend === 'improving') {\n        insights.push(`📈 Your scores are improving by ${trend.percentage.toFixed(1)}% on average!`);\n    } else if (trend.trend === 'declining') {\n        insights.push(`📉 Your recent scores have declined. Consider reviewing the material again.`);\n    }\n    return insights;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/quiz-utils.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\r\n * Combines multiple class names into a single string, merging Tailwind CSS classes properly\r\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFeEM7O0NBRUMsR0FDTSxTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam92YW5cXE9uZURyaXZlXFxEZXNrdG9wXFxjb2duaW1vc2l0eV9VSVxcbGF0ZXN0IHJlcG9cXGNvZ25pX2FwaVxcZnJvbnRlbmRcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxyXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcclxuXHJcbi8qKlxyXG4gKiBDb21iaW5lcyBtdWx0aXBsZSBjbGFzcyBuYW1lcyBpbnRvIGEgc2luZ2xlIHN0cmluZywgbWVyZ2luZyBUYWlsd2luZCBDU1MgY2xhc3NlcyBwcm9wZXJseVxyXG4gKi9cclxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XHJcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cquiz%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cquiz%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/quiz/page.tsx */ \"(ssr)/./app/quiz/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2pvdmFuJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDY29nbmltb3NpdHlfVUklNUMlNUNsYXRlc3QlMjByZXBvJTVDJTVDY29nbmlfYXBpJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNxdWl6JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtKQUErSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcam92YW5cXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxjb2duaW1vc2l0eV9VSVxcXFxsYXRlc3QgcmVwb1xcXFxjb2duaV9hcGlcXFxcZnJvbnRlbmRcXFxcYXBwXFxcXHF1aXpcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Capp%5C%5Cquiz%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjovan%5C%5COneDrive%5C%5CDesktop%5C%5Ccognimosity_UI%5C%5Clatest%20repo%5C%5Ccogni_api%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?d272":
/*!********************************!*\
  !*** supports-color (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/@radix-ui","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/tailwind-merge","vendor-chunks/sonner","vendor-chunks/lucide-react","vendor-chunks/motion-dom","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/class-variance-authority","vendor-chunks/ms","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/motion-utils","vendor-chunks/@swc","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fquiz%2Fpage&page=%2Fquiz%2Fpage&appPaths=%2Fquiz%2Fpage&pagePath=private-next-app-dir%2Fquiz%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjovan%5COneDrive%5CDesktop%5Ccognimosity_UI%5Clatest%20repo%5Ccogni_api%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjovan%5COneDrive%5CDesktop%5Ccognimosity_UI%5Clatest%20repo%5Ccogni_api%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();