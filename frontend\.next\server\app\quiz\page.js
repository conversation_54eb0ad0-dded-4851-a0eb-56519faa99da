(()=>{var e={};e.id=6602,e.ids=[6602],e.modules={1996:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>G});var s=r(60687),a=r(43210),i=r(16189),n=r(24934),l=r(62688);let o=(0,l.A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var c=r(55192),d=r(15616),u=r(11273),m=r(14163),p="Progress",[x,h]=(0,u.A)(p),[f,g]=x(p),y=a.forwardRef((e,t)=>{var r,a;let{__scopeProgress:i,value:n=null,max:l,getValueLabel:o=b,...c}=e;(l||0===l)&&!z(l)&&console.error((r=`${l}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let d=z(l)?l:100;null===n||k(n,d)||console.error((a=`${n}`,`Invalid prop \`value\` of value \`${a}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let u=k(n,d)?n:null,p=w(u)?o(u,d):void 0;return(0,s.jsx)(f,{scope:i,value:u,max:d,children:(0,s.jsx)(m.sG.div,{"aria-valuemax":d,"aria-valuemin":0,"aria-valuenow":w(u)?u:void 0,"aria-valuetext":p,role:"progressbar","data-state":N(u,d),"data-value":u??void 0,"data-max":d,...c,ref:t})})});y.displayName=p;var v="ProgressIndicator",j=a.forwardRef((e,t)=>{let{__scopeProgress:r,...a}=e,i=g(v,r);return(0,s.jsx)(m.sG.div,{"data-state":N(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...a,ref:t})});function b(e,t){return`${Math.round(e/t*100)}%`}function N(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function w(e){return"number"==typeof e}function z(e){return w(e)&&!isNaN(e)&&e>0}function k(e,t){return w(e)&&!isNaN(e)&&e<=t&&e>=0}j.displayName=v;var S=r(96241);let q=a.forwardRef(({className:e,value:t,...r},a)=>(0,s.jsx)(y,{ref:a,className:(0,S.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:(0,s.jsx)(j,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));q.displayName=y.displayName;var C=r(82080),P=r(28947);let A=(0,l.A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),R=(0,l.A)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]);var _=r(13943),T=r(48730),$=r(97905),D=r(88920),I=r(69327),E=r(59556),O=r(71702),M=r(59531);function Q({documentId:e,onQuizStart:t,onQuizEnd:r}){let[i,l]=(0,a.useState)([]),[o,u]=(0,a.useState)(0),[m,p]=(0,a.useState)([]),[x,h]=(0,a.useState)(!1),[f,g]=(0,a.useState)(!1),[y,v]=(0,a.useState)(0),[j,b]=(0,a.useState)(0),[N,w]=(0,a.useState)(0),[z,k]=(0,a.useState)(!1),[S,Q]=(0,a.useState)(!1),U=e=>{let t=Math.floor(e/1e3),r=Math.floor(t/60);return`${r.toString().padStart(2,"0")}:${(t%60).toString().padStart(2,"0")}`},L=async()=>{if(!e){(0,O.oR)({title:"Error",description:"No document selected for quiz",variant:"destructive"});return}Q(!0);try{let t=await E.quizApi.getOrGenerateQuiz(e);if(t.quizzes&&t.quizzes.length>0)l(t.quizzes),p(Array(t.quizzes.length).fill("")),t.generated?(0,O.oR)({title:"Success",description:"Quiz generated successfully!"}):(0,O.oR)({title:"Quiz Loaded",description:"Existing quiz loaded successfully!"});else throw Error("No quiz data received")}catch(e){console.error("Error loading/generating quiz:",e),(0,O.oR)({title:"Error",description:"Failed to load or generate quiz. Please try again.",variant:"destructive"})}finally{Q(!1)}},F=async()=>{if(!e){(0,O.oR)({title:"Error",description:"No document selected for quiz generation",variant:"destructive"});return}Q(!0);try{let t=await E.quizApi.generateQuiz(e,5);if(t.quizzes&&t.quizzes.length>0)l(t.quizzes),p(Array(t.quizzes.length).fill("")),(0,O.oR)({title:"Success",description:"New quiz generated successfully!"});else throw Error("No quiz data received")}catch(e){console.error("Error generating quiz:",e),(0,O.oR)({title:"Error",description:"Failed to generate quiz. Please try again.",variant:"destructive"})}finally{Q(!1)}},B=async()=>{h(!0),v(Date.now()),b(0);try{t?.()}catch(e){console.error("Failed to pause time tracking for quiz:",e)}},G=e=>{let t=[...m];t[o]=e,p(t)},Z=()=>(0,M.WS)(i,m),H=async()=>{k(!0);let t=Z(),s=Math.floor(j/1e3);w(t),g(!0);try{r?.()}catch(e){console.error("Failed to resume time tracking after quiz:",e)}try{let r=JSON.parse(localStorage.getItem("user")||"{}");r.id&&e&&(await E.performanceApi.createPerformance({student:r.id,document:e,quiz_score:t,time_taken:s,remarks:`Quiz completed with ${Math.round(t)}% score in ${U(j)}`}),(0,O.oR)({title:"Quiz Completed!",description:`Your score: ${Math.round(t)}% - Performance saved successfully`}))}catch(e){console.error("Error saving performance:",e),(0,O.oR)({title:"Quiz Completed!",description:`Your score: ${Math.round(t)}% - Note: Performance could not be saved`,variant:"destructive"})}finally{k(!1)}};if(0===i.length)return(0,s.jsx)("div",{className:"flex flex-col h-full",children:(0,s.jsx)(I.F,{className:"flex-1",children:(0,s.jsx)("div",{className:"p-6 space-y-6",children:(0,s.jsx)($.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,s.jsxs)(c.Zp,{className:"border border-border bg-card",children:[(0,s.jsxs)(c.aR,{className:"text-center",children:[(0,s.jsx)($.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"mx-auto mb-4",children:(0,s.jsx)("div",{className:"w-16 h-16 bg-primary rounded-full flex items-center justify-center",children:(0,s.jsx)(C.A,{className:"h-8 w-8 text-primary-foreground"})})}),(0,s.jsx)(c.ZB,{className:"text-2xl text-foreground",children:"Quiz Center"}),(0,s.jsx)(c.BT,{className:"text-base",children:e?S?"Creating your personalized quiz...":"Ready to test your knowledge? Generate a quiz based on your document content.":"Select a document to view or generate a quiz"})]}),(0,s.jsxs)(c.Wu,{className:"space-y-4",children:[S?(0,s.jsxs)($.P.div,{initial:{opacity:0},animate:{opacity:1},className:"flex flex-col items-center justify-center py-8 space-y-4",children:[(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-4 border-muted border-t-primary"})}),(0,s.jsx)($.P.p,{animate:{opacity:[.5,1,.5]},transition:{duration:2,repeat:1/0},className:"text-sm text-muted-foreground",children:"Analyzing your document and crafting questions..."})]}):(0,s.jsxs)($.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},className:"grid gap-3",children:[(0,s.jsxs)(n.$,{onClick:L,disabled:!e,variant:"outline",className:"w-full h-12",size:"lg",children:[(0,s.jsx)(P.A,{className:"h-5 w-5 mr-2"}),"Load Existing Quiz"]}),(0,s.jsxs)(n.$,{onClick:F,disabled:!e,className:"w-full h-12",size:"lg",children:[(0,s.jsx)(A,{className:"h-5 w-5 mr-2"}),"Generate New Quiz"]})]}),!e&&(0,s.jsx)($.P.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"text-sm text-muted-foreground mt-4 text-center bg-muted/50 p-3 rounded-lg border",children:"⚠️ Please select a document to generate a quiz."})]})]})})})})});if(f){let e=(0,M.Fd)(N);return(0,s.jsx)("div",{className:"flex flex-col h-full",children:(0,s.jsx)(I.F,{className:"flex-1",children:(0,s.jsx)("div",{className:"p-6 space-y-6",children:(0,s.jsx)($.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},children:(0,s.jsxs)(c.Zp,{className:"border border-border bg-card",children:[(0,s.jsxs)(c.aR,{className:"text-center",children:[(0,s.jsx)($.P.div,{initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0},transition:{delay:.2,type:"spring",stiffness:200},className:"mx-auto mb-4",children:(0,s.jsx)("div",{className:"w-20 h-20 bg-primary rounded-full flex items-center justify-center",children:(0,s.jsx)(R,{className:"h-10 w-10 text-primary-foreground"})})}),(0,s.jsxs)($.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:[(0,s.jsx)(c.ZB,{className:"text-3xl text-foreground",children:"\uD83C\uDF89 Quiz Completed!"}),(0,s.jsxs)(c.BT,{className:`text-lg font-medium ${e.color}`,children:[e.level," - ",e.description]})]})]}),(0,s.jsxs)(c.Wu,{className:"space-y-6",children:[(0,s.jsxs)($.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},className:"grid grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"text-center p-4 bg-muted/50 rounded-lg border",children:[(0,s.jsxs)($.P.p,{initial:{scale:0},animate:{scale:1},transition:{delay:.5,type:"spring",stiffness:300},className:"text-4xl font-bold text-foreground mb-2",children:[Math.round(N),"%"]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground font-medium",children:"Your Score"})]}),(0,s.jsxs)("div",{className:"text-center p-4 bg-muted/50 rounded-lg border",children:[(0,s.jsx)($.P.p,{initial:{scale:0},animate:{scale:1},transition:{delay:.6,type:"spring",stiffness:300},className:"text-4xl font-bold text-foreground mb-2",children:U(j)}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground font-medium",children:"Time Taken"})]})]}),(0,s.jsxs)($.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},className:"flex gap-3",children:[(0,s.jsxs)(n.$,{onClick:()=>{u(0),p(Array(i.length).fill("")),h(!1),g(!1),v(0),b(0),w(0)},variant:"outline",className:"flex-1 h-12 border hover:bg-muted transition-all duration-200",size:"lg",children:[(0,s.jsx)(_.A,{className:"h-5 w-5 mr-2"}),"Retake Quiz"]}),(0,s.jsxs)(n.$,{onClick:F,className:"flex-1 h-12",size:"lg",children:[(0,s.jsx)(A,{className:"h-5 w-5 mr-2"}),"New Quiz"]})]})]})]})})})})})}return(0,s.jsx)("div",{className:"flex flex-col h-full",children:(0,s.jsx)(I.F,{className:"flex-1",children:(0,s.jsxs)("div",{className:"p-6 space-y-6",children:[(0,s.jsxs)($.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"bg-muted/50 p-4 rounded-lg border",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-primary rounded-full flex items-center justify-center",children:(0,s.jsx)(T.A,{className:"h-5 w-5 text-primary-foreground"})}),(0,s.jsx)("span",{className:"font-mono text-xl font-bold text-foreground",children:U(j)})]}),(0,s.jsxs)("div",{className:"text-sm font-medium bg-background px-3 py-1 rounded-full border",children:["Question ",o+1," of ",i.length]})]}),(0,s.jsx)(q,{value:(o+1)/i.length*100,className:"h-3"})]}),(0,s.jsx)(D.N,{mode:"wait",children:x?(0,s.jsx)($.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:(0,s.jsxs)(c.Zp,{className:"border border-border bg-card",children:[(0,s.jsx)(c.aR,{children:(0,s.jsx)($.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.1},children:(0,s.jsxs)(c.ZB,{className:"text-xl text-foreground",children:["Question ",o+1]})})}),(0,s.jsxs)(c.Wu,{className:"space-y-6",children:[(0,s.jsx)($.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-muted/50 p-4 rounded-lg border",children:(0,s.jsx)("p",{className:"text-lg leading-relaxed",children:i[o].question})}),(0,s.jsx)($.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:(0,s.jsx)(d.T,{placeholder:"Type your detailed answer here... \uD83D\uDCDD",value:m[o],onChange:e=>G(e.target.value),className:"min-h-[120px] text-base border focus:border-primary bg-background"})}),(0,s.jsxs)($.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4},className:"flex justify-between gap-3",children:[(0,s.jsx)(n.$,{onClick:()=>{o>0&&u(o-1)},disabled:0===o,variant:"outline",className:"h-11 border hover:bg-muted disabled:opacity-50",size:"lg",children:"← Previous"}),o===i.length-1?(0,s.jsx)(n.$,{onClick:H,disabled:z||!m[o].trim(),className:"h-11",size:"lg",children:z?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-primary-foreground border-t-transparent mr-2"}),"Submitting..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(R,{className:"h-5 w-5 mr-2"}),"Submit Quiz"]})}):(0,s.jsx)(n.$,{onClick:()=>{o<i.length-1&&u(o+1)},disabled:!m[o].trim(),className:"h-11",size:"lg",children:"Next →"})]})]})]})},`question-${o}`):(0,s.jsx)($.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},transition:{duration:.3},children:(0,s.jsxs)(c.Zp,{className:"border border-border bg-card",children:[(0,s.jsxs)(c.aR,{className:"text-center",children:[(0,s.jsx)($.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"mx-auto mb-4",children:(0,s.jsx)("div",{className:"w-16 h-16 bg-primary rounded-full flex items-center justify-center",children:(0,s.jsx)(P.A,{className:"h-8 w-8 text-primary-foreground"})})}),(0,s.jsx)(c.ZB,{className:"text-2xl text-foreground",children:"Ready to Start?"}),(0,s.jsxs)(c.BT,{className:"text-lg",children:["You have ",i.length," questions to answer. Take your time and good luck! \uD83C\uDF40"]})]}),(0,s.jsx)(c.Wu,{children:(0,s.jsxs)(n.$,{onClick:B,className:"w-full h-12",size:"lg",children:[(0,s.jsx)(A,{className:"h-5 w-5 mr-2"}),"Start Quiz"]})})]})},"start-screen")})]})})})}var U=r(38115),L=r(92892),F=r(26456);function B(){let e=(0,i.useSearchParams)(),t=(0,i.useRouter)(),{theme:r}=(0,L.D)(),a=e.get("documentId")?parseInt(e.get("documentId")):void 0,{startQuiz:l,endQuiz:c,isActive:d,isPaused:u}=(0,F.U)({documentId:a,enabled:!!a,isProcessingComplete:!0,onQuizStart:()=>console.log("Quiz started - timer paused"),onQuizEnd:()=>console.log("Quiz ended - timer resumed")});return a?(0,s.jsx)(U.b,{children:(0,s.jsxs)("div",{className:"h-[calc(100vh-65px)] flex flex-col bg-background text-foreground",children:[(0,s.jsx)("div",{className:"border-b border-border p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)(n.$,{variant:"ghost",size:"sm",onClick:()=>{a?t.push(`/process?documentId=${a}`):t.push("/")},className:"gap-2",children:[(0,s.jsx)(o,{className:"h-4 w-4"}),"Back to Document"]}),(0,s.jsx)("h1",{className:"text-xl font-semibold",children:"Quiz"})]})}),(0,s.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,s.jsx)(Q,{documentId:a,onQuizStart:l,onQuizEnd:c})})]})}):(0,s.jsx)(U.b,{children:(0,s.jsx)("div",{className:"h-[calc(100vh-65px)] flex items-center justify-center",children:(0,s.jsxs)("div",{className:`rounded-lg p-8 text-center ${"light"===r?"bg-white border border-black":"bg-neutral-800"}`,children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"No Document Selected"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please select a document to take a quiz."}),(0,s.jsx)(n.$,{onClick:()=>t.push("/"),className:"bg-purple-600 hover:bg-purple-700",children:"Go to Home"})]})})})}function G(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)(U.b,{children:(0,s.jsx)("div",{className:"h-[calc(100vh-65px)] flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Loading quiz..."})]})})}),children:(0,s.jsx)(B,{})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15616:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var s=r(60687),a=r(43210),i=r(96241);let n=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...t}));n.displayName="Textarea"},16709:(e,t,r)=>{Promise.resolve().then(r.bind(r,27009))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26456:(e,t,r)=>{"use strict";r.d(t,{U:()=>i});var s=r(43210),a=r(16189);function i({documentId:e,enabled:t=!0,isProcessingComplete:r=!1,onQuizStart:n,onQuizEnd:l}={}){let o=(0,s.useRef)(null),c=(0,s.useRef)(!1),d=(0,s.useRef)(!1),u=(0,s.useRef)(e),m=(0,s.useRef)(null);(0,a.useRouter)();let p=process.env.NEXT_PUBLIC_API_URL||"http://localhost:8000/api",x=()=>{let e=localStorage.getItem("token");return{"Content-Type":"application/json",Authorization:`Token ${e}`}};(0,s.useCallback)(async()=>{if(t&&e&&r&&!c.current)try{if(!localStorage.getItem("token"))return;let t=await fetch(`${p}/users/platform-time/start/`,{method:"POST",headers:x(),body:JSON.stringify({document_id:e})});if(t.ok){let r=await t.json();o.current=r.session_id,c.current=!0,d.current=!1,u.current=e,m.current=new Date,console.log("Document time session started:",r.session_id,r.message)}}catch(e){console.error("Error starting document time session:",e)}},[e,t,r]);let h=(0,s.useCallback)(async(t="manual")=>{if(e&&c.current&&!d.current)try{if(!localStorage.getItem("token"))return;(await fetch(`${p}/users/platform-time/pause/`,{method:"POST",headers:x(),body:JSON.stringify({document_id:e,reason:t})})).ok&&(d.current=!0,console.log(`Document time session paused: ${t}`))}catch(e){console.error("Error pausing document time session:",e)}},[e]),f=(0,s.useCallback)(async()=>{if(e&&c.current&&d.current)try{if(!localStorage.getItem("token"))return;(await fetch(`${p}/users/platform-time/resume/`,{method:"POST",headers:x(),body:JSON.stringify({document_id:e})})).ok&&(d.current=!1,console.log("Document time session resumed"))}catch(e){console.error("Error resuming document time session:",e)}},[e]),g=(0,s.useCallback)(async()=>{if(e&&c.current)try{if(!localStorage.getItem("token"))return;await fetch(`${p}/users/platform-time/end/`,{method:"POST",headers:x(),body:JSON.stringify({document_id:e})}),o.current=null,c.current=!1,d.current=!1,m.current=null,console.log("Document time session ended")}catch(e){console.error("Error ending document time session:",e)}},[e]),y=(0,s.useCallback)(()=>{h("quiz"),n?.()},[h,n]),v=(0,s.useCallback)(()=>{f(),l?.()},[f,l]);return{sessionId:o.current,isActive:c.current,isPaused:d.current,sessionStartTime:m.current,startQuiz:y,endQuiz:v,pauseSession:h,resumeSession:f,endSession:g}}},27009:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\quiz\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\quiz\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28947:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29861:(e,t,r)=>{Promise.resolve().then(r.bind(r,1996))},33873:e=>{"use strict";e.exports=require("path")},38115:(e,t,r)=>{"use strict";r.d(t,{b:()=>f});var s=r(60687),a=r(43210),i=r(16189),n=r(82427),l=r(24934),o=r(12941),c=r(40083),d=r(65668),u=r(92363),m=r(21134),p=r(363),x=r(55629),h=r(92892);function f({children:e,showQuizButton:t=!1,showUpgradeButton:r=!1,showUserEmail:f=!1,documentId:g=null}){let[y,v]=(0,a.useState)(!1),{theme:j,setTheme:b}=(0,h.D)(),[N,w]=(0,a.useState)(!1),[z,k]=(0,a.useState)(""),S=(0,i.useRouter)(),q=()=>{localStorage.removeItem("isLoggedIn"),localStorage.removeItem("username"),w(!1),k(""),S.push("/")};return(0,s.jsxs)("div",{className:"flex h-screen bg-background text-foreground overflow-hidden",children:[(0,s.jsx)(n.B,{isOpen:y,setIsOpen:v,isLoggedIn:N,username:z,onLogout:q}),(0,s.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,s.jsxs)("div",{className:"p-4 flex justify-between items-center",children:[(0,s.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-10 w-10 rounded-full",onClick:()=>v(!0),children:(0,s.jsx)(o.A,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[f&&N&&(0,s.jsxs)(x.rI,{children:[(0,s.jsx)(x.ty,{asChild:!0,children:(0,s.jsx)(l.$,{className:"bg-purple-600 hover:bg-purple-700",children:z})}),(0,s.jsx)(x.SQ,{align:"end",children:(0,s.jsxs)(x._2,{onClick:q,children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Log out"})]})})]}),t&&g&&(0,s.jsxs)(l.$,{onClick:()=>S.push(`/quiz?documentId=${g}`),variant:"outline",size:"sm",className:"gap-2",children:[(0,s.jsx)(d.A,{className:"h-4 w-4"}),"Quiz"]}),r&&(0,s.jsxs)(l.$,{onClick:()=>S.push("/subscription"),className:"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0",size:"sm",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Upgrade"]}),(0,s.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-10 w-10 rounded-full",onClick:()=>b("dark"===j?"light":"dark"),children:"dark"===j?(0,s.jsx)(m.A,{className:"h-5 w-5"}):(0,s.jsx)(p.A,{className:"h-5 w-5"})})]})]}),e]})]})}},55192:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>l,wL:()=>u});var s=r(60687),a=r(43210),i=r(96241);let n=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent";let u=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59531:(e,t,r)=>{"use strict";function s(e,t){if(0===e.length||0===t.length)return 0;let r=0;return e.forEach((e,s)=>{let a=t[s]?.toLowerCase().trim()||"",i=e.answer.toLowerCase().trim();a&&(a.includes(i)||i.includes(a)||function(e,t){let r=e.toLowerCase().split(/\s+/).filter(e=>e.length>2),s=t.toLowerCase().split(/\s+/).filter(e=>e.length>2);return 0===r.length||0===s.length?0:r.filter(e=>s.includes(e)).length/Math.max(r.length,s.length)}(a,i)>.7)&&r++}),r/e.length*100}function a(e){let t=Math.floor(e/60),r=e%60;return t>0?`${t}m ${r}s`:`${r}s`}function i(e){return e>=90?{level:"Excellent",color:"text-foreground",description:"Outstanding performance!"}:e>=80?{level:"Good",color:"text-foreground",description:"Well done!"}:e>=70?{level:"Average",color:"text-muted-foreground",description:"Room for improvement"}:e>=60?{level:"Below Average",color:"text-muted-foreground",description:"Needs more practice"}:{level:"Poor",color:"text-muted-foreground",description:"Requires significant improvement"}}r.d(t,{Fd:()=>i,Uj:()=>a,WS:()=>s})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65668:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},69327:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var s=r(60687),a=r(43210),i=r(94707),n=r(96241);let l=a.forwardRef(({className:e,children:t,...r},a)=>(0,s.jsxs)(i.bL,{ref:a,className:(0,n.cn)("relative overflow-hidden",e),...r,children:[(0,s.jsx)(i.LM,{className:"h-full w-full rounded-[inherit]",children:t}),(0,s.jsx)(o,{}),(0,s.jsx)(i.OK,{})]}));l.displayName="ScrollArea";let o=a.forwardRef(({className:e,orientation:t="vertical",...r},a)=>(0,s.jsx)(i.VM,{ref:a,orientation:t,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 border-t border-t-transparent p-[1px]",e),...r,children:(0,s.jsx)(i.lr,{className:"relative flex-1 rounded-full bg-border"})}));o.displayName="ScrollBar"},69949:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["quiz",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,27009)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\quiz\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\quiz\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/quiz/page",pathname:"/quiz",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},71702:(e,t,r)=>{"use strict";r.d(t,{oR:()=>d}),r(43210);let s=0,a=new Map,i=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],o={toasts:[]};function c(e){o=n(o,e),l.forEach(e=>{e(o)})}function d({...e}){let t=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[6171,3955,1060,9085,8757,4089,8946,5287],()=>r(69949));module.exports=s})();