(()=>{var e={};e.id=6626,e.ids=[6626],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6141:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=r(65239),n=r(48088),i=r(88170),a=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d={children:["",{children:["subscription",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,87457)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\subscription\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\subscription\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/subscription/page",pathname:"/subscription",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17511:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(60687),n=r(24934),i=r(55192),a=r(13964),o=r(16189),l=r(38115);function d(){let e=(0,o.useRouter)();return(0,t.jsx)(l.b,{children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto py-8 px-4",children:[(0,t.jsx)(n.$,{variant:"ghost",onClick:()=>e.back(),className:"mb-8",children:"← Back"}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-center mb-4",children:"Choose Your Plan"}),(0,t.jsx)("p",{className:"text-center text-muted-foreground mb-12",children:"Select the plan that best fits your learning needs"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,t.jsxs)(i.Zp,{className:"border-border bg-card hover:border-purple-500 transition-all duration-300",children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{className:"text-xl",children:"Daily Plan"}),(0,t.jsx)(i.BT,{children:"Perfect for quick learning sessions"}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("span",{className:"text-3xl font-bold",children:"$1.99"}),(0,t.jsx)("span",{className:"text-muted-foreground",children:" / day"})]})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(a.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,t.jsx)("span",{children:"Unlimited uploads"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(a.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,t.jsx)("span",{children:"Basic AI features"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(a.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,t.jsx)("span",{children:"24-hour access"})]})]})}),(0,t.jsx)(i.wL,{children:(0,t.jsx)(n.$,{className:"w-full bg-purple-600 hover:bg-purple-700",children:"Subscribe Now"})})]}),(0,t.jsxs)(i.Zp,{className:"border-purple-500 bg-card relative",children:[(0,t.jsx)("div",{className:"absolute top-0 right-0 bg-purple-500 text-white text-xs px-3 py-1 rounded-bl-lg rounded-tr-lg",children:"POPULAR"}),(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{className:"text-xl",children:"Monthly Plan"}),(0,t.jsx)(i.BT,{children:"Our most popular option"}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("span",{className:"text-3xl font-bold",children:"$19.99"}),(0,t.jsx)("span",{className:"text-muted-foreground",children:" / month"})]})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(a.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,t.jsx)("span",{children:"Unlimited uploads"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(a.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,t.jsx)("span",{children:"Advanced AI features"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(a.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,t.jsx)("span",{children:"5 custom spaces"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(a.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,t.jsx)("span",{children:"Priority support"})]})]})}),(0,t.jsx)(i.wL,{children:(0,t.jsx)(n.$,{className:"w-full bg-purple-600 hover:bg-purple-700",children:"Subscribe Now"})})]}),(0,t.jsxs)(i.Zp,{className:"border-border bg-card hover:border-purple-500 transition-all duration-300",children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{className:"text-xl",children:"Yearly Plan"}),(0,t.jsx)(i.BT,{children:"Best value for committed learners"}),(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("span",{className:"text-3xl font-bold",children:"$199.99"}),(0,t.jsx)("span",{className:"text-muted-foreground",children:" / year"}),(0,t.jsx)("div",{className:"text-sm text-purple-500 font-medium",children:"Save $40"})]})]}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("ul",{className:"space-y-2",children:[(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(a.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,t.jsx)("span",{children:"Everything in Monthly"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(a.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,t.jsx)("span",{children:"Unlimited spaces"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(a.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,t.jsx)("span",{children:"Advanced analytics"})]}),(0,t.jsxs)("li",{className:"flex items-center",children:[(0,t.jsx)(a.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,t.jsx)("span",{children:"Dedicated support"})]})]})}),(0,t.jsx)(i.wL,{children:(0,t.jsx)(n.$,{className:"w-full bg-purple-600 hover:bg-purple-700",children:"Subscribe Now"})})]})]})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23843:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},24934:(e,s,r)=>{"use strict";r.d(s,{$:()=>d});var t=r(60687),n=r(43210),i=r(8730),a=r(24224),o=r(96241);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef(({className:e,variant:s,size:r,asChild:n=!1,...a},d)=>{let c=n?i.DX:"button";return(0,t.jsx)(c,{className:(0,o.cn)(l({variant:s,size:r,className:e})),ref:d,...a})});d.displayName="Button"},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32222:(e,s,r)=>{Promise.resolve().then(r.bind(r,83066)),Promise.resolve().then(r.bind(r,6931))},33873:e=>{"use strict";e.exports=require("path")},37915:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},38115:(e,s,r)=>{"use strict";r.d(s,{b:()=>v});var t=r(60687),n=r(43210),i=r(16189),a=r(82427),o=r(24934),l=r(12941),d=r(40083),c=r(65668),p=r(92363),m=r(21134),u=r(363),h=r(55629),x=r(92892);function v({children:e,showQuizButton:s=!1,showUpgradeButton:r=!1,showUserEmail:v=!1,documentId:f=null}){let[j,b]=(0,n.useState)(!1),{theme:g,setTheme:N}=(0,x.D)(),[y,w]=(0,n.useState)(!1),[P,C]=(0,n.useState)(""),k=(0,i.useRouter)(),_=()=>{localStorage.removeItem("isLoggedIn"),localStorage.removeItem("username"),w(!1),C(""),k.push("/")};return(0,t.jsxs)("div",{className:"flex h-screen bg-background text-foreground overflow-hidden",children:[(0,t.jsx)(a.B,{isOpen:j,setIsOpen:b,isLoggedIn:y,username:P,onLogout:_}),(0,t.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,t.jsxs)("div",{className:"p-4 flex justify-between items-center",children:[(0,t.jsx)(o.$,{variant:"ghost",size:"icon",className:"h-10 w-10 rounded-full",onClick:()=>b(!0),children:(0,t.jsx)(l.A,{className:"h-5 w-5"})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[v&&y&&(0,t.jsxs)(h.rI,{children:[(0,t.jsx)(h.ty,{asChild:!0,children:(0,t.jsx)(o.$,{className:"bg-purple-600 hover:bg-purple-700",children:P})}),(0,t.jsx)(h.SQ,{align:"end",children:(0,t.jsxs)(h._2,{onClick:_,children:[(0,t.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Log out"})]})})]}),s&&f&&(0,t.jsxs)(o.$,{onClick:()=>k.push(`/quiz?documentId=${f}`),variant:"outline",size:"sm",className:"gap-2",children:[(0,t.jsx)(c.A,{className:"h-4 w-4"}),"Quiz"]}),r&&(0,t.jsxs)(o.$,{onClick:()=>k.push("/subscription"),className:"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0",size:"sm",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Upgrade"]}),(0,t.jsx)(o.$,{variant:"ghost",size:"icon",className:"h-10 w-10 rounded-full",onClick:()=>N("dark"===g?"light":"dark"),children:"dark"===g?(0,t.jsx)(m.A,{className:"h-5 w-5"}):(0,t.jsx)(u.A,{className:"h-5 w-5"})})]})]}),e]})]})}},41965:(e,s,r)=>{Promise.resolve().then(r.bind(r,17511))},44173:(e,s,r)=>{Promise.resolve().then(r.bind(r,87457))},55192:(e,s,r)=>{"use strict";r.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>a,aR:()=>o,wL:()=>p});var t=r(60687),n=r(43210),i=r(96241);let a=n.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));a.displayName="Card";let o=n.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));o.displayName="CardHeader";let l=n.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));l.displayName="CardTitle";let d=n.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=n.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let p=n.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s}));p.displayName="CardFooter"},58014:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d,metadata:()=>l});var t=r(37413);r(82704);var n=r(61421),i=r.n(n),a=r(83066),o=r(6931);let l={title:"Cognimosity",description:"Learning platform",generator:"v0.dev"};function d({children:e}){return(0,t.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,t.jsx)("body",{className:i().className,children:(0,t.jsxs)(a.ThemeProvider,{defaultTheme:"dark",enableSystem:!0,disableTransitionOnChange:!0,children:[e,(0,t.jsx)(o.Toaster,{})]})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65668:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},72390:(e,s,r)=>{Promise.resolve().then(r.bind(r,92892)),Promise.resolve().then(r.bind(r,52581))},82704:()=>{},83066:(e,s,r)=>{"use strict";r.d(s,{ThemeProvider:()=>n});var t=r(12907);let n=(0,t.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\components\\theme-provider.tsx","ThemeProvider");(0,t.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\components\\theme-provider.tsx","useTheme")},87457:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\subscription\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\app\\subscription\\page.tsx","default")},92892:(e,s,r)=>{"use strict";r.d(s,{D:()=>o,ThemeProvider:()=>a});var t=r(60687),n=r(43210);let i=(0,n.createContext)({theme:"system",setTheme:()=>null});function a({children:e,defaultTheme:s="system",storageKey:r="ui-theme",enableSystem:a=!0,disableTransitionOnChange:o=!1,...l}){let[d,c]=(0,n.useState)(s);return(0,t.jsx)(i.Provider,{...l,value:{theme:d,setTheme:e=>{c(e)}},children:e})}let o=()=>{let e=(0,n.useContext)(i);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},96241:(e,s,r)=>{"use strict";r.d(s,{cn:()=>i});var t=r(49384),n=r(82348);function i(...e){return(0,n.QP)((0,t.$)(e))}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[6171,3955,9085,8757,5287],()=>r(6141));module.exports=t})();