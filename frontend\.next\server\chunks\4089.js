"use strict";exports.id=4089,exports.ids=[4089],exports.modules={13943:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},94707:(e,t,r)=>{r.d(t,{OK:()=>G,bL:()=>K,VM:()=>T,lr:()=>I,LM:()=>Z});var n=r(43210);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return n.useCallback(function(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}(...e),e)}r(51215);var i=r(60687),a=Symbol("radix.slottable");function s(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{var r;let o,i;let{children:a,...s}=e,u=l(n.isValidElement(a)?(r=a,(i=(o=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in o&&o.isReactWarning)?r.ref:(i=(o=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in o&&o.isReactWarning)?r.props.ref:r.props.ref||r.ref):void 0,t);if(n.isValidElement(a)){let e=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(s,a.props);return a.type!==n.Fragment&&(e.ref=u),n.cloneElement(a,e)}return n.Children.count(a)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...l}=e,a=n.Children.toArray(o),u=a.find(s);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?r:t,{...l,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),c=globalThis?.document?n.useLayoutEffect:()=>{},d=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[o,l]=n.useState(),i=n.useRef(null),a=n.useRef(e),s=n.useRef("none"),[u,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=f(i.current);s.current="mounted"===u?e:"none"},[u]),c(()=>{let t=i.current,r=a.current;if(r!==e){let n=s.current,o=f(t);e?d("MOUNT"):"none"===o||t?.display==="none"?d("UNMOUNT"):r&&n!==o?d("ANIMATION_OUT"):d("UNMOUNT"),a.current=e}},[e,d]),c(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,r=r=>{let n=f(i.current).includes(r.animationName);if(r.target===o&&n&&(d("ANIMATION_END"),!a.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(s.current=f(i.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:n.useCallback(e=>{i.current=e?getComputedStyle(e):null,l(e)},[])}}(t),i="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),a=l(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||o.isPresent?n.cloneElement(i,{ref:a}):null};function f(e){return e?.animationName||"none"}function p(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}d.displayName="Presence";var h=n.createContext(void 0);function v(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}var m="ScrollArea",[w,g]=function(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return o.scopeName=e,[function(t,o){let l=n.createContext(o),a=r.length;r=[...r,o];let s=t=>{let{scope:r,children:o,...s}=t,u=r?.[e]?.[a]||l,c=n.useMemo(()=>s,Object.values(s));return(0,i.jsx)(u.Provider,{value:c,children:o})};return s.displayName=t+"Provider",[s,function(r,i){let s=i?.[e]?.[a]||l,u=n.useContext(s);if(u)return u;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(o,...t)]}(m),[b,y]=w(m),S=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:o="hover",dir:a,scrollHideDelay:s=600,...c}=e,[d,f]=n.useState(null),[p,v]=n.useState(null),[m,w]=n.useState(null),[g,y]=n.useState(null),[S,E]=n.useState(null),[x,R]=n.useState(0),[T,C]=n.useState(0),[N,P]=n.useState(!1),[L,_]=n.useState(!1),j=l(t,e=>f(e)),A=function(e){let t=n.useContext(h);return e||t||"ltr"}(a);return(0,i.jsx)(b,{scope:r,type:o,dir:A,scrollHideDelay:s,scrollArea:d,viewport:p,onViewportChange:v,content:m,onContentChange:w,scrollbarX:g,onScrollbarXChange:y,scrollbarXEnabled:N,onScrollbarXEnabledChange:P,scrollbarY:S,onScrollbarYChange:E,scrollbarYEnabled:L,onScrollbarYEnabledChange:_,onCornerWidthChange:R,onCornerHeightChange:C,children:(0,i.jsx)(u.div,{dir:A,...c,ref:j,style:{position:"relative","--radix-scroll-area-corner-width":x+"px","--radix-scroll-area-corner-height":T+"px",...e.style}})})});S.displayName=m;var E="ScrollAreaViewport",x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:o,nonce:a,...s}=e,c=y(E,r),d=l(t,n.useRef(null),c.onViewportChange);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,i.jsx)(u.div,{"data-radix-scroll-area-viewport":"",...s,ref:d,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,i.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});x.displayName=E;var R="ScrollAreaScrollbar",T=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=y(R,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:s}=l,u="horizontal"===e.orientation;return n.useEffect(()=>(u?a(!0):s(!0),()=>{u?a(!1):s(!1)}),[u,a,s]),"hover"===l.type?(0,i.jsx)(C,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,i.jsx)(N,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,i.jsx)(P,{...o,ref:t,forceMount:r}):"always"===l.type?(0,i.jsx)(L,{...o,ref:t}):null});T.displayName=R;var C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=y(R,e.__scopeScrollArea),[a,s]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,i.jsx)(d,{present:r||a,children:(0,i.jsx)(P,{"data-state":a?"visible":"hidden",...o,ref:t})})}),N=n.forwardRef((e,t)=>{var r;let{forceMount:o,...l}=e,a=y(R,e.__scopeScrollArea),s="horizontal"===e.orientation,u=B(()=>f("SCROLL_END"),100),[c,f]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>r[e][t]??e,"hidden"));return n.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>f("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,a.scrollHideDelay,f]),n.useEffect(()=>{let e=a.viewport,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(f("SCROLL"),u()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[a.viewport,s,f,u]),(0,i.jsx)(d,{present:o||"hidden"!==c,children:(0,i.jsx)(L,{"data-state":"hidden"===c?"hidden":"visible",...l,ref:t,onPointerEnter:v(e.onPointerEnter,()=>f("POINTER_ENTER")),onPointerLeave:v(e.onPointerLeave,()=>f("POINTER_LEAVE"))})})}),P=n.forwardRef((e,t)=>{let r=y(R,e.__scopeScrollArea),{forceMount:o,...l}=e,[a,s]=n.useState(!1),u="horizontal"===e.orientation,c=B(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(u?e:t)}},10);return q(r.viewport,c),q(r.content,c),(0,i.jsx)(d,{present:o||a,children:(0,i.jsx)(L,{"data-state":a?"visible":"hidden",...l,ref:t})})}),L=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=y(R,e.__scopeScrollArea),a=n.useRef(null),s=n.useRef(0),[u,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=F(u.viewport,u.content),f={...o,sizes:u,onSizesChange:c,hasThumb:!!(d>0&&d<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:e=>s.current=e};function p(e,t){return function(e,t,r,n="ltr"){let o=X(r),l=t||o/2,i=r.scrollbar.paddingStart+l,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),s=r.content-r.viewport;return $([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,s.current,u,t)}return"horizontal"===r?(0,i.jsx)(_,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&a.current){let e=Y(l.viewport.scrollLeft,u,l.dir);a.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===r?(0,i.jsx)(j,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&a.current){let e=Y(l.viewport.scrollTop,u);a.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),_=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,s=y(R,e.__scopeScrollArea),[u,c]=n.useState(),d=n.useRef(null),f=l(t,d,s.onScrollbarXChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,i.jsx)(O,{"data-orientation":"horizontal",...a,ref:f,sizes:r,style:{bottom:0,left:"rtl"===s.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===s.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":X(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(s.viewport){let n=s.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&s.viewport&&u&&o({content:s.viewport.scrollWidth,viewport:s.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:V(u.paddingLeft),paddingEnd:V(u.paddingRight)}})}})}),j=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,s=y(R,e.__scopeScrollArea),[u,c]=n.useState(),d=n.useRef(null),f=l(t,d,s.onScrollbarYChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,i.jsx)(O,{"data-orientation":"vertical",...a,ref:f,sizes:r,style:{top:0,right:"ltr"===s.dir?0:void 0,left:"rtl"===s.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":X(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(s.viewport){let n=s.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&s.viewport&&u&&o({content:s.viewport.scrollHeight,viewport:s.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:V(u.paddingTop),paddingEnd:V(u.paddingBottom)}})}})}),[A,D]=w(R),O=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:o,hasThumb:a,onThumbChange:s,onThumbPointerUp:c,onThumbPointerDown:d,onThumbPositionChange:f,onDragScroll:h,onWheelScroll:m,onResize:w,...g}=e,b=y(R,r),[S,E]=n.useState(null),x=l(t,e=>E(e)),T=n.useRef(null),C=n.useRef(""),N=b.viewport,P=o.content-o.viewport,L=p(m),_=p(f),j=B(w,10);function D(e){T.current&&h({x:e.clientX-T.current.left,y:e.clientY-T.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;S?.contains(t)&&L(e,P)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[N,S,P,L]),n.useEffect(_,[o,_]),q(S,j),q(b.content,j),(0,i.jsx)(A,{scope:r,scrollbar:S,hasThumb:a,onThumbChange:p(s),onThumbPointerUp:p(c),onThumbPositionChange:_,onThumbPointerDown:p(d),children:(0,i.jsx)(u.div,{...g,ref:x,style:{position:"absolute",...g.style},onPointerDown:v(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),T.current=S.getBoundingClientRect(),C.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),D(e))}),onPointerMove:v(e.onPointerMove,D),onPointerUp:v(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=C.current,b.viewport&&(b.viewport.style.scrollBehavior=""),T.current=null})})})}),M="ScrollAreaThumb",I=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=D(M,e.__scopeScrollArea);return(0,i.jsx)(d,{present:r||o.hasThumb,children:(0,i.jsx)(W,{ref:t,...n})})}),W=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:o,...a}=e,s=y(M,r),c=D(M,r),{onThumbPositionChange:d}=c,f=l(t,e=>c.onThumbChange(e)),p=n.useRef(void 0),h=B(()=>{p.current&&(p.current(),p.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{h(),p.current||(p.current=k(e,d),d())};return d(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,h,d]),(0,i.jsx)(u.div,{"data-state":c.hasThumb?"visible":"hidden",...a,ref:f,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:v(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;c.onThumbPointerDown({x:r,y:n})}),onPointerUp:v(e.onPointerUp,c.onThumbPointerUp)})});I.displayName=M;var U="ScrollAreaCorner",z=n.forwardRef((e,t)=>{let r=y(U,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,i.jsx)(H,{...e,ref:t}):null});z.displayName=U;var H=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...o}=e,l=y(U,r),[a,s]=n.useState(0),[c,d]=n.useState(0),f=!!(a&&c);return q(l.scrollbarX,()=>{let e=l.scrollbarX?.offsetHeight||0;l.onCornerHeightChange(e),d(e)}),q(l.scrollbarY,()=>{let e=l.scrollbarY?.offsetWidth||0;l.onCornerWidthChange(e),s(e)}),f?(0,i.jsx)(u.div,{...o,ref:t,style:{width:a,height:c,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function V(e){return e?parseInt(e,10):0}function F(e,t){let r=e/t;return isNaN(r)?0:r}function X(e){let t=F(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function Y(e,t,r="ltr"){let n=X(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=function(e,[t,r]){return Math.min(r,Math.max(t,e))}(e,"ltr"===r?[0,i]:[-1*i,0]);return $([0,i],[0,l-n])(a)}function $(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var k=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=r.left!==l.left,a=r.top!==l.top;(i||a)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function B(e,t){let r=p(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function q(e,t){let r=p(t);c(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var K=S,Z=x,G=z}};