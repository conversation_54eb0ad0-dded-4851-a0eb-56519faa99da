"use strict";exports.id=4184,exports.ids=[4184],exports.modules={38115:(e,s,t)=>{t.d(s,{b:()=>f});var r=t(60687),a=t(43210),i=t(16189),n=t(82427),l=t(24934),o=t(12941),d=t(40083),c=t(65668),m=t(92363),x=t(21134),u=t(363),h=t(55629),p=t(92892);function f({children:e,showQuizButton:s=!1,showUpgradeButton:t=!1,showUserEmail:f=!1,documentId:g=null}){let[j,v]=(0,a.useState)(!1),{theme:N,setTheme:y}=(0,p.D)(),[b,w]=(0,a.useState)(!1),[S,A]=(0,a.useState)(""),T=(0,i.useRouter)(),_=()=>{localStorage.removeItem("isLoggedIn"),localStorage.removeItem("username"),w(!1),A(""),T.push("/")};return(0,r.jsxs)("div",{className:"flex h-screen bg-background text-foreground overflow-hidden",children:[(0,r.jsx)(n.B,{isOpen:j,setIsOpen:v,isLoggedIn:b,username:S,onLogout:_}),(0,r.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,r.jsxs)("div",{className:"p-4 flex justify-between items-center",children:[(0,r.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-10 w-10 rounded-full",onClick:()=>v(!0),children:(0,r.jsx)(o.A,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[f&&b&&(0,r.jsxs)(h.rI,{children:[(0,r.jsx)(h.ty,{asChild:!0,children:(0,r.jsx)(l.$,{className:"bg-purple-600 hover:bg-purple-700",children:S})}),(0,r.jsx)(h.SQ,{align:"end",children:(0,r.jsxs)(h._2,{onClick:_,children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Log out"})]})})]}),s&&g&&(0,r.jsxs)(l.$,{onClick:()=>T.push(`/quiz?documentId=${g}`),variant:"outline",size:"sm",className:"gap-2",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),"Quiz"]}),t&&(0,r.jsxs)(l.$,{onClick:()=>T.push("/subscription"),className:"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0",size:"sm",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Upgrade"]}),(0,r.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-10 w-10 rounded-full",onClick:()=>y("dark"===N?"light":"dark"),children:"dark"===N?(0,r.jsx)(x.A,{className:"h-5 w-5"}):(0,r.jsx)(u.A,{className:"h-5 w-5"})})]})]}),e]})]})}},55192:(e,s,t)=>{t.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>l,wL:()=>m});var r=t(60687),a=t(43210),i=t(96241);let n=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));n.displayName="Card";let l=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let m=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s}));m.displayName="CardFooter"},55366:(e,s,t)=>{t.d(s,{Z:()=>z});var r=t(60687),a=t(43210),i=t(55192),n=t(24224),l=t(96241);let o=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:s,...t}){return(0,r.jsx)("div",{className:(0,l.cn)(o({variant:s}),e),...t})}var c=t(24934),m=t(85910),x=t(48482),u=t(61855),h=t(85168),p=t(27747),f=t(9920),g=t(38246),j=t(49481),v=t(2041),N=t(48888),y=t(61678),b=t(50326),w=t(28947),S=t(48730),A=t(86561),T=t(25541),_=t(82080),k=t(40228),R=t(59556),C=t(71702),D=t(59531);function z({documentId:e,studentId:s}){let[t,n]=(0,a.useState)([]),[l,o]=(0,a.useState)(!0),[z,I]=(0,a.useState)({averageScore:0,averageTime:0,totalAttempts:0,bestScore:0,improvement:0}),E=async()=>{o(!0);try{let t;if(s){let e=await R.performanceApi.getStudentPerformances(s);t=e.results||e}else if(e){let s=await R.performanceApi.getPerformanceStats(e);t=s.results||s}else{let e=await R.performanceApi.getPerformances();t=e.results||e}n(t),O(t)}catch(e){console.error("Error fetching performances:",e),(0,C.oR)({title:"Error",description:"Failed to load performance data",variant:"destructive"})}finally{o(!1)}},O=e=>{if(0===e.length){I({averageScore:0,averageTime:0,totalAttempts:0,bestScore:0,improvement:0});return}let s=e.reduce((e,s)=>e+s.quiz_score,0),t=e.reduce((e,s)=>e+s.time_taken,0),r=s/e.length,a=t/e.length,i=Math.max(...e.map(e=>e.quiz_score)),n=[...e].sort((e,s)=>new Date(e.created_at).getTime()-new Date(s.created_at).getTime()),l=n.length>1?n[n.length-1].quiz_score-n[0].quiz_score:0;I({averageScore:Math.round(100*r)/100,averageTime:Math.round(a),totalAttempts:e.length,bestScore:Math.round(100*i)/100,improvement:Math.round(100*l)/100})},M=e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),W=e=>e>=80?"default":e>=60?"secondary":"destructive",Z=t.sort((e,s)=>new Date(e.created_at).getTime()-new Date(s.created_at).getTime()).map((e,s)=>({attempt:s+1,score:e.quiz_score,time:Math.round(e.time_taken/60),date:M(e.created_at)}));return l?(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,r.jsx)("div",{className:"h-24 bg-gray-200 rounded"},s))})]})}):(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Performance Dashboard"}),(0,r.jsx)(c.$,{onClick:E,variant:"outline",size:"sm",children:"Refresh"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(w.A,{className:"h-4 w-4 text-blue-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Average Score"}),(0,r.jsxs)("p",{className:"text-2xl font-bold",children:[z.averageScore,"%"]})]})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(S.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Avg Time"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:(0,D.Uj)(z.averageTime)})]})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(A.A,{className:"h-4 w-4 text-yellow-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Best Score"}),(0,r.jsxs)("p",{className:"text-2xl font-bold",children:[z.bestScore,"%"]})]})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(T.A,{className:"h-4 w-4 text-purple-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Improvement"}),(0,r.jsxs)("p",{className:`text-2xl font-bold ${z.improvement>=0?"text-green-600":"text-red-600"}`,children:[z.improvement>0?"+":"",z.improvement,"%"]})]})]})})})]}),(0,r.jsxs)(m.tU,{defaultValue:"overview",className:"space-y-4",children:[(0,r.jsxs)(m.j7,{children:[(0,r.jsx)(m.Xi,{value:"overview",children:"Overview"}),(0,r.jsx)(m.Xi,{value:"history",children:"History"}),(0,r.jsx)(m.Xi,{value:"analytics",children:"Analytics"})]}),(0,r.jsx)(m.av,{value:"overview",className:"space-y-4",children:Z.length>0&&(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Score Progress"}),(0,r.jsx)(i.BT,{children:"Your quiz scores over time"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)(x.u,{width:"100%",height:300,children:(0,r.jsxs)(u.Q,{data:Z,children:[(0,r.jsx)(h.d,{strokeDasharray:"3 3"}),(0,r.jsx)(p.W,{dataKey:"attempt"}),(0,r.jsx)(f.h,{domain:[0,100]}),(0,r.jsx)(g.m,{formatter:(e,s)=>["score"===s?`${e}%`:`${e}m`,"score"===s?"Score":"Time"]}),(0,r.jsx)(j.G,{type:"monotone",dataKey:"score",stroke:"#8884d8",fill:"#8884d8",fillOpacity:.3})]})})})]})}),(0,r.jsx)(m.av,{value:"history",className:"space-y-4",children:(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Quiz History"}),(0,r.jsx)(i.BT,{children:"All your quiz attempts"})]}),(0,r.jsx)(i.Wu,{children:0===t.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(_.A,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No quiz attempts yet"})]}):(0,r.jsx)("div",{className:"space-y-3",children:t.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(k.A,{className:"h-4 w-4 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.document_title}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:M(e.created_at)})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)(d,{variant:W(e.quiz_score),children:[Math.round(e.quiz_score),"%"]}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:(0,D.Uj)(e.time_taken)})]})]},e.id))})})]})}),(0,r.jsx)(m.av,{value:"analytics",className:"space-y-4",children:Z.length>0&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{children:"Score Distribution"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)(x.u,{width:"100%",height:250,children:(0,r.jsxs)(v.E,{data:Z,children:[(0,r.jsx)(h.d,{strokeDasharray:"3 3"}),(0,r.jsx)(p.W,{dataKey:"attempt"}),(0,r.jsx)(f.h,{domain:[0,100]}),(0,r.jsx)(g.m,{formatter:e=>[`${e}%`,"Score"]}),(0,r.jsx)(N.y,{dataKey:"score",fill:"#8884d8"})]})})})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{children:"Time Trends"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)(x.u,{width:"100%",height:250,children:(0,r.jsxs)(y.b,{data:Z,children:[(0,r.jsx)(h.d,{strokeDasharray:"3 3"}),(0,r.jsx)(p.W,{dataKey:"attempt"}),(0,r.jsx)(f.h,{}),(0,r.jsx)(g.m,{formatter:e=>[`${e}m`,"Time"]}),(0,r.jsx)(b.N,{type:"monotone",dataKey:"time",stroke:"#82ca9d",strokeWidth:2})]})})})]})]})})]})]})}},59531:(e,s,t)=>{function r(e,s){if(0===e.length||0===s.length)return 0;let t=0;return e.forEach((e,r)=>{let a=s[r]?.toLowerCase().trim()||"",i=e.answer.toLowerCase().trim();a&&(a.includes(i)||i.includes(a)||function(e,s){let t=e.toLowerCase().split(/\s+/).filter(e=>e.length>2),r=s.toLowerCase().split(/\s+/).filter(e=>e.length>2);return 0===t.length||0===r.length?0:t.filter(e=>r.includes(e)).length/Math.max(t.length,r.length)}(a,i)>.7)&&t++}),t/e.length*100}function a(e){let s=Math.floor(e/60),t=e%60;return s>0?`${s}m ${t}s`:`${t}s`}function i(e){return e>=90?{level:"Excellent",color:"text-foreground",description:"Outstanding performance!"}:e>=80?{level:"Good",color:"text-foreground",description:"Well done!"}:e>=70?{level:"Average",color:"text-muted-foreground",description:"Room for improvement"}:e>=60?{level:"Below Average",color:"text-muted-foreground",description:"Needs more practice"}:{level:"Poor",color:"text-muted-foreground",description:"Requires significant improvement"}}t.d(s,{Fd:()=>i,Uj:()=>a,WS:()=>r})},71702:(e,s,t)=>{t.d(s,{oR:()=>c}),t(43210);let r=0,a=new Map,i=e=>{if(a.has(e))return;let s=setTimeout(()=>{a.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,s)},n=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=s;return t?i(t):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===s.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)}}},l=[],o={toasts:[]};function d(e){o=n(o,e),l.forEach(e=>{e(o)})}function c({...e}){let s=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),t=()=>d({type:"DISMISS_TOAST",toastId:s});return d({type:"ADD_TOAST",toast:{...e,id:s,open:!0,onOpenChange:e=>{e||t()}}}),{id:s,dismiss:t,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:s}})}}},85910:(e,s,t)=>{t.d(s,{Xi:()=>d,av:()=>c,j7:()=>o,tU:()=>l});var r=t(60687),a=t(43210),i=t(55146),n=t(96241);let l=i.bL,o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.B8,{ref:t,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));o.displayName=i.B8.displayName;let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.l9,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));d.displayName=i.l9.displayName;let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)(i.UC,{ref:t,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));c.displayName=i.UC.displayName}};