"use strict";exports.id=6591,exports.ids=[6591],exports.modules={1082:(e,n,t)=>{t.d(n,{A:()=>u});var r=t(66320),o=t(44360),i=t(1356),a=t(44589);let u=function(e,n){return((0,a.A)(e)?r.A:i.A)(e,(0,o.A)(n,3))}},1356:(e,n,t)=>{t.d(n,{A:()=>i});var r=t(85e3),o=t(30982);let i=function(e,n){var t=-1,i=(0,o.A)(e)?Array(e.length):[];return(0,r.A)(e,function(e,r,o){i[++t]=n(e,r,o)}),i}},11925:(e,n,t)=>{t.d(n,{A:()=>o});var r=t(45372);let o=function(e){var n=(0,r.A)(e),t=n%1;return n==n?t?n-t:n:0}},11999:(e,n,t)=>{t.d(n,{A:()=>o});var r=t(42058);let o=function(e,n,t){for(var o=-1,i=e.length;++o<i;){var a=e[o],u=n(a);if(null!=u&&(void 0===d?u==u&&!(0,r.A)(u):t(u,d)))var d=u,s=a}return s}},17300:(e,n,t)=>{t.d(n,{A:()=>r});let r=function(e,n){return e<n}},41552:(e,n,t)=>{t.d(n,{A:()=>s});var r=t(95662),o=t(17420),i=t(42484),a=t(87814),u=Object.prototype,d=u.hasOwnProperty;let s=(0,r.A)(function(e,n){e=Object(e);var t=-1,r=n.length,s=r>2?n[2]:void 0;for(s&&(0,i.A)(n[0],n[1],s)&&(r=1);++t<r;)for(var h=n[t],c=(0,a.A)(h),f=-1,l=c.length;++f<l;){var v=c[f],g=e[v];(void 0===g||(0,o.A)(g,u[v])&&!d.call(e,v))&&(e[v]=h[v])}return e})},43760:(e,n,t)=>{t.d(n,{A:()=>a});var r=t(11999),o=t(17300),i=t(88500);let a=function(e){return e&&e.length?(0,r.A)(e,i.A,o.A):void 0}},45372:(e,n,t)=>{t.d(n,{A:()=>g});var r=/\s/;let o=function(e){for(var n=e.length;n--&&r.test(e.charAt(n)););return n};var i=/^\s+/,a=t(80737),u=t(42058),d=0/0,s=/^[-+]0x[0-9a-f]+$/i,h=/^0b[01]+$/i,c=/^0o[0-7]+$/i,f=parseInt;let l=function(e){if("number"==typeof e)return e;if((0,u.A)(e))return d;if((0,a.A)(e)){var n,t="function"==typeof e.valueOf?e.valueOf():e;e=(0,a.A)(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=(n=e)?n.slice(0,o(n)+1).replace(i,""):n;var r=h.test(e);return r||c.test(e)?f(e.slice(2),r?2:8):s.test(e)?d:+e};var v=1/0;let g=function(e){return e?(e=l(e))===v||e===-v?(e<0?-1:1)*17976931348623157e292:e==e?e:0:0===e?e:0}},46503:(e,n,t)=>{t.d(n,{T:()=>A});var r=t(5086),o=t(71442),i=t(3254),a=t(12176),u=t(56837),d=t(52850),s=t(56764),h=t(71776),c=t(95662),f=t(4062),l=t(86137),v=(0,c.A)(function(e){return(0,f.A)((0,h.A)(e,1,l.A,!0))}),g=t(7086),p=t(35135);class A{constructor(e={}){this._isDirected=!Object.prototype.hasOwnProperty.call(e,"directed")||e.directed,this._isMultigraph=!!Object.prototype.hasOwnProperty.call(e,"multigraph")&&e.multigraph,this._isCompound=!!Object.prototype.hasOwnProperty.call(e,"compound")&&e.compound,this._label=void 0,this._defaultNodeLabelFn=r.A(void 0),this._defaultEdgeLabelFn=r.A(void 0),this._nodes={},this._isCompound&&(this._parent={},this._children={},this._children["\0"]={}),this._in={},this._preds={},this._out={},this._sucs={},this._edgeObjs={},this._edgeLabels={}}isDirected(){return this._isDirected}isMultigraph(){return this._isMultigraph}isCompound(){return this._isCompound}setGraph(e){return this._label=e,this}graph(){return this._label}setDefaultNodeLabel(e){return o.A(e)||(e=r.A(e)),this._defaultNodeLabelFn=e,this}nodeCount(){return this._nodeCount}nodes(){return i.A(this._nodes)}sources(){var e=this;return a.A(this.nodes(),function(n){return u.A(e._in[n])})}sinks(){var e=this;return a.A(this.nodes(),function(n){return u.A(e._out[n])})}setNodes(e,n){var t=arguments,r=this;return d.A(e,function(e){t.length>1?r.setNode(e,n):r.setNode(e)}),this}setNode(e,n){return Object.prototype.hasOwnProperty.call(this._nodes,e)?arguments.length>1&&(this._nodes[e]=n):(this._nodes[e]=arguments.length>1?n:this._defaultNodeLabelFn(e),this._isCompound&&(this._parent[e]="\0",this._children[e]={},this._children["\0"][e]=!0),this._in[e]={},this._preds[e]={},this._out[e]={},this._sucs[e]={},++this._nodeCount),this}node(e){return this._nodes[e]}hasNode(e){return Object.prototype.hasOwnProperty.call(this._nodes,e)}removeNode(e){if(Object.prototype.hasOwnProperty.call(this._nodes,e)){var n=e=>this.removeEdge(this._edgeObjs[e]);delete this._nodes[e],this._isCompound&&(this._removeFromParentsChildList(e),delete this._parent[e],d.A(this.children(e),e=>{this.setParent(e)}),delete this._children[e]),d.A(i.A(this._in[e]),n),delete this._in[e],delete this._preds[e],d.A(i.A(this._out[e]),n),delete this._out[e],delete this._sucs[e],--this._nodeCount}return this}setParent(e,n){if(!this._isCompound)throw Error("Cannot set parent in a non-compound graph");if(s.A(n))n="\0";else{n+="";for(var t=n;!s.A(t);t=this.parent(t))if(t===e)throw Error("Setting "+n+" as parent of "+e+" would create a cycle");this.setNode(n)}return this.setNode(e),this._removeFromParentsChildList(e),this._parent[e]=n,this._children[n][e]=!0,this}_removeFromParentsChildList(e){delete this._children[this._parent[e]][e]}parent(e){if(this._isCompound){var n=this._parent[e];if("\0"!==n)return n}}children(e){if(s.A(e)&&(e="\0"),this._isCompound){var n=this._children[e];if(n)return i.A(n)}else if("\0"===e)return this.nodes();else if(this.hasNode(e))return[]}predecessors(e){var n=this._preds[e];if(n)return i.A(n)}successors(e){var n=this._sucs[e];if(n)return i.A(n)}neighbors(e){var n=this.predecessors(e);if(n)return v(n,this.successors(e))}isLeaf(e){var n;return 0===(this.isDirected()?this.successors(e):this.neighbors(e)).length}filterNodes(e){var n=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});n.setGraph(this.graph());var t=this;d.A(this._nodes,function(t,r){e(r)&&n.setNode(r,t)}),d.A(this._edgeObjs,function(e){n.hasNode(e.v)&&n.hasNode(e.w)&&n.setEdge(e,t.edge(e))});var r={};return this._isCompound&&d.A(n.nodes(),function(e){n.setParent(e,function e(o){var i=t.parent(o);return void 0===i||n.hasNode(i)?(r[o]=i,i):i in r?r[i]:e(i)}(e))}),n}setDefaultEdgeLabel(e){return o.A(e)||(e=r.A(e)),this._defaultEdgeLabelFn=e,this}edgeCount(){return this._edgeCount}edges(){return g.A(this._edgeObjs)}setPath(e,n){var t=this,r=arguments;return p.A(e,function(e,o){return r.length>1?t.setEdge(e,o,n):t.setEdge(e,o),o}),this}setEdge(){var e,n,t,r,o=!1,i=arguments[0];"object"==typeof i&&null!==i&&"v"in i?(e=i.v,n=i.w,t=i.name,2==arguments.length&&(r=arguments[1],o=!0)):(e=i,n=arguments[1],t=arguments[3],arguments.length>2&&(r=arguments[2],o=!0)),e=""+e,n=""+n,s.A(t)||(t=""+t);var a=m(this._isDirected,e,n,t);if(Object.prototype.hasOwnProperty.call(this._edgeLabels,a))return o&&(this._edgeLabels[a]=r),this;if(!s.A(t)&&!this._isMultigraph)throw Error("Cannot set a named edge when isMultigraph = false");this.setNode(e),this.setNode(n),this._edgeLabels[a]=o?r:this._defaultEdgeLabelFn(e,n,t);var u=function(e,n,t,r){var o=""+n,i=""+t;if(!e&&o>i){var a=o;o=i,i=a}var u={v:o,w:i};return r&&(u.name=r),u}(this._isDirected,e,n,t);return e=u.v,n=u.w,Object.freeze(u),this._edgeObjs[a]=u,b(this._preds[n],e),b(this._sucs[e],n),this._in[n][a]=u,this._out[e][a]=u,this._edgeCount++,this}edge(e,n,t){var r=1==arguments.length?y(this._isDirected,arguments[0]):m(this._isDirected,e,n,t);return this._edgeLabels[r]}hasEdge(e,n,t){var r=1==arguments.length?y(this._isDirected,arguments[0]):m(this._isDirected,e,n,t);return Object.prototype.hasOwnProperty.call(this._edgeLabels,r)}removeEdge(e,n,t){var r=1==arguments.length?y(this._isDirected,arguments[0]):m(this._isDirected,e,n,t),o=this._edgeObjs[r];return o&&(e=o.v,n=o.w,delete this._edgeLabels[r],delete this._edgeObjs[r],w(this._preds[n],e),w(this._sucs[e],n),delete this._in[n][r],delete this._out[e][r],this._edgeCount--),this}inEdges(e,n){var t=this._in[e];if(t){var r=g.A(t);return n?a.A(r,function(e){return e.v===n}):r}}outEdges(e,n){var t=this._out[e];if(t){var r=g.A(t);return n?a.A(r,function(e){return e.w===n}):r}}nodeEdges(e,n){var t=this.inEdges(e,n);if(t)return t.concat(this.outEdges(e,n))}}function b(e,n){e[n]?e[n]++:e[n]=1}function w(e,n){--e[n]||delete e[n]}function m(e,n,t,r){var o=""+n,i=""+t;if(!e&&o>i){var a=o;o=i,i=a}return o+"\x01"+i+"\x01"+(s.A(r)?"\0":r)}function y(e,n){return m(e,n.v,n.w,n.name)}A.prototype._nodeCount=0,A.prototype._edgeCount=0},47263:(e,n,t)=>{t.d(n,{A:()=>h});var r=t(51334),o=t(90139),i=t(86138),a=t(88493),u=t(80737),d=t(17601);let s=function(e,n,t,r){if(!(0,u.A)(e))return e;n=(0,i.A)(n,e);for(var s=-1,h=n.length,c=h-1,f=e;null!=f&&++s<h;){var l=(0,d.A)(n[s]),v=t;if("__proto__"===l||"constructor"===l||"prototype"===l)break;if(s!=c){var g=f[l];void 0===(v=r?r(g,l,f):void 0)&&(v=(0,u.A)(g)?g:(0,a.A)(n[s+1])?[]:{})}(0,o.A)(f,l,v),f=f[l]}return e},h=function(e,n,t){for(var o=-1,a=n.length,u={};++o<a;){var d=n[o],h=(0,r.A)(e,d);t(h,d)&&s(u,(0,i.A)(d,e),h)}return u}},49040:(e,n,t)=>{t.d(n,{A:()=>h});var r,o=t(44360),i=t(30982),a=t(3254),u=t(96987),d=t(11925),s=Math.max;let h=(r=function(e,n,t){var r=null==e?0:e.length;if(!r)return -1;var i=null==t?0:(0,d.A)(t);return i<0&&(i=s(r+i,0)),(0,u.A)(e,(0,o.A)(n,3),i)},function(e,n,t){var u=Object(e);if(!(0,i.A)(e)){var d=(0,o.A)(n,3);e=(0,a.A)(e),n=function(e){return d(u[e],e,u)}}var s=r(e,n,t);return s>-1?u[d?e[s]:s]:void 0})},52448:(e,n,t)=>{t.d(n,{A:()=>a});var r=Object.prototype.hasOwnProperty;let o=function(e,n){return null!=e&&r.call(e,n)};var i=t(36190);let a=function(e,n){return null!=e&&(0,i.A)(e,n,o)}},71093:(e,n,t)=>{t.d(n,{T:()=>r.T});var r=t(46503)},75250:(e,n,t)=>{t.d(n,{A:()=>r});let r=function(e){var n=null==e?0:e.length;return n?e[n-1]:void 0}},86007:(e,n,t)=>{t.d(n,{A:()=>a});var r=t(10811),o=t(44589),i=t(26602);let a=function(e){return"string"==typeof e||!(0,o.A)(e)&&(0,i.A)(e)&&"[object String]"==(0,r.A)(e)}},86018:(e,n,t)=>{t.d(n,{A:()=>o});var r=t(71776);let o=function(e){return(null==e?0:e.length)?(0,r.A)(e,1):[]}},86591:(e,n,t)=>{t.d(n,{Zp:()=>e9});var r=t(52850),o=t(2623),i=0;let a=function(e){var n=++i;return(0,o.A)(e)+n};var u=t(5086),d=t(86018),s=t(1082),h=Math.ceil,c=Math.max;let f=function(e,n,t,r){for(var o=-1,i=c(h((n-e)/(t||1)),0),a=Array(i);i--;)a[r?i:++o]=e,e+=t;return a};var l=t(42484),v=t(45372);let g=function(e,n,t){return t&&"number"!=typeof t&&(0,l.A)(e,n,t)&&(n=t=void 0),e=(0,v.A)(e),void 0===n?(n=e,e=0):n=(0,v.A)(n),t=void 0===t?e<n?1:-1:(0,v.A)(t),f(e,n,t,void 0)};var p=t(71093);class A{constructor(){var e={};e._next=e._prev=e,this._sentinel=e}dequeue(){var e=this._sentinel,n=e._prev;if(n!==e)return b(n),n}enqueue(e){var n=this._sentinel;e._prev&&e._next&&b(e),e._next=n._next,n._next._prev=e,n._next=e,e._prev=n}toString(){for(var e=[],n=this._sentinel,t=n._prev;t!==n;)e.push(JSON.stringify(t,w)),t=t._prev;return"["+e.join(", ")+"]"}}function b(e){e._prev._next=e._next,e._next._prev=e._prev,delete e._next,delete e._prev}function w(e,n){if("_next"!==e&&"_prev"!==e)return n}var m=u.A(1);function y(e,n,t,o,i){var a=i?[]:void 0;return r.A(e.inEdges(o.v),function(r){var o=e.edge(r),u=e.node(r.v);i&&a.push({v:r.v,w:r.w}),u.out-=o,_(n,t,u)}),r.A(e.outEdges(o.v),function(r){var o=e.edge(r),i=r.w,a=e.node(i);a.in-=o,_(n,t,a)}),e.removeNode(o.v),a}function _(e,n,t){t.out?t.in?e[t.out-t.in+n].enqueue(t):e[e.length-1].enqueue(t):e[0].enqueue(t)}var E=t(74651),x=t(47263),O=t(20825),k=t(27650),N=t(78173),P=function(e){return(0,N.A)((0,k.A)(e,void 0,d.A),e+"")}(function(e,n){return null==e?{}:(0,x.A)(e,n,function(n,t){return(0,O.A)(e,t)})}),j=t(41552),C=t(11999);let I=function(e,n){return e>n};var L=t(88500);let T=function(e){return e&&e.length?(0,C.A)(e,L.A,I):void 0};var M=t(75250),R=t(17580),F=t(57413),D=t(44360);let S=function(e,n){var t={};return n=(0,D.A)(n,3),(0,F.A)(e,function(e,r,o){(0,R.A)(t,r,n(e,r,o))}),t};var G=t(56764),V=t(43760),B=t(52448),q=t(1617);let Y=function(){return q.A.Date.now()};function z(e,n,t,r){var o;do o=a(r);while(e.hasNode(o));return t.dummy=n,e.setNode(o,t),o}function $(e){var n=new p.T({multigraph:e.isMultigraph()}).setGraph(e.graph());return r.A(e.nodes(),function(t){e.children(t).length||n.setNode(t,e.node(t))}),r.A(e.edges(),function(t){n.setEdge(t,e.edge(t))}),n}function J(e,n){var t,r,o=e.x,i=e.y,a=n.x-o,u=n.y-i,d=e.width/2,s=e.height/2;if(!a&&!u)throw Error("Not possible to find intersection inside of the rectangle");return Math.abs(u)*d>Math.abs(a)*s?(u<0&&(s=-s),t=s*a/u,r=s):(a<0&&(d=-d),t=d,r=d*u/a),{x:o+t,y:i+r}}function Z(e){var n=s.A(g(K(e)+1),function(){return[]});return r.A(e.nodes(),function(t){var r=e.node(t),o=r.rank;G.A(o)||(n[o][r.order]=t)}),n}function H(e,n,t,r){var o={width:0,height:0};return arguments.length>=4&&(o.rank=t,o.order=r),z(e,"border",o,n)}function K(e){return T(s.A(e.nodes(),function(n){var t=e.node(n).rank;if(!G.A(t))return t}))}function Q(e,n){var t=Y();try{return n()}finally{console.log(e+" time: "+(Y()-t)+"ms")}}function U(e,n){return n()}function W(e,n,t,r,o,i){var a=o[n][i-1],u=z(e,"border",{width:0,height:0,rank:i,borderType:n},t);o[n][i]=u,e.setParent(u,r),a&&e.setEdge(a,u,{weight:1})}function X(e){r.A(e.nodes(),function(n){ee(e.node(n))}),r.A(e.edges(),function(n){ee(e.edge(n))})}function ee(e){var n=e.width;e.width=e.height,e.height=n}function en(e){e.y=-e.y}function et(e){var n=e.x;e.x=e.y,e.y=n}var er=t(17300);let eo=function(e,n){return e&&e.length?(0,C.A)(e,(0,D.A)(n,2),er.A):void 0};function ei(e){var n={};r.A(e.sources(),function t(r){var o=e.node(r);if(Object.prototype.hasOwnProperty.call(n,r))return o.rank;n[r]=!0;var i=V.A(s.A(e.outEdges(r),function(n){return t(n.w)-e.edge(n).minlen}));return(i===Number.POSITIVE_INFINITY||null==i)&&(i=0),o.rank=i})}function ea(e,n){return e.node(n.w).rank-e.node(n.v).rank-e.edge(n).minlen}function eu(e){var n,t,o,i,a=new p.T({directed:!1}),u=e.nodes()[0],d=e.nodeCount();for(a.setNode(u,{});n=a,t=e,r.A(n.nodes(),function e(o){r.A(t.nodeEdges(o),function(r){var i=r.v,a=o===i?r.w:i;n.hasNode(a)||ea(t,r)||(n.setNode(a,{}),n.setEdge(o,a,{}),e(a))})}),n.nodeCount()<d;)o=function(e,n){return eo(n.edges(),function(t){if(e.hasNode(t.v)!==e.hasNode(t.w))return ea(n,t)})}(a,e),i=a.hasNode(o.v)?ea(e,o):-ea(e,o),function(e,n,t){r.A(e.nodes(),function(e){n.node(e).rank+=t})}(a,e,i);return a}var ed=t(49040),es=t(12176),eh=(u.A(1),u.A(1),t(83060)),ec=t(3923),ef=t(30982),el=t(86007),ev=(0,t(23121).A)("length"),eg=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]"),ep="\ud800-\udfff",eA="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",eb="\ud83c[\udffb-\udfff]",ew="[^"+ep+"]",em="(?:\ud83c[\udde6-\uddff]){2}",ey="[\ud800-\udbff][\udc00-\udfff]",e_="(?:"+eA+"|"+eb+")?",eE="[\\ufe0e\\ufe0f]?",ex="(?:\\u200d(?:"+[ew,em,ey].join("|")+")"+eE+e_+")*",eO=RegExp(eb+"(?="+eb+")|"+("(?:"+[ew+eA+"?",eA,em,ey,"["+ep+"]"].join("|"))+")"+(eE+e_+ex),"g");let ek=function(e){for(var n=eO.lastIndex=0;eO.test(e);)++n;return n},eN=function(e){return eg.test(e)?ek(e):ev(e)},eP=function(e){if(null==e)return 0;if((0,ef.A)(e))return(0,el.A)(e)?eN(e):e.length;var n=(0,ec.A)(e);return"[object Map]"==n||"[object Set]"==n?e.size:(0,eh.A)(e).length};function ej(){}ej.prototype=Error();var eC=t(44589);function eI(e,n,t){eC.A(n)||(n=[n]);var o=(e.isDirected()?e.successors:e.neighbors).bind(e),i=[],a={};return r.A(n,function(n){if(!e.hasNode(n))throw Error("Graph does not have node: "+n);(function e(n,t,o,i,a,u){!Object.prototype.hasOwnProperty.call(i,t)&&(i[t]=!0,o||u.push(t),r.A(a(t),function(t){e(n,t,o,i,a,u)}),o&&u.push(t))})(e,n,"post"===t,a,o,i)}),i}function eL(e){n=e,t=new p.T().setGraph(n.graph()),r.A(n.nodes(),function(e){t.setNode(e,n.node(e))}),r.A(n.edges(),function(e){var r=t.edge(e.v,e.w)||{weight:0,minlen:1},o=n.edge(e);t.setEdge(e.v,e.w,{weight:r.weight+o.weight,minlen:Math.max(r.minlen,o.minlen)})}),ei(e=t);var n,t,o,i,a=eu(e);for(eR(a),eT(a,e);o=eF(a);)i=eD(a,e,o),eS(a,e,o,i)}function eT(e,n){var t=eI(e,e.nodes(),"post");t=t.slice(0,t.length-1),r.A(t,function(t){var r,o,i,a;r=e,o=n,i=t,a=r.node(i).parent,r.edge(i,a).cutvalue=eM(r,o,i)})}function eM(e,n,t){var o=e.node(t).parent,i=!0,a=n.edge(t,o),u=0;return a||(i=!1,a=n.edge(o,t)),u=a.weight,r.A(n.nodeEdges(t),function(r){var a=r.v===t,d=a?r.w:r.v;if(d!==o){var s,h,c,f=a===i,l=n.edge(r).weight;if(u+=f?l:-l,s=e,h=t,c=d,s.hasEdge(h,c)){var v=e.edge(t,d).cutvalue;u+=f?-v:v}}}),u}function eR(e,n){arguments.length<2&&(n=e.nodes()[0]),function e(n,t,o,i,a){var u=o,d=n.node(i);return t[i]=!0,r.A(n.neighbors(i),function(r){Object.prototype.hasOwnProperty.call(t,r)||(o=e(n,t,o,r,i))}),d.low=u,d.lim=o++,a?d.parent=a:delete d.parent,o}(e,{},1,n)}function eF(e){return ed.A(e.edges(),function(n){return e.edge(n).cutvalue<0})}function eD(e,n,t){var r=t.v,o=t.w;n.hasEdge(r,o)||(r=t.w,o=t.v);var i=e.node(r),a=e.node(o),u=i,d=!1;return i.lim>a.lim&&(u=a,d=!0),eo(es.A(n.edges(),function(n){return d===eG(e,e.node(n.v),u)&&d!==eG(e,e.node(n.w),u)}),function(e){return ea(n,e)})}function eS(e,n,t,o){var i,a,u,d,s=t.v,h=t.w;e.removeEdge(s,h),e.setEdge(o.v,o.w,{}),eR(e),eT(e,n),i=e,a=n,u=ed.A(i.nodes(),function(e){return!a.node(e).parent}),d=(d=eI(i,u,"pre")).slice(1),r.A(d,function(e){var n=i.node(e).parent,t=a.edge(e,n),r=!1;t||(t=a.edge(n,e),r=!0),a.node(e).rank=a.node(n).rank+(r?t.minlen:-t.minlen)})}function eG(e,n,t){return t.low<=n.lim&&n.lim<=t.lim}t(46503),eL.initLowLimValues=eR,eL.initCutValues=eT,eL.calcCutValue=eM,eL.leaveEdge=eF,eL.enterEdge=eD,eL.exchangeEdges=eS;var eV=t(7086),eB=t(35135),eq=t(38818),eY=t(90139);let ez=function(e,n,t){for(var r=-1,o=e.length,i=n.length,a={};++r<o;){var u=r<i?n[r]:void 0;t(a,e[r],u)}return a};var e$=t(71776),eJ=t(66320),eZ=t(51334),eH=t(1356);let eK=function(e,n){var t=e.length;for(e.sort(n);t--;)e[t]=e[t].value;return e};var eQ=t(82641),eU=t(42058);let eW=function(e,n){if(e!==n){var t=void 0!==e,r=null===e,o=e==e,i=(0,eU.A)(e),a=void 0!==n,u=null===n,d=n==n,s=(0,eU.A)(n);if(!u&&!s&&!i&&e>n||i&&a&&d&&!u&&!s||r&&a&&d||!t&&d||!o)return 1;if(!r&&!i&&!s&&e<n||s&&t&&o&&!r&&!i||u&&t&&o||!a&&o||!d)return -1}return 0},eX=function(e,n,t){for(var r=-1,o=e.criteria,i=n.criteria,a=o.length,u=t.length;++r<a;){var d=eW(o[r],i[r]);if(d){if(r>=u)return d;return d*("desc"==t[r]?-1:1)}}return e.index-n.index},e0=function(e,n,t){n=n.length?(0,eJ.A)(n,function(e){return(0,eC.A)(e)?function(n){return(0,eZ.A)(n,1===e.length?e[0]:e)}:e}):[L.A];var r=-1;return n=(0,eJ.A)(n,(0,eQ.A)(D.A)),eK((0,eH.A)(e,function(e,t,o){return{criteria:(0,eJ.A)(n,function(n){return n(e)}),index:++r,value:e}}),function(e,n){return eX(e,n,t)})};var e1=(0,t(95662).A)(function(e,n){if(null==e)return[];var t=n.length;return t>1&&(0,l.A)(e,n[0],n[1])?n=[]:t>2&&(0,l.A)(n[0],n[1],n[2])&&(n=[n[0]]),e0(e,(0,e$.A)(n,1),[])});function e2(e,n,t){for(var r;n.length&&(r=M.A(n)).i<=t;)n.pop(),e.push(r.vs),t++;return t}function e3(e,n,t){return s.A(n,function(n){var o,i;return o=function(e){for(var n;e.hasNode(n=a("_root")););return n}(e),i=new p.T({compound:!0}).setGraph({root:o}).setDefaultNodeLabel(function(n){return e.node(n)}),r.A(e.nodes(),function(a){var u=e.node(a),d=e.parent(a);(u.rank===n||u.minRank<=n&&n<=u.maxRank)&&(i.setNode(a),i.setParent(a,d||o),r.A(e[t](a),function(n){var t=n.v===a?n.w:n.v,r=i.edge(t,a),o=G.A(r)?0:r.weight;i.setEdge(t,a,{weight:e.edge(n).weight+o})}),Object.prototype.hasOwnProperty.call(u,"minRank")&&i.setNode(a,{borderLeft:u.borderLeft[n],borderRight:u.borderRight[n]}))}),i})}function e6(e,n){r.A(n,function(n){r.A(n,function(n,t){e.node(n).order=t})})}var e5=t(70706),e8=t(521),e4=t(87814);function e7(e,n,t){if(n>t){var r=n;n=t,t=r}var o=e[n];o||(e[n]=o={}),o[t]=!0}function e9(e,n){var t=n&&n.debugTiming?Q:U;t("layout",()=>{var n=t("  buildLayoutGraph",()=>{var n,t,o;return n=e,t=new p.T({multigraph:!0,compound:!0}),o=ns(n.graph()),t.setGraph(E.A({},nn,nd(o,ne),P(o,nt))),r.A(n.nodes(),function(e){var r=ns(n.node(e));t.setNode(e,j.A(nd(r,nr),no)),t.setParent(e,n.parent(e))}),r.A(n.edges(),function(e){var r=ns(n.edge(e));t.setEdge(e,E.A({},na,nd(r,ni),P(r,nu)))}),t});t("  runLayout",()=>{var e,o;return e=n,void((o=t)("    makeSpaceForEdgeLabels",()=>{var n,t;return t=(n=e).graph(),void(t.ranksep/=2,r.A(n.edges(),function(e){var r=n.edge(e);r.minlen*=2,"c"!==r.labelpos.toLowerCase()&&("TB"===t.rankdir||"BT"===t.rankdir?r.width+=r.labeloffset:r.height+=r.labeloffset)}))}),o("    removeSelfEdges",()=>{var n;return n=e,void r.A(n.edges(),function(e){if(e.v===e.w){var t=n.node(e.v);t.selfEdges||(t.selfEdges=[]),t.selfEdges.push({e:e,label:n.edge(e)}),n.removeEdge(e)}})}),o("    acyclic",()=>{var n,t,o,i,u,h;return n="greedy"===e.graph().acyclicer?function(e,n){if(1>=e.nodeCount())return[];var t,o,i,a,u,h,c,f=(t=e,o=n||m,i=new p.T,a=0,u=0,r.A(t.nodes(),function(e){i.setNode(e,{v:e,in:0,out:0})}),r.A(t.edges(),function(e){var n=i.edge(e.v,e.w)||0,t=o(e);i.setEdge(e.v,e.w,n+t),u=Math.max(u,i.node(e.v).out+=t),a=Math.max(a,i.node(e.w).in+=t)}),h=g(u+a+3).map(function(){return new A}),c=a+1,r.A(i.nodes(),function(e){_(h,c,i.node(e))}),{graph:i,buckets:h,zeroIdx:c}),l=function(e,n,t){for(var r,o=[],i=n[n.length-1],a=n[0];e.nodeCount();){for(;r=a.dequeue();)y(e,n,t,r);for(;r=i.dequeue();)y(e,n,t,r);if(e.nodeCount()){for(var u=n.length-2;u>0;--u)if(r=n[u].dequeue()){o=o.concat(y(e,n,t,r,!0));break}}}return o}(f.graph,f.buckets,f.zeroIdx);return d.A(s.A(l,function(n){return e.outEdges(n.v,n.w)}))}(e,(t=e,function(e){return t.edge(e).weight})):(o=e,i=[],u={},h={},r.A(o.nodes(),function e(n){!Object.prototype.hasOwnProperty.call(h,n)&&(h[n]=!0,u[n]=!0,r.A(o.outEdges(n),function(n){Object.prototype.hasOwnProperty.call(u,n.w)?i.push(n):e(n.w)}),delete u[n])}),i),void r.A(n,function(n){var t=e.edge(n);e.removeEdge(n),t.forwardName=n.name,t.reversed=!0,e.setEdge(n.w,n.v,t,a("rev"))})}),o("    nestingGraph.run",()=>{var n,t,o,i,a,u,d;return n=z(e,"root",{},"_root"),a=e,u={},r.A(a.children(),function(e){!function e(n,t){var o=a.children(n);o&&o.length&&r.A(o,function(n){e(n,t+1)}),u[n]=t}(e,1)}),o=2*(t=T(eV.A(u))-1)+1,e.graph().nestingRoot=n,r.A(e.edges(),function(n){e.edge(n).minlen*=o}),i=(d=e,eB.A(d.edges(),function(e,n){return e+d.edge(n).weight},0)+1),void(r.A(e.children(),function(a){(function e(n,t,o,i,a,u,d){var s=n.children(d);if(!s.length){d!==t&&n.setEdge(t,d,{weight:0,minlen:o});return}var h=H(n,"_bt"),c=H(n,"_bb"),f=n.node(d);n.setParent(h,d),f.borderTop=h,n.setParent(c,d),f.borderBottom=c,r.A(s,function(r){e(n,t,o,i,a,u,r);var s=n.node(r),f=s.borderTop?s.borderTop:r,l=s.borderBottom?s.borderBottom:r,v=s.borderTop?i:2*i,g=f!==l?1:a-u[d]+1;n.setEdge(h,f,{weight:v,minlen:g,nestingEdge:!0}),n.setEdge(l,c,{weight:v,minlen:g,nestingEdge:!0})}),n.parent(d)||n.setEdge(t,h,{weight:0,minlen:a+u[d]})})(e,n,o,i,t,u,a)}),e.graph().nodeRankFactor=o)}),o("    rank",()=>(function(e){switch(e.graph().ranker){case"network-simplex":default:eL(e);break;case"tight-tree":var n;ei(n=e),eu(n);break;case"longest-path":ei(e)}})($(e))),o("    injectEdgeLabelProxies",()=>{var n;return n=e,void r.A(n.edges(),function(e){var t=n.edge(e);if(t.width&&t.height){var r=n.node(e.v),o={rank:(n.node(e.w).rank-r.rank)/2+r.rank,e:e};z(n,"edge-proxy",o,"_ep")}})}),o("    removeEmptyRanks",()=>{var n,t,o,i;return n=V.A(s.A(e.nodes(),function(n){return e.node(n).rank})),t=[],r.A(e.nodes(),function(r){var o=e.node(r).rank-n;t[o]||(t[o]=[]),t[o].push(r)}),o=0,i=e.graph().nodeRankFactor,void r.A(t,function(n,t){G.A(n)&&t%i!=0?--o:o&&r.A(n,function(n){e.node(n).rank+=o})})}),o("    nestingGraph.cleanup",()=>{var n;return n=e.graph(),void(e.removeNode(n.nestingRoot),delete n.nestingRoot,r.A(e.edges(),function(n){e.edge(n).nestingEdge&&e.removeEdge(n)}))}),o("    normalizeRanks",()=>{var n;return n=V.A(s.A(e.nodes(),function(n){return e.node(n).rank})),void r.A(e.nodes(),function(t){var r=e.node(t);B.A(r,"rank")&&(r.rank-=n)})}),o("    assignRankMinMax",()=>{var n,t;return n=e,t=0,void(r.A(n.nodes(),function(e){var r=n.node(e);r.borderTop&&(r.minRank=n.node(r.borderTop).rank,r.maxRank=n.node(r.borderBottom).rank,t=T(t,r.maxRank))}),n.graph().maxRank=t)}),o("    removeEdgeLabelProxies",()=>{var n;return n=e,void r.A(n.nodes(),function(e){var t=n.node(e);"edge-proxy"===t.dummy&&(n.edge(t.e).labelRank=t.rank,n.removeNode(e))})}),o("    normalize.run",()=>{e.graph().dummyChains=[],r.A(e.edges(),function(n){(function(e,n){var t,r,o=n.v,i=e.node(o).rank,a=n.w,u=e.node(a).rank,d=n.name,s=e.edge(n),h=s.labelRank;if(u!==i+1){e.removeEdge(n);var c=void 0;for(r=0,++i;i<u;++r,++i)s.points=[],t=z(e,"edge",c={width:0,height:0,edgeLabel:s,edgeObj:n,rank:i},"_d"),i===h&&(c.width=s.width,c.height=s.height,c.dummy="edge-label",c.labelpos=s.labelpos),e.setEdge(o,t,{weight:s.weight},d),0===r&&e.graph().dummyChains.push(t),o=t;e.setEdge(o,a,{weight:s.weight},d)}})(e,n)})}),o("    parentDummyChains",()=>{var n,t,o;return n=e,t={},o=0,r.A(n.children(),function e(i){var a=o;r.A(n.children(i),e),t[i]={low:a,lim:o++}}),void r.A(e.graph().dummyChains,function(n){for(var r=e.node(n),o=r.edgeObj,i=function(e,n,t,r){var o,i,a=[],u=[],d=Math.min(n[t].low,n[r].low),s=Math.max(n[t].lim,n[r].lim);o=t;do a.push(o=e.parent(o));while(o&&(n[o].low>d||s>n[o].lim));for(i=o,o=r;(o=e.parent(o))!==i;)u.push(o);return{path:a.concat(u.reverse()),lca:i}}(e,t,o.v,o.w),a=i.path,u=i.lca,d=0,s=a[0],h=!0;n!==o.w;){if(r=e.node(n),h){for(;(s=a[d])!==u&&e.node(s).maxRank<r.rank;)d++;s===u&&(h=!1)}if(!h){for(;d<a.length-1&&e.node(s=a[d+1]).minRank<=r.rank;)d++;s=a[d]}e.setParent(n,s),n=e.successors(n)[0]}})}),o("    addBorderSegments",()=>{r.A(e.children(),function n(t){var o=e.children(t),i=e.node(t);if(o.length&&r.A(o,n),Object.prototype.hasOwnProperty.call(i,"minRank")){i.borderLeft=[],i.borderRight=[];for(var a=i.minRank,u=i.maxRank+1;a<u;++a)W(e,"borderLeft","_bl",t,i,a),W(e,"borderRight","_br",t,i,a)}})}),o("    order",()=>(function(e){var n=K(e),t=e3(e,g(1,n+1),"inEdges"),o=e3(e,g(n-1,-1,-1),"outEdges"),i=(a={},u=es.A(e.nodes(),function(n){return!e.children(n).length}),h=T(s.A(u,function(n){return e.node(n).rank})),c=s.A(g(h+1),function(){return[]}),f=e1(u,function(n){return e.node(n).rank}),r.A(f,function n(t){B.A(a,t)||(a[t]=!0,c[e.node(t).rank].push(t),r.A(e.successors(t),n))}),c);e6(e,i);for(var a,u,h,c,f,l,v=Number.POSITIVE_INFINITY,A=0,b=0;b<4;++A,++b){(function(e,n){var t=new p.T;r.A(e,function(e){var o,i,a,u=e.graph().root,h=function e(n,t,o,i){var a,u,h,c,f,l,v,g,p,A,b,w=n.children(t),m=n.node(t),y=m?m.borderLeft:void 0,_=m?m.borderRight:void 0,E={};y&&(w=es.A(w,function(e){return e!==y&&e!==_}));var x=(a=w,s.A(a,function(e){var t=n.inEdges(e);if(!t.length)return{v:e};var r=eB.A(t,function(e,t){var r=n.edge(t),o=n.node(t.v);return{sum:e.sum+r.weight*o.order,weight:e.weight+r.weight}},{sum:0,weight:0});return{v:e,barycenter:r.sum/r.weight,weight:r.weight}}));r.A(x,function(t){if(n.children(t.v).length){var r,a,u=e(n,t.v,o,i);E[t.v]=u,Object.prototype.hasOwnProperty.call(u,"barycenter")&&(r=t,a=u,G.A(r.barycenter)?(r.barycenter=a.barycenter,r.weight=a.weight):(r.barycenter=(r.barycenter*r.weight+a.barycenter*a.weight)/(r.weight+a.weight),r.weight+=a.weight))}});var O=(u={},r.A(x,function(e,n){var t=u[e.v]={indegree:0,in:[],out:[],vs:[e.v],i:n};G.A(e.barycenter)||(t.barycenter=e.barycenter,t.weight=e.weight)}),r.A(o.edges(),function(e){var n=u[e.v],t=u[e.w];G.A(n)||G.A(t)||(t.indegree++,n.out.push(u[e.w]))}),function(e){for(var n=[];e.length;){var t=e.pop();n.push(t),r.A(t.in.reverse(),function(e){return function(n){!n.merged&&(G.A(n.barycenter)||G.A(e.barycenter)||n.barycenter>=e.barycenter)&&function(e,n){var t=0,r=0;e.weight&&(t+=e.barycenter*e.weight,r+=e.weight),n.weight&&(t+=n.barycenter*n.weight,r+=n.weight),e.vs=n.vs.concat(e.vs),e.barycenter=t/r,e.weight=r,e.i=Math.min(n.i,e.i),n.merged=!0}(e,n)}}(t)),r.A(t.out,function(n){return function(t){t.in.push(n),0==--t.indegree&&e.push(t)}}(t))}return s.A(es.A(n,function(e){return!e.merged}),function(e){return P(e,["vs","i","barycenter","weight"])})}(es.A(u,function(e){return!e.indegree})));(function(e,n){r.A(e,function(e){e.vs=d.A(e.vs.map(function(e){return n[e]?n[e].vs:e}))})})(O,E);var k=(f=(h={lhs:[],rhs:[]},r.A(O,function(e){var n;(n=e,Object.prototype.hasOwnProperty.call(n,"barycenter"))?h.lhs.push(e):h.rhs.push(e)}),c=h).lhs,l=e1(c.rhs,function(e){return-e.i}),v=[],g=0,p=0,A=0,f.sort(function(e){return function(n,t){return n.barycenter<t.barycenter?-1:n.barycenter>t.barycenter?1:e?t.i-n.i:n.i-t.i}}(!!i)),A=e2(v,l,A),r.A(f,function(e){A+=e.vs.length,v.push(e.vs),g+=e.barycenter*e.weight,p+=e.weight,A=e2(v,l,A)}),b={vs:d.A(v)},p&&(b.barycenter=g/p,b.weight=p),b);if(y&&(k.vs=d.A([y,k.vs,_]),n.predecessors(y).length)){var N=n.node(n.predecessors(y)[0]),j=n.node(n.predecessors(_)[0]);Object.prototype.hasOwnProperty.call(k,"barycenter")||(k.barycenter=0,k.weight=0),k.barycenter=(k.barycenter*k.weight+N.order+j.order)/(k.weight+2),k.weight+=2}return k}(e,u,t,n);r.A(h.vs,function(n,t){e.node(n).order=t}),o=h.vs,a={},r.A(o,function(n){for(var r,o,u=e.parent(n);u;){if((r=e.parent(u))?(o=a[r],a[r]=u):(o=i,i=u),o&&o!==u){t.setEdge(o,u);return}u=r}})})})(A%2?t:o,A%4>=2),i=Z(e);var w,m=function(e,n){for(var t=0,o=1;o<n.length;++o)t+=function(e,n,t){for(var o=ez(t||[],s.A(t,function(e,n){return n})||[],eY.A),i=d.A(s.A(n,function(n){return e1(s.A(e.outEdges(n),function(n){return{pos:o[n.w],weight:e.edge(n).weight}}),"pos")})),a=1;a<t.length;)a<<=1;var u=2*a-1;a-=1;var h=s.A(Array(u),function(){return 0}),c=0;return r.A(i.forEach(function(e){var n=e.pos+a;h[n]+=e.weight;for(var t=0;n>0;)n%2&&(t+=h[n+1]),n=n-1>>1,h[n]+=e.weight;c+=e.weight*t})),c}(e,n[o-1],n[o]);return t}(e,i);m<v&&(b=0,w=i,l=(0,eq.A)(w,5),v=m)}e6(e,l)})(e)),o("    insertSelfEdges",()=>{var n,t;return t=Z(n=e),void r.A(t,function(e){var t=0;r.A(e,function(e,o){var i=n.node(e);i.order=o+t,r.A(i.selfEdges,function(e){z(n,"selfedge",{width:e.label.width,height:e.label.height,rank:i.rank,order:o+ ++t,e:e.e,label:e.label},"_se")}),delete i.selfEdges})})}),o("    adjustCoordinateSystem",()=>{var n;("lr"===(n=e.graph().rankdir.toLowerCase())||"rl"===n)&&X(e)}),o("    position",()=>{var n,t,o,i,a,u,d,h,c,f,l,v,A,b,w,m,y;w=Z(b=n=$(n=e)),m=b.graph().ranksep,y=0,r.A(w,function(e){var n=T(s.A(e,function(e){return b.node(e).height}));r.A(e,function(e){b.node(e).y=y+n/2}),y+=n+m}),i=Z(t=n),u=E.A((a={},eB.A(i,function(e,n){var o=0,i=0,u=e.length,d=M.A(n);return r.A(n,function(e,s){var h=function(e,n){if(e.node(n).dummy)return ed.A(e.predecessors(n),function(n){return e.node(n).dummy})}(t,e),c=h?t.node(h).order:u;(h||e===d)&&(r.A(n.slice(i,s+1),function(e){r.A(t.predecessors(e),function(n){var r=t.node(n),i=r.order;(i<o||c<i)&&!(r.dummy&&t.node(e).dummy)&&e7(a,n,e)})}),i=s+1,o=c)}),n}),a),function(e,n){var t={};function o(n,o,i,a,u){var d;r.A(g(o,i),function(o){d=n[o],e.node(d).dummy&&r.A(e.predecessors(d),function(n){var r=e.node(n);r.dummy&&(r.order<a||r.order>u)&&e7(t,n,d)})})}return eB.A(n,function(n,t){var i,a=-1,u=0;return r.A(t,function(r,d){if("border"===e.node(r).dummy){var s=e.predecessors(r);s.length&&(i=e.node(s[0]).order,o(t,u,d,a,i),u=d,a=i)}o(t,u,t.length,i,n.length)}),t}),t}(t,i)),d={},r.A(["u","d"],function(e){o="u"===e?i:eV.A(i).reverse(),r.A(["l","r"],function(n){"r"===n&&(o=s.A(o,function(e){return eV.A(e).reverse()}));var i,a,h,c,f=("u"===e?t.predecessors:t.successors).bind(t),l=(i=o,a={},h={},c={},r.A(i,function(e){r.A(e,function(e,n){a[e]=e,h[e]=e,c[e]=n})}),r.A(i,function(e){var n=-1;r.A(e,function(e){var t=f(e);if(t.length)for(var r=((t=e1(t,function(e){return c[e]})).length-1)/2,o=Math.floor(r),i=Math.ceil(r);o<=i;++o){var d=t[o];h[e]===e&&n<c[d]&&!function(e,n,t){if(n>t){var r=n;n=t,t=r}return!!e[n]&&Object.prototype.hasOwnProperty.call(e[n],t)}(u,e,d)&&(h[d]=e,h[e]=a[e]=a[d],n=c[d])}})}),{root:a,align:h}),v=function(e,n,t,o,i){var a,u,d,s,h,c,f,l,v,g,A={},b=(a=e,u=n,d=t,s=i,l=new p.T,g=(h=(v=a.graph()).nodesep,c=v.edgesep,f=s,function(e,n,t){var r,o,i=e.node(n),a=e.node(t);if(r=0+i.width/2,Object.prototype.hasOwnProperty.call(i,"labelpos"))switch(i.labelpos.toLowerCase()){case"l":o=-i.width/2;break;case"r":o=i.width/2}if(o&&(r+=f?o:-o),o=0,r+=(i.dummy?c:h)/2,r+=(a.dummy?c:h)/2,r+=a.width/2,Object.prototype.hasOwnProperty.call(a,"labelpos"))switch(a.labelpos.toLowerCase()){case"l":o=a.width/2;break;case"r":o=-a.width/2}return o&&(r+=f?o:-o),o=0,r}),r.A(u,function(e){var n;r.A(e,function(e){var t=d[e];if(l.setNode(t),n){var r=d[n],o=l.edge(r,t);l.setEdge(r,t,Math.max(g(a,e,n),o||0))}n=e})}),l),w=i?"borderLeft":"borderRight";function m(e,n){for(var t=b.nodes(),r=t.pop(),o={};r;)o[r]?e(r):(o[r]=!0,t.push(r),t=t.concat(n(r))),r=t.pop()}return m(function(e){A[e]=b.inEdges(e).reduce(function(e,n){return Math.max(e,A[n.v]+b.edge(n))},0)},b.predecessors.bind(b)),m(function(n){var t=b.outEdges(n).reduce(function(e,n){return Math.min(e,A[n.w]-b.edge(n))},Number.POSITIVE_INFINITY),r=e.node(n);t!==Number.POSITIVE_INFINITY&&r.borderType!==w&&(A[n]=Math.max(A[n],t))},b.successors.bind(b)),r.A(o,function(e){A[e]=A[t[e]]}),A}(t,o,l.root,l.align,"r"===n);"r"===n&&(v=S(v,function(e){return-e})),d[e+n]=v})}),h=eo(eV.A(d),function(e){var n=Number.NEGATIVE_INFINITY,r=Number.POSITIVE_INFINITY;return null==e||(0,e8.A)(e,(0,e5.A)(function(e,o){var i,a,u=(i=t,a=o,i.node(a).width/2);n=Math.max(e+u,n),r=Math.min(e-u,r)}),e4.A),n-r}),c=eV.A(h),f=V.A(c),l=T(c),r.A(["u","d"],function(e){r.A(["l","r"],function(n){var t,r=e+n,o=d[r];if(o!==h){var i=eV.A(o);(t="l"===n?f-V.A(i):l-T(i))&&(d[r]=S(o,function(e){return e+t}))}})}),v=t.graph().align,(A=S(d.ul,function(e,n){if(v)return d[v.toLowerCase()][n];var t=e1(s.A(d,n));return(t[1]+t[2])/2}))&&(0,F.A)(A,(0,e5.A)(function(e,t){n.node(t).x=e}))}),o("    positionSelfEdges",()=>{var n;return n=e,void r.A(n.nodes(),function(e){var t=n.node(e);if("selfedge"===t.dummy){var r=n.node(t.e.v),o=r.x+r.width/2,i=r.y,a=t.x-o,u=r.height/2;n.setEdge(t.e,t.label),n.removeNode(e),t.label.points=[{x:o+2*a/3,y:i-u},{x:o+5*a/6,y:i-u},{x:o+a,y:i},{x:o+5*a/6,y:i+u},{x:o+2*a/3,y:i+u}],t.label.x=t.x,t.label.y=t.y}})}),o("    removeBorderNodes",()=>{var n;return n=e,void(r.A(n.nodes(),function(e){if(n.children(e).length){var t=n.node(e),r=n.node(t.borderTop),o=n.node(t.borderBottom),i=n.node(M.A(t.borderLeft)),a=n.node(M.A(t.borderRight));t.width=Math.abs(a.x-i.x),t.height=Math.abs(o.y-r.y),t.x=i.x+t.width/2,t.y=r.y+t.height/2}}),r.A(n.nodes(),function(e){"border"===n.node(e).dummy&&n.removeNode(e)}))}),o("    normalize.undo",()=>{r.A(e.graph().dummyChains,function(n){var t,r=e.node(n),o=r.edgeLabel;for(e.setEdge(r.edgeObj,o);r.dummy;)t=e.successors(n)[0],e.removeNode(n),o.points.push({x:r.x,y:r.y}),"edge-label"===r.dummy&&(o.x=r.x,o.y=r.y,o.width=r.width,o.height=r.height),n=t,r=e.node(n)})}),o("    fixupEdgeLabelCoords",()=>{var n;return n=e,void r.A(n.edges(),function(e){var t=n.edge(e);if(Object.prototype.hasOwnProperty.call(t,"x"))switch(("l"===t.labelpos||"r"===t.labelpos)&&(t.width-=t.labeloffset),t.labelpos){case"l":t.x-=t.width/2+t.labeloffset;break;case"r":t.x+=t.width/2+t.labeloffset}})}),o("    undoCoordinateSystem",()=>{var n,t,o;("bt"===(n=e.graph().rankdir.toLowerCase())||"rl"===n)&&(t=e,r.A(t.nodes(),function(e){en(t.node(e))}),r.A(t.edges(),function(e){var n=t.edge(e);r.A(n.points,en),Object.prototype.hasOwnProperty.call(n,"y")&&en(n)})),("lr"===n||"rl"===n)&&(o=e,r.A(o.nodes(),function(e){et(o.node(e))}),r.A(o.edges(),function(e){var n=o.edge(e);r.A(n.points,et),Object.prototype.hasOwnProperty.call(n,"x")&&et(n)}),X(e))}),o("    translateGraph",()=>(function(e){var n=Number.POSITIVE_INFINITY,t=0,o=Number.POSITIVE_INFINITY,i=0,a=e.graph(),u=a.marginx||0,d=a.marginy||0;function s(e){var r=e.x,a=e.y,u=e.width,d=e.height;n=Math.min(n,r-u/2),t=Math.max(t,r+u/2),o=Math.min(o,a-d/2),i=Math.max(i,a+d/2)}r.A(e.nodes(),function(n){s(e.node(n))}),r.A(e.edges(),function(n){var t=e.edge(n);Object.prototype.hasOwnProperty.call(t,"x")&&s(t)}),n-=u,o-=d,r.A(e.nodes(),function(t){var r=e.node(t);r.x-=n,r.y-=o}),r.A(e.edges(),function(t){var i=e.edge(t);r.A(i.points,function(e){e.x-=n,e.y-=o}),Object.prototype.hasOwnProperty.call(i,"x")&&(i.x-=n),Object.prototype.hasOwnProperty.call(i,"y")&&(i.y-=o)}),a.width=t-n+u,a.height=i-o+d})(e)),o("    assignNodeIntersects",()=>{var n;return n=e,void r.A(n.edges(),function(e){var t,r,o=n.edge(e),i=n.node(e.v),a=n.node(e.w);o.points?(t=o.points[0],r=o.points[o.points.length-1]):(o.points=[],t=a,r=i),o.points.unshift(J(i,t)),o.points.push(J(a,r))})}),o("    reversePoints",()=>{var n;return n=e,void r.A(n.edges(),function(e){var t=n.edge(e);t.reversed&&t.points.reverse()})}),o("    acyclic.undo",()=>{r.A(e.edges(),function(n){var t=e.edge(n);if(t.reversed){e.removeEdge(n);var r=t.forwardName;delete t.reversed,delete t.forwardName,e.setEdge(n.w,n.v,t,r)}})}))}),t("  updateInputGraph",()=>{var t,o;return t=e,o=n,void(r.A(t.nodes(),function(e){var n=t.node(e),r=o.node(e);n&&(n.x=r.x,n.y=r.y,o.children(e).length&&(n.width=r.width,n.height=r.height))}),r.A(t.edges(),function(e){var n=t.edge(e),r=o.edge(e);n.points=r.points,Object.prototype.hasOwnProperty.call(r,"x")&&(n.x=r.x,n.y=r.y)}),t.graph().width=o.graph().width,t.graph().height=o.graph().height)})})}var ne=["nodesep","edgesep","ranksep","marginx","marginy"],nn={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"},nt=["acyclicer","ranker","rankdir","align"],nr=["width","height"],no={width:0,height:0},ni=["minlen","weight","width","height","labeloffset"],na={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},nu=["labelpos"];function nd(e,n){return S(P(e,n),Number)}function ns(e){var n={};return r.A(e,function(e,t){n[t.toLowerCase()]=e}),n}}};