"use strict";exports.id=8757,exports.ids=[8757],exports.modules={363:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},1359:(e,t,n)=>{n.d(t,{Oh:()=>o});var r=n(43210),i=0;function o(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??s()),document.body.insertAdjacentElement("beforeend",e[1]??s()),i++,()=>{1===i&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),i--}},[])}function s(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2117:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]])},7044:(e,t,n)=>{n.d(t,{B:()=>r});let r="undefined"!=typeof window},11860:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12157:(e,t,n)=>{n.d(t,{L:()=>r});let r=(0,n(43210).createContext)({})},12941:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},13964:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14952:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15124:(e,t,n)=>{n.d(t,{E:()=>i});var r=n(43210);let i=n(7044).B?r.useLayoutEffect:r.useEffect},21134:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},21279:(e,t,n)=>{n.d(t,{t:()=>r});let r=(0,n(43210).createContext)(null)},25028:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(43210),i=n(51215),o=n(14163),s=n(66156),a=n(60687),l=r.forwardRef((e,t)=>{let{container:n,...l}=e,[u,c]=r.useState(!1);(0,s.N)(()=>c(!0),[]);let h=n||u&&globalThis?.document?.body;return h?i.createPortal((0,a.jsx)(o.sG.div,{...l,ref:t}),h):null});l.displayName="Portal"},26312:(e,t,n)=>{n.d(t,{H_:()=>e4,UC:()=>e2,YJ:()=>e5,q7:()=>e6,VF:()=>e7,JU:()=>e3,ZL:()=>e1,z6:()=>e9,hN:()=>e8,bL:()=>eJ,wv:()=>te,Pb:()=>tt,G5:()=>tr,ZP:()=>tn,l9:()=>e0});var r=n(43210),i=n(70569),o=n(98599),s=n(11273),a=n(65551),l=n(14163),u=n(9510),c=n(43),h=n(31355),d=n(1359),f=n(32547),p=n(96963),m=n(38674),v=n(25028),g=n(46059),y=n(72942),x=n(8730),w=n(13495),b=n(63376),P=n(42247),T=n(60687),A=["Enter"," "],E=["ArrowUp","PageDown","End"],S=["ArrowDown","PageUp","Home",...E],C={ltr:[...A,"ArrowRight"],rtl:[...A,"ArrowLeft"]},M={ltr:["ArrowLeft"],rtl:["ArrowRight"]},R="Menu",[k,D,L]=(0,u.N)(R),[j,V]=(0,s.A)(R,[L,m.Bk,y.RG]),F=(0,m.Bk)(),O=(0,y.RG)(),[B,I]=j(R),[N,U]=j(R),W=e=>{let{__scopeMenu:t,open:n=!1,children:i,dir:o,onOpenChange:s,modal:a=!0}=e,l=F(t),[u,h]=r.useState(null),d=r.useRef(!1),f=(0,w.c)(s),p=(0,c.jH)(o);return r.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,T.jsx)(m.bL,{...l,children:(0,T.jsx)(B,{scope:t,open:n,onOpenChange:f,content:u,onContentChange:h,children:(0,T.jsx)(N,{scope:t,onClose:r.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:d,dir:p,modal:a,children:i})})})};W.displayName=R;var $=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,i=F(n);return(0,T.jsx)(m.Mz,{...i,...r,ref:t})});$.displayName="MenuAnchor";var _="MenuPortal",[z,H]=j(_,{forceMount:void 0}),K=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:i}=e,o=I(_,t);return(0,T.jsx)(z,{scope:t,forceMount:n,children:(0,T.jsx)(g.C,{present:n||o.open,children:(0,T.jsx)(v.Z,{asChild:!0,container:i,children:r})})})};K.displayName=_;var Y="MenuContent",[X,G]=j(Y),q=r.forwardRef((e,t)=>{let n=H(Y,e.__scopeMenu),{forceMount:r=n.forceMount,...i}=e,o=I(Y,e.__scopeMenu),s=U(Y,e.__scopeMenu);return(0,T.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,T.jsx)(g.C,{present:r||o.open,children:(0,T.jsx)(k.Slot,{scope:e.__scopeMenu,children:s.modal?(0,T.jsx)(Z,{...i,ref:t}):(0,T.jsx)(Q,{...i,ref:t})})})})}),Z=r.forwardRef((e,t)=>{let n=I(Y,e.__scopeMenu),s=r.useRef(null),a=(0,o.s)(t,s);return r.useEffect(()=>{let e=s.current;if(e)return(0,b.Eq)(e)},[]),(0,T.jsx)(J,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=r.forwardRef((e,t)=>{let n=I(Y,e.__scopeMenu);return(0,T.jsx)(J,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),J=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:s=!1,trapFocus:a,onOpenAutoFocus:l,onCloseAutoFocus:u,disableOutsidePointerEvents:c,onEntryFocus:p,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:A,disableOutsideScroll:C,...M}=e,R=I(Y,n),k=U(Y,n),L=F(n),j=O(n),V=D(n),[B,N]=r.useState(null),W=r.useRef(null),$=(0,o.s)(t,W,R.onContentChange),_=r.useRef(0),z=r.useRef(""),H=r.useRef(0),K=r.useRef(null),G=r.useRef("right"),q=r.useRef(0),Z=C?P.A:r.Fragment,Q=C?{as:x.DX,allowPinchZoom:!0}:void 0,J=e=>{let t=z.current+e,n=V().filter(e=>!e.disabled),r=document.activeElement,i=n.find(e=>e.ref.current===r)?.textValue,o=function(e,t,n){var r;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===i.length&&(o=o.filter(e=>e!==n));let s=o.find(e=>e.toLowerCase().startsWith(i.toLowerCase()));return s!==n?s:void 0}(n.map(e=>e.textValue),t,i),s=n.find(e=>e.textValue===o)?.ref.current;(function e(t){z.current=t,window.clearTimeout(_.current),""!==t&&(_.current=window.setTimeout(()=>e(""),1e3))})(t),s&&setTimeout(()=>s.focus())};r.useEffect(()=>()=>window.clearTimeout(_.current),[]),(0,d.Oh)();let ee=r.useCallback(e=>G.current===K.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let s=t[e].x,a=t[e].y,l=t[o].x,u=t[o].y;a>r!=u>r&&n<(l-s)*(r-a)/(u-a)+s&&(i=!i)}return i}({x:e.clientX,y:e.clientY},t)}(e,K.current?.area),[]);return(0,T.jsx)(X,{scope:n,searchRef:z,onItemEnter:r.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),onItemLeave:r.useCallback(e=>{ee(e)||(W.current?.focus(),N(null))},[ee]),onTriggerLeave:r.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),pointerGraceTimerRef:H,onPointerGraceIntentChange:r.useCallback(e=>{K.current=e},[]),children:(0,T.jsx)(Z,{...Q,children:(0,T.jsx)(f.n,{asChild:!0,trapped:a,onMountAutoFocus:(0,i.m)(l,e=>{e.preventDefault(),W.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,T.jsx)(h.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:A,children:(0,T.jsx)(y.bL,{asChild:!0,...j,dir:k.dir,orientation:"vertical",loop:s,currentTabStopId:B,onCurrentTabStopIdChange:N,onEntryFocus:(0,i.m)(p,e=>{k.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,T.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eC(R.open),"data-radix-menu-content":"",dir:k.dir,...L,...M,ref:$,style:{outline:"none",...M.style},onKeyDown:(0,i.m)(M.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&J(e.key));let i=W.current;if(e.target!==i||!S.includes(e.key))return;e.preventDefault();let o=V().filter(e=>!e.disabled).map(e=>e.ref.current);E.includes(e.key)&&o.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(o)}),onBlur:(0,i.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(_.current),z.current="")}),onPointerMove:(0,i.m)(e.onPointerMove,ek(e=>{let t=e.target,n=q.current!==e.clientX;e.currentTarget.contains(t)&&n&&(G.current=e.clientX>q.current?"right":"left",q.current=e.clientX)}))})})})})})})});q.displayName=Y;var ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,T.jsx)(l.sG.div,{role:"group",...r,ref:t})});ee.displayName="MenuGroup";var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,T.jsx)(l.sG.div,{...r,ref:t})});et.displayName="MenuLabel";var en="MenuItem",er="menu.itemSelect",ei=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:s,...a}=e,u=r.useRef(null),c=U(en,e.__scopeMenu),h=G(en,e.__scopeMenu),d=(0,o.s)(t,u),f=r.useRef(!1);return(0,T.jsx)(eo,{...a,ref:d,disabled:n,onClick:(0,i.m)(e.onClick,()=>{let e=u.current;if(!n&&e){let t=new CustomEvent(er,{bubbles:!0,cancelable:!0});e.addEventListener(er,e=>s?.(e),{once:!0}),(0,l.hO)(e,t),t.defaultPrevented?f.current=!1:c.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),f.current=!0},onPointerUp:(0,i.m)(e.onPointerUp,e=>{f.current||e.currentTarget?.click()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=""!==h.searchRef.current;!n&&(!t||" "!==e.key)&&A.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ei.displayName=en;var eo=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:s=!1,textValue:a,...u}=e,c=G(en,n),h=O(n),d=r.useRef(null),f=(0,o.s)(t,d),[p,m]=r.useState(!1),[v,g]=r.useState("");return r.useEffect(()=>{let e=d.current;e&&g((e.textContent??"").trim())},[u.children]),(0,T.jsx)(k.ItemSlot,{scope:n,disabled:s,textValue:a??v,children:(0,T.jsx)(y.q7,{asChild:!0,...h,focusable:!s,children:(0,T.jsx)(l.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":s||void 0,"data-disabled":s?"":void 0,...u,ref:f,onPointerMove:(0,i.m)(e.onPointerMove,ek(e=>{s?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,i.m)(e.onPointerLeave,ek(e=>c.onItemLeave(e))),onFocus:(0,i.m)(e.onFocus,()=>m(!0)),onBlur:(0,i.m)(e.onBlur,()=>m(!1))})})})}),es=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,T.jsx)(ep,{scope:e.__scopeMenu,checked:n,children:(0,T.jsx)(ei,{role:"menuitemcheckbox","aria-checked":eM(n)?"mixed":n,...o,ref:t,"data-state":eR(n),onSelect:(0,i.m)(o.onSelect,()=>r?.(!!eM(n)||!n),{checkForDefaultPrevented:!1})})})});es.displayName="MenuCheckboxItem";var ea="MenuRadioGroup",[el,eu]=j(ea,{value:void 0,onValueChange:()=>{}}),ec=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...i}=e,o=(0,w.c)(r);return(0,T.jsx)(el,{scope:e.__scopeMenu,value:n,onValueChange:o,children:(0,T.jsx)(ee,{...i,ref:t})})});ec.displayName=ea;var eh="MenuRadioItem",ed=r.forwardRef((e,t)=>{let{value:n,...r}=e,o=eu(eh,e.__scopeMenu),s=n===o.value;return(0,T.jsx)(ep,{scope:e.__scopeMenu,checked:s,children:(0,T.jsx)(ei,{role:"menuitemradio","aria-checked":s,...r,ref:t,"data-state":eR(s),onSelect:(0,i.m)(r.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ed.displayName=eh;var ef="MenuItemIndicator",[ep,em]=j(ef,{checked:!1}),ev=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...i}=e,o=em(ef,n);return(0,T.jsx)(g.C,{present:r||eM(o.checked)||!0===o.checked,children:(0,T.jsx)(l.sG.span,{...i,ref:t,"data-state":eR(o.checked)})})});ev.displayName=ef;var eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,T.jsx)(l.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});eg.displayName="MenuSeparator";var ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,i=F(n);return(0,T.jsx)(m.i3,{...i,...r,ref:t})});ey.displayName="MenuArrow";var ex="MenuSub",[ew,eb]=j(ex),eP=e=>{let{__scopeMenu:t,children:n,open:i=!1,onOpenChange:o}=e,s=I(ex,t),a=F(t),[l,u]=r.useState(null),[c,h]=r.useState(null),d=(0,w.c)(o);return r.useEffect(()=>(!1===s.open&&d(!1),()=>d(!1)),[s.open,d]),(0,T.jsx)(m.bL,{...a,children:(0,T.jsx)(B,{scope:t,open:i,onOpenChange:d,content:c,onContentChange:h,children:(0,T.jsx)(ew,{scope:t,contentId:(0,p.B)(),triggerId:(0,p.B)(),trigger:l,onTriggerChange:u,children:n})})})};eP.displayName=ex;var eT="MenuSubTrigger",eA=r.forwardRef((e,t)=>{let n=I(eT,e.__scopeMenu),s=U(eT,e.__scopeMenu),a=eb(eT,e.__scopeMenu),l=G(eT,e.__scopeMenu),u=r.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:h}=l,d={__scopeMenu:e.__scopeMenu},f=r.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return r.useEffect(()=>f,[f]),r.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),h(null)}},[c,h]),(0,T.jsx)($,{asChild:!0,...d,children:(0,T.jsx)(eo,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":a.contentId,"data-state":eC(n.open),...e,ref:(0,o.t)(t,a.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,i.m)(e.onPointerMove,ek(t=>{l.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||u.current||(l.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:(0,i.m)(e.onPointerLeave,ek(e=>{f();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,i="right"===r,o=t[i?"left":"right"],s=t[i?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:o,y:t.top},{x:s,y:t.top},{x:s,y:t.bottom},{x:o,y:t.bottom}],side:r}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,i.m)(e.onKeyDown,t=>{let r=""!==l.searchRef.current;!e.disabled&&(!r||" "!==t.key)&&C[s.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});eA.displayName=eT;var eE="MenuSubContent",eS=r.forwardRef((e,t)=>{let n=H(Y,e.__scopeMenu),{forceMount:s=n.forceMount,...a}=e,l=I(Y,e.__scopeMenu),u=U(Y,e.__scopeMenu),c=eb(eE,e.__scopeMenu),h=r.useRef(null),d=(0,o.s)(t,h);return(0,T.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,T.jsx)(g.C,{present:s||l.open,children:(0,T.jsx)(k.Slot,{scope:e.__scopeMenu,children:(0,T.jsx)(J,{id:c.contentId,"aria-labelledby":c.triggerId,...a,ref:d,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{u.isUsingKeyboardRef.current&&h.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,i.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=M[u.dir].includes(e.key);t&&n&&(l.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function eC(e){return e?"open":"closed"}function eM(e){return"indeterminate"===e}function eR(e){return eM(e)?"indeterminate":e?"checked":"unchecked"}function ek(e){return t=>"mouse"===t.pointerType?e(t):void 0}eS.displayName=eE;var eD="DropdownMenu",[eL,ej]=(0,s.A)(eD,[V]),eV=V(),[eF,eO]=eL(eD),eB=e=>{let{__scopeDropdownMenu:t,children:n,dir:i,open:o,defaultOpen:s,onOpenChange:l,modal:u=!0}=e,c=eV(t),h=r.useRef(null),[d=!1,f]=(0,a.i)({prop:o,defaultProp:s,onChange:l});return(0,T.jsx)(eF,{scope:t,triggerId:(0,p.B)(),triggerRef:h,contentId:(0,p.B)(),open:d,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:u,children:(0,T.jsx)(W,{...c,open:d,onOpenChange:f,dir:i,modal:u,children:n})})};eB.displayName=eD;var eI="DropdownMenuTrigger",eN=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...s}=e,a=eO(eI,n),u=eV(n);return(0,T.jsx)($,{asChild:!0,...u,children:(0,T.jsx)(l.sG.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...s,ref:(0,o.t)(t,a.triggerRef),onPointerDown:(0,i.m)(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(a.onOpenToggle(),a.open||e.preventDefault())}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eN.displayName=eI;var eU=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eV(t);return(0,T.jsx)(K,{...r,...n})};eU.displayName="DropdownMenuPortal";var eW="DropdownMenuContent",e$=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,s=eO(eW,n),a=eV(n),l=r.useRef(!1);return(0,T.jsx)(q,{id:s.contentId,"aria-labelledby":s.triggerId,...a,...o,ref:t,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{l.current||s.triggerRef.current?.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,i.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!s.modal||r)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e$.displayName=eW;var e_=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eV(n);return(0,T.jsx)(ee,{...i,...r,ref:t})});e_.displayName="DropdownMenuGroup";var ez=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eV(n);return(0,T.jsx)(et,{...i,...r,ref:t})});ez.displayName="DropdownMenuLabel";var eH=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eV(n);return(0,T.jsx)(ei,{...i,...r,ref:t})});eH.displayName="DropdownMenuItem";var eK=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eV(n);return(0,T.jsx)(es,{...i,...r,ref:t})});eK.displayName="DropdownMenuCheckboxItem";var eY=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eV(n);return(0,T.jsx)(ec,{...i,...r,ref:t})});eY.displayName="DropdownMenuRadioGroup";var eX=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eV(n);return(0,T.jsx)(ed,{...i,...r,ref:t})});eX.displayName="DropdownMenuRadioItem";var eG=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eV(n);return(0,T.jsx)(ev,{...i,...r,ref:t})});eG.displayName="DropdownMenuItemIndicator";var eq=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eV(n);return(0,T.jsx)(eg,{...i,...r,ref:t})});eq.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eV(n);return(0,T.jsx)(ey,{...i,...r,ref:t})}).displayName="DropdownMenuArrow";var eZ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eV(n);return(0,T.jsx)(eA,{...i,...r,ref:t})});eZ.displayName="DropdownMenuSubTrigger";var eQ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,i=eV(n);return(0,T.jsx)(eS,{...i,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eQ.displayName="DropdownMenuSubContent";var eJ=eB,e0=eN,e1=eU,e2=e$,e5=e_,e3=ez,e6=eH,e4=eK,e9=eY,e8=eX,e7=eG,te=eq,tt=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:i,defaultOpen:o}=e,s=eV(t),[l=!1,u]=(0,a.i)({prop:r,defaultProp:o,onChange:i});return(0,T.jsx)(eP,{...s,open:l,onOpenChange:u,children:n})},tn=eZ,tr=eQ},31355:(e,t,n)=>{n.d(t,{qW:()=>d});var r,i=n(43210),o=n(70569),s=n(14163),a=n(98599),l=n(13495),u=n(60687),c="dismissableLayer.update",h=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=i.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:d,onPointerDownOutside:m,onFocusOutside:v,onInteractOutside:g,onDismiss:y,...x}=e,w=i.useContext(h),[b,P]=i.useState(null),T=b?.ownerDocument??globalThis?.document,[,A]=i.useState({}),E=(0,a.s)(t,e=>P(e)),S=Array.from(w.layers),[C]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),M=S.indexOf(C),R=b?S.indexOf(b):-1,k=w.layersWithOutsidePointerEventsDisabled.size>0,D=R>=M,L=function(e,t=globalThis?.document){let n=(0,l.c)(e),r=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){p("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=r,t.addEventListener("click",o.current,{once:!0})):r()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...w.branches].some(e=>e.contains(t));!D||n||(m?.(e),g?.(e),e.defaultPrevented||y?.())},T),j=function(e,t=globalThis?.document){let n=(0,l.c)(e),r=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!r.current&&p("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...w.branches].some(e=>e.contains(t))||(v?.(e),g?.(e),e.defaultPrevented||y?.())},T);return function(e,t=globalThis?.document){let n=(0,l.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{R===w.layers.size-1&&(d?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},T),i.useEffect(()=>{if(b)return n&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(r=T.body.style.pointerEvents,T.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(b)),w.layers.add(b),f(),()=>{n&&1===w.layersWithOutsidePointerEventsDisabled.size&&(T.body.style.pointerEvents=r)}},[b,T,n,w]),i.useEffect(()=>()=>{b&&(w.layers.delete(b),w.layersWithOutsidePointerEventsDisabled.delete(b),f())},[b,w]),i.useEffect(()=>{let e=()=>A({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(s.sG.div,{...x,ref:E,style:{pointerEvents:k?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,L.onPointerDownCapture)})});function f(){let e=new CustomEvent(c);document.dispatchEvent(e)}function p(e,t,n,{discrete:r}){let i=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),r?(0,s.hO)(i,o):i.dispatchEvent(o)}d.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(h),r=i.useRef(null),o=(0,a.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(s.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch"},32547:(e,t,n)=>{n.d(t,{n:()=>h});var r=n(43210),i=n(98599),o=n(14163),s=n(13495),a=n(60687),l="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},h=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:h=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[x,w]=r.useState(null),b=(0,s.c)(v),P=(0,s.c)(g),T=r.useRef(null),A=(0,i.s)(t,e=>w(e)),E=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(h){let e=function(e){if(E.paused||!x)return;let t=e.target;x.contains(t)?T.current=t:p(T.current,{select:!0})},t=function(e){if(E.paused||!x)return;let t=e.relatedTarget;null===t||x.contains(t)||p(T.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&p(x)});return x&&n.observe(x,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[h,x,E.paused]),r.useEffect(()=>{if(x){m.add(E);let e=document.activeElement;if(!x.contains(e)){let t=new CustomEvent(l,c);x.addEventListener(l,b),x.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(p(r,{select:t}),document.activeElement!==n)return}(d(x).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&p(x))}return()=>{x.removeEventListener(l,b),setTimeout(()=>{let t=new CustomEvent(u,c);x.addEventListener(u,P),x.dispatchEvent(t),t.defaultPrevented||p(e??document.body,{select:!0}),x.removeEventListener(u,P),m.remove(E)},0)}}},[x,b,P,E]);let S=r.useCallback(e=>{if(!n&&!h||E.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[i,o]=function(e){let t=d(e);return[f(t,e),f(t.reverse(),e)]}(t);i&&o?e.shiftKey||r!==o?e.shiftKey&&r===i&&(e.preventDefault(),n&&p(o,{select:!0})):(e.preventDefault(),n&&p(i,{select:!0})):r===t&&e.preventDefault()}},[n,h,E.paused]);return(0,a.jsx)(o.sG.div,{tabIndex:-1,...y,ref:A,onKeyDown:S})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function f(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function p(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}h.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},32582:(e,t,n)=>{n.d(t,{Q:()=>r});let r=(0,n(43210).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},38674:(e,t,n)=>{n.d(t,{Mz:()=>eG,i3:()=>eZ,UC:()=>eq,bL:()=>eX,Bk:()=>eL});var r=n(43210);let i=["top","right","bottom","left"],o=Math.min,s=Math.max,a=Math.round,l=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},h={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function f(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(f(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>h[e])}function x(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function P(e,t,n){let r,{reference:i,floating:o}=e,s=g(t),a=m(g(t)),l=v(a),u=f(t),c="y"===s,h=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,y=i[l]/2-o[l]/2;switch(u){case"top":r={x:h,y:i.y-o.height};break;case"bottom":r={x:h,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(p(t)){case"start":r[a]-=y*(n&&c?-1:1);break;case"end":r[a]+=y*(n&&c?-1:1)}return r}let T=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:s}=n,a=o.filter(Boolean),l=await (null==s.isRTL?void 0:s.isRTL(t)),u=await s.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:h}=P(u,r,l),d=r,f={},p=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:v,y:g,data:y,reset:x}=await m({x:c,y:h,initialPlacement:r,placement:d,strategy:i,middlewareData:f,rects:u,platform:s,elements:{reference:e,floating:t}});c=null!=v?v:c,h=null!=g?g:h,f={...f,[o]:{...f[o],...y}},x&&p<=50&&(p++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(u=!0===x.rects?await s.getElementRects({reference:e,floating:t,strategy:i}):x.rects),{x:c,y:h}=P(u,d,l)),n=-1)}return{x:c,y:h,placement:d,strategy:i,middlewareData:f}};async function A(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:s,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:h="floating",altBoundary:f=!1,padding:p=0}=d(t,e),m=w(p),v=a[f?"floating"===h?"reference":"floating":h],g=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(v)))||n?v:v.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:l})),y="floating"===h?{x:r,y:i,width:s.floating.width,height:s.floating.height}:s.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),P=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},T=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:x,strategy:l}):y);return{top:(g.top-T.top+m.top)/P.y,bottom:(T.bottom-g.bottom+m.bottom)/P.y,left:(g.left-T.left+m.left)/P.x,right:(T.right-g.right+m.right)/P.x}}function E(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function S(e){return i.some(t=>e[t]>=0)}async function C(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),s=f(n),a=p(n),l="y"===g(n),u=["left","top"].includes(s)?-1:1,c=o&&l?-1:1,h=d(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof h?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return a&&"number"==typeof y&&(v="end"===a?-1*y:y),l?{x:v*c,y:m*u}:{x:m*u,y:v*c}}function M(){return"undefined"!=typeof window}function R(e){return L(e)?(e.nodeName||"").toLowerCase():"#document"}function k(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function D(e){var t;return null==(t=(L(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function L(e){return!!M()&&(e instanceof Node||e instanceof k(e).Node)}function j(e){return!!M()&&(e instanceof Element||e instanceof k(e).Element)}function V(e){return!!M()&&(e instanceof HTMLElement||e instanceof k(e).HTMLElement)}function F(e){return!!M()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof k(e).ShadowRoot)}function O(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=W(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function B(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function I(e){let t=N(),n=j(e)?W(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function N(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function U(e){return["html","body","#document"].includes(R(e))}function W(e){return k(e).getComputedStyle(e)}function $(e){return j(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function _(e){if("html"===R(e))return e;let t=e.assignedSlot||e.parentNode||F(e)&&e.host||D(e);return F(t)?t.host:t}function z(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=_(t);return U(n)?t.ownerDocument?t.ownerDocument.body:t.body:V(n)&&O(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),s=k(i);if(o){let e=H(s);return t.concat(s,s.visualViewport||[],O(i)?i:[],e&&n?z(e):[])}return t.concat(i,z(i,[],n))}function H(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function K(e){let t=W(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=V(e),o=i?e.offsetWidth:n,s=i?e.offsetHeight:r,l=a(n)!==o||a(r)!==s;return l&&(n=o,r=s),{width:n,height:r,$:l}}function Y(e){return j(e)?e:e.contextElement}function X(e){let t=Y(e);if(!V(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=K(t),s=(o?a(n.width):n.width)/r,l=(o?a(n.height):n.height)/i;return s&&Number.isFinite(s)||(s=1),l&&Number.isFinite(l)||(l=1),{x:s,y:l}}let G=u(0);function q(e){let t=k(e);return N()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:G}function Z(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),s=Y(e),a=u(1);t&&(r?j(r)&&(a=X(r)):a=X(e));let l=(void 0===(i=n)&&(i=!1),r&&(!i||r===k(s))&&i)?q(s):u(0),c=(o.left+l.x)/a.x,h=(o.top+l.y)/a.y,d=o.width/a.x,f=o.height/a.y;if(s){let e=k(s),t=r&&j(r)?k(r):r,n=e,i=H(n);for(;i&&r&&t!==n;){let e=X(i),t=i.getBoundingClientRect(),r=W(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,s=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,h*=e.y,d*=e.x,f*=e.y,c+=o,h+=s,i=H(n=k(i))}}return b({width:d,height:f,x:c,y:h})}function Q(e,t){let n=$(e).scrollLeft;return t?t.left+n:Z(D(e)).left+n}function J(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Q(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=k(e),r=D(e),i=n.visualViewport,o=r.clientWidth,s=r.clientHeight,a=0,l=0;if(i){o=i.width,s=i.height;let e=N();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,l=i.offsetTop)}return{width:o,height:s,x:a,y:l}}(e,n);else if("document"===t)r=function(e){let t=D(e),n=$(e),r=e.ownerDocument.body,i=s(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=s(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+Q(e),l=-n.scrollTop;return"rtl"===W(r).direction&&(a+=s(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:l}}(D(e));else if(j(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=V(e)?X(e):u(1),s=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:s,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=q(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===W(e).position}function en(e,t){if(!V(e)||"fixed"===W(e).position)return null;if(t)return t(e);let n=e.offsetParent;return D(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=k(e);if(B(e))return n;if(!V(e)){let t=_(e);for(;t&&!U(t);){if(j(t)&&!et(t))return t;t=_(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(R(r))&&et(r);)r=en(r,t);return r&&U(r)&&et(r)&&!I(r)?n:r||function(e){let t=_(e);for(;V(t)&&!U(t);){if(I(t))return t;if(B(t))break;t=_(t)}return null}(e)||n}let ei=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=V(t),i=D(t),o="fixed"===n,s=Z(e,!0,o,t),a={scrollLeft:0,scrollTop:0},l=u(0);if(r||!r&&!o){if(("body"!==R(t)||O(i))&&(a=$(t)),r){let e=Z(t,!0,o,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=Q(i))}o&&!r&&i&&(l.x=Q(i));let c=!i||r||o?u(0):J(i,a);return{x:s.left+a.scrollLeft-l.x-c.x,y:s.top+a.scrollTop-l.y-c.y,width:s.width,height:s.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,s=D(r),a=!!t&&B(t.floating);if(r===s||a&&o)return n;let l={scrollLeft:0,scrollTop:0},c=u(1),h=u(0),d=V(r);if((d||!d&&!o)&&(("body"!==R(r)||O(s))&&(l=$(r)),V(r))){let e=Z(r);c=X(r),h.x=e.x+r.clientLeft,h.y=e.y+r.clientTop}let f=!s||d||o?u(0):J(s,l,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+h.x+f.x,y:n.y*c.y-l.scrollTop*c.y+h.y+f.y}},getDocumentElement:D,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?B(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=z(e,[],!1).filter(e=>j(e)&&"body"!==R(e)),i=null,o="fixed"===W(e).position,s=o?_(e):e;for(;j(s)&&!U(s);){let t=W(s),n=I(s);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||O(s)&&!n&&function e(t,n){let r=_(t);return!(r===n||!j(r)||U(r))&&("fixed"===W(r).position||e(r,n))}(e,s))?r=r.filter(e=>e!==s):i=t,s=_(s)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=a[0],u=a.reduce((e,n)=>{let r=ee(t,n,i);return e.top=s(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=s(r.left,e.left),e},ee(t,l,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:ei,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=K(e);return{width:t,height:n}},getScale:X,isElement:j,isRTL:function(e){return"rtl"===W(e).direction}};function es(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:l,elements:u,middlewareData:c}=t,{element:h,padding:f=0}=d(e,t)||{};if(null==h)return{};let y=w(f),x={x:n,y:r},b=m(g(i)),P=v(b),T=await l.getDimensions(h),A="y"===b,E=A?"clientHeight":"clientWidth",S=a.reference[P]+a.reference[b]-x[b]-a.floating[P],C=x[b]-a.reference[b],M=await (null==l.getOffsetParent?void 0:l.getOffsetParent(h)),R=M?M[E]:0;R&&await (null==l.isElement?void 0:l.isElement(M))||(R=u.floating[E]||a.floating[P]);let k=R/2-T[P]/2-1,D=o(y[A?"top":"left"],k),L=o(y[A?"bottom":"right"],k),j=R-T[P]-L,V=R/2-T[P]/2+(S/2-C/2),F=s(D,o(V,j)),O=!c.arrow&&null!=p(i)&&V!==F&&a.reference[P]/2-(V<D?D:L)-T[P]/2<0,B=O?V<D?V-D:V-j:0;return{[b]:x[b]+B,data:{[b]:F,centerOffset:V-F-B,...O&&{alignmentOffset:B}},reset:O}}}),el=(e,t,n)=>{let r=new Map,i={platform:eo,...n},o={...i.platform,_c:r};return T(e,t,{...i,platform:o})};var eu=n(51215),ec="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function eh(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eh(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!eh(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ef(e,t){let n=ed(e);return Math.round(t*n)/n}function ep(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:s,middlewareData:a}=t,l=await C(t,e);return s===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+l.x,y:o+l.y,data:{...l,placement:s}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:l=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=d(e,t),h={x:n,y:r},p=await A(t,c),v=g(f(i)),y=m(v),x=h[y],w=h[v];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=x+p[e],r=x-p[t];x=s(n,o(x,r))}if(l){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=w+p[e],r=w-p[t];w=s(n,o(w,r))}let b=u.fn({...t,[y]:x,[v]:w});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[y]:a,[v]:l}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:s}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=d(e,t),c={x:n,y:r},h=g(i),p=m(h),v=c[p],y=c[h],x=d(a,t),w="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(l){let e="y"===p?"height":"width",t=o.reference[p]-o.floating[e]+w.mainAxis,n=o.reference[p]+o.reference[e]-w.mainAxis;v<t?v=t:v>n&&(v=n)}if(u){var b,P;let e="y"===p?"width":"height",t=["top","left"].includes(f(i)),n=o.reference[h]-o.floating[e]+(t&&(null==(b=s.offset)?void 0:b[h])||0)+(t?0:w.crossAxis),r=o.reference[h]+o.reference[e]+(t?0:(null==(P=s.offset)?void 0:P[h])||0)-(t?w.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[p]:v,[h]:y}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,s,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:h,platform:w,elements:b}=t,{mainAxis:P=!0,crossAxis:T=!0,fallbackPlacements:E,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:C="none",flipAlignment:M=!0,...R}=d(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let k=f(l),D=g(h),L=f(h)===h,j=await (null==w.isRTL?void 0:w.isRTL(b.floating)),V=E||(L||!M?[x(h)]:function(e){let t=x(e);return[y(e),t,y(t)]}(h)),F="none"!==C;!E&&F&&V.push(...function(e,t,n,r){let i=p(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(f(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(y)))),o}(h,M,C,j));let O=[h,...V],B=await A(t,R),I=[],N=(null==(r=u.flip)?void 0:r.overflows)||[];if(P&&I.push(B[k]),T){let e=function(e,t,n){void 0===n&&(n=!1);let r=p(e),i=m(g(e)),o=v(i),s="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(s=x(s)),[s,x(s)]}(l,c,j);I.push(B[e[0]],B[e[1]])}if(N=[...N,{placement:l,overflows:I}],!I.every(e=>e<=0)){let e=((null==(i=u.flip)?void 0:i.index)||0)+1,t=O[e];if(t){let n="alignment"===T&&D!==g(t),r=(null==(s=N[0])?void 0:s.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:N},reset:{placement:t}}}let n=null==(o=N.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(S){case"bestFit":{let e=null==(a=N.filter(e=>{if(F){let t=g(e.placement);return t===D||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=h}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a;let{placement:l,rects:u,platform:c,elements:h}=t,{apply:m=()=>{},...v}=d(e,t),y=await A(t,v),x=f(l),w=p(l),b="y"===g(l),{width:P,height:T}=u.floating;"top"===x||"bottom"===x?(i=x,a=w===(await (null==c.isRTL?void 0:c.isRTL(h.floating))?"start":"end")?"left":"right"):(a=x,i="end"===w?"top":"bottom");let E=T-y.top-y.bottom,S=P-y.left-y.right,C=o(T-y[i],E),M=o(P-y[a],S),R=!t.middlewareData.shift,k=C,D=M;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(D=S),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(k=E),R&&!w){let e=s(y.left,0),t=s(y.right,0),n=s(y.top,0),r=s(y.bottom,0);b?D=P-2*(0!==e||0!==t?e+t:s(y.left,y.right)):k=T-2*(0!==n||0!==r?n+r:s(y.top,y.bottom))}await m({...t,availableWidth:D,availableHeight:k});let L=await c.getDimensions(h.floating);return P!==L.width||T!==L.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=d(e,t);switch(r){case"referenceHidden":{let e=E(await A(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:S(e)}}}case"escaped":{let e=E(await A(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:S(e)}}}default:return{}}}}}(e),options:[e,t]}),eP=(e,t)=>({...em(e),options:[e,t]});var eT=n(14163),eA=n(60687),eE=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eA.jsx)(eT.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eA.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eE.displayName="Arrow";var eS=n(98599),eC=n(11273),eM=n(13495),eR=n(66156),ek="Popper",[eD,eL]=(0,eC.A)(ek),[ej,eV]=eD(ek),eF=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eA.jsx)(ej,{scope:t,anchor:i,onAnchorChange:o,children:n})};eF.displayName=ek;var eO="PopperAnchor",eB=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,s=eV(eO,n),a=r.useRef(null),l=(0,eS.s)(t,a);return r.useEffect(()=>{s.onAnchorChange(i?.current||a.current)}),i?null:(0,eA.jsx)(eT.sG.div,{...o,ref:l})});eB.displayName=eO;var eI="PopperContent",[eN,eU]=eD(eI),eW=r.forwardRef((e,t)=>{let{__scopePopper:n,side:i="bottom",sideOffset:a=0,align:u="center",alignOffset:c=0,arrowPadding:h=0,avoidCollisions:d=!0,collisionBoundary:f=[],collisionPadding:p=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...x}=e,w=eV(eI,n),[b,P]=r.useState(null),T=(0,eS.s)(t,e=>P(e)),[A,E]=r.useState(null),S=function(e){let[t,n]=r.useState(void 0);return(0,eR.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(A),C=S?.width??0,M=S?.height??0,R="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},k=Array.isArray(f)?f:[f],L=k.length>0,j={padding:R,boundary:k.filter(eH),altBoundary:L},{refs:V,floatingStyles:F,placement:O,isPositioned:B,middlewareData:I}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:s,floating:a}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[h,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[f,p]=r.useState(i);eh(f,i)||p(i);let[m,v]=r.useState(null),[g,y]=r.useState(null),x=r.useCallback(e=>{e!==T.current&&(T.current=e,v(e))},[]),w=r.useCallback(e=>{e!==A.current&&(A.current=e,y(e))},[]),b=s||m,P=a||g,T=r.useRef(null),A=r.useRef(null),E=r.useRef(h),S=null!=u,C=ep(u),M=ep(o),R=ep(c),k=r.useCallback(()=>{if(!T.current||!A.current)return;let e={placement:t,strategy:n,middleware:f};M.current&&(e.platform=M.current),el(T.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==R.current};D.current&&!eh(E.current,t)&&(E.current=t,eu.flushSync(()=>{d(t)}))})},[f,t,n,M,R]);ec(()=>{!1===c&&E.current.isPositioned&&(E.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let D=r.useRef(!1);ec(()=>(D.current=!0,()=>{D.current=!1}),[]),ec(()=>{if(b&&(T.current=b),P&&(A.current=P),b&&P){if(C.current)return C.current(b,P,k);k()}},[b,P,k,C,S]);let L=r.useMemo(()=>({reference:T,floating:A,setReference:x,setFloating:w}),[x,w]),j=r.useMemo(()=>({reference:b,floating:P}),[b,P]),V=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=ef(j.floating,h.x),r=ef(j.floating,h.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,l,j.floating,h.x,h.y]);return r.useMemo(()=>({...h,update:k,refs:L,elements:j,floatingStyles:V}),[h,k,L,j,V])}({strategy:"fixed",placement:i+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:h="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,f=Y(e),p=a||u?[...f?z(f):[],...z(t)]:[];p.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=f&&h?function(e,t){let n,r=null,i=D(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function u(c,h){void 0===c&&(c=!1),void 0===h&&(h=1),a();let d=e.getBoundingClientRect(),{left:f,top:p,width:m,height:v}=d;if(c||t(),!m||!v)return;let g=l(p),y=l(i.clientWidth-(f+m)),x={rootMargin:-g+"px "+-y+"px "+-l(i.clientHeight-(p+v))+"px "+-l(f)+"px",threshold:s(0,o(1,h))||1},w=!0;function b(t){let r=t[0].intersectionRatio;if(r!==h){if(!w)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||es(d,e.getBoundingClientRect())||u(),w=!1}try{r=new IntersectionObserver(b,{...x,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,x)}r.observe(e)}(!0),a}(f,n):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===f&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),f&&!d&&g.observe(f),g.observe(t));let y=d?Z(e):null;return d&&function t(){let r=Z(e);y&&!es(y,r)&&n(),y=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(i)}})(...e,{animationFrame:"always"===g}),elements:{reference:w.anchor},middleware:[ev({mainAxis:a+M,alignmentAxis:c}),d&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ey():void 0,...j}),d&&ex({...j}),ew({...j,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:i,height:o}=t.reference,s=e.floating.style;s.setProperty("--radix-popper-available-width",`${n}px`),s.setProperty("--radix-popper-available-height",`${r}px`),s.setProperty("--radix-popper-anchor-width",`${i}px`),s.setProperty("--radix-popper-anchor-height",`${o}px`)}}),A&&eP({element:A,padding:h}),eK({arrowWidth:C,arrowHeight:M}),v&&eb({strategy:"referenceHidden",...j})]}),[N,U]=eY(O),W=(0,eM.c)(y);(0,eR.N)(()=>{B&&W?.()},[B,W]);let $=I.arrow?.x,_=I.arrow?.y,H=I.arrow?.centerOffset!==0,[K,X]=r.useState();return(0,eR.N)(()=>{b&&X(window.getComputedStyle(b).zIndex)},[b]),(0,eA.jsx)("div",{ref:V.setFloating,"data-radix-popper-content-wrapper":"",style:{...F,transform:B?F.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:K,"--radix-popper-transform-origin":[I.transformOrigin?.x,I.transformOrigin?.y].join(" "),...I.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eA.jsx)(eN,{scope:n,placedSide:N,onArrowChange:E,arrowX:$,arrowY:_,shouldHideArrow:H,children:(0,eA.jsx)(eT.sG.div,{"data-side":N,"data-align":U,...x,ref:T,style:{...x.style,animation:B?void 0:"none"}})})})});eW.displayName=eI;var e$="PopperArrow",e_={top:"bottom",right:"left",bottom:"top",left:"right"},ez=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eU(e$,n),o=e_[i.placedSide];return(0,eA.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eA.jsx)(eE,{...r,ref:t,style:{...r.style,display:"block"}})})});function eH(e){return null!==e}ez.displayName=e$;var eK=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:i}=t,o=i.arrow?.centerOffset!==0,s=o?0:e.arrowWidth,a=o?0:e.arrowHeight,[l,u]=eY(n),c={start:"0%",center:"50%",end:"100%"}[u],h=(i.arrow?.x??0)+s/2,d=(i.arrow?.y??0)+a/2,f="",p="";return"bottom"===l?(f=o?c:`${h}px`,p=`${-a}px`):"top"===l?(f=o?c:`${h}px`,p=`${r.floating.height+a}px`):"right"===l?(f=`${-a}px`,p=o?c:`${d}px`):"left"===l&&(f=`${r.floating.width+a}px`,p=o?c:`${d}px`),{data:{x:f,y:p}}}});function eY(e){let[t,n="center"]=e.split("-");return[t,n]}var eX=eF,eG=eB,eq=eW,eZ=ez},40083:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},41312:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},42247:(e,t,n)=>{n.d(t,{A:()=>H});var r,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function o(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n}Object.create;Object.create;var s=("function"==typeof SuppressedError&&SuppressedError,n(43210)),a="right-scroll-bar-position",l="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,h=new WeakMap;function d(e){return e}var f=function(e){void 0===e&&(e={});var t,n,r,o,s=(t=null,void 0===n&&(n=d),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},s=function(){return Promise.resolve().then(i)};s(),r={push:function(e){t.push(e),s()},filter:function(e){return t=t.filter(e),r}}}});return s.options=i({async:!0,ssr:!1},e),s}(),p=function(){},m=s.forwardRef(function(e,t){var n,r,a,l,d=s.useRef(null),m=s.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),v=m[0],g=m[1],y=e.forwardProps,x=e.children,w=e.className,b=e.removeScrollBar,P=e.enabled,T=e.shards,A=e.sideCar,E=e.noIsolation,S=e.inert,C=e.allowPinchZoom,M=e.as,R=e.gapMode,k=o(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),D=(n=[d,t],r=function(e){return n.forEach(function(t){return u(t,e)})},(a=(0,s.useState)(function(){return{value:null,callback:r,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=r,l=a.facade,c(function(){var e=h.get(l);if(e){var t=new Set(e),r=new Set(n),i=l.current;t.forEach(function(e){r.has(e)||u(e,null)}),r.forEach(function(e){t.has(e)||u(e,i)})}h.set(l,n)},[n]),l),L=i(i({},k),v);return s.createElement(s.Fragment,null,P&&s.createElement(A,{sideCar:f,removeScrollBar:b,shards:T,noIsolation:E,inert:S,setCallbacks:g,allowPinchZoom:!!C,lockRef:d,gapMode:R}),y?s.cloneElement(s.Children.only(x),i(i({},L),{ref:D})):s.createElement(void 0===M?"div":M,i({},L,{className:w,ref:D}),x))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:l,zeroRight:a};var v=function(e){var t=e.sideCar,n=o(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return s.createElement(r,i({},n))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(i){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,s;(o=t).styleSheet?o.styleSheet.cssText=i:o.appendChild(document.createTextNode(i)),s=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(s)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},x=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},b=function(e){return parseInt(e||"",10)||0},P=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],i=t["padding"===e?"paddingRight":"marginRight"];return[b(n),b(r),b(i)]},T=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=P(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},A=x(),E="data-scroll-locked",S=function(e,t,n,r){var i=e.left,o=e.top,s=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(E,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(s,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(E,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},C=function(){var e=parseInt(document.body.getAttribute(E)||"0",10);return isFinite(e)?e:0},M=function(){s.useEffect(function(){return document.body.setAttribute(E,(C()+1).toString()),function(){var e=C()-1;e<=0?document.body.removeAttribute(E):document.body.setAttribute(E,e.toString())}},[])},R=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,i=void 0===r?"margin":r;M();var o=s.useMemo(function(){return T(i)},[i]);return s.createElement(A,{styles:S(o,!t,i,n?"":"!important")})},k=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return k=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){k=!1}var L=!!k&&{passive:!1},j=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},V=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),F(e,r)){var i=O(e,r);if(i[1]>i[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},F=function(e,t){return"v"===e?j(t,"overflowY"):j(t,"overflowX")},O=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},B=function(e,t,n,r,i){var o,s=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),a=s*r,l=n.target,u=t.contains(l),c=!1,h=a>0,d=0,f=0;do{var p=O(e,l),m=p[0],v=p[1]-p[2]-s*m;(m||v)&&F(e,l)&&(d+=v,f+=m),l=l instanceof ShadowRoot?l.host:l.parentNode}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return h&&(i&&1>Math.abs(d)||!i&&a>d)?c=!0:!h&&(i&&1>Math.abs(f)||!i&&-a>f)&&(c=!0),c},I=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},N=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},W=0,$=[];let _=(f.useMedium(function(e){var t=s.useRef([]),n=s.useRef([0,0]),r=s.useRef(),i=s.useState(W++)[0],o=s.useState(x)[0],a=s.useRef(e);s.useEffect(function(){a.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var l=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var i,o=I(e),s=n.current,l="deltaX"in e?e.deltaX:s[0]-o[0],u="deltaY"in e?e.deltaY:s[1]-o[1],c=e.target,h=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===h&&"range"===c.type)return!1;var d=V(h,c);if(!d)return!0;if(d?i=h:(i="v"===h?"h":"v",d=V(h,c)),!d)return!1;if(!r.current&&"changedTouches"in e&&(l||u)&&(r.current=i),!i)return!0;var f=r.current||i;return B(f,t,e,"h"===f?l:u,!0)},[]),u=s.useCallback(function(e){if($.length&&$[$.length-1]===o){var n="deltaY"in e?N(e):I(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var i=(a.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?l(e,i[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=s.useCallback(function(e,n,r,i){var o={name:e,delta:n,target:r,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),h=s.useCallback(function(e){n.current=I(e),r.current=void 0},[]),d=s.useCallback(function(t){c(t.type,N(t),t.target,l(t,e.lockRef.current))},[]),f=s.useCallback(function(t){c(t.type,I(t),t.target,l(t,e.lockRef.current))},[]);s.useEffect(function(){return $.push(o),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:f}),document.addEventListener("wheel",u,L),document.addEventListener("touchmove",u,L),document.addEventListener("touchstart",h,L),function(){$=$.filter(function(e){return e!==o}),document.removeEventListener("wheel",u,L),document.removeEventListener("touchmove",u,L),document.removeEventListener("touchstart",h,L)}},[]);var p=e.removeScrollBar,m=e.inert;return s.createElement(s.Fragment,null,m?s.createElement(o,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,p?s.createElement(R,{gapMode:e.gapMode}):null)}),v);var z=s.forwardRef(function(e,t){return s.createElement(m,i({},e,{ref:t,sideCar:_}))});z.classNames=m.classNames;let H=z},48730:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},63376:(e,t,n)=>{n.d(t,{Eq:()=>c});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},i=new WeakMap,o=new WeakMap,s={},a=0,l=function(e){return e&&(e.host||l(e.parentNode))},u=function(e,t,n,r){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});s[n]||(s[n]=new WeakMap);var c=s[n],h=[],d=new Set,f=new Set(u),p=function(e){!(!e||d.has(e))&&(d.add(e),p(e.parentNode))};u.forEach(p);var m=function(e){!(!e||f.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(d.has(e))m(e);else try{var t=e.getAttribute(r),s=null!==t&&"false"!==t,a=(i.get(e)||0)+1,l=(c.get(e)||0)+1;i.set(e,a),c.set(e,l),h.push(e),1===a&&s&&o.set(e,!0),1===l&&e.setAttribute(n,"true"),s||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),d.clear(),a++,function(){h.forEach(function(e){var t=i.get(e)-1,s=c.get(e)-1;i.set(e,t),c.set(e,s),t||(o.has(e)||e.removeAttribute(r),o.delete(e)),s||e.removeAttribute(n)}),--a||(i=new WeakMap,i=new WeakMap,o=new WeakMap,s={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var i=Array.from(Array.isArray(e)?e:[e]),o=t||r(e);return o?(i.push.apply(i,Array.from(o.querySelectorAll("[aria-live]"))),u(i,o,n,"aria-hidden")):function(){return null}}},65104:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Cuboid",[["path",{d:"m21.12 6.4-6.05-4.06a2 2 0 0 0-2.17-.05L2.95 8.41a2 2 0 0 0-.95 1.7v5.82a2 2 0 0 0 .88 1.66l6.05 4.07a2 2 0 0 0 2.17.05l9.95-6.12a2 2 0 0 0 .95-1.7V8.06a2 2 0 0 0-.88-1.66Z",key:"1u2ovd"}],["path",{d:"M10 22v-8L2.25 9.15",key:"11pn4q"}],["path",{d:"m10 14 11.77-6.87",key:"1kt1wh"}]])},65822:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},72789:(e,t,n)=>{n.d(t,{M:()=>i});var r=n(43210);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},82080:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},86044:(e,t,n)=>{n.d(t,{xQ:()=>o});var r=n(43210),i=n(21279);function o(e=!0){let t=(0,r.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:s,register:a}=t,l=(0,r.useId)();(0,r.useEffect)(()=>{e&&a(l)},[e]);let u=(0,r.useCallback)(()=>e&&s&&s(l),[l,s,e]);return!n&&s?[!1,u]:[!0]}},88920:(e,t,n)=>{n.d(t,{N:()=>g});var r=n(60687),i=n(43210),o=n(12157),s=n(72789),a=n(21279),l=n(32582);class u extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function c({children:e,isPresent:t}){let n=(0,i.useId)(),o=(0,i.useRef)(null),s=(0,i.useRef)({width:0,height:0,top:0,left:0}),{nonce:a}=(0,i.useContext)(l.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:r,top:i,left:l}=s.current;if(t||!o.current||!e||!r)return;o.current.dataset.motionPopId=n;let u=document.createElement("style");return a&&(u.nonce=a),document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            top: ${i}px !important;
            left: ${l}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),(0,r.jsx)(u,{isPresent:t,childRef:o,sizeRef:s,children:i.cloneElement(e,{ref:o})})}let h=({children:e,initial:t,isPresent:n,onExitComplete:o,custom:l,presenceAffectsLayout:u,mode:h})=>{let f=(0,s.M)(d),p=(0,i.useId)(),m=(0,i.useCallback)(e=>{for(let t of(f.set(e,!0),f.values()))if(!t)return;o&&o()},[f,o]),v=(0,i.useMemo)(()=>({id:p,initial:t,isPresent:n,custom:l,onExitComplete:m,register:e=>(f.set(e,!1),()=>f.delete(e))}),u?[Math.random(),m]:[n,m]);return(0,i.useMemo)(()=>{f.forEach((e,t)=>f.set(t,!1))},[n]),i.useEffect(()=>{n||f.size||!o||o()},[n]),"popLayout"===h&&(e=(0,r.jsx)(c,{isPresent:n,children:e})),(0,r.jsx)(a.t.Provider,{value:v,children:e})};function d(){return new Map}var f=n(86044);let p=e=>e.key||"";function m(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}var v=n(15124);let g=({children:e,custom:t,initial:n=!0,onExitComplete:a,presenceAffectsLayout:l=!0,mode:u="sync",propagate:c=!1})=>{let[d,g]=(0,f.xQ)(c),y=(0,i.useMemo)(()=>m(e),[e]),x=c&&!d?[]:y.map(p),w=(0,i.useRef)(!0),b=(0,i.useRef)(y),P=(0,s.M)(()=>new Map),[T,A]=(0,i.useState)(y),[E,S]=(0,i.useState)(y);(0,v.E)(()=>{w.current=!1,b.current=y;for(let e=0;e<E.length;e++){let t=p(E[e]);x.includes(t)?P.delete(t):!0!==P.get(t)&&P.set(t,!1)}},[E,x.length,x.join("-")]);let C=[];if(y!==T){let e=[...y];for(let t=0;t<E.length;t++){let n=E[t],r=p(n);x.includes(r)||(e.splice(t,0,n),C.push(n))}"wait"===u&&C.length&&(e=C),S(m(e)),A(y);return}let{forceRender:M}=(0,i.useContext)(o.L);return(0,r.jsx)(r.Fragment,{children:E.map(e=>{let i=p(e),o=(!c||!!d)&&(y===E||x.includes(i));return(0,r.jsx)(h,{isPresent:o,initial:(!w.current||!!n)&&void 0,custom:o?void 0:t,presenceAffectsLayout:l,mode:u,onExitComplete:o?void 0:()=>{if(!P.has(i))return;P.set(i,!0);let e=!0;P.forEach(t=>{t||(e=!1)}),e&&(null==M||M(),S(b.current),c&&(null==g||g()),a&&a())},children:e},i)})})}},92363:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},96474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(62688).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},97905:(e,t,n)=>{let r;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}n.d(t,{P:()=>om});let o=e=>Array.isArray(e);function s(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function a(e){return"string"==typeof e||Array.isArray(e)}function l(e){let t=[{},{}];return null==e||e.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function u(e,t,n,r){if("function"==typeof t){let[i,o]=l(r);t=t(void 0!==n?n:e.custom,i,o)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,o]=l(r);t=t(void 0!==n?n:e.custom,i,o)}return t}function c(e,t,n){let r=e.getProps();return u(r,t,void 0!==n?n:r.custom,e)}let h=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],d=["initial",...h];function f(e){let t;return()=>(void 0===t&&(t=e()),t)}let p=f(()=>void 0!==window.ScrollTimeline);class m{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>"finished"in e?e.finished:e))}getAll(e){return this.animations[0][e]}setAll(e,t){for(let n=0;n<this.animations.length;n++)this.animations[n][e]=t}attachTimeline(e,t){let n=this.animations.map(n=>p()&&n.attachTimeline?n.attachTimeline(e):"function"==typeof t?t(n):void 0);return()=>{n.forEach((e,t)=>{e&&e(),this.animations[t].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach(t=>t[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class v extends m{then(e,t){return Promise.all(this.animations).then(e).catch(t)}}function g(e,t){return e?e[t]||e.default||e:void 0}function y(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function x(e){return"function"==typeof e}function w(e,t){e.timeline=t,e.onfinish=null}let b=e=>Array.isArray(e)&&"number"==typeof e[0],P={linearEasing:void 0},T=function(e,t){let n=f(e);return()=>{var e;return null!==(e=P[t])&&void 0!==e?e:n()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),A=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r},E=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=e(A(0,i-1,t))+", ";return`linear(${r.substring(0,r.length-2)})`},S=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,C={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:S([0,.65,.55,1]),circOut:S([.55,0,1,.45]),backIn:S([.31,.01,.66,-.59]),backOut:S([.33,1.53,.69,.99])},M={x:!1,y:!1};function R(e,t){let n=function(e,t,n){var r;if(e instanceof Element)return[e];if("string"==typeof e){let t=document,n=(r=void 0,t.querySelectorAll(e));return n?Array.from(n):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function k(e){return t=>{"touch"===t.pointerType||M.x||M.y||e(t)}}let D=(e,t)=>!!t&&(e===t||D(e,t.parentElement)),L=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary,j=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),V=new WeakSet;function F(e){return t=>{"Enter"===t.key&&e(t)}}function O(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let B=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=F(()=>{if(V.has(n))return;O(n,"down");let e=F(()=>{O(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>O(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function I(e){return L(e)&&!(M.x||M.y)}let N=e=>1e3*e,U=e=>e/1e3,W=e=>e,$=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],_=new Set($),z=new Set(["width","height","top","left","right","bottom",...$]),H=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),K=e=>o(e)?e[e.length-1]||0:e,Y={skipAnimations:!1,useManualTiming:!1},X=["read","resolveKeyframes","update","preRender","render","postRender"];function G(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,s=X.reduce((e,t)=>(e[t]=function(e){let t=new Set,n=new Set,r=!1,i=!1,o=new WeakSet,s={delta:0,timestamp:0,isProcessing:!1};function a(t){o.has(t)&&(l.schedule(t),e()),t(s)}let l={schedule:(e,i=!1,s=!1)=>{let a=s&&r?t:n;return i&&o.add(e),a.has(e)||a.add(e),e},cancel:e=>{n.delete(e),o.delete(e)},process:e=>{if(s=e,r){i=!0;return}r=!0,[t,n]=[n,t],t.forEach(a),t.clear(),r=!1,i&&(i=!1,l.process(e))}};return l}(o),e),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:h,postRender:d}=s,f=()=>{let o=Y.useManualTiming?i.timestamp:performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(o-i.timestamp,40),1),i.timestamp=o,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),c.process(i),h.process(i),d.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(f))},p=()=>{n=!0,r=!0,i.isProcessing||e(f)};return{schedule:X.reduce((e,t)=>{let r=s[t];return e[t]=(e,t=!1,i=!1)=>(n||p(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<X.length;t++)s[X[t]].cancel(e)},state:i,steps:s}}let{schedule:q,cancel:Z,state:Q,steps:J}=G("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:W,!0);function ee(){r=void 0}let et={now:()=>(void 0===r&&et.set(Q.isProcessing||Y.useManualTiming?Q.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(ee)}};function en(e,t){-1===e.indexOf(t)&&e.push(t)}function er(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class ei{constructor(){this.subscriptions=[]}add(e){return en(this.subscriptions,e),()=>er(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r){if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let eo=e=>!isNaN(parseFloat(e)),es={current:void 0};class ea{constructor(e,t={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=et.now();this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=et.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=eo(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new ei);let n=this.events[e].add(t);return"change"===e?()=>{n(),q.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return es.current&&es.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=et.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function el(e,t){return new ea(e,t)}let eu=e=>!!(e&&e.getVelocity);function ec(e,t){let n=e.getValue("willChange");if(eu(n)&&n.add)return n.add(t)}let eh=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),ed="data-"+eh("framerAppearId"),ef={current:!1},ep=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function em(e,t,n,r){if(e===t&&n===r)return W;let i=t=>(function(e,t,n,r,i){let o,s;let a=0;do(o=ep(s=t+(n-t)/2,r,i)-e)>0?n=s:t=s;while(Math.abs(o)>1e-7&&++a<12);return s})(t,0,1,e,n);return e=>0===e||1===e?e:ep(i(e),t,r)}let ev=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,eg=e=>t=>1-e(1-t),ey=em(.33,1.53,.69,.99),ex=eg(ey),ew=ev(ex),eb=e=>(e*=2)<1?.5*ex(e):.5*(2-Math.pow(2,-10*(e-1))),eP=e=>1-Math.sin(Math.acos(e)),eT=eg(eP),eA=ev(eP),eE=e=>/^0[^.\s]+$/u.test(e),eS=(e,t,n)=>n>t?t:n<e?e:n,eC={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},eM={...eC,transform:e=>eS(0,1,e)},eR={...eC,default:1},ek=e=>Math.round(1e5*e)/1e5,eD=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,eL=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ej=(e,t)=>n=>!!("string"==typeof n&&eL.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),eV=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,o,s,a]=r.match(eD);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},eF=e=>eS(0,255,e),eO={...eC,transform:e=>Math.round(eF(e))},eB={test:ej("rgb","red"),parse:eV("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+eO.transform(e)+", "+eO.transform(t)+", "+eO.transform(n)+", "+ek(eM.transform(r))+")"},eI={test:ej("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:eB.transform},eN=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eU=eN("deg"),eW=eN("%"),e$=eN("px"),e_=eN("vh"),ez=eN("vw"),eH={...eW,parse:e=>eW.parse(e)/100,transform:e=>eW.transform(100*e)},eK={test:ej("hsl","hue"),parse:eV("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+eW.transform(ek(t))+", "+eW.transform(ek(n))+", "+ek(eM.transform(r))+")"},eY={test:e=>eB.test(e)||eI.test(e)||eK.test(e),parse:e=>eB.test(e)?eB.parse(e):eK.test(e)?eK.parse(e):eI.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eB.transform(e):eK.transform(e)},eX=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eG="number",eq="color",eZ=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eQ(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],o=0,s=t.replace(eZ,e=>(eY.test(e)?(r.color.push(o),i.push(eq),n.push(eY.parse(e))):e.startsWith("var(")?(r.var.push(o),i.push("var"),n.push(e)):(r.number.push(o),i.push(eG),n.push(parseFloat(e))),++o,"${}")).split("${}");return{values:n,split:s,indexes:r,types:i}}function eJ(e){return eQ(e).values}function e0(e){let{split:t,types:n}=eQ(e),r=t.length;return e=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],void 0!==e[o]){let t=n[o];t===eG?i+=ek(e[o]):t===eq?i+=eY.transform(e[o]):i+=e[o]}return i}}let e1=e=>"number"==typeof e?0:e,e2={test:function(e){var t,n;return isNaN(e)&&"string"==typeof e&&((null===(t=e.match(eD))||void 0===t?void 0:t.length)||0)+((null===(n=e.match(eX))||void 0===n?void 0:n.length)||0)>0},parse:eJ,createTransformer:e0,getAnimatableNone:function(e){let t=eJ(e);return e0(e)(t.map(e1))}},e5=new Set(["brightness","contrast","saturate","opacity"]);function e3(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(eD)||[];if(!r)return e;let i=n.replace(r,""),o=+!!e5.has(t);return r!==n&&(o*=100),t+"("+o+i+")"}let e6=/\b([a-z-]*)\(.*?\)/gu,e4={...e2,getAnimatableNone:e=>{let t=e.match(e6);return t?t.map(e3).join(" "):e}},e9={...eC,transform:Math.round},e8={borderWidth:e$,borderTopWidth:e$,borderRightWidth:e$,borderBottomWidth:e$,borderLeftWidth:e$,borderRadius:e$,radius:e$,borderTopLeftRadius:e$,borderTopRightRadius:e$,borderBottomRightRadius:e$,borderBottomLeftRadius:e$,width:e$,maxWidth:e$,height:e$,maxHeight:e$,top:e$,right:e$,bottom:e$,left:e$,padding:e$,paddingTop:e$,paddingRight:e$,paddingBottom:e$,paddingLeft:e$,margin:e$,marginTop:e$,marginRight:e$,marginBottom:e$,marginLeft:e$,backgroundPositionX:e$,backgroundPositionY:e$,rotate:eU,rotateX:eU,rotateY:eU,rotateZ:eU,scale:eR,scaleX:eR,scaleY:eR,scaleZ:eR,skew:eU,skewX:eU,skewY:eU,distance:e$,translateX:e$,translateY:e$,translateZ:e$,x:e$,y:e$,z:e$,perspective:e$,transformPerspective:e$,opacity:eM,originX:eH,originY:eH,originZ:e$,zIndex:e9,size:e$,fillOpacity:eM,strokeOpacity:eM,numOctaves:e9},e7={...e8,color:eY,backgroundColor:eY,outlineColor:eY,fill:eY,stroke:eY,borderColor:eY,borderTopColor:eY,borderRightColor:eY,borderBottomColor:eY,borderLeftColor:eY,filter:e4,WebkitFilter:e4},te=e=>e7[e];function tt(e,t){let n=te(e);return n!==e4&&(n=e2),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let tn=new Set(["auto","none","0"]),tr=e=>e===eC||e===e$,ti=(e,t)=>parseFloat(e.split(", ")[t]),to=(e,t)=>(n,{transform:r})=>{if("none"===r||!r)return 0;let i=r.match(/^matrix3d\((.+)\)$/u);if(i)return ti(i[1],t);{let t=r.match(/^matrix\((.+)\)$/u);return t?ti(t[1],e):0}},ts=new Set(["x","y","z"]),ta=$.filter(e=>!ts.has(e)),tl={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:to(4,13),y:to(5,14)};tl.translateX=tl.x,tl.translateY=tl.y;let tu=new Set,tc=!1,th=!1;function td(){if(th){let e=Array.from(tu).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return ta.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{var r;null===(r=e.getValue(t))||void 0===r||r.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}th=!1,tc=!1,tu.forEach(e=>e.complete()),tu.clear()}function tf(){tu.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(th=!0)})}class tp{constructor(e,t,n,r,i,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(tu.add(this),tc||(tc=!0,q.read(tf),q.resolveKeyframes(td))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;for(let i=0;i<e.length;i++)if(null===e[i]){if(0===i){let i=null==r?void 0:r.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,o);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=o),r&&void 0===i&&r.set(e[0])}else e[i]=e[i-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),tu.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,tu.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let tm=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),tv=e=>t=>"string"==typeof t&&t.startsWith(e),tg=tv("--"),ty=tv("var(--"),tx=e=>!!ty(e)&&tw.test(e.split("/*")[0].trim()),tw=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,tb=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tP=e=>t=>t.test(e),tT=[eC,e$,eW,eU,ez,e_,{test:e=>"auto"===e,parse:e=>e}],tA=e=>tT.find(tP(e));class tE extends tp{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&tx(r=r.trim())){let i=function e(t,n,r=1){W(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,o]=function(e){let t=tb.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${null!=n?n:r}`,i]}(t);if(!i)return;let s=window.getComputedStyle(n).getPropertyValue(i);if(s){let e=s.trim();return tm(e)?parseFloat(e):e}return tx(o)?e(o,n,r+1):o}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!z.has(n)||2!==e.length)return;let[r,i]=e,o=tA(r),s=tA(i);if(o!==s){if(tr(o)&&tr(s))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||eE(r))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!tn.has(t)&&eQ(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=tt(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tl[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){var e;let{element:t,name:n,unresolvedKeyframes:r}=this;if(!t||!t.current)return;let i=t.getValue(n);i&&i.jump(this.measuredOrigin,!1);let o=r.length-1,s=r[o];r[o]=tl[n](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),(null===(e=this.removedTransforms)||void 0===e?void 0:e.length)&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}let tS=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(e2.test(e)||"0"===e)&&!e.startsWith("url(")),tC=e=>null!==e;function tM(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(tC),o=t&&"loop"!==n&&t%2==1?0:i.length-1;return o&&void 0!==r?r:i[o]}class tR{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:o="loop",...s}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=et.now(),this.options={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:o,...s},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(tf(),td()),this._resolved}onKeyframesResolved(e,t){this.resolvedAt=et.now(),this.hasAttemptedResolve=!0;let{name:n,type:r,velocity:i,delay:o,onComplete:s,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],s=tS(i,t),a=tS(o,t);return W(s===a,`You are trying to animate ${t} from "${i}" to "${o}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${o} via the \`style\` property.`),!!s&&!!a&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||x(n))&&r)}(e,n,r,i)){if(ef.current||!o){a&&a(tM(e,this.options,t)),s&&s(),this.resolveFinishedPromise();return}this.options.duration=0}let u=this.initPlayback(e,t);!1!==u&&(this._resolved={keyframes:e,finalKeyframe:t,...u},this.onPostResolved())}onPostResolved(){}then(e,t){return this.currentFinishedPromise.then(e,t)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}let tk=(e,t,n)=>e+(t-e)*n;function tD(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function tL(e,t){return n=>n>0?t:e}let tj=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},tV=[eI,eB,eK],tF=e=>tV.find(t=>t.test(e));function tO(e){let t=tF(e);if(W(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===eK&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,o=0,s=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,a=2*n-r;i=tD(a,r,e+1/3),o=tD(a,r,e),s=tD(a,r,e-1/3)}else i=o=s=n;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*s),alpha:r}}(n)),n}let tB=(e,t)=>{let n=tO(e),r=tO(t);if(!n||!r)return tL(e,t);let i={...n};return e=>(i.red=tj(n.red,r.red,e),i.green=tj(n.green,r.green,e),i.blue=tj(n.blue,r.blue,e),i.alpha=tk(n.alpha,r.alpha,e),eB.transform(i))},tI=(e,t)=>n=>t(e(n)),tN=(...e)=>e.reduce(tI),tU=new Set(["none","hidden"]);function tW(e,t){return n=>tk(e,t,n)}function t$(e){return"number"==typeof e?tW:"string"==typeof e?tx(e)?tL:eY.test(e)?tB:tH:Array.isArray(e)?t_:"object"==typeof e?eY.test(e)?tB:tz:tL}function t_(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>t$(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function tz(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=t$(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let tH=(e,t)=>{let n=e2.createTransformer(t),r=eQ(e),i=eQ(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?tU.has(e)&&!i.values.length||tU.has(t)&&!r.values.length?function(e,t){return tU.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):tN(t_(function(e,t){var n;let r=[],i={color:0,var:0,number:0};for(let o=0;o<t.values.length;o++){let s=t.types[o],a=e.indexes[s][i[s]],l=null!==(n=e.values[a])&&void 0!==n?n:0;r[o]=l,i[s]++}return r}(r,i),i.values),n):(W(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tL(e,t))};function tK(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?tk(e,t,n):t$(e)(e,t)}function tY(e,t,n){var r,i;let o=Math.max(t-5,0);return r=n-e(o),(i=t-o)?1e3/i*r:0}let tX={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tG(e,t){return e*Math.sqrt(1-t*t)}let tq=["duration","bounce"],tZ=["stiffness","damping","mass"];function tQ(e,t){return t.some(t=>void 0!==e[t])}function tJ(e=tX.visualDuration,t=tX.bounce){let n;let r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:o}=r,s=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:h,duration:d,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:tX.velocity,stiffness:tX.stiffness,damping:tX.damping,mass:tX.mass,isResolvedFromDuration:!1,...e};if(!tQ(e,tZ)&&tQ(e,tq)){if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*eS(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:tX.mass,stiffness:r,damping:i}}else{let n=function({duration:e=tX.duration,bounce:t=tX.bounce,velocity:n=tX.velocity,mass:r=tX.mass}){let i,o;W(e<=N(tX.maxDuration),"Spring duration must be 10 seconds or less");let s=1-t;s=eS(tX.minDamping,tX.maxDamping,s),e=eS(tX.minDuration,tX.maxDuration,U(e)),s<1?(i=t=>{let r=t*s,i=r*e;return .001-(r-n)/tG(t,s)*Math.exp(-i)},o=t=>{let r=t*s*e,o=Math.pow(s,2)*Math.pow(t,2)*e,a=Math.exp(-r),l=tG(Math.pow(t,2),s);return(r*n+n-o)*a*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),o=t=>e*e*(n-t)*Math.exp(-t*e));let a=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,o,5/e);if(e=N(e),isNaN(a))return{stiffness:tX.stiffness,damping:tX.damping,duration:e};{let t=Math.pow(a,2)*r;return{stiffness:t,damping:2*s*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:tX.mass}).isResolvedFromDuration=!0}}return t}({...r,velocity:-U(r.velocity||0)}),m=f||0,v=c/(2*Math.sqrt(u*h)),g=a-s,x=U(Math.sqrt(u/h)),w=5>Math.abs(g);if(i||(i=w?tX.restSpeed.granular:tX.restSpeed.default),o||(o=w?tX.restDelta.granular:tX.restDelta.default),v<1){let e=tG(x,v);n=t=>a-Math.exp(-v*x*t)*((m+v*x*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}else if(1===v)n=e=>a-Math.exp(-x*e)*(g+(m+x*g)*e);else{let e=x*Math.sqrt(v*v-1);n=t=>{let n=Math.exp(-v*x*t),r=Math.min(e*t,300);return a-n*((m+v*x*g)*Math.sinh(r)+e*g*Math.cosh(r))/e}}let b={calculatedDuration:p&&d||null,next:e=>{let t=n(e);if(p)l.done=e>=d;else{let r=0;v<1&&(r=0===e?N(m):tY(n,e,t));let s=Math.abs(a-t)<=o;l.done=Math.abs(r)<=i&&s}return l.value=l.done?a:t,l},toString:()=>{let e=Math.min(y(b),2e4),t=E(t=>b.next(e*t).value,e,30);return e+"ms "+t}};return b}function t0({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){let h,d;let f=e[0],p={done:!1,value:f},m=e=>void 0!==a&&e<a||void 0!==l&&e>l,v=e=>void 0===a?l:void 0===l?a:Math.abs(a-e)<Math.abs(l-e)?a:l,g=n*t,y=f+g,x=void 0===s?y:s(y);x!==y&&(g=x-f);let w=e=>-g*Math.exp(-e/r),b=e=>x+w(e),P=e=>{let t=w(e),n=b(e);p.done=Math.abs(t)<=u,p.value=p.done?x:n},T=e=>{m(p.value)&&(h=e,d=tJ({keyframes:[p.value,v(p.value)],velocity:tY(b,e,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return T(0),{calculatedDuration:null,next:e=>{let t=!1;return(d||void 0!==h||(t=!0,P(e),T(e)),void 0!==h&&e>=h)?d.next(e-h):(t||P(e),p)}}}let t1=em(.42,0,1,1),t2=em(0,0,.58,1),t5=em(.42,0,.58,1),t3=e=>Array.isArray(e)&&"number"!=typeof e[0],t6={linear:W,easeIn:t1,easeInOut:t5,easeOut:t2,circIn:eP,circInOut:eA,circOut:eT,backIn:ex,backInOut:ew,backOut:ey,anticipate:eb},t4=e=>{if(b(e)){W(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,n,r,i]=e;return em(t,n,r,i)}return"string"==typeof e?(W(void 0!==t6[e],`Invalid easing type '${e}'`),t6[e]):e};function t9({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){let i=t3(r)?r.map(t4):t4(r),o={done:!1,value:t[0]},s=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let o=e.length;if(W(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let s=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,n){let r=[],i=n||tK,o=e.length-1;for(let n=0;n<o;n++){let o=i(e[n],e[n+1]);t&&(o=tN(Array.isArray(t)?t[n]||W:t,o)),r.push(o)}return r}(t,r,i),l=a.length,u=n=>{if(s&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=A(e[r],e[r+1],n);return a[r](i)};return n?t=>u(eS(e[0],e[o-1],t)):u}((n&&n.length===t.length?n:function(e){let t=[0];return function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=A(0,t,r);e.push(tk(n,1,i))}}(t,e.length-1),t}(t)).map(t=>t*e),t,{ease:Array.isArray(i)?i:t.map(()=>i||t5).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(o.value=s(t),o.done=t>=e,o)}}let t8=e=>{let t=({timestamp:t})=>e(t);return{start:()=>q.update(t,!0),stop:()=>Z(t),now:()=>Q.isProcessing?Q.timestamp:et.now()}},t7={decay:t0,inertia:t0,tween:t9,keyframes:t9,spring:tJ},ne=e=>e/100;class nt extends tR{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:e}=this.options;e&&e()};let{name:t,motionValue:n,element:r,keyframes:i}=this.options,o=(null==r?void 0:r.KeyframeResolver)||tp;this.resolver=new o(i,(e,t)=>this.onKeyframesResolved(e,t),t,n,r),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){let t,n;let{type:r="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:s,velocity:a=0}=this.options,l=x(r)?r:t7[r]||t9;l!==t9&&"number"!=typeof e[0]&&(t=tN(ne,tK(e[0],e[1])),e=[0,100]);let u=l({...this.options,keyframes:e});"mirror"===s&&(n=l({...this.options,keyframes:[...e].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=y(u));let{calculatedDuration:c}=u,h=c+o;return{generator:u,mirroredGenerator:n,mapPercentToKeyframes:t,calculatedDuration:c,resolvedDuration:h,totalDuration:h*(i+1)-o}}onPostResolved(){let{autoplay:e=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&e?this.state=this.pendingPlayState:this.pause()}tick(e,t=!1){let{resolved:n}=this;if(!n){let{keyframes:e}=this.options;return{done:!0,value:e[e.length-1]}}let{finalKeyframe:r,generator:i,mirroredGenerator:o,mapPercentToKeyframes:s,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=n;if(null===this.startTime)return i.next(0);let{delay:h,repeat:d,repeatType:f,repeatDelay:p,onUpdate:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-u/this.speed,this.startTime)),t?this.currentTime=e:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;let v=this.currentTime-h*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=i;if(d){let e=Math.min(this.currentTime,u)/c,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,d+1))%2&&("reverse"===f?(n=1-n,p&&(n-=p/c)):"mirror"===f&&(x=o)),y=eS(0,1,n)*c}let w=g?{done:!1,value:a[0]}:x.next(y);s&&(w.value=s(w.value));let{done:b}=w;g||null===l||(b=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return P&&void 0!==r&&(w.value=tM(a,this.options,r)),m&&m(w.value),P&&this.finish(),w}get duration(){let{resolved:e}=this;return e?U(e.calculatedDuration):0}get time(){return U(this.currentTime)}set time(e){e=N(e),this.currentTime=e,null!==this.holdTime||0===this.speed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=U(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:e=t8,onPlay:t,startTime:n}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),t&&t();let r=this.driver.now();null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=r):this.startTime=null!=n?n:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(e=this.currentTime)&&void 0!==e?e:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:e}=this.options;e&&e()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}let nn=new Set(["opacity","clipPath","filter","transform"]),nr=f(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ni={anticipate:eb,backInOut:ew,circInOut:eA};class no extends tR{constructor(e){super(e);let{name:t,motionValue:n,element:r,keyframes:i}=this.options;this.resolver=new tE(i,(e,t)=>this.onKeyframesResolved(e,t),t,n,r),this.resolver.scheduleResolve()}initPlayback(e,t){var n;let{duration:r=300,times:i,ease:o,type:s,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof o&&T()&&o in ni&&(o=ni[o]),x((n=this.options).type)||"spring"===n.type||!function e(t){return!!("function"==typeof t&&T()||!t||"string"==typeof t&&(t in C||T())||b(t)||Array.isArray(t)&&t.every(e))}(n.ease)){let{onComplete:t,onUpdate:n,motionValue:a,element:l,...u}=this.options,c=function(e,t){let n=new nt({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0}),r={done:!1,value:e[0]},i=[],o=0;for(;!r.done&&o<2e4;)i.push((r=n.sample(o)).value),o+=10;return{times:void 0,keyframes:i,duration:o-10,ease:"linear"}}(e,u);1===(e=c.keyframes).length&&(e[1]=e[0]),r=c.duration,i=c.times,o=c.ease,s="keyframes"}let c=function(e,t,n,{delay:r=0,duration:i=300,repeat:o=0,repeatType:s="loop",ease:a="easeInOut",times:l}={}){let u={[t]:n};l&&(u.offset=l);let c=function e(t,n){if(t)return"function"==typeof t&&T()?E(t,n):b(t)?S(t):Array.isArray(t)?t.map(t=>e(t,n)||C.easeOut):C[t]}(a,i);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"})}(a.owner.current,l,e,{...this.options,duration:r,times:i,ease:o});return c.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(w(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{let{onComplete:n}=this.options;a.set(tM(e,this.options,t)),n&&n(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:i,type:s,ease:o,keyframes:e}}get duration(){let{resolved:e}=this;if(!e)return 0;let{duration:t}=e;return U(t)}get time(){let{resolved:e}=this;if(!e)return 0;let{animation:t}=e;return U(t.currentTime||0)}set time(e){let{resolved:t}=this;if(!t)return;let{animation:n}=t;n.currentTime=N(e)}get speed(){let{resolved:e}=this;if(!e)return 1;let{animation:t}=e;return t.playbackRate}set speed(e){let{resolved:t}=this;if(!t)return;let{animation:n}=t;n.playbackRate=e}get state(){let{resolved:e}=this;if(!e)return"idle";let{animation:t}=e;return t.playState}get startTime(){let{resolved:e}=this;if(!e)return null;let{animation:t}=e;return t.startTime}attachTimeline(e){if(this._resolved){let{resolved:t}=this;if(!t)return W;let{animation:n}=t;w(n,e)}else this.pendingTimeline=e;return W}play(){if(this.isStopped)return;let{resolved:e}=this;if(!e)return;let{animation:t}=e;"finished"===t.playState&&this.updateFinishedPromise(),t.play()}pause(){let{resolved:e}=this;if(!e)return;let{animation:t}=e;t.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:e}=this;if(!e)return;let{animation:t,keyframes:n,duration:r,type:i,ease:o,times:s}=e;if("idle"===t.playState||"finished"===t.playState)return;if(this.time){let{motionValue:e,onUpdate:t,onComplete:a,element:l,...u}=this.options,c=new nt({...u,keyframes:n,duration:r,type:i,ease:o,times:s,isGenerator:!0}),h=N(this.time);e.setWithVelocity(c.sample(h-10).value,c.sample(h).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:e}=this;e&&e.animation.finish()}cancel(){let{resolved:e}=this;e&&e.animation.cancel()}static supports(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:o,type:s}=e;if(!t||!t.owner||!(t.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=t.owner.getProps();return nr()&&n&&nn.has(n)&&!a&&!l&&!r&&"mirror"!==i&&0!==o&&"inertia"!==s}}let ns={type:"spring",stiffness:500,damping:25,restSpeed:10},na=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),nl={type:"keyframes",duration:.8},nu={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},nc=(e,{keyframes:t})=>t.length>2?nl:_.has(e)?e.startsWith("scale")?na(t[1]):ns:nu,nh=(e,t,n,r={},i,o)=>s=>{let a=g(r,e)||{},l=a.delay||r.delay||0,{elapsed:u=0}=r;u-=N(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:e=>{t.set(e),a.onUpdate&&a.onUpdate(e)},onComplete:()=>{s(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(a)&&(c={...c,...nc(e,c)}),c.duration&&(c.duration=N(c.duration)),c.repeatDelay&&(c.repeatDelay=N(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let h=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0!==c.delay||(h=!0)),(ef.current||Y.skipAnimations)&&(h=!0,c.duration=0,c.delay=0),h&&!o&&void 0!==t.get()){let e=tM(c.keyframes,a);if(void 0!==e)return q.update(()=>{c.onUpdate(e),c.onComplete()}),new v([])}return!o&&no.supports(c)?new no(c):new nt(c)};function nd(e,t,{delay:n=0,transitionOverride:r,type:i}={}){var o;let{transition:s=e.getDefaultTransition(),transitionEnd:a,...l}=t;r&&(s=r);let u=[],h=i&&e.animationState&&e.animationState.getState()[i];for(let t in l){let r=e.getValue(t,null!==(o=e.latestValues[t])&&void 0!==o?o:null),i=l[t];if(void 0===i||h&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(h,t))continue;let a={delay:n,...g(s||{},t)},c=!1;if(window.MotionHandoffAnimation){let n=e.props[ed];if(n){let e=window.MotionHandoffAnimation(n,t,q);null!==e&&(a.startTime=e,c=!0)}}ec(e,t),r.start(nh(t,r,i,e.shouldReduceMotion&&z.has(t)?{type:!1}:a,e,c));let d=r.animation;d&&u.push(d)}return a&&Promise.all(u).then(()=>{q.update(()=>{a&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=c(e,t)||{};for(let t in i={...i,...n}){let n=K(i[t]);e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,el(n))}}(e,a)})}),u}function nf(e,t,n={}){var r;let i=c(e,t,"exit"===n.type?null===(r=e.presenceContext)||void 0===r?void 0:r.custom:void 0),{transition:o=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(o=n.transitionOverride);let s=i?()=>Promise.all(nd(e,i,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:i=0,staggerChildren:s,staggerDirection:a}=o;return function(e,t,n=0,r=0,i=1,o){let s=[],a=(e.variantChildren.size-1)*r,l=1===i?(e=0)=>e*r:(e=0)=>a-e*r;return Array.from(e.variantChildren).sort(np).forEach((e,r)=>{e.notify("AnimationStart",t),s.push(nf(e,t,{...o,delay:n+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(s)}(e,t,i+r,s,a,n)}:()=>Promise.resolve(),{when:l}=o;if(!l)return Promise.all([s(),a(n.delay)]);{let[e,t]="beforeChildren"===l?[s,a]:[a,s];return e().then(()=>t())}}function np(e,t){return e.sortNodePosition(t)}let nm=d.length,nv=[...h].reverse(),ng=h.length;function ny(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nx(){return{animate:ny(!0),whileInView:ny(),whileHover:ny(),whileTap:ny(),whileDrag:ny(),whileFocus:ny(),exit:ny()}}class nw{constructor(e){this.isMounted=!1,this.node=e}update(){}}class nb extends nw{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>nf(e,t,n)));else if("string"==typeof t)r=nf(e,t,n);else{let i="function"==typeof t?c(e,t,n.custom):t;r=Promise.all(nd(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=nx(),r=!0,l=t=>(n,r)=>{var i;let o=c(e,r,"exit"===t?null===(i=e.presenceContext)||void 0===i?void 0:i.custom:void 0);if(o){let{transition:e,transitionEnd:t,...r}=o;n={...n,...r,...t}}return n};function u(u){let{props:c}=e,h=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<nm;e++){let r=d[e],i=t.props[r];(a(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},f=[],p=new Set,m={},v=1/0;for(let t=0;t<ng;t++){var g,y;let d=nv[t],x=n[d],w=void 0!==c[d]?c[d]:h[d],b=a(w),P=d===u?x.isActive:null;!1===P&&(v=t);let T=w===h[d]&&w!==c[d]&&b;if(T&&r&&e.manuallyAnimateOnMount&&(T=!1),x.protectedKeys={...m},!x.isActive&&null===P||!w&&!x.prevProp||i(w)||"boolean"==typeof w)continue;let A=(g=x.prevProp,"string"==typeof(y=w)?y!==g:!!Array.isArray(y)&&!s(y,g)),E=A||d===u&&x.isActive&&!T&&b||t>v&&b,S=!1,C=Array.isArray(w)?w:[w],M=C.reduce(l(d),{});!1===P&&(M={});let{prevResolvedValues:R={}}=x,k={...R,...M},D=t=>{E=!0,p.has(t)&&(S=!0,p.delete(t)),x.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in k){let t=M[e],n=R[e];if(m.hasOwnProperty(e))continue;let r=!1;(o(t)&&o(n)?s(t,n):t===n)?void 0!==t&&p.has(e)?D(e):x.protectedKeys[e]=!0:null!=t?D(e):p.add(e)}x.prevProp=w,x.prevResolvedValues=M,x.isActive&&(m={...m,...M}),r&&e.blockInitialAnimation&&(E=!1);let L=!(T&&A)||S;E&&L&&f.push(...C.map(e=>({animation:e,options:{type:d}})))}if(p.size){let t={};p.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=null!=r?r:null}),f.push({animation:t})}let x=!!f.length;return r&&(!1===c.initial||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(x=!1),r=!1,x?t(f):Promise.resolve()}return{animateChanges:u,setActive:function(t,r){var i;if(n[t].isActive===r)return Promise.resolve();null===(i=e.variantChildren)||void 0===i||i.forEach(e=>{var n;return null===(n=e.animationState)||void 0===n?void 0:n.setActive(t,r)}),n[t].isActive=r;let o=u(t);for(let e in n)n[e].protectedKeys={};return o},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=nx(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null===(e=this.unmountControls)||void 0===e||e.call(this)}}let nP=0;class nT extends nw{constructor(){super(...arguments),this.id=nP++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}function nA(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function nE(e){return{point:{x:e.pageX,y:e.pageY}}}let nS=e=>t=>L(t)&&e(t,nE(t));function nC(e,t,n,r){return nA(e,t,nS(n),r)}let nM=(e,t)=>Math.abs(e-t);class nR{constructor(e,t,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nL(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(nM(e.x,t.x)**2+nM(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=Q;this.history.push({...r,timestamp:i});let{onStart:o,onMove:s}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=nk(t,this.transformPagePoint),q.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=nL("pointercancel"===e.type?this.lastMoveEventInfo:nk(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},!L(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;let o=nk(nE(e),this.transformPagePoint),{point:s}=o,{timestamp:a}=Q;this.history=[{...s,timestamp:a}];let{onSessionStart:l}=t;l&&l(e,nL(o,this.history)),this.removeListeners=tN(nC(this.contextWindow,"pointermove",this.handlePointerMove),nC(this.contextWindow,"pointerup",this.handlePointerUp),nC(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),Z(this.updatePoint)}}function nk(e,t){return t?{point:t(e.point)}:e}function nD(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nL({point:e},t){return{point:e,delta:nD(e,nj(t)),offset:nD(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=nj(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>N(.1)));)n--;if(!r)return{x:0,y:0};let o=U(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};let s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function nj(e){return e[e.length-1]}function nV(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function nF(e){return e.max-e.min}function nO(e,t,n,r=.5){e.origin=r,e.originPoint=tk(t.min,t.max,e.origin),e.scale=nF(n)/nF(t),e.translate=tk(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nB(e,t,n,r){nO(e.x,t.x,n.x,r?r.originX:void 0),nO(e.y,t.y,n.y,r?r.originY:void 0)}function nI(e,t,n){e.min=n.min+t.min,e.max=e.min+nF(t)}function nN(e,t,n){e.min=t.min-n.min,e.max=e.min+nF(t)}function nU(e,t,n){nN(e.x,t.x,n.x),nN(e.y,t.y,n.y)}function nW(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function n$(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function n_(e,t,n){return{min:nz(e,t),max:nz(e,n)}}function nz(e,t){return"number"==typeof e?e:e[t]||0}let nH=()=>({translate:0,scale:1,origin:0,originPoint:0}),nK=()=>({x:nH(),y:nH()}),nY=()=>({min:0,max:0}),nX=()=>({x:nY(),y:nY()});function nG(e){return[e("x"),e("y")]}function nq({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function nZ(e){return void 0===e||1===e}function nQ({scale:e,scaleX:t,scaleY:n}){return!nZ(e)||!nZ(t)||!nZ(n)}function nJ(e){return nQ(e)||n0(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function n0(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function n1(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function n2(e,t=0,n=1,r,i){e.min=n1(e.min,t,n,r,i),e.max=n1(e.max,t,n,r,i)}function n5(e,{x:t,y:n}){n2(e.x,t.translate,t.scale,t.originPoint),n2(e.y,n.translate,n.scale,n.originPoint)}function n3(e,t){e.min=e.min+t,e.max=e.max+t}function n6(e,t,n,r,i=.5){let o=tk(e.min,e.max,i);n2(e,t,n,o,r)}function n4(e,t){n6(e.x,t.x,t.scaleX,t.scale,t.originX),n6(e.y,t.y,t.scaleY,t.scale,t.originY)}function n9(e,t){return nq(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let n8=({current:e})=>e?e.ownerDocument.defaultView:null,n7=new WeakMap;class re{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nX(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new nR(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(nE(e).point)},onStart:(e,t)=>{var n;let{drag:r,dragPropagation:i,onDragStart:o}=this.getProps();if(r&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(n=r)||"y"===n?M[n]?null:(M[n]=!0,()=>{M[n]=!1}):M.x||M.y?null:(M.x=M.y=!0,()=>{M.x=M.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nG(e=>{let t=this.getAxisMotionValue(e).get()||0;if(eW.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=nF(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),o&&q.postRender(()=>o(e,t)),ec(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:s}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(s),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>nG(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:n8(this.visualElement)})}stop(e,t){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:i}=this.getProps();i&&q.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!rt(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?tk(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?tk(n,e,r.max):Math.min(e,n)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,i=this.constraints;t&&nV(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:nW(e.x,n,i),y:nW(e.y,t,r)}}(r.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:n_(e,"left","right"),y:n_(e,"top","bottom")}}(n),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&nG(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!nV(t))return!1;let r=t.current;W(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,n){let r=n9(e,n),{scroll:i}=t;return i&&(n3(r.x,i.offset.x),n3(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),s={x:n$((e=i.layout.layoutBox).x,o.x),y:n$(e.y,o.y)};if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=nq(e))}return s}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(nG(s=>{if(!rt(s,t,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[s]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return ec(this.visualElement,e),n.start(nh(e,n,0,t,this.visualElement,!1))}stopAnimation(){nG(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nG(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){nG(t=>{let{drag:n}=this.getProps();if(!rt(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-tk(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!nV(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};nG(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=nF(e),i=nF(t);return i>r?n=A(t.min,t.max-r,e.min):r>i&&(n=A(e.min,e.max-i,t.min)),eS(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),nG(t=>{if(!rt(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set(tk(i,o,r[t]))})}addListeners(){if(!this.visualElement.current)return;n7.set(this.visualElement,this);let e=nC(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();nV(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),q.read(t);let i=nA(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(nG(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:s}}}function rt(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class rn extends nw{constructor(e){super(e),this.removeGroupControls=W,this.removeListeners=W,this.controls=new re(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||W}unmount(){this.removeGroupControls(),this.removeListeners()}}let rr=e=>(t,n)=>{e&&q.postRender(()=>e(t,n))};class ri extends nw{constructor(){super(...arguments),this.removePointerDownListener=W}onPointerDown(e){this.session=new nR(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:n8(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:rr(e),onStart:rr(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&q.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=nC(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var ro,rs,ra=n(60687),rl=n(43210),ru=n(86044),rc=n(12157);let rh=(0,rl.createContext)({}),rd={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let rp={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!e$.test(e))return e;e=parseFloat(e)}let n=rf(e,t.target.x),r=rf(e,t.target.y);return`${n}% ${r}%`}},rm={},{schedule:rv,cancel:rg}=G(queueMicrotask,!1);class ry extends rl.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;Object.assign(rm,rw),i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rd.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,o=n.projection;return o&&(o.isPresent=i,r||e.layoutDependency!==t||void 0===t?o.willUpdate():this.safeToRemove(),e.isPresent===i||(i?o.promote():o.relegate()||q.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),rv.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rx(e){let[t,n]=(0,ru.xQ)(),r=(0,rl.useContext)(rc.L);return(0,ra.jsx)(ry,{...e,layoutGroup:r,switchLayoutGroup:(0,rl.useContext)(rh),isPresent:t,safeToRemove:n})}let rw={borderRadius:{...rp,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:rp,borderTopRightRadius:rp,borderBottomLeftRadius:rp,borderBottomRightRadius:rp,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=e2.parse(e);if(r.length>5)return e;let i=e2.createTransformer(e),o=+("number"!=typeof r[0]),s=n.x.scale*t.x,a=n.y.scale*t.y;r[0+o]/=s,r[1+o]/=a;let l=tk(s,a,.5);return"number"==typeof r[2+o]&&(r[2+o]/=l),"number"==typeof r[3+o]&&(r[3+o]/=l),i(r)}}},rb=(e,t)=>e.depth-t.depth;class rP{constructor(){this.children=[],this.isDirty=!1}add(e){en(this.children,e),this.isDirty=!0}remove(e){er(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rb),this.isDirty=!1,this.children.forEach(e)}}function rT(e){let t=eu(e)?e.get():e;return H(t)?t.toValue():t}let rA=["TopLeft","TopRight","BottomLeft","BottomRight"],rE=rA.length,rS=e=>"string"==typeof e?parseFloat(e):e,rC=e=>"number"==typeof e||e$.test(e);function rM(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rR=rD(0,.5,eT),rk=rD(.5,.95,W);function rD(e,t,n){return r=>r<e?0:r>t?1:n(A(e,t,r))}function rL(e,t){e.min=t.min,e.max=t.max}function rj(e,t){rL(e.x,t.x),rL(e.y,t.y)}function rV(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rF(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function rO(e,t,[n,r,i],o,s){!function(e,t=0,n=1,r=.5,i,o=e,s=e){if(eW.test(t)&&(t=parseFloat(t),t=tk(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let a=tk(o.min,o.max,r);e===o&&(a-=t),e.min=rF(e.min,t,n,a,i),e.max=rF(e.max,t,n,a,i)}(e,t[n],t[r],t[i],t.scale,o,s)}let rB=["x","scaleX","originX"],rI=["y","scaleY","originY"];function rN(e,t,n,r){rO(e.x,t,rB,n?n.x:void 0,r?r.x:void 0),rO(e.y,t,rI,n?n.y:void 0,r?r.y:void 0)}function rU(e){return 0===e.translate&&1===e.scale}function rW(e){return rU(e.x)&&rU(e.y)}function r$(e,t){return e.min===t.min&&e.max===t.max}function r_(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rz(e,t){return r_(e.x,t.x)&&r_(e.y,t.y)}function rH(e){return nF(e.x)/nF(e.y)}function rK(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rY{constructor(){this.members=[]}add(e){en(this.members,e),e.scheduleRender()}remove(e){if(er(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rX={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},rG="undefined"!=typeof window&&void 0!==window.MotionDebug,rq=["","X","Y","Z"],rZ={visibility:"hidden"},rQ=0;function rJ(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function r0({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=null==t?void 0:t()){this.id=rQ++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,rG&&(rX.totalNodes=rX.resolvedTargetDeltas=rX.recalculatedProjection=0),this.nodes.forEach(r5),this.nodes.forEach(ie),this.nodes.forEach(it),this.nodes.forEach(r3),rG&&window.MotionDebug.record(rX)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rP)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new ei),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,n=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:r,layout:i,visualElement:o}=this.options;if(o&&!o.current&&o.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(i||r)&&(this.isLayoutDirty=!0),e){let n;let r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=et.now(),r=({timestamp:i})=>{let o=i-n;o>=250&&(Z(r),e(o-t))};return q.read(r,!0),()=>Z(r)}(r,250),rd.hasAnimatedSinceResize&&(rd.hasAnimatedSinceResize=!1,this.nodes.forEach(r7))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&o&&(r||i)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let i=this.options.transition||o.getDefaultTransition()||il,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!rz(this.targetLayout,r)||n,u=!t&&n;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,u);let t={...g(i,"layout"),onPlay:s,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||r7(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Z(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ir),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[ed];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",q,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(r4);return}this.isUpdating||this.nodes.forEach(r9),this.isUpdating=!1,this.nodes.forEach(r8),this.nodes.forEach(r1),this.nodes.forEach(r2),this.clearAllSnapshots();let e=et.now();Q.delta=eS(0,1e3/60,e-Q.timestamp),Q.timestamp=e,Q.isProcessing=!0,J.update.process(Q),J.preRender.process(Q),J.render.process(Q),Q.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,rv.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(r6),this.sharedNodes.forEach(ii)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,q.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){q.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nX(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rW(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;e&&(t||nJ(this.latestValues)||o)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),ih((t=r).x),ih(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){var e;let{visualElement:t}=this.options;if(!t)return nX();let n=t.measureViewportBox();if(!((null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)||this.path.some(ip))){let{scroll:e}=this.root;e&&(n3(n.x,e.offset.x),n3(n.y,e.offset.y))}return n}removeElementScroll(e){var t;let n=nX();if(rj(n,e),null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)return n;for(let t=0;t<this.path.length;t++){let r=this.path[t],{scroll:i,options:o}=r;r!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&rj(n,e),n3(n.x,i.offset.x),n3(n.y,i.offset.y))}return n}applyTransform(e,t=!1){let n=nX();rj(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&n4(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),nJ(r.latestValues)&&n4(n,r.latestValues)}return nJ(this.latestValues)&&n4(n,this.latestValues),n}removeTransform(e){let t=nX();rj(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!nJ(n.latestValues))continue;nQ(n.latestValues)&&n.updateSnapshot();let r=nX();rj(r,n.measurePageBox()),rN(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return nJ(this.latestValues)&&rN(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Q.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,n,r,i;let o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);let s=!!this.resumingFrom||this!==o;if(!(e||s&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=Q.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nX(),this.relativeTargetOrigin=nX(),nU(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rj(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=nX(),this.targetWithTransforms=nX()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),n=this.target,r=this.relativeTarget,i=this.relativeParent.target,nI(n.x,r.x,i.x),nI(n.y,r.y,i.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rj(this.target,this.layout.layoutBox),n5(this.target,this.targetDelta)):rj(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nX(),this.relativeTargetOrigin=nX(),nU(this.relativeTargetOrigin,this.target,e.target),rj(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}rG&&rX.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||nQ(this.parent.latestValues)||n0(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),n=!!this.resumingFrom||this!==t,r=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(r=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===Q.timestamp&&(r=!1),r)return;let{layout:i,layoutId:o}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||o))return;rj(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;(function(e,t,n,r=!1){let i,o;let s=n.length;if(s){t.x=t.y=1;for(let a=0;a<s;a++){o=(i=n[a]).projectionDelta;let{visualElement:s}=i.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&n4(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,n5(e,o)),r&&nJ(i.latestValues)&&n4(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}})(this.layoutCorrected,this.treeScale,this.path,n),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=nX());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rV(this.prevProjectionDelta.x,this.projectionDelta.x),rV(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nB(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&rK(this.projectionDelta.x,this.prevProjectionDelta.x)&&rK(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),rG&&rX.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){var t;if(null===(t=this.options.visualElement)||void 0===t||t.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=nK(),this.projectionDelta=nK(),this.projectionDeltaWithTransform=nK()}setAnimationOrigin(e,t=!1){let n;let r=this.snapshot,i=r?r.latestValues:{},o={...this.latestValues},s=nK();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=nX(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,h=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(ia));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(io(s.x,e.x,r),io(s.y,e.y,r),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,f,p,m,v;if(nU(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=a,v=r,is(f.x,p.x,m.x,v),is(f.y,p.y,m.y,v),n&&(u=this.relativeTarget,d=n,r$(u.x,d.x)&&r$(u.y,d.y)))this.isProjectionDirty=!1;n||(n=nX()),rj(n,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,n,r,i,o){i?(e.opacity=tk(0,void 0!==n.opacity?n.opacity:1,rR(r)),e.opacityExit=tk(void 0!==t.opacity?t.opacity:1,0,rk(r))):o&&(e.opacity=tk(void 0!==t.opacity?t.opacity:1,void 0!==n.opacity?n.opacity:1,r));for(let i=0;i<rE;i++){let o=`border${rA[i]}Radius`,s=rM(t,o),a=rM(n,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||rC(s)===rC(a)?(e[o]=Math.max(tk(rS(s),rS(a),r),0),(eW.test(a)||eW.test(s))&&(e[o]+="%")):e[o]=a)}(t.rotate||n.rotate)&&(e.rotate=tk(t.rotate||0,n.rotate||0,r))}(o,i,this.latestValues,r,h,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Z(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=q.update(()=>{rd.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,n){let r=eu(0)?0:el(e);return r.start(nh("",r,1e3,n)),r.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&id(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||nX();let t=nF(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=nF(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}rj(t,n),n4(t,i),nB(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rY),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&rJ("z",e,r,this.animationValues);for(let t=0;t<rq.length;t++)rJ(`rotate${rq[t]}`,e,r,this.animationValues),rJ(`skew${rq[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){var t,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return rZ;let r={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=rT(null==e?void 0:e.pointerEvents)||"",r.transform=i?i(this.latestValues,""):"none",r;let o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rT(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!nJ(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let s=o.animationValues||o.latestValues;this.applyTransformsToTarget(),r.transform=function(e,t,n){let r="",i=e.x.translate/t.x,o=e.y.translate/t.y,s=(null==n?void 0:n.z)||0;if((i||o||s)&&(r=`translate3d(${i}px, ${o}px, ${s}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:s,skewY:a}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),o&&(r+=`rotateY(${o}deg) `),s&&(r+=`skewX(${s}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),i&&(r.transform=i(s,r.transform));let{x:a,y:l}=this.projectionDelta;for(let e in r.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?r.opacity=o===this?null!==(n=null!==(t=s.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:r.opacity=o===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,rm){if(void 0===s[e])continue;let{correct:t,applyTo:n}=rm[e],i="none"===r.transform?s[e]:t(s[e],o);if(n){let e=n.length;for(let t=0;t<e;t++)r[n[t]]=i}else r[e]=i}return this.options.layoutId&&(r.pointerEvents=o===this?rT(null==e?void 0:e.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(r4),this.root.sharedNodes.clear()}}}function r1(e){e.updateLayout()}function r2(e){var t;let n=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:r}=e.layout,{animationType:i}=e.options,o=n.source!==e.layout.source;"size"===i?nG(e=>{let r=o?n.measuredBox[e]:n.layoutBox[e],i=nF(r);r.min=t[e].min,r.max=r.min+i}):id(i,n.layoutBox,t)&&nG(r=>{let i=o?n.measuredBox[r]:n.layoutBox[r],s=nF(t[r]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+s)});let s=nK();nB(s,t,n.layoutBox);let a=nK();o?nB(a,e.applyTransform(r,!0),n.measuredBox):nB(a,t,n.layoutBox);let l=!rW(s),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:o}=r;if(i&&o){let s=nX();nU(s,n.layoutBox,i.layoutBox);let a=nX();nU(a,t,o.layoutBox),rz(s,a)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=s,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:n,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function r5(e){rG&&rX.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function r3(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function r6(e){e.clearSnapshot()}function r4(e){e.clearMeasurements()}function r9(e){e.isLayoutDirty=!1}function r8(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function r7(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function ie(e){e.resolveTargetDelta()}function it(e){e.calcProjection()}function ir(e){e.resetSkewAndRotation()}function ii(e){e.removeLeadSnapshot()}function io(e,t,n){e.translate=tk(t.translate,0,n),e.scale=tk(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function is(e,t,n,r){e.min=tk(t.min,n.min,r),e.max=tk(t.max,n.max,r)}function ia(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let il={duration:.45,ease:[.4,0,.1,1]},iu=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),ic=iu("applewebkit/")&&!iu("chrome/")?Math.round:W;function ih(e){e.min=ic(e.min),e.max=ic(e.max)}function id(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rH(t)-rH(n)))}function ip(e){var t;return e!==e.root&&(null===(t=e.scroll)||void 0===t?void 0:t.wasRoot)}let im=r0({attachResizeListener:(e,t)=>nA(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),iv={current:void 0},ig=r0({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!iv.current){let e=new im({});e.mount(window),e.setOptions({layoutScroll:!0}),iv.current=e}return iv.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function iy(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&q.postRender(()=>i(t,nE(t)))}class ix extends nw{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=R(e,n),s=k(e=>{let{target:n}=e,r=t(e);if("function"!=typeof r||!n)return;let o=k(e=>{r(e),n.removeEventListener("pointerleave",o)});n.addEventListener("pointerleave",o,i)});return r.forEach(e=>{e.addEventListener("pointerenter",s,i)}),o}(e,e=>(iy(this.node,e,"Start"),e=>iy(this.node,e,"End"))))}unmount(){}}class iw extends nw{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tN(nA(this.node.current,"focus",()=>this.onFocus()),nA(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function ib(e,t,n){let{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&q.postRender(()=>i(t,nE(t)))}class iP extends nw{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,o]=R(e,n),s=e=>{let r=e.currentTarget;if(!I(e)||V.has(r))return;V.add(r);let o=t(e),s=(e,t)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),I(e)&&V.has(r)&&(V.delete(r),"function"==typeof o&&o(e,{success:t}))},a=e=>{s(e,n.useGlobalTarget||D(r,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",a,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{!j.has(e.tagName)&&-1===e.tabIndex&&null===e.getAttribute("tabindex")&&(e.tabIndex=0),(n.useGlobalTarget?window:e).addEventListener("pointerdown",s,i),e.addEventListener("focus",e=>B(e,i),i)}),o}(e,e=>(ib(this.node,e,"Start"),(e,{success:t})=>ib(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let iT=new WeakMap,iA=new WeakMap,iE=e=>{let t=iT.get(e.target);t&&t(e)},iS=e=>{e.forEach(iE)},iC={some:0,all:1};class iM extends nw{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:iC[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;iA.has(n)||iA.set(n,{});let r=iA.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(iS,{root:e,...t})),r[i]}(t);return iT.set(e,n),r.observe(e),()=>{iT.delete(e),r.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let iR=(0,rl.createContext)({strict:!1});var ik=n(32582);let iD=(0,rl.createContext)({});function iL(e){return i(e.animate)||d.some(t=>a(e[t]))}function ij(e){return!!(iL(e)||e.variants)}function iV(e){return Array.isArray(e)?e.join(" "):e}var iF=n(7044);let iO={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iB={};for(let e in iO)iB[e]={isEnabled:t=>iO[e].some(e=>!!t[e])};let iI=Symbol.for("motionComponentSymbol");var iN=n(21279),iU=n(15124);let iW=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i$(e){if("string"!=typeof e||e.includes("-"));else if(iW.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var i_=n(72789);let iz=e=>(t,n)=>{let r=(0,rl.useContext)(iD),o=(0,rl.useContext)(iN.t),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,o,s){let a={latestValues:function(e,t,n,r){let o={},s=r(e,{});for(let e in s)o[e]=rT(s[e]);let{initial:a,animate:l}=e,c=iL(e),h=ij(e);t&&h&&!c&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===l&&(l=t.animate));let d=!!n&&!1===n.initial,f=(d=d||!1===a)?l:a;if(f&&"boolean"!=typeof f&&!i(f)){let t=Array.isArray(f)?f:[f];for(let n=0;n<t.length;n++){let r=u(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=d?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(r,o,s,e),renderState:t()};return n&&(a.onMount=e=>n({props:r,current:e,...a}),a.onUpdate=e=>n(e)),a})(e,t,r,o);return n?s():(0,i_.M)(s)},iH=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iK={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iY=$.length;function iX(e,t,n){let{style:r,vars:i,transformOrigin:o}=e,s=!1,a=!1;for(let e in t){let n=t[e];if(_.has(e)){s=!0;continue}if(tg(e)){i[e]=n;continue}{let t=iH(n,e8[e]);e.startsWith("origin")?(a=!0,o[e]=t):r[e]=t}}if(!t.transform&&(s||n?r.transform=function(e,t,n){let r="",i=!0;for(let o=0;o<iY;o++){let s=$[o],a=e[s];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!s.startsWith("scale"):0===parseFloat(a))||n){let e=iH(a,e8[s]);if(!l){i=!1;let t=iK[s]||s;r+=`${t}(${e}) `}n&&(t[s]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),a){let{originX:e="50%",originY:t="50%",originZ:n=0}=o;r.transformOrigin=`${e} ${t} ${n}`}}let iG={offset:"stroke-dashoffset",array:"stroke-dasharray"},iq={offset:"strokeDashoffset",array:"strokeDasharray"};function iZ(e,t,n){return"string"==typeof e?e:e$.transform(t+n*e)}function iQ(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},c,h){if(iX(e,u,h),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:f,dimensions:p}=e;d.transform&&(p&&(f.transform=d.transform),delete d.transform),p&&(void 0!==i||void 0!==o||f.transform)&&(f.transformOrigin=function(e,t,n){let r=iZ(t,e.x,e.width),i=iZ(n,e.y,e.height);return`${r} ${i}`}(p,void 0!==i?i:.5,void 0!==o?o:.5)),void 0!==t&&(d.x=t),void 0!==n&&(d.y=n),void 0!==r&&(d.scale=r),void 0!==s&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let o=i?iG:iq;e[o.offset]=e$.transform(-r);let s=e$.transform(t),a=e$.transform(n);e[o.array]=`${s} ${a}`}(d,s,a,l,!1)}let iJ=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),i0=()=>({...iJ(),attrs:{}}),i1=e=>"string"==typeof e&&"svg"===e.toLowerCase();function i2(e,{style:t,vars:n},r,i){for(let o in Object.assign(e.style,t,i&&i.getProjectionStyles(r)),n)e.style.setProperty(o,n[o])}let i5=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function i3(e,t,n,r){for(let n in i2(e,t,void 0,r),t.attrs)e.setAttribute(i5.has(n)?n:eh(n),t.attrs[n])}function i6(e,{layout:t,layoutId:n}){return _.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!rm[e]||"opacity"===e)}function i4(e,t,n){var r;let{style:i}=e,o={};for(let s in i)(eu(i[s])||t.style&&eu(t.style[s])||i6(s,e)||(null===(r=null==n?void 0:n.getValue(s))||void 0===r?void 0:r.liveStyle)!==void 0)&&(o[s]=i[s]);return o}function i9(e,t,n){let r=i4(e,t,n);for(let n in e)(eu(e[n])||eu(t[n]))&&(r[-1!==$.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}let i8=["x","y","width","height","cx","cy","r"],i7={useVisualState:iz({scrapeMotionValuesFromProps:i9,createRenderState:i0,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:i})=>{if(!n)return;let o=!!e.drag;if(!o){for(let e in i)if(_.has(e)){o=!0;break}}if(!o)return;let s=!t;if(t)for(let n=0;n<i8.length;n++){let r=i8[n];e[r]!==t[r]&&(s=!0)}s&&q.read(()=>{(function(e,t){try{t.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(e){t.dimensions={x:0,y:0,width:0,height:0}}})(n,r),q.render(()=>{iQ(r,i,i1(n.tagName),e.transformTemplate),i3(n,r)})})}})},oe={useVisualState:iz({scrapeMotionValuesFromProps:i4,createRenderState:iJ})};function ot(e,t,n){for(let r in t)eu(t[r])||i6(r,n)||(e[r]=t[r])}let on=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function or(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||on.has(e)}let oi=e=>!or(e);try{!function(e){e&&(oi=t=>t.startsWith("on")?!or(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}let oo={current:null},os={current:!1},oa=[...tT,eY,e2],ol=e=>oa.find(tP(e)),ou=new WeakMap,oc=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class oh{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tp,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=et.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,q.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=o;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=s,this.blockInitialAnimation=!!i,this.isControllingVariants=iL(t),this.isVariantNode=ij(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:c,...h}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in h){let t=h[e];void 0!==a[e]&&eu(t)&&t.set(a[e],!1)}}mount(e){this.current=e,ou.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),os.current||function(){if(os.current=!0,iF.B){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>oo.current=e.matches;e.addListener(t),t()}else oo.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||oo.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in ou.delete(this.current),this.projection&&this.projection.unmount(),Z(this.notifyUpdate),Z(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=_.has(e),i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&q.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iB){let t=iB[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nX()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<oc.length;t++){let n=oc[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],o=n[r];if(eu(i))e.addValue(r,i);else if(eu(o))e.addValue(r,el(i,{owner:e}));else if(o!==i){if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,el(void 0!==t?t:i,{owner:e}))}}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=el(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){var n;let r=void 0===this.latestValues[e]&&this.current?null!==(n=this.getBaseTargetFromProps(this.props,e))&&void 0!==n?n:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=r&&("string"==typeof r&&(tm(r)||eE(r))?r=parseFloat(r):!ol(r)&&e2.test(t)&&(r=tt(e,t)),this.setBaseTarget(e,eu(r)?r.get():r)),eu(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let n;let{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let i=u(this.props,r,null===(t=this.presenceContext)||void 0===t?void 0:t.custom);i&&(n=i[e])}if(r&&void 0!==n)return n;let i=this.getBaseTargetFromProps(this.props,e);return void 0===i||eu(i)?void 0!==this.initialValues[e]&&void 0===n?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new ei),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class od extends oh{constructor(){super(...arguments),this.KeyframeResolver=tE}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;eu(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}class of extends od{constructor(){super(...arguments),this.type="html",this.renderInstance=i2}readValueFromInstance(e,t){if(_.has(t)){let e=te(t);return e&&e.default||0}{let n=window.getComputedStyle(e),r=(tg(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return n9(e,t)}build(e,t,n){iX(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return i4(e,t,n)}}class op extends od{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nX}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(_.has(t)){let e=te(t);return e&&e.default||0}return t=i5.has(t)?t:eh(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return i9(e,t,n)}build(e,t,n){iQ(e,t,this.isSVGTag,n.transformTemplate)}renderInstance(e,t,n,r){i3(e,t,n,r)}mount(e){this.isSVGTag=i1(e.tagName),super.mount(e)}}let om=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((ro={animation:{Feature:nb},exit:{Feature:nT},inView:{Feature:iM},tap:{Feature:iP},focus:{Feature:iw},hover:{Feature:ix},pan:{Feature:ri},drag:{Feature:rn,ProjectionNode:ig,MeasureLayout:rx},layout:{ProjectionNode:ig,MeasureLayout:rx}},rs=(e,t)=>i$(e)?new op(t):new of(t,{allowProjection:e!==rl.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){var o,s;function l(e,o){var s,l,u;let c;let h={...(0,rl.useContext)(ik.Q),...e,layoutId:function({layoutId:e}){let t=(0,rl.useContext)(rc.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:d}=h,f=function(e){let{initial:t,animate:n}=function(e,t){if(iL(e)){let{initial:t,animate:n}=e;return{initial:!1===t||a(t)?t:void 0,animate:a(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,rl.useContext)(iD));return(0,rl.useMemo)(()=>({initial:t,animate:n}),[iV(t),iV(n)])}(e),p=r(e,d);if(!d&&iF.B){l=0,u=0,(0,rl.useContext)(iR).strict;let e=function(e){let{drag:t,layout:n}=iB;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==n?void 0:n.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(h);c=e.MeasureLayout,f.visualElement=function(e,t,n,r,i){var o,s;let{visualElement:a}=(0,rl.useContext)(iD),l=(0,rl.useContext)(iR),u=(0,rl.useContext)(iN.t),c=(0,rl.useContext)(ik.Q).reducedMotion,h=(0,rl.useRef)(null);r=r||l.renderer,!h.current&&r&&(h.current=r(e,{visualState:t,parent:a,props:n,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:c}));let d=h.current,f=(0,rl.useContext)(rh);d&&!d.projection&&i&&("html"===d.type||"svg"===d.type)&&function(e,t,n,r){let{layoutId:i,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!s||a&&nV(a),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}(h.current,n,i,f);let p=(0,rl.useRef)(!1);(0,rl.useInsertionEffect)(()=>{d&&p.current&&d.update(n,u)});let m=n[ed],v=(0,rl.useRef)(!!m&&!(null===(o=window.MotionHandoffIsComplete)||void 0===o?void 0:o.call(window,m))&&(null===(s=window.MotionHasOptimisedAnimation)||void 0===s?void 0:s.call(window,m)));return(0,iU.E)(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),rv.render(d.render),v.current&&d.animationState&&d.animationState.animateChanges())}),(0,rl.useEffect)(()=>{d&&(!v.current&&d.animationState&&d.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var e;null===(e=window.MotionHandoffMarkAsComplete)||void 0===e||e.call(window,m)}),v.current=!1))}),d}(i,p,h,t,e.ProjectionNode)}return(0,ra.jsxs)(iD.Provider,{value:f,children:[c&&f.visualElement?(0,ra.jsx)(c,{visualElement:f.visualElement,...h}):null,n(i,e,(s=f.visualElement,(0,rl.useCallback)(e=>{e&&p.onMount&&p.onMount(e),s&&(e?s.mount(e):s.unmount()),o&&("function"==typeof o?o(e):nV(o)&&(o.current=e))},[s])),p,d,f.visualElement)]})}e&&function(e){for(let t in e)iB[t]={...iB[t],...e[t]}}(e),l.displayName=`motion.${"string"==typeof i?i:`create(${null!==(s=null!==(o=i.displayName)&&void 0!==o?o:i.name)&&void 0!==s?s:""})`}`;let u=(0,rl.forwardRef)(l);return u[iI]=i,u}({...i$(e)?i7:oe,preloadedFeatures:ro,useRender:function(e=!1){return(t,n,r,{latestValues:i},o)=>{let s=(i$(t)?function(e,t,n,r){let i=(0,rl.useMemo)(()=>{let n=i0();return iQ(n,t,i1(r),e.transformTemplate),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};ot(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return ot(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,rl.useMemo)(()=>{let n=iJ();return iX(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(n,i,o,t),a=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(oi(i)||!0===n&&or(i)||!t&&!or(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l=t!==rl.Fragment?{...a,...s,ref:r}:{},{children:u}=n,c=(0,rl.useMemo)(()=>eu(u)?u.get():u,[u]);return(0,rl.createElement)(t,{...l,children:c})}}(t),createVisualElement:rs,Component:e})}))}};