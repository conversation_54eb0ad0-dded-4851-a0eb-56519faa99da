exports.id=8946,exports.ids=[8946],exports.modules={23843:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},24934:(e,t,r)=>{"use strict";r.d(t,{$:()=>u});var a=r(60687),o=r(43210),n=r(8730),s=r(24224),i=r(96241);let c=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=o.forwardRef(({className:e,variant:t,size:r,asChild:o=!1,...s},u)=>{let d=o?n.DX:"button";return(0,a.jsx)(d,{className:(0,i.cn)(c({variant:t,size:r,className:e})),ref:u,...s})});u.displayName="Button"},32222:(e,t,r)=>{Promise.resolve().then(r.bind(r,83066)),Promise.resolve().then(r.bind(r,6931))},37915:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},57729:()=>{},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>c});var a=r(37413);r(82704);var o=r(61421),n=r.n(o),s=r(83066),i=r(6931);let c={title:"Cognimosity",description:"Learning platform",generator:"v0.dev"};function u({children:e}){return(0,a.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,a.jsx)("body",{className:n().className,children:(0,a.jsxs)(s.ThemeProvider,{defaultTheme:"dark",enableSystem:!0,disableTransitionOnChange:!0,children:[e,(0,a.jsx)(i.Toaster,{})]})})})}},59556:(e,t,r)=>{"use strict";r.r(t),r.d(t,{authApi:()=>s,blueprintApi:()=>d,chapterApi:()=>w,chatApi:()=>c,default:()=>P,documentApi:()=>i,flashcardsApi:()=>h,flowchartApi:()=>m,generateChapters:()=>_,generateFlashcards:()=>b,generateFlowchart:()=>f,generateQuiz:()=>v,performanceApi:()=>u,processBlueprint:()=>l,quizApi:()=>g,summaryApi:()=>p,timeTrackingApi:()=>y});var a=r(51060);let o=process.env.NEXT_PUBLIC_API_URL||"http://localhost:8000/api",n=a.A.create({baseURL:o});n.interceptors.request.use(e=>{let t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Token ${t}`),e});let s={signIn:async e=>{try{return(await n.post("/users/login/",e)).data}catch(e){throw e}},register:async e=>{try{return(await n.post("/users/register/",e)).data}catch(e){throw e}},verifyOTP:async e=>{try{return(await n.post("/users/verify-otp/",e)).data}catch(e){throw e}},logout:async()=>{try{return(await n.post("/users/logout/")).data}catch(e){throw e}}},i={uploadDocument:async(e,t)=>{let r=new FormData;return r.append("file",e),t&&r.append("group",t.toString()),(await n.post("/documents/upload/",r)).data},getDocuments:async e=>(await n.get("/documents/",{params:e?{group:e}:{}})).data,createGroup:async e=>(await n.post("/documents/groups/",e)).data,getGroups:async()=>(await n.get("/documents/groups/")).data,updateGroup:async(e,t)=>(await n.patch(`/documents/groups/${e}/`,t)).data,deleteGroup:async e=>(await n.delete(`/documents/groups/${e}/`)).data,moveDocument:async(e,t)=>(await n.patch(`/documents/${e}/`,{group:t})).data,getDocumentStatus:async e=>{try{return(await n.get(`/documents/${e}/`)).data}catch(e){throw e}},getDocumentDetails:async e=>{try{return(await n.get(`/documents/${e}/`)).data}catch(e){throw e}},getDocumentFileUrl:e=>`${o}/documents/${e}/file/`,getDocumentFileBlob:async e=>{try{return(await n.get(`/documents/${e}/file/`,{responseType:"blob"})).data}catch(e){throw e}}},c={sendMessage:async(e,t,r="openai")=>{try{return(await n.post("/chat/message/",{message:e,document_id:t,model:r})).data}catch(e){throw e}},getHistory:async()=>{try{return(await n.get("/chat/history/")).data}catch(e){throw e}}},u={createPerformance:async e=>{try{return(await n.post("/users/performance/",e)).data}catch(e){throw e}},getPerformances:async e=>{try{let t=e?`/users/performance/?document=${e}`:"/users/performance/";return(await n.get(t)).data}catch(e){throw e}},getStudentPerformances:async e=>{try{return(await n.get(`/users/performance/?student=${e}`)).data}catch(e){throw e}},getPerformanceStats:async e=>{try{return(await n.get(`/users/performance/?document=${e}`)).data}catch(e){throw e}}},d={getBlueprint:async e=>{try{return(await n.get(`/documents/${e}/blueprint/`)).data}catch(e){throw console.error("Error getting blueprint:",e),e}},uploadBlueprint:async(e,t)=>{let r=new FormData;if(t instanceof File)r.append("file",t);else{let e=new Blob([t],{type:"text/plain"});r.append("file",new File([e],"blueprint.txt",{type:"text/plain"}))}try{return(await n.post(`/documents/${e}/blueprint/`,r,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(t){let e="Failed to upload blueprint";throw t.response&&t.response.data&&t.response.data.error&&(e=t.response.data.error),Error(e)}},generateBlueprint:async(e,t,r="gemini")=>{try{let o=process.env.NEXT_PUBLIC_FASTAPI_URL||"http://localhost:8001",n=localStorage.getItem("token");return(await a.A.post(`${o}/generate-blueprint/${e}`,{document_id:e,llm_model:r,focus_areas:t},{headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error generating blueprint:",e),e}},getBlueprintTopics:async e=>{try{return(await n.get(`/documents/topics/by_document/?document_id=${e}`)).data}catch(e){throw console.error("Error getting blueprint topics:",e),e}},getBlueprintTopicContent:async e=>{try{return(await n.get(`/documents/topics/${e}/content_details/`)).data}catch(e){throw console.error("Error getting topic content:",e),e}}},l=async(e,t,r="openai")=>{try{return(await n.post(`/process-blueprint/${e}/`,{document_id:e,blueprint_text:t,llm_model:r})).data}catch(e){throw console.error("Error processing blueprint:",e),e}},h={getFlashcards:async e=>{try{return(await n.get(`/documents/flashcards/${e}/`)).data}catch(e){throw console.error("Error getting flashcards:",e),e}},generateFlashcards:async(e,t=10,r="gemini")=>{try{return(await n.post("/api/documents/flashcards/generate/",{document_id:e,num_flashcards:t,llm_model:r,force:!0})).data}catch(e){throw console.error("Error generating flashcards:",e),e}},generateFlashcardsEnhanced:async(e,t=10,r="gemini")=>{try{let o=process.env.NEXT_PUBLIC_FASTAPI_URL||"http://localhost:8001",n=localStorage.getItem("token");return(await a.A.post(`${o}/generate-flashcards/${e}`,{document_id:e,llm_model:r,num_flashcards:t},{headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error generating enhanced flashcards:",e),e}}},m={getFlowchart:async e=>{try{return(await n.get(`/documents/flowcharts/${e}/`)).data}catch(e){throw console.error("Error getting flowchart:",e),e}},generateFlowchart:async(e,t="gemini")=>{try{return(await n.post("/documents/flowcharts/generate/",{document_id:e,llm_model:t,force:!0})).data}catch(e){throw console.error("Error generating flowchart:",e),e}},generateFlowchartEnhanced:async(e,t="gemini")=>{try{let r=process.env.NEXT_PUBLIC_FASTAPI_URL||"http://localhost:8001",o=localStorage.getItem("token");return(await a.A.post(`${r}/generate-flowchart/${e}`,{document_id:e,llm_model:t},{headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error generating enhanced flowchart:",e),e}}},p={getSummary:async e=>{try{return(await n.get(`/api/summaries/${e}/`)).data}catch(e){throw console.error("Error getting summary:",e),e}},generateSummary:async(e,t="comprehensive",r="gemini")=>{try{let o=process.env.NEXT_PUBLIC_FASTAPI_URL||"http://localhost:8001",n=localStorage.getItem("token");return(await a.A.post(`${o}/generate-summary/${e}`,{document_id:e,llm_model:r,summary_type:t},{headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error generating summary:",e),e}}},g={getOrGenerateQuiz:async e=>{try{return(await n.get(`/documents/${e}/quiz/`)).data}catch(e){throw console.error("Error getting/generating quiz:",e),e}},generateQuiz:async(e,t=5,r="gemini")=>{try{return(await n.post(`/documents/${e}/quiz/`,{num_questions:t,llm_model:r})).data}catch(e){throw console.error("Error generating quiz:",e),e}},generateQuizEnhanced:async(e,t=5,r="gemini")=>{try{let o=process.env.NEXT_PUBLIC_FASTAPI_URL||"http://localhost:8001",n=localStorage.getItem("token");return(await a.A.post(`${o}/generate-quiz/${e}`,{document_id:e,llm_model:r,num_questions:t},{headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"}})).data}catch(e){throw console.error("Error generating enhanced quiz:",e),e}},getQuizzes:async e=>{try{return(await n.get(`/quizzes/?document=${e}`)).data}catch(e){throw console.error("Error fetching quizzes:",e),e}},submitQuizAnswer:async(e,t)=>{try{return(await n.post(`/quizzes/${e}/submit/`,{answers:t})).data}catch(e){throw console.error("Error submitting quiz:",e),e}}},y={startSession:async e=>{try{return(await n.post("/users/time-tracking/start-session/",{document_id:e})).data}catch(e){throw console.error("Error starting time tracking session:",e),e}},updateActivity:async e=>{try{return(await n.post("/users/time-tracking/update-activity/",{session_id:e})).data}catch(e){throw console.error("Error updating session activity:",e),e}},pauseSession:async(e,t)=>{try{return(await n.post("/users/time-tracking/pause-resume/",{session_id:e,action:"pause",notes:t||"Session paused"})).data}catch(e){throw console.error("Error pausing session:",e),e}},resumeSession:async e=>{try{return(await n.post("/users/time-tracking/pause-resume/",{session_id:e,action:"resume"})).data}catch(e){throw console.error("Error resuming session:",e),e}},endSession:async e=>{try{return(await n.post("/users/time-tracking/end-session/",{session_id:e})).data}catch(e){throw console.error("Error ending session:",e),e}},getDocumentStats:async e=>{try{return(await n.get(`/users/time-tracking/document/${e}/stats/`)).data}catch(e){throw console.error("Error getting document stats:",e),e}},getUserOverview:async()=>{try{return(await n.get("/users/time-tracking/overview/")).data}catch(e){throw console.error("Error getting user overview:",e),e}},cleanupSessions:async()=>{try{return(await n.post("/users/time-tracking/cleanup/")).data}catch(e){throw console.error("Error cleaning up sessions:",e),e}}},w={getOrGenerateChapters:async e=>{try{return(await n.get(`/documents/${e}/chapters/`)).data}catch(e){throw console.error("Error getting/generating chapters:",e),e}},generateChapters:async(e,t=5,r="gemini")=>{try{return(await n.post(`/documents/${e}/chapters/`,{num_chapters:t,llm_model:r})).data}catch(e){throw console.error("Error generating chapters:",e),e}}},v=g.generateQuiz,f=m.generateFlowchart,b=h.generateFlashcards,_=w.generateChapters,P=n},72390:(e,t,r)=>{Promise.resolve().then(r.bind(r,92892)),Promise.resolve().then(r.bind(r,52581))},82704:()=>{},83066:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});var a=r(12907);let o=(0,a.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\components\\theme-provider.tsx","ThemeProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\cognimosity_UI\\latest repo\\cogni_api\\frontend\\components\\theme-provider.tsx","useTheme")},92892:(e,t,r)=>{"use strict";r.d(t,{D:()=>i,ThemeProvider:()=>s});var a=r(60687),o=r(43210);let n=(0,o.createContext)({theme:"system",setTheme:()=>null});function s({children:e,defaultTheme:t="system",storageKey:r="ui-theme",enableSystem:s=!0,disableTransitionOnChange:i=!1,...c}){let[u,d]=(0,o.useState)(t);return(0,a.jsx)(n.Provider,{...c,value:{theme:u,setTheme:e=>{d(e)}},children:e})}let i=()=>{let e=(0,o.useContext)(n);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},96241:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(49384),o=r(82348);function n(...e){return(0,o.QP)((0,a.$)(e))}}};