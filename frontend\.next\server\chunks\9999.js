"use strict";exports.id=9999,exports.ids=[9999],exports.modules={1982:(t,e,a)=>{a.d(e,{CP:()=>l,HT:()=>d,PB:()=>h,aC:()=>c,lC:()=>o,m:()=>n,tk:()=>i});var r=a(77466),s=a(61685),i=(0,r.K2)((t,e)=>{let a=t.append("rect");if(a.attr("x",e.x),a.attr("y",e.y),a.attr("fill",e.fill),a.attr("stroke",e.stroke),a.attr("width",e.width),a.attr("height",e.height),e.name&&a.attr("name",e.name),e.rx&&a.attr("rx",e.rx),e.ry&&a.attr("ry",e.ry),void 0!==e.attrs)for(let t in e.attrs)a.attr(t,e.attrs[t]);return e.class&&a.attr("class",e.class),a},"drawRect"),o=(0,r.K2)((t,e)=>{i(t,{x:e.startx,y:e.starty,width:e.stopx-e.startx,height:e.stopy-e.starty,fill:e.fill,stroke:e.stroke,class:"rect"}).lower()},"drawBackgroundRect"),n=(0,r.K2)((t,e)=>{let a=e.text.replace(r.H1," "),s=t.append("text");s.attr("x",e.x),s.attr("y",e.y),s.attr("class","legend"),s.style("text-anchor",e.anchor),e.class&&s.attr("class",e.class);let i=s.append("tspan");return i.attr("x",e.x+2*e.textMargin),i.text(a),s},"drawText"),c=(0,r.K2)((t,e,a,r)=>{let i=t.append("image");i.attr("x",e),i.attr("y",a);let o=(0,s.J)(r);i.attr("xlink:href",o)},"drawImage"),l=(0,r.K2)((t,e,a,r)=>{let i=t.append("use");i.attr("x",e),i.attr("y",a);let o=(0,s.J)(r);i.attr("xlink:href",`#${o}`)},"drawEmbeddedImage"),h=(0,r.K2)(()=>({x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0}),"getNoteRect"),d=(0,r.K2)(()=>({x:0,y:0,width:100,height:100,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:!0}),"getTextObj")},19999:(t,e,a)=>{a.d(e,{diagram:()=>tx});var r=a(1982),s=a(92356),i=a(21123),o=a(77466),n=a(64652),c=a(61685),l=function(){var t=(0,o.K2)(function(t,e,a,r){for(a=a||{},r=t.length;r--;a[t[r]]=e);return a},"o"),e=[1,2],a=[1,3],r=[1,4],s=[2,4],i=[1,9],n=[1,11],c=[1,13],l=[1,14],h=[1,16],d=[1,17],p=[1,18],g=[1,24],u=[1,25],x=[1,26],y=[1,27],m=[1,28],b=[1,29],T=[1,30],E=[1,31],f=[1,32],w=[1,33],I=[1,34],L=[1,35],P=[1,36],_=[1,37],k=[1,38],N=[1,39],v=[1,41],A=[1,42],M=[1,43],D=[1,44],O=[1,45],S=[1,46],R=[1,4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,48,49,50,52,53,54,59,60,61,62,70],Y=[4,5,16,50,52,53],K=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],C=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,49,50,52,53,54,59,60,61,62,70],B=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,48,50,52,53,54,59,60,61,62,70],$=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,50,52,53,54,59,60,61,62,70],V=[68,69,70],F=[1,122],W={trace:(0,o.K2)(function(){},"trace"),yy:{},symbols_:{error:2,start:3,SPACE:4,NEWLINE:5,SD:6,document:7,line:8,statement:9,box_section:10,box_line:11,participant_statement:12,create:13,box:14,restOfLine:15,end:16,signal:17,autonumber:18,NUM:19,off:20,activate:21,actor:22,deactivate:23,note_statement:24,links_statement:25,link_statement:26,properties_statement:27,details_statement:28,title:29,legacy_title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,loop:36,rect:37,opt:38,alt:39,else_sections:40,par:41,par_sections:42,par_over:43,critical:44,option_sections:45,break:46,option:47,and:48,else:49,participant:50,AS:51,participant_actor:52,destroy:53,note:54,placement:55,text2:56,over:57,actor_pair:58,links:59,link:60,properties:61,details:62,spaceList:63,",":64,left_of:65,right_of:66,signaltype:67,"+":68,"-":69,ACTOR:70,SOLID_OPEN_ARROW:71,DOTTED_OPEN_ARROW:72,SOLID_ARROW:73,BIDIRECTIONAL_SOLID_ARROW:74,DOTTED_ARROW:75,BIDIRECTIONAL_DOTTED_ARROW:76,SOLID_CROSS:77,DOTTED_CROSS:78,SOLID_POINT:79,DOTTED_POINT:80,TXT:81,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NEWLINE",6:"SD",13:"create",14:"box",15:"restOfLine",16:"end",18:"autonumber",19:"NUM",20:"off",21:"activate",23:"deactivate",29:"title",30:"legacy_title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"loop",37:"rect",38:"opt",39:"alt",41:"par",43:"par_over",44:"critical",46:"break",47:"option",48:"and",49:"else",50:"participant",51:"AS",52:"participant_actor",53:"destroy",54:"note",57:"over",59:"links",60:"link",61:"properties",62:"details",64:",",65:"left_of",66:"right_of",68:"+",69:"-",70:"ACTOR",71:"SOLID_OPEN_ARROW",72:"DOTTED_OPEN_ARROW",73:"SOLID_ARROW",74:"BIDIRECTIONAL_SOLID_ARROW",75:"DOTTED_ARROW",76:"BIDIRECTIONAL_DOTTED_ARROW",77:"SOLID_CROSS",78:"DOTTED_CROSS",79:"SOLID_POINT",80:"DOTTED_POINT",81:"TXT"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[10,0],[10,2],[11,2],[11,1],[11,1],[9,1],[9,2],[9,4],[9,2],[9,4],[9,3],[9,3],[9,2],[9,3],[9,3],[9,2],[9,2],[9,2],[9,2],[9,2],[9,1],[9,1],[9,2],[9,2],[9,1],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[45,1],[45,4],[42,1],[42,4],[40,1],[40,4],[12,5],[12,3],[12,5],[12,3],[12,3],[24,4],[24,4],[25,3],[26,3],[27,3],[28,3],[63,2],[63,1],[58,3],[58,1],[55,1],[55,1],[17,5],[17,5],[17,4],[22,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[56,1]],performAction:(0,o.K2)(function(t,e,a,r,s,i,o){var n=i.length-1;switch(s){case 3:return r.apply(i[n]),i[n];case 4:case 9:case 8:case 13:this.$=[];break;case 5:case 10:i[n-1].push(i[n]),this.$=i[n-1];break;case 6:case 7:case 11:case 12:case 62:this.$=i[n];break;case 15:i[n].type="createParticipant",this.$=i[n];break;case 16:i[n-1].unshift({type:"boxStart",boxData:r.parseBoxData(i[n-2])}),i[n-1].push({type:"boxEnd",boxText:i[n-2]}),this.$=i[n-1];break;case 18:this.$={type:"sequenceIndex",sequenceIndex:Number(i[n-2]),sequenceIndexStep:Number(i[n-1]),sequenceVisible:!0,signalType:r.LINETYPE.AUTONUMBER};break;case 19:this.$={type:"sequenceIndex",sequenceIndex:Number(i[n-1]),sequenceIndexStep:1,sequenceVisible:!0,signalType:r.LINETYPE.AUTONUMBER};break;case 20:this.$={type:"sequenceIndex",sequenceVisible:!1,signalType:r.LINETYPE.AUTONUMBER};break;case 21:this.$={type:"sequenceIndex",sequenceVisible:!0,signalType:r.LINETYPE.AUTONUMBER};break;case 22:this.$={type:"activeStart",signalType:r.LINETYPE.ACTIVE_START,actor:i[n-1].actor};break;case 23:this.$={type:"activeEnd",signalType:r.LINETYPE.ACTIVE_END,actor:i[n-1].actor};break;case 29:r.setDiagramTitle(i[n].substring(6)),this.$=i[n].substring(6);break;case 30:r.setDiagramTitle(i[n].substring(7)),this.$=i[n].substring(7);break;case 31:this.$=i[n].trim(),r.setAccTitle(this.$);break;case 32:case 33:this.$=i[n].trim(),r.setAccDescription(this.$);break;case 34:i[n-1].unshift({type:"loopStart",loopText:r.parseMessage(i[n-2]),signalType:r.LINETYPE.LOOP_START}),i[n-1].push({type:"loopEnd",loopText:i[n-2],signalType:r.LINETYPE.LOOP_END}),this.$=i[n-1];break;case 35:i[n-1].unshift({type:"rectStart",color:r.parseMessage(i[n-2]),signalType:r.LINETYPE.RECT_START}),i[n-1].push({type:"rectEnd",color:r.parseMessage(i[n-2]),signalType:r.LINETYPE.RECT_END}),this.$=i[n-1];break;case 36:i[n-1].unshift({type:"optStart",optText:r.parseMessage(i[n-2]),signalType:r.LINETYPE.OPT_START}),i[n-1].push({type:"optEnd",optText:r.parseMessage(i[n-2]),signalType:r.LINETYPE.OPT_END}),this.$=i[n-1];break;case 37:i[n-1].unshift({type:"altStart",altText:r.parseMessage(i[n-2]),signalType:r.LINETYPE.ALT_START}),i[n-1].push({type:"altEnd",signalType:r.LINETYPE.ALT_END}),this.$=i[n-1];break;case 38:i[n-1].unshift({type:"parStart",parText:r.parseMessage(i[n-2]),signalType:r.LINETYPE.PAR_START}),i[n-1].push({type:"parEnd",signalType:r.LINETYPE.PAR_END}),this.$=i[n-1];break;case 39:i[n-1].unshift({type:"parStart",parText:r.parseMessage(i[n-2]),signalType:r.LINETYPE.PAR_OVER_START}),i[n-1].push({type:"parEnd",signalType:r.LINETYPE.PAR_END}),this.$=i[n-1];break;case 40:i[n-1].unshift({type:"criticalStart",criticalText:r.parseMessage(i[n-2]),signalType:r.LINETYPE.CRITICAL_START}),i[n-1].push({type:"criticalEnd",signalType:r.LINETYPE.CRITICAL_END}),this.$=i[n-1];break;case 41:i[n-1].unshift({type:"breakStart",breakText:r.parseMessage(i[n-2]),signalType:r.LINETYPE.BREAK_START}),i[n-1].push({type:"breakEnd",optText:r.parseMessage(i[n-2]),signalType:r.LINETYPE.BREAK_END}),this.$=i[n-1];break;case 43:this.$=i[n-3].concat([{type:"option",optionText:r.parseMessage(i[n-1]),signalType:r.LINETYPE.CRITICAL_OPTION},i[n]]);break;case 45:this.$=i[n-3].concat([{type:"and",parText:r.parseMessage(i[n-1]),signalType:r.LINETYPE.PAR_AND},i[n]]);break;case 47:this.$=i[n-3].concat([{type:"else",altText:r.parseMessage(i[n-1]),signalType:r.LINETYPE.ALT_ELSE},i[n]]);break;case 48:i[n-3].draw="participant",i[n-3].type="addParticipant",i[n-3].description=r.parseMessage(i[n-1]),this.$=i[n-3];break;case 49:i[n-1].draw="participant",i[n-1].type="addParticipant",this.$=i[n-1];break;case 50:i[n-3].draw="actor",i[n-3].type="addParticipant",i[n-3].description=r.parseMessage(i[n-1]),this.$=i[n-3];break;case 51:i[n-1].draw="actor",i[n-1].type="addParticipant",this.$=i[n-1];break;case 52:i[n-1].type="destroyParticipant",this.$=i[n-1];break;case 53:this.$=[i[n-1],{type:"addNote",placement:i[n-2],actor:i[n-1].actor,text:i[n]}];break;case 54:i[n-2]=[].concat(i[n-1],i[n-1]).slice(0,2),i[n-2][0]=i[n-2][0].actor,i[n-2][1]=i[n-2][1].actor,this.$=[i[n-1],{type:"addNote",placement:r.PLACEMENT.OVER,actor:i[n-2].slice(0,2),text:i[n]}];break;case 55:this.$=[i[n-1],{type:"addLinks",actor:i[n-1].actor,text:i[n]}];break;case 56:this.$=[i[n-1],{type:"addALink",actor:i[n-1].actor,text:i[n]}];break;case 57:this.$=[i[n-1],{type:"addProperties",actor:i[n-1].actor,text:i[n]}];break;case 58:this.$=[i[n-1],{type:"addDetails",actor:i[n-1].actor,text:i[n]}];break;case 61:this.$=[i[n-2],i[n]];break;case 63:this.$=r.PLACEMENT.LEFTOF;break;case 64:this.$=r.PLACEMENT.RIGHTOF;break;case 65:this.$=[i[n-4],i[n-1],{type:"addMessage",from:i[n-4].actor,to:i[n-1].actor,signalType:i[n-3],msg:i[n],activate:!0},{type:"activeStart",signalType:r.LINETYPE.ACTIVE_START,actor:i[n-1].actor}];break;case 66:this.$=[i[n-4],i[n-1],{type:"addMessage",from:i[n-4].actor,to:i[n-1].actor,signalType:i[n-3],msg:i[n]},{type:"activeEnd",signalType:r.LINETYPE.ACTIVE_END,actor:i[n-4].actor}];break;case 67:this.$=[i[n-3],i[n-1],{type:"addMessage",from:i[n-3].actor,to:i[n-1].actor,signalType:i[n-2],msg:i[n]}];break;case 68:this.$={type:"addParticipant",actor:i[n]};break;case 69:this.$=r.LINETYPE.SOLID_OPEN;break;case 70:this.$=r.LINETYPE.DOTTED_OPEN;break;case 71:this.$=r.LINETYPE.SOLID;break;case 72:this.$=r.LINETYPE.BIDIRECTIONAL_SOLID;break;case 73:this.$=r.LINETYPE.DOTTED;break;case 74:this.$=r.LINETYPE.BIDIRECTIONAL_DOTTED;break;case 75:this.$=r.LINETYPE.SOLID_CROSS;break;case 76:this.$=r.LINETYPE.DOTTED_CROSS;break;case 77:this.$=r.LINETYPE.SOLID_POINT;break;case 78:this.$=r.LINETYPE.DOTTED_POINT;break;case 79:this.$=r.parseMessage(i[n].trim().substring(1))}},"anonymous"),table:[{3:1,4:e,5:a,6:r},{1:[3]},{3:5,4:e,5:a,6:r},{3:6,4:e,5:a,6:r},t([1,4,5,13,14,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],s,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:i,5:n,8:8,9:10,12:12,13:c,14:l,17:15,18:h,21:d,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:P,50:_,52:k,53:N,54:v,59:A,60:M,61:D,62:O,70:S},t(R,[2,5]),{9:47,12:12,13:c,14:l,17:15,18:h,21:d,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:P,50:_,52:k,53:N,54:v,59:A,60:M,61:D,62:O,70:S},t(R,[2,7]),t(R,[2,8]),t(R,[2,14]),{12:48,50:_,52:k,53:N},{15:[1,49]},{5:[1,50]},{5:[1,53],19:[1,51],20:[1,52]},{22:54,70:S},{22:55,70:S},{5:[1,56]},{5:[1,57]},{5:[1,58]},{5:[1,59]},{5:[1,60]},t(R,[2,29]),t(R,[2,30]),{32:[1,61]},{34:[1,62]},t(R,[2,33]),{15:[1,63]},{15:[1,64]},{15:[1,65]},{15:[1,66]},{15:[1,67]},{15:[1,68]},{15:[1,69]},{15:[1,70]},{22:71,70:S},{22:72,70:S},{22:73,70:S},{67:74,71:[1,75],72:[1,76],73:[1,77],74:[1,78],75:[1,79],76:[1,80],77:[1,81],78:[1,82],79:[1,83],80:[1,84]},{55:85,57:[1,86],65:[1,87],66:[1,88]},{22:89,70:S},{22:90,70:S},{22:91,70:S},{22:92,70:S},t([5,51,64,71,72,73,74,75,76,77,78,79,80,81],[2,68]),t(R,[2,6]),t(R,[2,15]),t(Y,[2,9],{10:93}),t(R,[2,17]),{5:[1,95],19:[1,94]},{5:[1,96]},t(R,[2,21]),{5:[1,97]},{5:[1,98]},t(R,[2,24]),t(R,[2,25]),t(R,[2,26]),t(R,[2,27]),t(R,[2,28]),t(R,[2,31]),t(R,[2,32]),t(K,s,{7:99}),t(K,s,{7:100}),t(K,s,{7:101}),t(C,s,{40:102,7:103}),t(B,s,{42:104,7:105}),t(B,s,{7:105,42:106}),t($,s,{45:107,7:108}),t(K,s,{7:109}),{5:[1,111],51:[1,110]},{5:[1,113],51:[1,112]},{5:[1,114]},{22:117,68:[1,115],69:[1,116],70:S},t(V,[2,69]),t(V,[2,70]),t(V,[2,71]),t(V,[2,72]),t(V,[2,73]),t(V,[2,74]),t(V,[2,75]),t(V,[2,76]),t(V,[2,77]),t(V,[2,78]),{22:118,70:S},{22:120,58:119,70:S},{70:[2,63]},{70:[2,64]},{56:121,81:F},{56:123,81:F},{56:124,81:F},{56:125,81:F},{4:[1,128],5:[1,130],11:127,12:129,16:[1,126],50:_,52:k,53:N},{5:[1,131]},t(R,[2,19]),t(R,[2,20]),t(R,[2,22]),t(R,[2,23]),{4:i,5:n,8:8,9:10,12:12,13:c,14:l,16:[1,132],17:15,18:h,21:d,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:P,50:_,52:k,53:N,54:v,59:A,60:M,61:D,62:O,70:S},{4:i,5:n,8:8,9:10,12:12,13:c,14:l,16:[1,133],17:15,18:h,21:d,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:P,50:_,52:k,53:N,54:v,59:A,60:M,61:D,62:O,70:S},{4:i,5:n,8:8,9:10,12:12,13:c,14:l,16:[1,134],17:15,18:h,21:d,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:P,50:_,52:k,53:N,54:v,59:A,60:M,61:D,62:O,70:S},{16:[1,135]},{4:i,5:n,8:8,9:10,12:12,13:c,14:l,16:[2,46],17:15,18:h,21:d,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:P,49:[1,136],50:_,52:k,53:N,54:v,59:A,60:M,61:D,62:O,70:S},{16:[1,137]},{4:i,5:n,8:8,9:10,12:12,13:c,14:l,16:[2,44],17:15,18:h,21:d,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:P,48:[1,138],50:_,52:k,53:N,54:v,59:A,60:M,61:D,62:O,70:S},{16:[1,139]},{16:[1,140]},{4:i,5:n,8:8,9:10,12:12,13:c,14:l,16:[2,42],17:15,18:h,21:d,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:P,47:[1,141],50:_,52:k,53:N,54:v,59:A,60:M,61:D,62:O,70:S},{4:i,5:n,8:8,9:10,12:12,13:c,14:l,16:[1,142],17:15,18:h,21:d,22:40,23:p,24:19,25:20,26:21,27:22,28:23,29:g,30:u,31:x,33:y,35:m,36:b,37:T,38:E,39:f,41:w,43:I,44:L,46:P,50:_,52:k,53:N,54:v,59:A,60:M,61:D,62:O,70:S},{15:[1,143]},t(R,[2,49]),{15:[1,144]},t(R,[2,51]),t(R,[2,52]),{22:145,70:S},{22:146,70:S},{56:147,81:F},{56:148,81:F},{56:149,81:F},{64:[1,150],81:[2,62]},{5:[2,55]},{5:[2,79]},{5:[2,56]},{5:[2,57]},{5:[2,58]},t(R,[2,16]),t(Y,[2,10]),{12:151,50:_,52:k,53:N},t(Y,[2,12]),t(Y,[2,13]),t(R,[2,18]),t(R,[2,34]),t(R,[2,35]),t(R,[2,36]),t(R,[2,37]),{15:[1,152]},t(R,[2,38]),{15:[1,153]},t(R,[2,39]),t(R,[2,40]),{15:[1,154]},t(R,[2,41]),{5:[1,155]},{5:[1,156]},{56:157,81:F},{56:158,81:F},{5:[2,67]},{5:[2,53]},{5:[2,54]},{22:159,70:S},t(Y,[2,11]),t(C,s,{7:103,40:160}),t(B,s,{7:105,42:161}),t($,s,{7:108,45:162}),t(R,[2,48]),t(R,[2,50]),{5:[2,65]},{5:[2,66]},{81:[2,61]},{16:[2,47]},{16:[2,45]},{16:[2,43]}],defaultActions:{5:[2,1],6:[2,2],87:[2,63],88:[2,64],121:[2,55],122:[2,79],123:[2,56],124:[2,57],125:[2,58],147:[2,67],148:[2,53],149:[2,54],157:[2,65],158:[2,66],159:[2,61],160:[2,47],161:[2,45],162:[2,43]},parseError:(0,o.K2)(function(t,e){if(e.recoverable)this.trace(t);else{var a=Error(t);throw a.hash=e,a}},"parseError"),parse:(0,o.K2)(function(t){var e=this,a=[0],r=[],s=[null],i=[],n=this.table,c="",l=0,h=0,d=0,p=i.slice.call(arguments,1),g=Object.create(this.lexer),u={yy:{}};for(var x in this.yy)Object.prototype.hasOwnProperty.call(this.yy,x)&&(u.yy[x]=this.yy[x]);g.setInput(t,u.yy),u.yy.lexer=g,u.yy.parser=this,void 0===g.yylloc&&(g.yylloc={});var y=g.yylloc;i.push(y);var m=g.options&&g.options.ranges;function b(){var t;return"number"!=typeof(t=r.pop()||g.lex()||1)&&(t instanceof Array&&(t=(r=t).pop()),t=e.symbols_[t]||t),t}"function"==typeof u.yy.parseError?this.parseError=u.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,(0,o.K2)(function(t){a.length=a.length-2*t,s.length=s.length-t,i.length=i.length-t},"popStack"),(0,o.K2)(b,"lex");for(var T,E,f,w,I,L,P,_,k,N={};;){if(f=a[a.length-1],this.defaultActions[f]?w=this.defaultActions[f]:(null==T&&(T=b()),w=n[f]&&n[f][T]),void 0===w||!w.length||!w[0]){var v="";for(L in k=[],n[f])this.terminals_[L]&&L>2&&k.push("'"+this.terminals_[L]+"'");v=g.showPosition?"Parse error on line "+(l+1)+":\n"+g.showPosition()+"\nExpecting "+k.join(", ")+", got '"+(this.terminals_[T]||T)+"'":"Parse error on line "+(l+1)+": Unexpected "+(1==T?"end of input":"'"+(this.terminals_[T]||T)+"'"),this.parseError(v,{text:g.match,token:this.terminals_[T]||T,line:g.yylineno,loc:y,expected:k})}if(w[0]instanceof Array&&w.length>1)throw Error("Parse Error: multiple actions possible at state: "+f+", token: "+T);switch(w[0]){case 1:a.push(T),s.push(g.yytext),i.push(g.yylloc),a.push(w[1]),T=null,E?(T=E,E=null):(h=g.yyleng,c=g.yytext,l=g.yylineno,y=g.yylloc,d>0&&d--);break;case 2:if(P=this.productions_[w[1]][1],N.$=s[s.length-P],N._$={first_line:i[i.length-(P||1)].first_line,last_line:i[i.length-1].last_line,first_column:i[i.length-(P||1)].first_column,last_column:i[i.length-1].last_column},m&&(N._$.range=[i[i.length-(P||1)].range[0],i[i.length-1].range[1]]),void 0!==(I=this.performAction.apply(N,[c,h,l,u.yy,w[1],s,i].concat(p))))return I;P&&(a=a.slice(0,-1*P*2),s=s.slice(0,-1*P),i=i.slice(0,-1*P)),a.push(this.productions_[w[1]][0]),s.push(N.$),i.push(N._$),_=n[a[a.length-2]][a[a.length-1]],a.push(_);break;case 3:return!0}}return!0},"parse")};function q(){this.yy={}}return W.lexer={EOF:1,parseError:(0,o.K2)(function(t,e){if(this.yy.parser)this.yy.parser.parseError(t,e);else throw Error(t)},"parseError"),setInput:(0,o.K2)(function(t,e){return this.yy=e||this.yy||{},this._input=t,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,o.K2)(function(){var t=this._input[0];return this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t,t.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t},"input"),unput:(0,o.K2)(function(t){var e=t.length,a=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e),this.offset-=e;var r=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),a.length-1&&(this.yylineno-=a.length-1);var s=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:a?(a.length===r.length?this.yylloc.first_column:0)+r[r.length-a.length].length-a[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[s[0],s[0]+this.yyleng-e]),this.yyleng=this.yytext.length,this},"unput"),more:(0,o.K2)(function(){return this._more=!0,this},"more"),reject:(0,o.K2)(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:(0,o.K2)(function(t){this.unput(this.match.slice(t))},"less"),pastInput:(0,o.K2)(function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,o.K2)(function(){var t=this.match;return t.length<20&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,o.K2)(function(){var t=this.pastInput(),e=Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},"showPosition"),test_match:(0,o.K2)(function(t,e){var a,r,s;if(this.options.backtrack_lexer&&(s={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(s.yylloc.range=this.yylloc.range.slice(0))),(r=t[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=r.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:r?r[r.length-1].length-r[r.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.matches=t,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],a=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),a)return a;if(this._backtrack)for(var i in s)this[i]=s[i];return!1},"test_match"),next:(0,o.K2)(function(){if(this.done)return this.EOF;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var t,e,a,r,s=this._currentRules(),i=0;i<s.length;i++)if((a=this._input.match(this.rules[s[i]]))&&(!e||a[0].length>e[0].length)){if(e=a,r=i,this.options.backtrack_lexer){if(!1!==(t=this.test_match(a,s[i])))return t;if(!this._backtrack)return!1;e=!1;continue}if(!this.options.flex)break}return e?!1!==(t=this.test_match(e,s[r]))&&t:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,o.K2)(function(){var t=this.next();return t||this.lex()},"lex"),begin:(0,o.K2)(function(t){this.conditionStack.push(t)},"begin"),popState:(0,o.K2)(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,o.K2)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,o.K2)(function(t){return(t=this.conditionStack.length-1-Math.abs(t||0))>=0?this.conditionStack[t]:"INITIAL"},"topState"),pushState:(0,o.K2)(function(t){this.begin(t)},"pushState"),stateStackSize:(0,o.K2)(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,o.K2)(function(t,e,a,r){switch(a){case 0:case 51:case 66:return 5;case 1:case 2:case 3:case 4:case 5:break;case 6:return 19;case 7:return this.begin("LINE"),14;case 8:return this.begin("ID"),50;case 9:return this.begin("ID"),52;case 10:return 13;case 11:return this.begin("ID"),53;case 12:return e.yytext=e.yytext.trim(),this.begin("ALIAS"),70;case 13:return this.popState(),this.popState(),this.begin("LINE"),51;case 14:return this.popState(),this.popState(),5;case 15:return this.begin("LINE"),36;case 16:return this.begin("LINE"),37;case 17:return this.begin("LINE"),38;case 18:return this.begin("LINE"),39;case 19:return this.begin("LINE"),49;case 20:return this.begin("LINE"),41;case 21:return this.begin("LINE"),43;case 22:return this.begin("LINE"),48;case 23:return this.begin("LINE"),44;case 24:return this.begin("LINE"),47;case 25:return this.begin("LINE"),46;case 26:return this.popState(),15;case 27:return 16;case 28:return 65;case 29:return 66;case 30:return 59;case 31:return 60;case 32:return 61;case 33:return 62;case 34:return 57;case 35:return 54;case 36:return this.begin("ID"),21;case 37:return this.begin("ID"),23;case 38:return 29;case 39:return 30;case 40:return this.begin("acc_title"),31;case 41:return this.popState(),"acc_title_value";case 42:return this.begin("acc_descr"),33;case 43:return this.popState(),"acc_descr_value";case 44:this.begin("acc_descr_multiline");break;case 45:this.popState();break;case 46:return"acc_descr_multiline_value";case 47:return 6;case 48:return 18;case 49:return 20;case 50:return 64;case 52:return e.yytext=e.yytext.trim(),70;case 53:return 73;case 54:return 74;case 55:return 75;case 56:return 76;case 57:return 71;case 58:return 72;case 59:return 77;case 60:return 78;case 61:return 79;case 62:return 80;case 63:return 81;case 64:return 68;case 65:return 69;case 67:return"INVALID"}},"anonymous"),rules:[/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[0-9]+(?=[ \n]+))/i,/^(?:box\b)/i,/^(?:participant\b)/i,/^(?:actor\b)/i,/^(?:create\b)/i,/^(?:destroy\b)/i,/^(?:[^\<->\->:\n,;]+?([\-]*[^\<->\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i,/^(?:as\b)/i,/^(?:(?:))/i,/^(?:loop\b)/i,/^(?:rect\b)/i,/^(?:opt\b)/i,/^(?:alt\b)/i,/^(?:else\b)/i,/^(?:par\b)/i,/^(?:par_over\b)/i,/^(?:and\b)/i,/^(?:critical\b)/i,/^(?:option\b)/i,/^(?:break\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i,/^(?:end\b)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:links\b)/i,/^(?:link\b)/i,/^(?:properties\b)/i,/^(?:details\b)/i,/^(?:over\b)/i,/^(?:note\b)/i,/^(?:activate\b)/i,/^(?:deactivate\b)/i,/^(?:title\s[^#\n;]+)/i,/^(?:title:\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:sequenceDiagram\b)/i,/^(?:autonumber\b)/i,/^(?:off\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\+\<->\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\<->\->:\n,;]+)*)/i,/^(?:->>)/i,/^(?:<<->>)/i,/^(?:-->>)/i,/^(?:<<-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\)])/i,/^(?:--[\)])/i,/^(?::(?:(?:no)?wrap)?[^#\n;]+)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[45,46],inclusive:!1},acc_descr:{rules:[43],inclusive:!1},acc_title:{rules:[41],inclusive:!1},ID:{rules:[2,3,12],inclusive:!1},ALIAS:{rules:[2,3,13,14],inclusive:!1},LINE:{rules:[2,3,26],inclusive:!1},INITIAL:{rules:[0,1,3,4,5,6,7,8,9,10,11,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67],inclusive:!0}}},(0,o.K2)(q,"Parser"),q.prototype=W,W.Parser=q,new q}();l.parser=l;var h={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25,AUTONUMBER:26,CRITICAL_START:27,CRITICAL_OPTION:28,CRITICAL_END:29,BREAK_START:30,BREAK_END:31,PAR_OVER_START:32,BIDIRECTIONAL_SOLID:33,BIDIRECTIONAL_DOTTED:34},d={FILLED:0,OPEN:1},p={LEFTOF:0,RIGHTOF:1,OVER:2},g=class{constructor(){this.state=new s.m(()=>({prevActor:void 0,actors:new Map,createdActors:new Map,destroyedActors:new Map,boxes:[],messages:[],notes:[],sequenceNumbersEnabled:!1,wrapEnabled:void 0,currentBox:void 0,lastCreated:void 0,lastDestroyed:void 0})),this.setAccTitle=o.SV,this.setAccDescription=o.EI,this.setDiagramTitle=o.ke,this.getAccTitle=o.iN,this.getAccDescription=o.m7,this.getDiagramTitle=o.ab,this.apply=this.apply.bind(this),this.parseBoxData=this.parseBoxData.bind(this),this.parseMessage=this.parseMessage.bind(this),this.clear(),this.setWrap((0,o.D7)().wrap),this.LINETYPE=h,this.ARROWTYPE=d,this.PLACEMENT=p}static{(0,o.K2)(this,"SequenceDB")}addBox(t){this.state.records.boxes.push({name:t.text,wrap:t.wrap??this.autoWrap(),fill:t.color,actorKeys:[]}),this.state.records.currentBox=this.state.records.boxes.slice(-1)[0]}addActor(t,e,a,r){let s=this.state.records.currentBox,i=this.state.records.actors.get(t);if(i){if(this.state.records.currentBox&&i.box&&this.state.records.currentBox!==i.box)throw Error(`A same participant should only be defined in one Box: ${i.name} can't be in '${i.box.name}' and in '${this.state.records.currentBox.name}' at the same time.`);if(s=i.box?i.box:this.state.records.currentBox,i.box=s,i&&e===i.name&&null==a)return}if(a?.text==null&&(a={text:e,type:r}),(null==r||null==a.text)&&(a={text:e,type:r}),this.state.records.actors.set(t,{box:s,name:e,description:a.text,wrap:a.wrap??this.autoWrap(),prevActor:this.state.records.prevActor,links:{},properties:{},actorCnt:null,rectData:null,type:r??"participant"}),this.state.records.prevActor){let e=this.state.records.actors.get(this.state.records.prevActor);e&&(e.nextActor=t)}this.state.records.currentBox&&this.state.records.currentBox.actorKeys.push(t),this.state.records.prevActor=t}activationCount(t){let e;let a=0;if(!t)return 0;for(e=0;e<this.state.records.messages.length;e++)this.state.records.messages[e].type===this.LINETYPE.ACTIVE_START&&this.state.records.messages[e].from===t&&a++,this.state.records.messages[e].type===this.LINETYPE.ACTIVE_END&&this.state.records.messages[e].from===t&&a--;return a}addMessage(t,e,a,r){this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:t,to:e,message:a.text,wrap:a.wrap??this.autoWrap(),answer:r})}addSignal(t,e,a,r,s=!1){if(r===this.LINETYPE.ACTIVE_END&&1>this.activationCount(t??"")){let e=Error("Trying to inactivate an inactive participant ("+t+")");throw e.hash={text:"->>-",token:"->>-",line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["'ACTIVE_PARTICIPANT'"]},e}return this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:t,to:e,message:a?.text??"",wrap:a?.wrap??this.autoWrap(),type:r,activate:s}),!0}hasAtLeastOneBox(){return this.state.records.boxes.length>0}hasAtLeastOneBoxWithTitle(){return this.state.records.boxes.some(t=>t.name)}getMessages(){return this.state.records.messages}getBoxes(){return this.state.records.boxes}getActors(){return this.state.records.actors}getCreatedActors(){return this.state.records.createdActors}getDestroyedActors(){return this.state.records.destroyedActors}getActor(t){return this.state.records.actors.get(t)}getActorKeys(){return[...this.state.records.actors.keys()]}enableSequenceNumbers(){this.state.records.sequenceNumbersEnabled=!0}disableSequenceNumbers(){this.state.records.sequenceNumbersEnabled=!1}showSequenceNumbers(){return this.state.records.sequenceNumbersEnabled}setWrap(t){this.state.records.wrapEnabled=t}extractWrap(t){if(void 0===t)return{};t=t.trim();let e=null!==/^:?wrap:/.exec(t)||null===/^:?nowrap:/.exec(t)&&void 0;return{cleanedText:(void 0===e?t:t.replace(/^:?(?:no)?wrap:/,"")).trim(),wrap:e}}autoWrap(){return void 0!==this.state.records.wrapEnabled?this.state.records.wrapEnabled:o.D7().sequence?.wrap??!1}clear(){this.state.reset(),(0,o.IU)()}parseMessage(t){let e=t.trim(),{wrap:a,cleanedText:r}=this.extractWrap(e),s={text:r,wrap:a};return o.Rm.debug(`parseMessage: ${JSON.stringify(s)}`),s}parseBoxData(t){let e=/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/.exec(t),a=e?.[1]?e[1].trim():"transparent",r=e?.[2]?e[2].trim():void 0;if(window?.CSS)window.CSS.supports("color",a)||(a="transparent",r=t.trim());else{let e=new Option().style;e.color=a,e.color!==a&&(a="transparent",r=t.trim())}let{wrap:s,cleanedText:i}=this.extractWrap(r);return{text:i?(0,o.jZ)(i,(0,o.D7)()):void 0,color:a,wrap:s}}addNote(t,e,a){let r={actor:t,placement:e,message:a.text,wrap:a.wrap??this.autoWrap()},s=[].concat(t,t);this.state.records.notes.push(r),this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:s[0],to:s[1],message:a.text,wrap:a.wrap??this.autoWrap(),type:this.LINETYPE.NOTE,placement:e})}addLinks(t,e){let a=this.getActor(t);try{let t=(0,o.jZ)(e.text,(0,o.D7)());t=(t=t.replace(/&equals;/g,"=")).replace(/&amp;/g,"&");let r=JSON.parse(t);this.insertLinks(a,r)}catch(t){o.Rm.error("error while parsing actor link text",t)}}addALink(t,e){let a=this.getActor(t);try{let t={},r=(0,o.jZ)(e.text,(0,o.D7)()),s=r.indexOf("@"),i=(r=(r=r.replace(/&equals;/g,"=")).replace(/&amp;/g,"&")).slice(0,s-1).trim(),n=r.slice(s+1).trim();t[i]=n,this.insertLinks(a,t)}catch(t){o.Rm.error("error while parsing actor link text",t)}}insertLinks(t,e){if(null==t.links)t.links=e;else for(let a in e)t.links[a]=e[a]}addProperties(t,e){let a=this.getActor(t);try{let t=(0,o.jZ)(e.text,(0,o.D7)()),r=JSON.parse(t);this.insertProperties(a,r)}catch(t){o.Rm.error("error while parsing actor properties text",t)}}insertProperties(t,e){if(null==t.properties)t.properties=e;else for(let a in e)t.properties[a]=e[a]}boxEnd(){this.state.records.currentBox=void 0}addDetails(t,e){let a=this.getActor(t),r=document.getElementById(e.text);try{let t=r.innerHTML,e=JSON.parse(t);e.properties&&this.insertProperties(a,e.properties),e.links&&this.insertLinks(a,e.links)}catch(t){o.Rm.error("error while parsing actor details text",t)}}getActorProperty(t,e){if(t?.properties!==void 0)return t.properties[e]}apply(t){if(Array.isArray(t))t.forEach(t=>{this.apply(t)});else switch(t.type){case"sequenceIndex":this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:void 0,to:void 0,message:{start:t.sequenceIndex,step:t.sequenceIndexStep,visible:t.sequenceVisible},wrap:!1,type:t.signalType});break;case"addParticipant":this.addActor(t.actor,t.actor,t.description,t.draw);break;case"createParticipant":if(this.state.records.actors.has(t.actor))throw Error("It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior");this.state.records.lastCreated=t.actor,this.addActor(t.actor,t.actor,t.description,t.draw),this.state.records.createdActors.set(t.actor,this.state.records.messages.length);break;case"destroyParticipant":this.state.records.lastDestroyed=t.actor,this.state.records.destroyedActors.set(t.actor,this.state.records.messages.length);break;case"activeStart":case"activeEnd":this.addSignal(t.actor,void 0,void 0,t.signalType);break;case"addNote":this.addNote(t.actor,t.placement,t.text);break;case"addLinks":this.addLinks(t.actor,t.text);break;case"addALink":this.addALink(t.actor,t.text);break;case"addProperties":this.addProperties(t.actor,t.text);break;case"addDetails":this.addDetails(t.actor,t.text);break;case"addMessage":if(this.state.records.lastCreated){if(t.to!==this.state.records.lastCreated)throw Error("The created participant "+this.state.records.lastCreated.name+" does not have an associated creating message after its declaration. Please check the sequence diagram.");this.state.records.lastCreated=void 0}else if(this.state.records.lastDestroyed){if(t.to!==this.state.records.lastDestroyed&&t.from!==this.state.records.lastDestroyed)throw Error("The destroyed participant "+this.state.records.lastDestroyed.name+" does not have an associated destroying message after its declaration. Please check the sequence diagram.");this.state.records.lastDestroyed=void 0}this.addSignal(t.from,t.to,t.msg,t.signalType,t.activate);break;case"boxStart":this.addBox(t.boxData);break;case"boxEnd":this.boxEnd();break;case"loopStart":this.addSignal(void 0,void 0,t.loopText,t.signalType);break;case"loopEnd":case"rectEnd":case"optEnd":case"altEnd":case"parEnd":case"criticalEnd":case"breakEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"rectStart":this.addSignal(void 0,void 0,t.color,t.signalType);break;case"optStart":this.addSignal(void 0,void 0,t.optText,t.signalType);break;case"altStart":case"else":this.addSignal(void 0,void 0,t.altText,t.signalType);break;case"setAccTitle":(0,o.SV)(t.text);break;case"parStart":case"and":this.addSignal(void 0,void 0,t.parText,t.signalType);break;case"criticalStart":this.addSignal(void 0,void 0,t.criticalText,t.signalType);break;case"option":this.addSignal(void 0,void 0,t.optionText,t.signalType);break;case"breakStart":this.addSignal(void 0,void 0,t.breakText,t.signalType)}}getConfig(){return(0,o.D7)().sequence}},u=(0,o.K2)(t=>`.actor {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
  }

  text.actor > tspan {
    fill: ${t.actorTextColor};
    stroke: none;
  }

  .actor-line {
    stroke: ${t.actorLineColor};
  }

  .messageLine0 {
    stroke-width: 1.5;
    stroke-dasharray: none;
    stroke: ${t.signalColor};
  }

  .messageLine1 {
    stroke-width: 1.5;
    stroke-dasharray: 2, 2;
    stroke: ${t.signalColor};
  }

  #arrowhead path {
    fill: ${t.signalColor};
    stroke: ${t.signalColor};
  }

  .sequenceNumber {
    fill: ${t.sequenceNumberColor};
  }

  #sequencenumber {
    fill: ${t.signalColor};
  }

  #crosshead path {
    fill: ${t.signalColor};
    stroke: ${t.signalColor};
  }

  .messageText {
    fill: ${t.signalTextColor};
    stroke: none;
  }

  .labelBox {
    stroke: ${t.labelBoxBorderColor};
    fill: ${t.labelBoxBkgColor};
  }

  .labelText, .labelText > tspan {
    fill: ${t.labelTextColor};
    stroke: none;
  }

  .loopText, .loopText > tspan {
    fill: ${t.loopTextColor};
    stroke: none;
  }

  .loopLine {
    stroke-width: 2px;
    stroke-dasharray: 2, 2;
    stroke: ${t.labelBoxBorderColor};
    fill: ${t.labelBoxBorderColor};
  }

  .note {
    //stroke: #decc93;
    stroke: ${t.noteBorderColor};
    fill: ${t.noteBkgColor};
  }

  .noteText, .noteText > tspan {
    fill: ${t.noteTextColor};
    stroke: none;
  }

  .activation0 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .activation1 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .activation2 {
    fill: ${t.activationBkgColor};
    stroke: ${t.activationBorderColor};
  }

  .actorPopupMenu {
    position: absolute;
  }

  .actorPopupMenuPanel {
    position: absolute;
    fill: ${t.actorBkg};
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));
}
  .actor-man line {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
  }
  .actor-man circle, line {
    stroke: ${t.actorBorder};
    fill: ${t.actorBkg};
    stroke-width: 2px;
  }
`,"getStyles"),x="actor-top",y="actor-bottom",m="actor-man",b=(0,o.K2)(function(t,e){return(0,r.tk)(t,e)},"drawRect"),T=(0,o.K2)(function(t,e,a,r,s){if(void 0===e.links||null===e.links||0===Object.keys(e.links).length)return{height:0,width:0};let i=e.links,o=e.actorCnt,n=e.rectData;var l="none";s&&(l="block !important");let h=t.append("g");h.attr("id","actor"+o+"_popup"),h.attr("class","actorPopupMenu"),h.attr("display",l);var d="";void 0!==n.class&&(d=" "+n.class);let p=n.width>a?n.width:a,g=h.append("rect");if(g.attr("class","actorPopupMenuPanel"+d),g.attr("x",n.x),g.attr("y",n.height),g.attr("fill",n.fill),g.attr("stroke",n.stroke),g.attr("width",p),g.attr("height",n.height),g.attr("rx",n.rx),g.attr("ry",n.ry),null!=i){var u=20;for(let t in i){var x=h.append("a"),y=(0,c.J)(i[t]);x.attr("xlink:href",y),x.attr("target","_blank"),q(r)(t,x,n.x+10,n.height+u,p,20,{class:"actor"},r),u+=30}}return g.attr("height",u),{height:n.height+u,width:p}},"drawPopup"),E=(0,o.K2)(function(t){return"var pu = document.getElementById('"+t+"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }"},"popupMenuToggle"),f=(0,o.K2)(async function(t,e,a=null){let r=t.append("foreignObject"),s=await (0,o.VJ)(e.text,(0,o.zj)()),i=r.append("xhtml:div").attr("style","width: fit-content;").attr("xmlns","http://www.w3.org/1999/xhtml").html(s).node().getBoundingClientRect();if(r.attr("height",Math.round(i.height)).attr("width",Math.round(i.width)),"noteText"===e.class){let a=t.node().firstChild;a.setAttribute("height",i.height+2*e.textMargin);let s=a.getBBox();r.attr("x",Math.round(s.x+s.width/2-i.width/2)).attr("y",Math.round(s.y+s.height/2-i.height/2))}else if(a){let{startx:t,stopx:s,starty:o}=a;if(t>s){let e=t;t=s,s=e}r.attr("x",Math.round(t+Math.abs(t-s)/2-i.width/2)),"loopText"===e.class?r.attr("y",Math.round(o)):r.attr("y",Math.round(o-i.height))}return[r]},"drawKatex"),w=(0,o.K2)(function(t,e){let a=0,r=0,s=e.text.split(o.Y2.lineBreakRegex),[n,c]=(0,i.I5)(e.fontSize),l=[],h=0,d=(0,o.K2)(()=>e.y,"yfunc");if(void 0!==e.valign&&void 0!==e.textMargin&&e.textMargin>0)switch(e.valign){case"top":case"start":d=(0,o.K2)(()=>Math.round(e.y+e.textMargin),"yfunc");break;case"middle":case"center":d=(0,o.K2)(()=>Math.round(e.y+(a+r+e.textMargin)/2),"yfunc");break;case"bottom":case"end":d=(0,o.K2)(()=>Math.round(e.y+(a+r+2*e.textMargin)-e.textMargin),"yfunc")}if(void 0!==e.anchor&&void 0!==e.textMargin&&void 0!==e.width)switch(e.anchor){case"left":case"start":e.x=Math.round(e.x+e.textMargin),e.anchor="start",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"middle":case"center":e.x=Math.round(e.x+e.width/2),e.anchor="middle",e.dominantBaseline="middle",e.alignmentBaseline="middle";break;case"right":case"end":e.x=Math.round(e.x+e.width-e.textMargin),e.anchor="end",e.dominantBaseline="middle",e.alignmentBaseline="middle"}for(let[o,p]of s.entries()){void 0!==e.textMargin&&0===e.textMargin&&void 0!==n&&(h=o*n);let s=t.append("text");s.attr("x",e.x),s.attr("y",d()),void 0!==e.anchor&&s.attr("text-anchor",e.anchor).attr("dominant-baseline",e.dominantBaseline).attr("alignment-baseline",e.alignmentBaseline),void 0!==e.fontFamily&&s.style("font-family",e.fontFamily),void 0!==c&&s.style("font-size",c),void 0!==e.fontWeight&&s.style("font-weight",e.fontWeight),void 0!==e.fill&&s.attr("fill",e.fill),void 0!==e.class&&s.attr("class",e.class),void 0!==e.dy?s.attr("dy",e.dy):0!==h&&s.attr("dy",h);let g=p||i.pe;if(e.tspan){let t=s.append("tspan");t.attr("x",e.x),void 0!==e.fill&&t.attr("fill",e.fill),t.text(g)}else s.text(g);void 0!==e.valign&&void 0!==e.textMargin&&e.textMargin>0&&(r+=(s._groups||s)[0][0].getBBox().height,a=r),l.push(s)}return l},"drawText"),I=(0,o.K2)(function(t,e){function a(t,e,a,r,s){return t+","+e+" "+(t+a)+","+e+" "+(t+a)+","+(e+r-s)+" "+(t+a-1.2*s)+","+(e+r)+" "+t+","+(e+r)}(0,o.K2)(a,"genPoints");let r=t.append("polygon");return r.attr("points",a(e.x,e.y,e.width,e.height,7)),r.attr("class","labelBox"),e.y=e.y+e.height/2,w(t,e),r},"drawLabel"),L=-1,P=(0,o.K2)((t,e,a,r)=>{t.select&&a.forEach(a=>{let s=e.get(a),i=t.select("#actor"+s.actorCnt);!r.mirrorActors&&s.stopy?i.attr("y2",s.stopy+s.height/2):r.mirrorActors&&i.attr("y2",s.stopy)})},"fixLifeLineHeights"),_=(0,o.K2)(function(t,e,a,s){let i=s?e.stopy:e.starty,n=e.x+e.width/2,c=i+e.height,l=t.append("g").lower();var h=l;s||(L++,Object.keys(e.links||{}).length&&!a.forceMenus&&h.attr("onclick",E(`actor${L}_popup`)).attr("cursor","pointer"),h.append("line").attr("id","actor"+L).attr("x1",n).attr("y1",c).attr("x2",n).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",e.name),h=l.append("g"),e.actorCnt=L,null!=e.links&&h.attr("id","root-"+L));let d=(0,r.PB)();var p="actor";e.properties?.class?p=e.properties.class:d.fill="#eaeaea",s?p+=` ${y}`:p+=` ${x}`,d.x=e.x,d.y=i,d.width=e.width,d.height=e.height,d.class=p,d.rx=3,d.ry=3,d.name=e.name;let g=b(h,d);if(e.rectData=d,e.properties?.icon){let t=e.properties.icon.trim();"@"===t.charAt(0)?(0,r.CP)(h,d.x+d.width-20,d.y+10,t.substr(1)):(0,r.aC)(h,d.x+d.width-20,d.y+10,t)}W(a,(0,o.Wi)(e.description))(e.description,h,d.x,d.y,d.width,d.height,{class:"actor actor-box"},a);let u=e.height;if(g.node){let t=g.node().getBBox();e.height=t.height,u=t.height}return u},"drawActorTypeParticipant"),k=(0,o.K2)(function(t,e,a,s){let i=s?e.stopy:e.starty,n=e.x+e.width/2,c=i+80,l=t.append("g").lower();s||(L++,l.append("line").attr("id","actor"+L).attr("x1",n).attr("y1",c).attr("x2",n).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",e.name),e.actorCnt=L);let h=t.append("g"),d=m;s?d+=` ${y}`:d+=` ${x}`,h.attr("class",d),h.attr("name",e.name);let p=(0,r.PB)();p.x=e.x,p.y=i,p.fill="#eaeaea",p.width=e.width,p.height=e.height,p.class="actor",p.rx=3,p.ry=3,h.append("line").attr("id","actor-man-torso"+L).attr("x1",n).attr("y1",i+25).attr("x2",n).attr("y2",i+45),h.append("line").attr("id","actor-man-arms"+L).attr("x1",n-18).attr("y1",i+33).attr("x2",n+18).attr("y2",i+33),h.append("line").attr("x1",n-18).attr("y1",i+60).attr("x2",n).attr("y2",i+45),h.append("line").attr("x1",n).attr("y1",i+45).attr("x2",n+18-2).attr("y2",i+60);let g=h.append("circle");return g.attr("cx",e.x+e.width/2),g.attr("cy",i+10),g.attr("r",15),g.attr("width",e.width),g.attr("height",e.height),e.height=h.node().getBBox().height,W(a,(0,o.Wi)(e.description))(e.description,h,p.x,p.y+35,p.width,p.height,{class:`actor ${m}`},a),e.height},"drawActorTypeActor"),N=(0,o.K2)(async function(t,e,a,r){switch(e.type){case"actor":return await k(t,e,a,r);case"participant":return await _(t,e,a,r)}},"drawActor"),v=(0,o.K2)(function(t,e,a){let r=t.append("g");O(r,e),e.name&&W(a)(e.name,r,e.x,e.y+(e.textMaxHeight||0)/2,e.width,0,{class:"text"},a),r.lower()},"drawBox"),A=(0,o.K2)(function(t){return t.append("g")},"anchorElement"),M=(0,o.K2)(function(t,e,a,s,i){let o=(0,r.PB)(),n=e.anchored;o.x=e.startx,o.y=e.starty,o.class="activation"+i%3,o.width=e.stopx-e.startx,o.height=a-e.starty,b(n,o)},"drawActivation"),D=(0,o.K2)(async function(t,e,a,s){let{boxMargin:i,boxTextMargin:n,labelBoxHeight:c,labelBoxWidth:l,messageFontFamily:h,messageFontSize:d,messageFontWeight:p}=s,g=t.append("g"),u=(0,o.K2)(function(t,e,a,r){return g.append("line").attr("x1",t).attr("y1",e).attr("x2",a).attr("y2",r).attr("class","loopLine")},"drawLoopLine");u(e.startx,e.starty,e.stopx,e.starty),u(e.stopx,e.starty,e.stopx,e.stopy),u(e.startx,e.stopy,e.stopx,e.stopy),u(e.startx,e.starty,e.startx,e.stopy),void 0!==e.sections&&e.sections.forEach(function(t){u(e.startx,t.y,e.stopx,t.y).style("stroke-dasharray","3, 3")});let x=(0,r.HT)();x.text=a,x.x=e.startx,x.y=e.starty,x.fontFamily=h,x.fontSize=d,x.fontWeight=p,x.anchor="middle",x.valign="middle",x.tspan=!1,x.width=l||50,x.height=c||20,x.textMargin=n,x.class="labelText",I(g,x),(x=V()).text=e.title,x.x=e.startx+l/2+(e.stopx-e.startx)/2,x.y=e.starty+i+n,x.anchor="middle",x.valign="middle",x.textMargin=n,x.class="loopText",x.fontFamily=h,x.fontSize=d,x.fontWeight=p,x.wrap=!0;let y=(0,o.Wi)(x.text)?await f(g,x,e):w(g,x);if(void 0!==e.sectionTitles){for(let[t,a]of Object.entries(e.sectionTitles))if(a.message){x.text=a.message,x.x=e.startx+(e.stopx-e.startx)/2,x.y=e.sections[t].y+i+n,x.class="loopText",x.anchor="middle",x.valign="middle",x.tspan=!1,x.fontFamily=h,x.fontSize=d,x.fontWeight=p,x.wrap=e.wrap,(0,o.Wi)(x.text)?(e.starty=e.sections[t].y,await f(g,x,e)):w(g,x);let r=Math.round(y.map(t=>(t._groups||t)[0][0].getBBox().height).reduce((t,e)=>t+e));e.sections[t].height+=r-(i+n)}}return e.height=Math.round(e.stopy-e.starty),g},"drawLoop"),O=(0,o.K2)(function(t,e){(0,r.lC)(t,e)},"drawBackgroundRect"),S=(0,o.K2)(function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},"insertDatabaseIcon"),R=(0,o.K2)(function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},"insertComputerIcon"),Y=(0,o.K2)(function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")},"insertClockIcon"),K=(0,o.K2)(function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",7.9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto-start-reverse").append("path").attr("d","M -1 0 L 10 5 L 0 10 z")},"insertArrowHead"),C=(0,o.K2)(function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",15.5).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"insertArrowFilledHead"),B=(0,o.K2)(function(t){t.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)},"insertSequenceNumber"),$=(0,o.K2)(function(t){t.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",4).attr("refY",4.5).append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1pt").attr("d","M 1,2 L 6,7 M 6,2 L 1,7")},"insertArrowCrossHead"),V=(0,o.K2)(function(){return{x:0,y:0,fill:void 0,anchor:void 0,style:"#666",width:void 0,height:void 0,textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0}},"getTextObj"),F=(0,o.K2)(function(){return{x:0,y:0,fill:"#EDF2AE",stroke:"#666",width:100,anchor:"start",height:100,rx:0,ry:0}},"getNoteRect"),W=function(){function t(t,e,a,r,i,o,n){s(e.append("text").attr("x",a+i/2).attr("y",r+o/2+5).style("text-anchor","middle").text(t),n)}function e(t,e,a,r,n,c,l,h){let{actorFontSize:d,actorFontFamily:p,actorFontWeight:g}=h,[u,x]=(0,i.I5)(d),y=t.split(o.Y2.lineBreakRegex);for(let t=0;t<y.length;t++){let i=t*u-u*(y.length-1)/2,o=e.append("text").attr("x",a+n/2).attr("y",r).style("text-anchor","middle").style("font-size",x).style("font-weight",g).style("font-family",p);o.append("tspan").attr("x",a+n/2).attr("dy",i).text(y[t]),o.attr("y",r+c/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),s(o,l)}}function a(t,a,r,i,o,n,c,l){let h=a.append("switch"),d=h.append("foreignObject").attr("x",r).attr("y",i).attr("width",o).attr("height",n).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");d.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t),e(t,h,r,i,o,n,c,l),s(d,c)}async function r(t,a,r,i,n,c,l,h){let d=await (0,o.Dl)(t,(0,o.zj)()),p=a.append("switch"),g=p.append("foreignObject").attr("x",r+n/2-d.width/2).attr("y",i+c/2-d.height/2).attr("width",d.width).attr("height",d.height).append("xhtml:div").style("height","100%").style("width","100%");g.append("div").style("text-align","center").style("vertical-align","middle").html(await (0,o.VJ)(t,(0,o.zj)())),e(t,p,r,i,n,c,l,h),s(g,l)}function s(t,e){for(let a in e)e.hasOwnProperty(a)&&t.attr(a,e[a])}return(0,o.K2)(t,"byText"),(0,o.K2)(e,"byTspan"),(0,o.K2)(a,"byFo"),(0,o.K2)(r,"byKatex"),(0,o.K2)(s,"_setTextAttrs"),function(s,i=!1){return i?r:"fo"===s.textPlacement?a:"old"===s.textPlacement?t:e}}(),q=function(){function t(t,e,a,s,i,o,n){r(e.append("text").attr("x",a).attr("y",s).style("text-anchor","start").text(t),n)}function e(t,e,a,s,i,n,c,l){let{actorFontSize:h,actorFontFamily:d,actorFontWeight:p}=l,g=t.split(o.Y2.lineBreakRegex);for(let t=0;t<g.length;t++){let i=t*h-h*(g.length-1)/2,o=e.append("text").attr("x",a).attr("y",s).style("text-anchor","start").style("font-size",h).style("font-weight",p).style("font-family",d);o.append("tspan").attr("x",a).attr("dy",i).text(g[t]),o.attr("y",s+n/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),r(o,c)}}function a(t,a,s,i,o,n,c,l){let h=a.append("switch"),d=h.append("foreignObject").attr("x",s).attr("y",i).attr("width",o).attr("height",n).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");d.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(t),e(t,h,s,i,o,n,c,l),r(d,c)}function r(t,e){for(let a in e)e.hasOwnProperty(a)&&t.attr(a,e[a])}return(0,o.K2)(t,"byText"),(0,o.K2)(e,"byTspan"),(0,o.K2)(a,"byFo"),(0,o.K2)(r,"_setTextAttrs"),function(r){return"fo"===r.textPlacement?a:"old"===r.textPlacement?t:e}}(),z={drawRect:b,drawText:w,drawLabel:I,drawActor:N,drawBox:v,drawPopup:T,anchorElement:A,drawActivation:M,drawLoop:D,drawBackgroundRect:O,insertArrowHead:K,insertArrowFilledHead:C,insertSequenceNumber:B,insertArrowCrossHead:$,insertDatabaseIcon:S,insertComputerIcon:R,insertClockIcon:Y,getTextObj:V,getNoteRect:F,fixLifeLineHeights:P,sanitizeUrl:c.J},H={},j={data:{startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},verticalPos:0,sequenceItems:[],activations:[],models:{getHeight:(0,o.K2)(function(){return Math.max.apply(null,0===this.actors.length?[0]:this.actors.map(t=>t.height||0))+(0===this.loops.length?0:this.loops.map(t=>t.height||0).reduce((t,e)=>t+e))+(0===this.messages.length?0:this.messages.map(t=>t.height||0).reduce((t,e)=>t+e))+(0===this.notes.length?0:this.notes.map(t=>t.height||0).reduce((t,e)=>t+e))},"getHeight"),clear:(0,o.K2)(function(){this.actors=[],this.boxes=[],this.loops=[],this.messages=[],this.notes=[]},"clear"),addBox:(0,o.K2)(function(t){this.boxes.push(t)},"addBox"),addActor:(0,o.K2)(function(t){this.actors.push(t)},"addActor"),addLoop:(0,o.K2)(function(t){this.loops.push(t)},"addLoop"),addMessage:(0,o.K2)(function(t){this.messages.push(t)},"addMessage"),addNote:(0,o.K2)(function(t){this.notes.push(t)},"addNote"),lastActor:(0,o.K2)(function(){return this.actors[this.actors.length-1]},"lastActor"),lastLoop:(0,o.K2)(function(){return this.loops[this.loops.length-1]},"lastLoop"),lastMessage:(0,o.K2)(function(){return this.messages[this.messages.length-1]},"lastMessage"),lastNote:(0,o.K2)(function(){return this.notes[this.notes.length-1]},"lastNote"),actors:[],boxes:[],loops:[],messages:[],notes:[]},init:(0,o.K2)(function(){this.sequenceItems=[],this.activations=[],this.models.clear(),this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},this.verticalPos=0,tr((0,o.D7)())},"init"),updateVal:(0,o.K2)(function(t,e,a,r){void 0===t[e]?t[e]=a:t[e]=r(a,t[e])},"updateVal"),updateBounds:(0,o.K2)(function(t,e,a,r){let s=this,i=0;function n(n){return(0,o.K2)(function(o){i++;let c=s.sequenceItems.length-i+1;s.updateVal(o,"starty",e-c*H.boxMargin,Math.min),s.updateVal(o,"stopy",r+c*H.boxMargin,Math.max),s.updateVal(j.data,"startx",t-c*H.boxMargin,Math.min),s.updateVal(j.data,"stopx",a+c*H.boxMargin,Math.max),"activation"!==n&&(s.updateVal(o,"startx",t-c*H.boxMargin,Math.min),s.updateVal(o,"stopx",a+c*H.boxMargin,Math.max),s.updateVal(j.data,"starty",e-c*H.boxMargin,Math.min),s.updateVal(j.data,"stopy",r+c*H.boxMargin,Math.max))},"updateItemBounds")}(0,o.K2)(n,"updateFn"),this.sequenceItems.forEach(n()),this.activations.forEach(n("activation"))},"updateBounds"),insert:(0,o.K2)(function(t,e,a,r){let s=o.Y2.getMin(t,a),i=o.Y2.getMax(t,a),n=o.Y2.getMin(e,r),c=o.Y2.getMax(e,r);this.updateVal(j.data,"startx",s,Math.min),this.updateVal(j.data,"starty",n,Math.min),this.updateVal(j.data,"stopx",i,Math.max),this.updateVal(j.data,"stopy",c,Math.max),this.updateBounds(s,n,i,c)},"insert"),newActivation:(0,o.K2)(function(t,e,a){let r=a.get(t.from),s=ts(t.from).length||0,i=r.x+r.width/2+(s-1)*H.activationWidth/2;this.activations.push({startx:i,starty:this.verticalPos+2,stopx:i+H.activationWidth,stopy:void 0,actor:t.from,anchored:z.anchorElement(e)})},"newActivation"),endActivation:(0,o.K2)(function(t){let e=this.activations.map(function(t){return t.actor}).lastIndexOf(t.from);return this.activations.splice(e,1)[0]},"endActivation"),createLoop:(0,o.K2)(function(t={message:void 0,wrap:!1,width:void 0},e){return{startx:void 0,starty:this.verticalPos,stopx:void 0,stopy:void 0,title:t.message,wrap:t.wrap,width:t.width,height:0,fill:e}},"createLoop"),newLoop:(0,o.K2)(function(t={message:void 0,wrap:!1,width:void 0},e){this.sequenceItems.push(this.createLoop(t,e))},"newLoop"),endLoop:(0,o.K2)(function(){return this.sequenceItems.pop()},"endLoop"),isLoopOverlap:(0,o.K2)(function(){return!!this.sequenceItems.length&&this.sequenceItems[this.sequenceItems.length-1].overlap},"isLoopOverlap"),addSectionToLoop:(0,o.K2)(function(t){let e=this.sequenceItems.pop();e.sections=e.sections||[],e.sectionTitles=e.sectionTitles||[],e.sections.push({y:j.getVerticalPos(),height:0}),e.sectionTitles.push(t),this.sequenceItems.push(e)},"addSectionToLoop"),saveVerticalPos:(0,o.K2)(function(){this.isLoopOverlap()&&(this.savedVerticalPos=this.verticalPos)},"saveVerticalPos"),resetVerticalPos:(0,o.K2)(function(){this.isLoopOverlap()&&(this.verticalPos=this.savedVerticalPos)},"resetVerticalPos"),bumpVerticalPos:(0,o.K2)(function(t){this.verticalPos=this.verticalPos+t,this.data.stopy=o.Y2.getMax(this.data.stopy,this.verticalPos)},"bumpVerticalPos"),getVerticalPos:(0,o.K2)(function(){return this.verticalPos},"getVerticalPos"),getBounds:(0,o.K2)(function(){return{bounds:this.data,models:this.models}},"getBounds")},U=(0,o.K2)(async function(t,e){j.bumpVerticalPos(H.boxMargin),e.height=H.boxMargin,e.starty=j.getVerticalPos();let a=(0,r.PB)();a.x=e.startx,a.y=e.starty,a.width=e.width||H.width,a.class="note";let s=t.append("g"),i=z.drawRect(s,a),n=(0,r.HT)();n.x=e.startx,n.y=e.starty,n.width=a.width,n.dy="1em",n.text=e.message,n.class="noteText",n.fontFamily=H.noteFontFamily,n.fontSize=H.noteFontSize,n.fontWeight=H.noteFontWeight,n.anchor=H.noteAlign,n.textMargin=H.noteMargin,n.valign="center";let c=Math.round(((0,o.Wi)(n.text)?await f(s,n):w(s,n)).map(t=>(t._groups||t)[0][0].getBBox().height).reduce((t,e)=>t+e));i.attr("height",c+2*H.noteMargin),e.height+=c+2*H.noteMargin,j.bumpVerticalPos(c+2*H.noteMargin),e.stopy=e.starty+c+2*H.noteMargin,e.stopx=e.startx+a.width,j.insert(e.startx,e.starty,e.stopx,e.stopy),j.models.addNote(e)},"drawNote"),X=(0,o.K2)(t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight}),"messageFont"),J=(0,o.K2)(t=>({fontFamily:t.noteFontFamily,fontSize:t.noteFontSize,fontWeight:t.noteFontWeight}),"noteFont"),G=(0,o.K2)(t=>({fontFamily:t.actorFontFamily,fontSize:t.actorFontSize,fontWeight:t.actorFontWeight}),"actorFont");async function Z(t,e){let a;j.bumpVerticalPos(10);let{startx:r,stopx:s,message:n}=e,c=o.Y2.splitBreaks(n).length,l=(0,o.Wi)(n),h=l?await (0,o.Dl)(n,(0,o.D7)()):i._K.calculateTextDimensions(n,X(H));if(!l){let t=h.height/c;e.height+=t,j.bumpVerticalPos(t)}let d=h.height-10,p=h.width;if(r===s){a=j.getVerticalPos()+d,H.rightAngles||(d+=H.boxMargin,a=j.getVerticalPos()+d),d+=30;let t=o.Y2.getMax(p/2,H.width/2);j.insert(r-t,j.getVerticalPos()-10+d,s+t,j.getVerticalPos()+30+d)}else d+=H.boxMargin,a=j.getVerticalPos()+d,j.insert(r,a-10,s,a);return j.bumpVerticalPos(d),e.height+=d,e.stopy=e.starty+e.height,j.insert(e.fromBounds,e.starty,e.toBounds,e.stopy),a}(0,o.K2)(Z,"boundMessage");var Q=(0,o.K2)(async function(t,e,a,s){let n;let{startx:c,stopx:l,starty:h,message:d,type:p,sequenceIndex:g,sequenceVisible:u}=e,x=i._K.calculateTextDimensions(d,X(H)),y=(0,r.HT)();y.x=c,y.y=h+10,y.width=l-c,y.class="messageText",y.dy="1em",y.text=d,y.fontFamily=H.messageFontFamily,y.fontSize=H.messageFontSize,y.fontWeight=H.messageFontWeight,y.anchor=H.messageAlign,y.valign="center",y.textMargin=H.wrapPadding,y.tspan=!1,(0,o.Wi)(y.text)?await f(t,y,{startx:c,stopx:l,starty:a}):w(t,y);let m=x.width;c===l?n=H.rightAngles?t.append("path").attr("d",`M  ${c},${a} H ${c+o.Y2.getMax(H.width/2,m/2)} V ${a+25} H ${c}`):t.append("path").attr("d","M "+c+","+a+" C "+(c+60)+","+(a-10)+" "+(c+60)+","+(a+30)+" "+c+","+(a+20)):((n=t.append("line")).attr("x1",c),n.attr("y1",a),n.attr("x2",l),n.attr("y2",a)),p===s.db.LINETYPE.DOTTED||p===s.db.LINETYPE.DOTTED_CROSS||p===s.db.LINETYPE.DOTTED_POINT||p===s.db.LINETYPE.DOTTED_OPEN||p===s.db.LINETYPE.BIDIRECTIONAL_DOTTED?(n.style("stroke-dasharray","3, 3"),n.attr("class","messageLine1")):n.attr("class","messageLine0");let b="";H.arrowMarkerAbsolute&&(b=(b=(b=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search).replace(/\(/g,"\\(")).replace(/\)/g,"\\)")),n.attr("stroke-width",2),n.attr("stroke","none"),n.style("fill","none"),(p===s.db.LINETYPE.SOLID||p===s.db.LINETYPE.DOTTED)&&n.attr("marker-end","url("+b+"#arrowhead)"),(p===s.db.LINETYPE.BIDIRECTIONAL_SOLID||p===s.db.LINETYPE.BIDIRECTIONAL_DOTTED)&&(n.attr("marker-start","url("+b+"#arrowhead)"),n.attr("marker-end","url("+b+"#arrowhead)")),(p===s.db.LINETYPE.SOLID_POINT||p===s.db.LINETYPE.DOTTED_POINT)&&n.attr("marker-end","url("+b+"#filled-head)"),(p===s.db.LINETYPE.SOLID_CROSS||p===s.db.LINETYPE.DOTTED_CROSS)&&n.attr("marker-end","url("+b+"#crosshead)"),(u||H.showSequenceNumbers)&&(n.attr("marker-start","url("+b+"#sequencenumber)"),t.append("text").attr("x",c).attr("y",a+4).attr("font-family","sans-serif").attr("font-size","12px").attr("text-anchor","middle").attr("class","sequenceNumber").text(g))},"drawMessage"),tt=(0,o.K2)(function(t,e,a,r,s,i,n){let c,l=0,h=0,d=0;for(let t of r){let r=e.get(t),i=r.box;c&&c!=i&&(n||j.models.addBox(c),h+=H.boxMargin+c.margin),i&&i!=c&&(n||(i.x=l+h,i.y=s),h+=i.margin),r.width=r.width||H.width,r.height=o.Y2.getMax(r.height||H.height,H.height),r.margin=r.margin||H.actorMargin,d=o.Y2.getMax(d,r.height),a.get(r.name)&&(h+=r.width/2),r.x=l+h,r.starty=j.getVerticalPos(),j.insert(r.x,s,r.x+r.width,r.height),l+=r.width+h,r.box&&(r.box.width=l+i.margin-r.box.x),h=r.margin,c=r.box,j.models.addActor(r)}c&&!n&&j.models.addBox(c),j.bumpVerticalPos(d)},"addActorRenderingData"),te=(0,o.K2)(async function(t,e,a,r){if(r){let r=0;for(let s of(j.bumpVerticalPos(2*H.boxMargin),a)){let a=e.get(s);a.stopy||(a.stopy=j.getVerticalPos());let i=await z.drawActor(t,a,H,!0);r=o.Y2.getMax(r,i)}j.bumpVerticalPos(r+H.boxMargin)}else for(let r of a){let a=e.get(r);await z.drawActor(t,a,H,!1)}},"drawActors"),ta=(0,o.K2)(function(t,e,a,r){let s=0,i=0;for(let o of a){let a=e.get(o),n=th(a),c=z.drawPopup(t,a,n,H,H.forceMenus,r);c.height>s&&(s=c.height),c.width+a.x>i&&(i=c.width+a.x)}return{maxHeight:s,maxWidth:i}},"drawActorsPopup"),tr=(0,o.K2)(function(t){(0,o.hH)(H,t),t.fontFamily&&(H.actorFontFamily=H.noteFontFamily=H.messageFontFamily=t.fontFamily),t.fontSize&&(H.actorFontSize=H.noteFontSize=H.messageFontSize=t.fontSize),t.fontWeight&&(H.actorFontWeight=H.noteFontWeight=H.messageFontWeight=t.fontWeight)},"setConf"),ts=(0,o.K2)(function(t){return j.activations.filter(function(e){return e.actor===t})},"actorActivations"),ti=(0,o.K2)(function(t,e){let a=e.get(t),r=ts(t);return[r.reduce(function(t,e){return o.Y2.getMin(t,e.startx)},a.x+a.width/2-1),r.reduce(function(t,e){return o.Y2.getMax(t,e.stopx)},a.x+a.width/2+1)]},"activationBounds");function to(t,e,a,r,s){j.bumpVerticalPos(a);let n=r;if(e.id&&e.message&&t[e.id]){let a=t[e.id].width,s=X(H);e.message=i._K.wrapLabel(`[${e.message}]`,a-2*H.wrapPadding,s),e.width=a,e.wrap=!0;let c=i._K.calculateTextDimensions(e.message,s),l=o.Y2.getMax(c.height,H.labelBoxHeight);n=r+l,o.Rm.debug(`${l} - ${e.message}`)}s(e),j.bumpVerticalPos(n)}function tn(t,e,a,r,s,i,n){function c(a,r){a.x<s.get(t.from).x?(j.insert(e.stopx-r,e.starty,e.startx,e.stopy+a.height/2+H.noteMargin),e.stopx=e.stopx+r):(j.insert(e.startx,e.starty,e.stopx+r,e.stopy+a.height/2+H.noteMargin),e.stopx=e.stopx-r)}function l(a,r){a.x<s.get(t.to).x?(j.insert(e.startx-r,e.starty,e.stopx,e.stopy+a.height/2+H.noteMargin),e.startx=e.startx+r):(j.insert(e.stopx,e.starty,e.startx+r,e.stopy+a.height/2+H.noteMargin),e.startx=e.startx-r)}if((0,o.K2)(c,"receiverAdjustment"),(0,o.K2)(l,"senderAdjustment"),i.get(t.to)==r){let e=s.get(t.to),r="actor"==e.type?21:e.width/2+3;c(e,r),e.starty=a-e.height/2,j.bumpVerticalPos(e.height/2)}else if(n.get(t.from)==r){let e=s.get(t.from);if(H.mirrorActors){let t="actor"==e.type?18:e.width/2;l(e,t)}e.stopy=a-e.height/2,j.bumpVerticalPos(e.height/2)}else if(n.get(t.to)==r){let e=s.get(t.to);if(H.mirrorActors){let t="actor"==e.type?21:e.width/2+3;c(e,t)}e.stopy=a-e.height/2,j.bumpVerticalPos(e.height/2)}}(0,o.K2)(to,"adjustLoopHeightForWrap"),(0,o.K2)(tn,"adjustCreatedDestroyedData");var tc=(0,o.K2)(async function(t,e,a,r){let s;let{securityLevel:i,sequence:c}=(0,o.D7)();H=c,"sandbox"===i&&(s=(0,n.Ltv)("#i"+e));let l="sandbox"===i?(0,n.Ltv)(s.nodes()[0].contentDocument.body):(0,n.Ltv)("body"),h="sandbox"===i?s.nodes()[0].contentDocument:document;j.init(),o.Rm.debug(r.db);let d="sandbox"===i?l.select(`[id="${e}"]`):(0,n.Ltv)(`[id="${e}"]`),p=r.db.getActors(),g=r.db.getCreatedActors(),u=r.db.getDestroyedActors(),x=r.db.getBoxes(),y=r.db.getActorKeys(),m=r.db.getMessages(),b=r.db.getDiagramTitle(),T=r.db.hasAtLeastOneBox(),E=r.db.hasAtLeastOneBoxWithTitle(),f=await tl(p,m,r);if(H.height=await td(p,f,x),z.insertComputerIcon(d),z.insertDatabaseIcon(d),z.insertClockIcon(d),T&&(j.bumpVerticalPos(H.boxMargin),E&&j.bumpVerticalPos(x[0].textMaxHeight)),!0===H.hideUnusedParticipants){let t=new Set;m.forEach(e=>{t.add(e.from),t.add(e.to)}),y=y.filter(e=>t.has(e))}tt(d,p,g,y,0,m,!1);let w=await tu(m,p,f,r);function I(t,e){let a=j.endActivation(t);a.starty+18>e&&(a.starty=e-6,e+=12),z.drawActivation(d,a,e,H,ts(t.from).length),j.insert(a.startx,e-10,a.stopx,e)}z.insertArrowHead(d),z.insertArrowCrossHead(d),z.insertArrowFilledHead(d),z.insertSequenceNumber(d),(0,o.K2)(I,"activeEnd");let L=1,_=1,k=[],N=[],v=0;for(let t of m){let e,a,s;switch(t.type){case r.db.LINETYPE.NOTE:j.resetVerticalPos(),a=t.noteModel,await U(d,a);break;case r.db.LINETYPE.ACTIVE_START:j.newActivation(t,d,p);break;case r.db.LINETYPE.ACTIVE_END:I(t,j.getVerticalPos());break;case r.db.LINETYPE.LOOP_START:to(w,t,H.boxMargin,H.boxMargin+H.boxTextMargin,t=>j.newLoop(t));break;case r.db.LINETYPE.LOOP_END:e=j.endLoop(),await z.drawLoop(d,e,"loop",H),j.bumpVerticalPos(e.stopy-j.getVerticalPos()),j.models.addLoop(e);break;case r.db.LINETYPE.RECT_START:to(w,t,H.boxMargin,H.boxMargin,t=>j.newLoop(void 0,t.message));break;case r.db.LINETYPE.RECT_END:e=j.endLoop(),N.push(e),j.models.addLoop(e),j.bumpVerticalPos(e.stopy-j.getVerticalPos());break;case r.db.LINETYPE.OPT_START:to(w,t,H.boxMargin,H.boxMargin+H.boxTextMargin,t=>j.newLoop(t));break;case r.db.LINETYPE.OPT_END:e=j.endLoop(),await z.drawLoop(d,e,"opt",H),j.bumpVerticalPos(e.stopy-j.getVerticalPos()),j.models.addLoop(e);break;case r.db.LINETYPE.ALT_START:to(w,t,H.boxMargin,H.boxMargin+H.boxTextMargin,t=>j.newLoop(t));break;case r.db.LINETYPE.ALT_ELSE:to(w,t,H.boxMargin+H.boxTextMargin,H.boxMargin,t=>j.addSectionToLoop(t));break;case r.db.LINETYPE.ALT_END:e=j.endLoop(),await z.drawLoop(d,e,"alt",H),j.bumpVerticalPos(e.stopy-j.getVerticalPos()),j.models.addLoop(e);break;case r.db.LINETYPE.PAR_START:case r.db.LINETYPE.PAR_OVER_START:to(w,t,H.boxMargin,H.boxMargin+H.boxTextMargin,t=>j.newLoop(t)),j.saveVerticalPos();break;case r.db.LINETYPE.PAR_AND:to(w,t,H.boxMargin+H.boxTextMargin,H.boxMargin,t=>j.addSectionToLoop(t));break;case r.db.LINETYPE.PAR_END:e=j.endLoop(),await z.drawLoop(d,e,"par",H),j.bumpVerticalPos(e.stopy-j.getVerticalPos()),j.models.addLoop(e);break;case r.db.LINETYPE.AUTONUMBER:L=t.message.start||L,_=t.message.step||_,t.message.visible?r.db.enableSequenceNumbers():r.db.disableSequenceNumbers();break;case r.db.LINETYPE.CRITICAL_START:to(w,t,H.boxMargin,H.boxMargin+H.boxTextMargin,t=>j.newLoop(t));break;case r.db.LINETYPE.CRITICAL_OPTION:to(w,t,H.boxMargin+H.boxTextMargin,H.boxMargin,t=>j.addSectionToLoop(t));break;case r.db.LINETYPE.CRITICAL_END:e=j.endLoop(),await z.drawLoop(d,e,"critical",H),j.bumpVerticalPos(e.stopy-j.getVerticalPos()),j.models.addLoop(e);break;case r.db.LINETYPE.BREAK_START:to(w,t,H.boxMargin,H.boxMargin+H.boxTextMargin,t=>j.newLoop(t));break;case r.db.LINETYPE.BREAK_END:e=j.endLoop(),await z.drawLoop(d,e,"break",H),j.bumpVerticalPos(e.stopy-j.getVerticalPos()),j.models.addLoop(e);break;default:try{(s=t.msgModel).starty=j.getVerticalPos(),s.sequenceIndex=L,s.sequenceVisible=r.db.showSequenceNumbers();let e=await Z(d,s);tn(t,s,e,v,p,g,u),k.push({messageModel:s,lineStartY:e}),j.models.addMessage(s)}catch(t){o.Rm.error("error while drawing message",t)}}[r.db.LINETYPE.SOLID_OPEN,r.db.LINETYPE.DOTTED_OPEN,r.db.LINETYPE.SOLID,r.db.LINETYPE.DOTTED,r.db.LINETYPE.SOLID_CROSS,r.db.LINETYPE.DOTTED_CROSS,r.db.LINETYPE.SOLID_POINT,r.db.LINETYPE.DOTTED_POINT,r.db.LINETYPE.BIDIRECTIONAL_SOLID,r.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type)&&(L+=_),v++}for(let t of(o.Rm.debug("createdActors",g),o.Rm.debug("destroyedActors",u),await te(d,p,y,!1),k))await Q(d,t.messageModel,t.lineStartY,r);for(let t of(H.mirrorActors&&await te(d,p,y,!0),N.forEach(t=>z.drawBackgroundRect(d,t)),P(d,p,y,H),j.models.boxes))t.height=j.getVerticalPos()-t.y,j.insert(t.x,t.y,t.x+t.width,t.height),t.startx=t.x,t.starty=t.y,t.stopx=t.startx+t.width,t.stopy=t.starty+t.height,t.stroke="rgb(0,0,0, 0.5)",z.drawBox(d,t,H);T&&j.bumpVerticalPos(H.boxMargin);let A=ta(d,p,y,h),{bounds:M}=j.getBounds();void 0===M.startx&&(M.startx=0),void 0===M.starty&&(M.starty=0),void 0===M.stopx&&(M.stopx=0),void 0===M.stopy&&(M.stopy=0);let D=M.stopy-M.starty;D<A.maxHeight&&(D=A.maxHeight);let O=D+2*H.diagramMarginY;H.mirrorActors&&(O=O-H.boxMargin+H.bottomMarginAdj);let S=M.stopx-M.startx;S<A.maxWidth&&(S=A.maxWidth);let R=S+2*H.diagramMarginX;b&&d.append("text").text(b).attr("x",(M.stopx-M.startx)/2-2*H.diagramMarginX).attr("y",-25),(0,o.a$)(d,O,R,H.useMaxWidth);let Y=40*!!b;d.attr("viewBox",M.startx-H.diagramMarginX+" -"+(H.diagramMarginY+Y)+" "+R+" "+(O+Y)),o.Rm.debug("models:",j.models)},"draw");async function tl(t,e,a){let r={};for(let s of e)if(t.get(s.to)&&t.get(s.from)){let e=t.get(s.to);if(s.placement===a.db.PLACEMENT.LEFTOF&&!e.prevActor||s.placement===a.db.PLACEMENT.RIGHTOF&&!e.nextActor)continue;let n=void 0!==s.placement,c=!n,l=n?J(H):X(H),h=s.wrap?i._K.wrapLabel(s.message,H.width-2*H.wrapPadding,l):s.message,d=((0,o.Wi)(h)?await (0,o.Dl)(s.message,(0,o.D7)()):i._K.calculateTextDimensions(h,l)).width+2*H.wrapPadding;c&&s.from===e.nextActor?r[s.to]=o.Y2.getMax(r[s.to]||0,d):c&&s.from===e.prevActor?r[s.from]=o.Y2.getMax(r[s.from]||0,d):c&&s.from===s.to?(r[s.from]=o.Y2.getMax(r[s.from]||0,d/2),r[s.to]=o.Y2.getMax(r[s.to]||0,d/2)):s.placement===a.db.PLACEMENT.RIGHTOF?r[s.from]=o.Y2.getMax(r[s.from]||0,d):s.placement===a.db.PLACEMENT.LEFTOF?r[e.prevActor]=o.Y2.getMax(r[e.prevActor]||0,d):s.placement===a.db.PLACEMENT.OVER&&(e.prevActor&&(r[e.prevActor]=o.Y2.getMax(r[e.prevActor]||0,d/2)),e.nextActor&&(r[s.from]=o.Y2.getMax(r[s.from]||0,d/2)))}return o.Rm.debug("maxMessageWidthPerActor:",r),r}(0,o.K2)(tl,"getMaxMessageWidthPerActor");var th=(0,o.K2)(function(t){let e=0,a=G(H);for(let r in t.links){let t=i._K.calculateTextDimensions(r,a).width+2*H.wrapPadding+2*H.boxMargin;e<t&&(e=t)}return e},"getRequiredPopupWidth");async function td(t,e,a){let r=0;for(let e of t.keys()){let a=t.get(e);a.wrap&&(a.description=i._K.wrapLabel(a.description,H.width-2*H.wrapPadding,G(H)));let s=(0,o.Wi)(a.description)?await (0,o.Dl)(a.description,(0,o.D7)()):i._K.calculateTextDimensions(a.description,G(H));a.width=a.wrap?H.width:o.Y2.getMax(H.width,s.width+2*H.wrapPadding),a.height=a.wrap?o.Y2.getMax(s.height,H.height):H.height,r=o.Y2.getMax(r,a.height)}for(let a in e){let r=t.get(a);if(!r)continue;let s=t.get(r.nextActor);if(!s){let t=e[a]+H.actorMargin-r.width/2;r.margin=o.Y2.getMax(t,H.actorMargin);continue}let i=e[a]+H.actorMargin-r.width/2-s.width/2;r.margin=o.Y2.getMax(i,H.actorMargin)}let s=0;return a.forEach(e=>{let a=X(H),r=e.actorKeys.reduce((e,a)=>e+=t.get(a).width+(t.get(a).margin||0),0);r-=2*H.boxTextMargin,e.wrap&&(e.name=i._K.wrapLabel(e.name,r-2*H.wrapPadding,a));let n=i._K.calculateTextDimensions(e.name,a);s=o.Y2.getMax(n.height,s);let c=o.Y2.getMax(r,n.width+2*H.wrapPadding);if(e.margin=H.boxTextMargin,r<c){let t=(c-r)/2;e.margin+=t}}),a.forEach(t=>t.textMaxHeight=s),o.Y2.getMax(r,H.height)}(0,o.K2)(td,"calculateActorMargins");var tp=(0,o.K2)(async function(t,e,a){let r=e.get(t.from),s=e.get(t.to),n=r.x,c=s.x,l=t.wrap&&t.message,h=(0,o.Wi)(t.message)?await (0,o.Dl)(t.message,(0,o.D7)()):i._K.calculateTextDimensions(l?i._K.wrapLabel(t.message,H.width,J(H)):t.message,J(H)),d={width:l?H.width:o.Y2.getMax(H.width,h.width+2*H.noteMargin),height:0,startx:r.x,stopx:0,starty:0,stopy:0,message:t.message};return t.placement===a.db.PLACEMENT.RIGHTOF?(d.width=l?o.Y2.getMax(H.width,h.width):o.Y2.getMax(r.width/2+s.width/2,h.width+2*H.noteMargin),d.startx=n+(r.width+H.actorMargin)/2):t.placement===a.db.PLACEMENT.LEFTOF?(d.width=l?o.Y2.getMax(H.width,h.width+2*H.noteMargin):o.Y2.getMax(r.width/2+s.width/2,h.width+2*H.noteMargin),d.startx=n-d.width+(r.width-H.actorMargin)/2):t.to===t.from?(h=i._K.calculateTextDimensions(l?i._K.wrapLabel(t.message,o.Y2.getMax(H.width,r.width),J(H)):t.message,J(H)),d.width=l?o.Y2.getMax(H.width,r.width):o.Y2.getMax(r.width,H.width,h.width+2*H.noteMargin),d.startx=n+(r.width-d.width)/2):(d.width=Math.abs(n+r.width/2-(c+s.width/2))+H.actorMargin,d.startx=n<c?n+r.width/2-H.actorMargin/2:c+s.width/2-H.actorMargin/2),l&&(d.message=i._K.wrapLabel(t.message,d.width-2*H.wrapPadding,J(H))),o.Rm.debug(`NM:[${d.startx},${d.stopx},${d.starty},${d.stopy}:${d.width},${d.height}=${t.message}]`),d},"buildNoteModel"),tg=(0,o.K2)(function(t,e,a){if(![a.db.LINETYPE.SOLID_OPEN,a.db.LINETYPE.DOTTED_OPEN,a.db.LINETYPE.SOLID,a.db.LINETYPE.DOTTED,a.db.LINETYPE.SOLID_CROSS,a.db.LINETYPE.DOTTED_CROSS,a.db.LINETYPE.SOLID_POINT,a.db.LINETYPE.DOTTED_POINT,a.db.LINETYPE.BIDIRECTIONAL_SOLID,a.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type))return{};let[r,s]=ti(t.from,e),[n,c]=ti(t.to,e),l=r<=n,h=l?s:r,d=l?n:c,p=Math.abs(n-c)>2,g=(0,o.K2)(t=>l?-t:t,"adjustValue");t.from===t.to?d=h:(t.activate&&!p&&(d+=g(H.activationWidth/2-1)),[a.db.LINETYPE.SOLID_OPEN,a.db.LINETYPE.DOTTED_OPEN].includes(t.type)||(d+=g(3)),[a.db.LINETYPE.BIDIRECTIONAL_SOLID,a.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(t.type)&&(h-=g(3)));let u=[r,s,n,c],x=Math.abs(h-d);t.wrap&&t.message&&(t.message=i._K.wrapLabel(t.message,o.Y2.getMax(x+2*H.wrapPadding,H.width),X(H)));let y=i._K.calculateTextDimensions(t.message,X(H));return{width:o.Y2.getMax(t.wrap?0:y.width+2*H.wrapPadding,x+2*H.wrapPadding,H.width),height:0,startx:h,stopx:d,starty:0,stopy:0,message:t.message,type:t.type,wrap:t.wrap,fromBounds:Math.min.apply(null,u),toBounds:Math.max.apply(null,u)}},"buildMessageModel"),tu=(0,o.K2)(async function(t,e,a,r){let s,i,n;let c={},l=[];for(let a of t){switch(a.type){case r.db.LINETYPE.LOOP_START:case r.db.LINETYPE.ALT_START:case r.db.LINETYPE.OPT_START:case r.db.LINETYPE.PAR_START:case r.db.LINETYPE.PAR_OVER_START:case r.db.LINETYPE.CRITICAL_START:case r.db.LINETYPE.BREAK_START:l.push({id:a.id,msg:a.message,from:Number.MAX_SAFE_INTEGER,to:Number.MIN_SAFE_INTEGER,width:0});break;case r.db.LINETYPE.ALT_ELSE:case r.db.LINETYPE.PAR_AND:case r.db.LINETYPE.CRITICAL_OPTION:a.message&&(c[(s=l.pop()).id]=s,c[a.id]=s,l.push(s));break;case r.db.LINETYPE.LOOP_END:case r.db.LINETYPE.ALT_END:case r.db.LINETYPE.OPT_END:case r.db.LINETYPE.PAR_END:case r.db.LINETYPE.CRITICAL_END:case r.db.LINETYPE.BREAK_END:c[(s=l.pop()).id]=s;break;case r.db.LINETYPE.ACTIVE_START:{let t=e.get(a.from?a.from:a.to.actor),r=ts(a.from?a.from:a.to.actor).length,s=t.x+t.width/2+(r-1)*H.activationWidth/2,i={startx:s,stopx:s+H.activationWidth,actor:a.from,enabled:!0};j.activations.push(i)}break;case r.db.LINETYPE.ACTIVE_END:{let t=j.activations.map(t=>t.actor).lastIndexOf(a.from);j.activations.splice(t,1).splice(0,1)}}void 0!==a.placement?(i=await tp(a,e,r),a.noteModel=i,l.forEach(t=>{(s=t).from=o.Y2.getMin(s.from,i.startx),s.to=o.Y2.getMax(s.to,i.startx+i.width),s.width=o.Y2.getMax(s.width,Math.abs(s.from-s.to))-H.labelBoxWidth})):(n=tg(a,e,r),a.msgModel=n,n.startx&&n.stopx&&l.length>0&&l.forEach(t=>{if(s=t,n.startx===n.stopx){let t=e.get(a.from),r=e.get(a.to);s.from=o.Y2.getMin(t.x-n.width/2,t.x-t.width/2,s.from),s.to=o.Y2.getMax(r.x+n.width/2,r.x+t.width/2,s.to),s.width=o.Y2.getMax(s.width,Math.abs(s.to-s.from))-H.labelBoxWidth}else s.from=o.Y2.getMin(n.startx,s.from),s.to=o.Y2.getMax(n.stopx,s.to),s.width=o.Y2.getMax(s.width,n.width)-H.labelBoxWidth}))}return j.activations=[],o.Rm.debug("Loop type widths:",c),c},"calculateLoopBounds"),tx={parser:l,get db(){return new g},renderer:{bounds:j,drawActors:te,drawActorsPopup:ta,setConf:tr,draw:tc},styles:u,init:(0,o.K2)(t=>{t.sequence||(t.sequence={}),t.wrap&&(t.sequence.wrap=t.wrap,(0,o.XV)({sequence:{wrap:t.wrap}}))},"init")}},92356:(t,e,a)=>{a.d(e,{m:()=>s});var r=a(77466),s=class{constructor(t){this.init=t,this.records=this.init()}static{(0,r.K2)(this,"ImperativeState")}reset(){this.records=this.init()}}}};