"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1455],{31886:(t,e,r)=>{r.r(e),r.d(e,{authApi:()=>s,blueprintApi:()=>d,chapterApi:()=>f,chatApi:()=>u,default:()=>A,documentApi:()=>i,flashcardsApi:()=>h,flowchartApi:()=>p,generateChapters:()=>E,generateFlashcards:()=>b,generateFlowchart:()=>_,generateQuiz:()=>v,performanceApi:()=>l,processBlueprint:()=>g,quizApi:()=>y,summaryApi:()=>m,timeTrackingApi:()=>w});var a=r(23464),n=r(49509);let o=n.env.NEXT_PUBLIC_API_URL||"http://localhost:8000/api",c=a.A.create({baseURL:o});c.interceptors.request.use(t=>{let e=localStorage.getItem("token");return e&&(t.headers.Authorization="Token ".concat(e)),t});let s={signIn:async t=>{try{return(await c.post("/users/login/",t)).data}catch(t){throw t}},register:async t=>{try{return(await c.post("/users/register/",t)).data}catch(t){throw t}},verifyOTP:async t=>{try{return(await c.post("/users/verify-otp/",t)).data}catch(t){throw t}},logout:async()=>{try{return(await c.post("/users/logout/")).data}catch(t){throw t}}},i={uploadDocument:async(t,e)=>{let r=new FormData;return r.append("file",t),e&&r.append("group",e.toString()),(await c.post("/documents/upload/",r)).data},getDocuments:async t=>(await c.get("/documents/",{params:t?{group:t}:{}})).data,createGroup:async t=>(await c.post("/documents/groups/",t)).data,getGroups:async()=>(await c.get("/documents/groups/")).data,updateGroup:async(t,e)=>(await c.patch("/documents/groups/".concat(t,"/"),e)).data,deleteGroup:async t=>(await c.delete("/documents/groups/".concat(t,"/"))).data,moveDocument:async(t,e)=>(await c.patch("/documents/".concat(t,"/"),{group:e})).data,getDocumentStatus:async t=>{try{return(await c.get("/documents/".concat(t,"/"))).data}catch(t){throw t}},getDocumentDetails:async t=>{try{return(await c.get("/documents/".concat(t,"/"))).data}catch(t){throw t}},getDocumentFileUrl:t=>"".concat(o,"/documents/").concat(t,"/file/"),getDocumentFileBlob:async t=>{try{return(await c.get("/documents/".concat(t,"/file/"),{responseType:"blob"})).data}catch(t){throw t}}},u={sendMessage:async function(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"openai";try{return(await c.post("/chat/message/",{message:t,document_id:e,model:r})).data}catch(t){throw t}},getHistory:async()=>{try{return(await c.get("/chat/history/")).data}catch(t){throw t}}},l={createPerformance:async t=>{try{return(await c.post("/users/performance/",t)).data}catch(t){throw t}},getPerformances:async t=>{try{return(await c.get(t?"/users/performance/?document=".concat(t):"/users/performance/")).data}catch(t){throw t}},getStudentPerformances:async t=>{try{return(await c.get("/users/performance/?student=".concat(t))).data}catch(t){throw t}},getPerformanceStats:async t=>{try{return(await c.get("/users/performance/?document=".concat(t))).data}catch(t){throw t}}},d={getBlueprint:async t=>{try{return(await c.get("/documents/".concat(t,"/blueprint/"))).data}catch(t){throw console.error("Error getting blueprint:",t),t}},uploadBlueprint:async(t,e)=>{let r=new FormData;if(e instanceof File)r.append("file",e);else{let t=new Blob([e],{type:"text/plain"});r.append("file",new File([t],"blueprint.txt",{type:"text/plain"}))}try{return(await c.post("/documents/".concat(t,"/blueprint/"),r,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(e){let t="Failed to upload blueprint";throw e.response&&e.response.data&&e.response.data.error&&(t=e.response.data.error),Error(t)}},generateBlueprint:async function(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"gemini";try{let o=n.env.NEXT_PUBLIC_FASTAPI_URL||"http://localhost:8001",c=localStorage.getItem("token");return(await a.A.post("".concat(o,"/generate-blueprint/").concat(t),{document_id:t,llm_model:r,focus_areas:e},{headers:{Authorization:"Bearer ".concat(c),"Content-Type":"application/json"}})).data}catch(t){throw console.error("Error generating blueprint:",t),t}},getBlueprintTopics:async t=>{try{return(await c.get("/documents/topics/by_document/?document_id=".concat(t))).data}catch(t){throw console.error("Error getting blueprint topics:",t),t}},getBlueprintTopicContent:async t=>{try{return(await c.get("/documents/topics/".concat(t,"/content_details/"))).data}catch(t){throw console.error("Error getting topic content:",t),t}}},g=async function(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"openai";try{return(await c.post("/process-blueprint/".concat(t,"/"),{document_id:t,blueprint_text:e,llm_model:r})).data}catch(t){throw console.error("Error processing blueprint:",t),t}},h={getFlashcards:async t=>{try{return(await c.get("/documents/flashcards/".concat(t,"/"))).data}catch(t){throw console.error("Error getting flashcards:",t),t}},generateFlashcards:async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"gemini";try{return(await c.post("/api/documents/flashcards/generate/",{document_id:t,num_flashcards:e,llm_model:r,force:!0})).data}catch(t){throw console.error("Error generating flashcards:",t),t}},generateFlashcardsEnhanced:async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"gemini";try{let o=n.env.NEXT_PUBLIC_FASTAPI_URL||"http://localhost:8001",c=localStorage.getItem("token");return(await a.A.post("".concat(o,"/generate-flashcards/").concat(t),{document_id:t,llm_model:r,num_flashcards:e},{headers:{Authorization:"Bearer ".concat(c),"Content-Type":"application/json"}})).data}catch(t){throw console.error("Error generating enhanced flashcards:",t),t}}},p={getFlowchart:async t=>{try{return(await c.get("/documents/flowcharts/".concat(t,"/"))).data}catch(t){throw console.error("Error getting flowchart:",t),t}},generateFlowchart:async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"gemini";try{return(await c.post("/documents/flowcharts/generate/",{document_id:t,llm_model:e,force:!0})).data}catch(t){throw console.error("Error generating flowchart:",t),t}},generateFlowchartEnhanced:async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"gemini";try{let r=n.env.NEXT_PUBLIC_FASTAPI_URL||"http://localhost:8001",o=localStorage.getItem("token");return(await a.A.post("".concat(r,"/generate-flowchart/").concat(t),{document_id:t,llm_model:e},{headers:{Authorization:"Bearer ".concat(o),"Content-Type":"application/json"}})).data}catch(t){throw console.error("Error generating enhanced flowchart:",t),t}}},m={getSummary:async t=>{try{return(await c.get("/api/summaries/".concat(t,"/"))).data}catch(t){throw console.error("Error getting summary:",t),t}},generateSummary:async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"comprehensive",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"gemini";try{let o=n.env.NEXT_PUBLIC_FASTAPI_URL||"http://localhost:8001",c=localStorage.getItem("token");return(await a.A.post("".concat(o,"/generate-summary/").concat(t),{document_id:t,llm_model:r,summary_type:e},{headers:{Authorization:"Bearer ".concat(c),"Content-Type":"application/json"}})).data}catch(t){throw console.error("Error generating summary:",t),t}}},y={getOrGenerateQuiz:async t=>{try{return(await c.get("/documents/".concat(t,"/quiz/"))).data}catch(t){throw console.error("Error getting/generating quiz:",t),t}},generateQuiz:async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"gemini";try{return(await c.post("/documents/".concat(t,"/quiz/"),{num_questions:e,llm_model:r})).data}catch(t){throw console.error("Error generating quiz:",t),t}},generateQuizEnhanced:async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"gemini";try{let o=n.env.NEXT_PUBLIC_FASTAPI_URL||"http://localhost:8001",c=localStorage.getItem("token");return(await a.A.post("".concat(o,"/generate-quiz/").concat(t),{document_id:t,llm_model:r,num_questions:e},{headers:{Authorization:"Bearer ".concat(c),"Content-Type":"application/json"}})).data}catch(t){throw console.error("Error generating enhanced quiz:",t),t}},getQuizzes:async t=>{try{return(await c.get("/quizzes/?document=".concat(t))).data}catch(t){throw console.error("Error fetching quizzes:",t),t}},submitQuizAnswer:async(t,e)=>{try{return(await c.post("/quizzes/".concat(t,"/submit/"),{answers:e})).data}catch(t){throw console.error("Error submitting quiz:",t),t}}},w={startSession:async t=>{try{return(await c.post("/users/time-tracking/start-session/",{document_id:t})).data}catch(t){throw console.error("Error starting time tracking session:",t),t}},updateActivity:async t=>{try{return(await c.post("/users/time-tracking/update-activity/",{session_id:t})).data}catch(t){throw console.error("Error updating session activity:",t),t}},pauseSession:async(t,e)=>{try{return(await c.post("/users/time-tracking/pause-resume/",{session_id:t,action:"pause",notes:e||"Session paused"})).data}catch(t){throw console.error("Error pausing session:",t),t}},resumeSession:async t=>{try{return(await c.post("/users/time-tracking/pause-resume/",{session_id:t,action:"resume"})).data}catch(t){throw console.error("Error resuming session:",t),t}},endSession:async t=>{try{return(await c.post("/users/time-tracking/end-session/",{session_id:t})).data}catch(t){throw console.error("Error ending session:",t),t}},getDocumentStats:async t=>{try{return(await c.get("/users/time-tracking/document/".concat(t,"/stats/"))).data}catch(t){throw console.error("Error getting document stats:",t),t}},getUserOverview:async()=>{try{return(await c.get("/users/time-tracking/overview/")).data}catch(t){throw console.error("Error getting user overview:",t),t}},cleanupSessions:async()=>{try{return(await c.post("/users/time-tracking/cleanup/")).data}catch(t){throw console.error("Error cleaning up sessions:",t),t}}},f={getOrGenerateChapters:async t=>{try{return(await c.get("/documents/".concat(t,"/chapters/"))).data}catch(t){throw console.error("Error getting/generating chapters:",t),t}},generateChapters:async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"gemini";try{return(await c.post("/documents/".concat(t,"/chapters/"),{num_chapters:e,llm_model:r})).data}catch(t){throw console.error("Error generating chapters:",t),t}}},v=y.generateQuiz,_=p.generateFlowchart,b=h.generateFlashcards,E=f.generateChapters,A=c},53999:(t,e,r)=>{r.d(e,{cn:()=>o});var a=r(52596),n=r(39688);function o(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return(0,n.QP)((0,a.$)(e))}},97168:(t,e,r)=>{r.d(e,{$:()=>u});var a=r(95155),n=r(12115),o=r(99708),c=r(74466),s=r(53999);let i=(0,c.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=n.forwardRef((t,e)=>{let{className:r,variant:n,size:c,asChild:u=!1,...l}=t,d=u?o.DX:"button";return(0,a.jsx)(d,{className:(0,s.cn)(i({variant:n,size:c,className:r})),ref:e,...l})});u.displayName="Button"}}]);