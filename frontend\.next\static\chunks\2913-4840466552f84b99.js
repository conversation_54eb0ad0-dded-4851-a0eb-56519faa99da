"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2913],{34964:(e,t,s)=>{s.d(t,{Xi:()=>o,av:()=>d,j7:()=>c,tU:()=>l});var r=s(95155),a=s(12115),i=s(60704),n=s(53999);let l=i.bL,c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(i.B8,{ref:t,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...a})});c.displayName=i.B8.displayName;let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(i.l9,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...a})});o.displayName=i.l9.displayName;let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(i.UC,{ref:t,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...a})});d.displayName=i.UC.displayName},35789:(e,t,s)=>{s.d(t,{b:()=>p});var r=s(95155),a=s(12115),i=s(35695),n=s(9685),l=s(97168),c=s(74783),o=s(34835),d=s(94788),m=s(17951),x=s(62098),u=s(93509),h=s(67133),f=s(99304);function p(e){let{children:t,showQuizButton:s=!1,showUpgradeButton:p=!1,showUserEmail:g=!1,documentId:j=null}=e,[v,N]=(0,a.useState)(!1),{theme:y,setTheme:b}=(0,f.D)(),[w,S]=(0,a.useState)(!1),[A,T]=(0,a.useState)(""),_=(0,i.useRouter)();(0,a.useEffect)(()=>{let e="true"===localStorage.getItem("isLoggedIn"),t=localStorage.getItem("username");e&&t&&(S(!0),T(t))},[]);let k=()=>{localStorage.removeItem("isLoggedIn"),localStorage.removeItem("username"),S(!1),T(""),_.push("/")};return(0,r.jsxs)("div",{className:"flex h-screen bg-background text-foreground overflow-hidden",children:[(0,r.jsx)(n.B,{isOpen:v,setIsOpen:N,isLoggedIn:w,username:A,onLogout:k}),(0,r.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,r.jsxs)("div",{className:"p-4 flex justify-between items-center",children:[(0,r.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-10 w-10 rounded-full",onClick:()=>N(!0),children:(0,r.jsx)(c.A,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[g&&w&&(0,r.jsxs)(h.rI,{children:[(0,r.jsx)(h.ty,{asChild:!0,children:(0,r.jsx)(l.$,{className:"bg-purple-600 hover:bg-purple-700",children:A})}),(0,r.jsx)(h.SQ,{align:"end",children:(0,r.jsxs)(h._2,{onClick:k,children:[(0,r.jsx)(o.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Log out"})]})})]}),s&&j&&(0,r.jsxs)(l.$,{onClick:()=>_.push("/quiz?documentId=".concat(j)),variant:"outline",size:"sm",className:"gap-2",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),"Quiz"]}),p&&(0,r.jsxs)(l.$,{onClick:()=>_.push("/subscription"),className:"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0",size:"sm",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Upgrade"]}),(0,r.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-10 w-10 rounded-full",onClick:()=>b("dark"===y?"light":"dark"),children:"dark"===y?(0,r.jsx)(x.A,{className:"h-5 w-5"}):(0,r.jsx)(u.A,{className:"h-5 w-5"})})]})]}),t]})]})}},53580:(e,t,s)=>{s.d(t,{oR:()=>d}),s(12115);let r=0,a=new Map,i=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),o({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?i(s):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function o(e){c=n(c,e),l.forEach(e=>{e(c)})}function d(e){let{...t}=e,s=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>o({type:"DISMISS_TOAST",toastId:s});return o({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||a()}}}),{id:s,dismiss:a,update:e=>o({type:"UPDATE_TOAST",toast:{...e,id:s}})}}},61139:(e,t,s)=>{function r(e,t){if(0===e.length||0===t.length)return 0;let s=0;return e.forEach((e,r)=>{var a;let i=(null===(a=t[r])||void 0===a?void 0:a.toLowerCase().trim())||"",n=e.answer.toLowerCase().trim();i&&(i.includes(n)||n.includes(i)||function(e,t){let s=e.toLowerCase().split(/\s+/).filter(e=>e.length>2),r=t.toLowerCase().split(/\s+/).filter(e=>e.length>2);return 0===s.length||0===r.length?0:s.filter(e=>r.includes(e)).length/Math.max(s.length,r.length)}(i,n)>.7)&&s++}),s/e.length*100}function a(e){let t=Math.floor(e/60),s=e%60;return t>0?"".concat(t,"m ").concat(s,"s"):"".concat(s,"s")}function i(e){return e>=90?{level:"Excellent",color:"text-foreground",description:"Outstanding performance!"}:e>=80?{level:"Good",color:"text-foreground",description:"Well done!"}:e>=70?{level:"Average",color:"text-muted-foreground",description:"Room for improvement"}:e>=60?{level:"Below Average",color:"text-muted-foreground",description:"Needs more practice"}:{level:"Poor",color:"text-muted-foreground",description:"Requires significant improvement"}}s.d(t,{Fd:()=>i,Uj:()=>a,WS:()=>r})},65749:(e,t,s)=>{s.d(t,{Z:()=>E});var r=s(95155),a=s(12115),i=s(88482),n=s(74466),l=s(53999);let c=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,l.cn)(c({variant:s}),t),...a})}var d=s(97168),m=s(34964),x=s(83540),u=s(99445),h=s(94754),f=s(96025),p=s(16238),g=s(94517),j=s(62341),v=s(3401),N=s(58227),y=s(93504),b=s(21374),w=s(16785),S=s(14186),A=s(69037),T=s(33109),_=s(5040),k=s(69074),C=s(31886),R=s(53580),D=s(61139);function E(e){let{documentId:t,studentId:s}=e,[n,l]=(0,a.useState)([]),[c,E]=(0,a.useState)(!0),[I,z]=(0,a.useState)({averageScore:0,averageTime:0,totalAttempts:0,bestScore:0,improvement:0});(0,a.useEffect)(()=>{O()},[t,s]);let O=async()=>{E(!0);try{let e;if(s){let t=await C.performanceApi.getStudentPerformances(s);e=t.results||t}else if(t){let s=await C.performanceApi.getPerformanceStats(t);e=s.results||s}else{let t=await C.performanceApi.getPerformances();e=t.results||t}l(e),M(e)}catch(e){console.error("Error fetching performances:",e),(0,R.oR)({title:"Error",description:"Failed to load performance data",variant:"destructive"})}finally{E(!1)}},M=e=>{if(0===e.length){z({averageScore:0,averageTime:0,totalAttempts:0,bestScore:0,improvement:0});return}let t=e.reduce((e,t)=>e+t.quiz_score,0),s=e.reduce((e,t)=>e+t.time_taken,0),r=t/e.length,a=s/e.length,i=Math.max(...e.map(e=>e.quiz_score)),n=[...e].sort((e,t)=>new Date(e.created_at).getTime()-new Date(t.created_at).getTime()),l=n.length>1?n[n.length-1].quiz_score-n[0].quiz_score:0;z({averageScore:Math.round(100*r)/100,averageTime:Math.round(a),totalAttempts:e.length,bestScore:Math.round(100*i)/100,improvement:Math.round(100*l)/100})},W=e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),Z=e=>e>=80?"default":e>=60?"secondary":"destructive",B=n.sort((e,t)=>new Date(e.created_at).getTime()-new Date(t.created_at).getTime()).map((e,t)=>({attempt:t+1,score:e.quiz_score,time:Math.round(e.time_taken/60),date:W(e.created_at)}));return c?(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,r.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsx)("div",{className:"h-24 bg-gray-200 rounded"},t))})]})}):(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Performance Dashboard"}),(0,r.jsx)(d.$,{onClick:O,variant:"outline",size:"sm",children:"Refresh"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(w.A,{className:"h-4 w-4 text-blue-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Average Score"}),(0,r.jsxs)("p",{className:"text-2xl font-bold",children:[I.averageScore,"%"]})]})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(S.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Avg Time"}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:(0,D.Uj)(I.averageTime)})]})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(A.A,{className:"h-4 w-4 text-yellow-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Best Score"}),(0,r.jsxs)("p",{className:"text-2xl font-bold",children:[I.bestScore,"%"]})]})]})})}),(0,r.jsx)(i.Zp,{children:(0,r.jsx)(i.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(T.A,{className:"h-4 w-4 text-purple-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Improvement"}),(0,r.jsxs)("p",{className:"text-2xl font-bold ".concat(I.improvement>=0?"text-green-600":"text-red-600"),children:[I.improvement>0?"+":"",I.improvement,"%"]})]})]})})})]}),(0,r.jsxs)(m.tU,{defaultValue:"overview",className:"space-y-4",children:[(0,r.jsxs)(m.j7,{children:[(0,r.jsx)(m.Xi,{value:"overview",children:"Overview"}),(0,r.jsx)(m.Xi,{value:"history",children:"History"}),(0,r.jsx)(m.Xi,{value:"analytics",children:"Analytics"})]}),(0,r.jsx)(m.av,{value:"overview",className:"space-y-4",children:B.length>0&&(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Score Progress"}),(0,r.jsx)(i.BT,{children:"Your quiz scores over time"})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)(x.u,{width:"100%",height:300,children:(0,r.jsxs)(u.Q,{data:B,children:[(0,r.jsx)(h.d,{strokeDasharray:"3 3"}),(0,r.jsx)(f.W,{dataKey:"attempt"}),(0,r.jsx)(p.h,{domain:[0,100]}),(0,r.jsx)(g.m,{formatter:(e,t)=>["score"===t?"".concat(e,"%"):"".concat(e,"m"),"score"===t?"Score":"Time"]}),(0,r.jsx)(j.G,{type:"monotone",dataKey:"score",stroke:"#8884d8",fill:"#8884d8",fillOpacity:.3})]})})})]})}),(0,r.jsx)(m.av,{value:"history",className:"space-y-4",children:(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Quiz History"}),(0,r.jsx)(i.BT,{children:"All your quiz attempts"})]}),(0,r.jsx)(i.Wu,{children:0===n.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(_.A,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"No quiz attempts yet"})]}):(0,r.jsx)("div",{className:"space-y-3",children:n.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(k.A,{className:"h-4 w-4 text-gray-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.document_title}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:W(e.created_at)})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)(o,{variant:Z(e.quiz_score),children:[Math.round(e.quiz_score),"%"]}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:(0,D.Uj)(e.time_taken)})]})]},e.id))})})]})}),(0,r.jsx)(m.av,{value:"analytics",className:"space-y-4",children:B.length>0&&(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{children:"Score Distribution"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)(x.u,{width:"100%",height:250,children:(0,r.jsxs)(v.E,{data:B,children:[(0,r.jsx)(h.d,{strokeDasharray:"3 3"}),(0,r.jsx)(f.W,{dataKey:"attempt"}),(0,r.jsx)(p.h,{domain:[0,100]}),(0,r.jsx)(g.m,{formatter:e=>["".concat(e,"%"),"Score"]}),(0,r.jsx)(N.y,{dataKey:"score",fill:"#8884d8"})]})})})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)(i.ZB,{children:"Time Trends"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsx)(x.u,{width:"100%",height:250,children:(0,r.jsxs)(y.b,{data:B,children:[(0,r.jsx)(h.d,{strokeDasharray:"3 3"}),(0,r.jsx)(f.W,{dataKey:"attempt"}),(0,r.jsx)(p.h,{}),(0,r.jsx)(g.m,{formatter:e=>["".concat(e,"m"),"Time"]}),(0,r.jsx)(b.N,{type:"monotone",dataKey:"time",stroke:"#82ca9d",strokeWidth:2})]})})})]})]})})]})]})}},88482:(e,t,s)=>{s.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>n,aR:()=>l,wL:()=>m});var r=s(95155),a=s(12115),i=s(53999);let n=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});n.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",s),...a})});l.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});c.displayName="CardTitle";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",s),...a})});o.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",s),...a})});d.displayName="CardContent";let m=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",s),...a})});m.displayName="CardFooter"}}]);