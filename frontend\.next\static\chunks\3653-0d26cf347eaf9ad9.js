"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3653],{28577:(e,t,r)=>{r.d(t,{OK:()=>G,bL:()=>K,VM:()=>R,lr:()=>I,LM:()=>Z});var n=r(12115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return n.useCallback(function(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}(...e),e)}r(47650);var i=r(95155),a=Symbol("radix.slottable");function s(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{var r,o,i;let a,s;let{children:u,...c}=e,d=l(n.isValidElement(u)?(s=(a=null===(o=Object.getOwnPropertyDescriptor((r=u).props,"ref"))||void 0===o?void 0:o.get)&&"isReactWarning"in a&&a.isReactWarning)?r.ref:(s=(a=null===(i=Object.getOwnPropertyDescriptor(r,"ref"))||void 0===i?void 0:i.get)&&"isReactWarning"in a&&a.isReactWarning)?r.props.ref:r.props.ref||r.ref:void 0,t);if(n.isValidElement(u)){let e=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=l(...t);return o(...t),n}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(c,u.props);return u.type!==n.Fragment&&(e.ref=d),n.cloneElement(u,e)}return n.Children.count(u)>1?n.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),r=n.forwardRef((e,r)=>{let{children:o,...l}=e,a=n.Children.toArray(o),u=a.find(s);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...l,ref:r,children:o})});return r.displayName="".concat(e,".Slot"),r}(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?r:t,{...l,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),c=globalThis?.document?n.useLayoutEffect:()=>{},d=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[o,l]=n.useState(),i=n.useRef(null),a=n.useRef(e),s=n.useRef("none"),[u,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=f(i.current);s.current="mounted"===u?e:"none"},[u]),c(()=>{let t=i.current,r=a.current;if(r!==e){let n=s.current,o=f(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):r&&n!==o?d("ANIMATION_OUT"):d("UNMOUNT"),a.current=e}},[e,d]),c(()=>{if(o){var e;let t;let r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=f(i.current).includes(e.animationName);if(e.target===o&&n&&(d("ANIMATION_END"),!a.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(s.current=f(i.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:n.useCallback(e=>{i.current=e?getComputedStyle(e):null,l(e)},[])}}(t),i="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),a=l(o.ref,function(e){var t,r;let n=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||o.isPresent?n.cloneElement(i,{ref:a}):null};function f(e){return(null==e?void 0:e.animationName)||"none"}function p(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}d.displayName="Presence";var v=n.createContext(void 0);function h(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}var m="ScrollArea",[w,g]=function(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return o.scopeName=e,[function(t,o){let l=n.createContext(o),a=r.length;r=[...r,o];let s=t=>{let{scope:r,children:o,...s}=t,u=r?.[e]?.[a]||l,c=n.useMemo(()=>s,Object.values(s));return(0,i.jsx)(u.Provider,{value:c,children:o})};return s.displayName=t+"Provider",[s,function(r,i){let s=i?.[e]?.[a]||l,u=n.useContext(s);if(u)return u;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(o,...t)]}(m),[b,y]=w(m),S=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:o="hover",dir:a,scrollHideDelay:s=600,...c}=e,[d,f]=n.useState(null),[p,h]=n.useState(null),[m,w]=n.useState(null),[g,y]=n.useState(null),[S,E]=n.useState(null),[x,C]=n.useState(0),[R,T]=n.useState(0),[N,P]=n.useState(!1),[_,L]=n.useState(!1),A=l(t,e=>f(e)),j=function(e){let t=n.useContext(v);return e||t||"ltr"}(a);return(0,i.jsx)(b,{scope:r,type:o,dir:j,scrollHideDelay:s,scrollArea:d,viewport:p,onViewportChange:h,content:m,onContentChange:w,scrollbarX:g,onScrollbarXChange:y,scrollbarXEnabled:N,onScrollbarXEnabledChange:P,scrollbarY:S,onScrollbarYChange:E,scrollbarYEnabled:_,onScrollbarYEnabledChange:L,onCornerWidthChange:C,onCornerHeightChange:T,children:(0,i.jsx)(u.div,{dir:j,...c,ref:A,style:{position:"relative","--radix-scroll-area-corner-width":x+"px","--radix-scroll-area-corner-height":R+"px",...e.style}})})});S.displayName=m;var E="ScrollAreaViewport",x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:o,nonce:a,...s}=e,c=y(E,r),d=l(t,n.useRef(null),c.onViewportChange);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,i.jsx)(u.div,{"data-radix-scroll-area-viewport":"",...s,ref:d,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,i.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});x.displayName=E;var C="ScrollAreaScrollbar",R=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=y(C,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:s}=l,u="horizontal"===e.orientation;return n.useEffect(()=>(u?a(!0):s(!0),()=>{u?a(!1):s(!1)}),[u,a,s]),"hover"===l.type?(0,i.jsx)(T,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,i.jsx)(N,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,i.jsx)(P,{...o,ref:t,forceMount:r}):"always"===l.type?(0,i.jsx)(_,{...o,ref:t}):null});R.displayName=C;var T=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=y(C,e.__scopeScrollArea),[a,s]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,i.jsx)(d,{present:r||a,children:(0,i.jsx)(P,{"data-state":a?"visible":"hidden",...o,ref:t})})}),N=n.forwardRef((e,t)=>{var r;let{forceMount:o,...l}=e,a=y(C,e.__scopeScrollArea),s="horizontal"===e.orientation,u=B(()=>f("SCROLL_END"),100),[c,f]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},"hidden"));return n.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>f("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,a.scrollHideDelay,f]),n.useEffect(()=>{let e=a.viewport,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(f("SCROLL"),u()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[a.viewport,s,f,u]),(0,i.jsx)(d,{present:o||"hidden"!==c,children:(0,i.jsx)(_,{"data-state":"hidden"===c?"hidden":"visible",...l,ref:t,onPointerEnter:h(e.onPointerEnter,()=>f("POINTER_ENTER")),onPointerLeave:h(e.onPointerLeave,()=>f("POINTER_LEAVE"))})})}),P=n.forwardRef((e,t)=>{let r=y(C,e.__scopeScrollArea),{forceMount:o,...l}=e,[a,s]=n.useState(!1),u="horizontal"===e.orientation,c=B(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(u?e:t)}},10);return q(r.viewport,c),q(r.content,c),(0,i.jsx)(d,{present:o||a,children:(0,i.jsx)(_,{"data-state":a?"visible":"hidden",...l,ref:t})})}),_=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=y(C,e.__scopeScrollArea),a=n.useRef(null),s=n.useRef(0),[u,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=V(u.viewport,u.content),f={...o,sizes:u,onSizesChange:c,hasThumb:!!(d>0&&d<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:e=>s.current=e};function p(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=F(r),l=t||o/2,i=r.scrollbar.paddingStart+l,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),s=r.content-r.viewport;return Y([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,s.current,u,t)}return"horizontal"===r?(0,i.jsx)(L,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&a.current){let e=X(l.viewport.scrollLeft,u,l.dir);a.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===r?(0,i.jsx)(A,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&a.current){let e=X(l.viewport.scrollTop,u);a.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),L=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,s=y(C,e.__scopeScrollArea),[u,c]=n.useState(),d=n.useRef(null),f=l(t,d,s.onScrollbarXChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,i.jsx)(O,{"data-orientation":"horizontal",...a,ref:f,sizes:r,style:{bottom:0,left:"rtl"===s.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===s.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":F(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(s.viewport){let n=s.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&s.viewport&&u&&o({content:s.viewport.scrollWidth,viewport:s.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:k(u.paddingLeft),paddingEnd:k(u.paddingRight)}})}})}),A=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,s=y(C,e.__scopeScrollArea),[u,c]=n.useState(),d=n.useRef(null),f=l(t,d,s.onScrollbarYChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,i.jsx)(O,{"data-orientation":"vertical",...a,ref:f,sizes:r,style:{top:0,right:"ltr"===s.dir?0:void 0,left:"rtl"===s.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":F(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(s.viewport){let n=s.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&s.viewport&&u&&o({content:s.viewport.scrollHeight,viewport:s.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:k(u.paddingTop),paddingEnd:k(u.paddingBottom)}})}})}),[j,D]=w(C),O=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:o,hasThumb:a,onThumbChange:s,onThumbPointerUp:c,onThumbPointerDown:d,onThumbPositionChange:f,onDragScroll:v,onWheelScroll:m,onResize:w,...g}=e,b=y(C,r),[S,E]=n.useState(null),x=l(t,e=>E(e)),R=n.useRef(null),T=n.useRef(""),N=b.viewport,P=o.content-o.viewport,_=p(m),L=p(f),A=B(w,10);function D(e){R.current&&v({x:e.clientX-R.current.left,y:e.clientY-R.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==S?void 0:S.contains(t))&&_(e,P)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[N,S,P,_]),n.useEffect(L,[o,L]),q(S,A),q(b.content,A),(0,i.jsx)(j,{scope:r,scrollbar:S,hasThumb:a,onThumbChange:p(s),onThumbPointerUp:p(c),onThumbPositionChange:L,onThumbPointerDown:p(d),children:(0,i.jsx)(u.div,{...g,ref:x,style:{position:"absolute",...g.style},onPointerDown:h(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),R.current=S.getBoundingClientRect(),T.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),D(e))}),onPointerMove:h(e.onPointerMove,D),onPointerUp:h(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=T.current,b.viewport&&(b.viewport.style.scrollBehavior=""),R.current=null})})})}),M="ScrollAreaThumb",I=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=D(M,e.__scopeScrollArea);return(0,i.jsx)(d,{present:r||o.hasThumb,children:(0,i.jsx)(W,{ref:t,...n})})}),W=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:o,...a}=e,s=y(M,r),c=D(M,r),{onThumbPositionChange:d}=c,f=l(t,e=>c.onThumbChange(e)),p=n.useRef(void 0),v=B(()=>{p.current&&(p.current(),p.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{v(),p.current||(p.current=$(e,d),d())};return d(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,v,d]),(0,i.jsx)(u.div,{"data-state":c.hasThumb?"visible":"hidden",...a,ref:f,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:h(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;c.onThumbPointerDown({x:r,y:n})}),onPointerUp:h(e.onPointerUp,c.onThumbPointerUp)})});I.displayName=M;var U="ScrollAreaCorner",z=n.forwardRef((e,t)=>{let r=y(U,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,i.jsx)(H,{...e,ref:t}):null});z.displayName=U;var H=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...o}=e,l=y(U,r),[a,s]=n.useState(0),[c,d]=n.useState(0),f=!!(a&&c);return q(l.scrollbarX,()=>{var e;let t=(null===(e=l.scrollbarX)||void 0===e?void 0:e.offsetHeight)||0;l.onCornerHeightChange(t),d(t)}),q(l.scrollbarY,()=>{var e;let t=(null===(e=l.scrollbarY)||void 0===e?void 0:e.offsetWidth)||0;l.onCornerWidthChange(t),s(t)}),f?(0,i.jsx)(u.div,{...o,ref:t,style:{width:a,height:c,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function k(e){return e?parseInt(e,10):0}function V(e,t){let r=e/t;return isNaN(r)?0:r}function F(e){let t=V(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function X(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=F(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=function(e,[t,r]){return Math.min(r,Math.max(t,e))}(e,"ltr"===r?[0,i]:[-1*i,0]);return Y([0,i],[0,l-n])(a)}function Y(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var $=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=r.left!==l.left,a=r.top!==l.top;(i||a)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function B(e,t){let r=p(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function q(e,t){let r=p(t);c(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var K=S,Z=x,G=z},40133:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])}}]);