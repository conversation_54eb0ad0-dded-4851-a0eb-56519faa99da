"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4428],{14592:(t,e,s)=>{s.d(e,{A:()=>n,P:()=>r});var i=s(47802),a=s(30685),n=(0,i.K2)((t,e)=>{let s;return"sandbox"===e&&(s=(0,a.Ltv)("#i"+t)),("sandbox"===e?(0,a.Ltv)(s.nodes()[0].contentDocument.body):(0,a.Ltv)("body")).select(`[id="${t}"]`)},"getDiagramElement"),r=(0,i.K2)((t,e,s,a)=>{t.attr("class",s);let{width:n,height:r,x:o,y:c}=u(t,e);(0,i.a$)(t,r,n,a);let h=l(o,c,n,r,e);t.attr("viewBox",h),i.Rm.debug(`viewBox configured: ${h} with padding: ${e}`)},"setupViewPortForSVG"),u=(0,i.K2)((t,e)=>{let s=t.node()?.getBBox()||{width:0,height:0,x:0,y:0};return{width:s.width+2*e,height:s.height+2*e,x:s.x,y:s.y}},"calculateDimensionsWithPadding"),l=(0,i.K2)((t,e,s,i,a)=>`${t-a} ${e-a} ${s} ${i}`,"createViewBox")},44428:(t,e,s)=>{s.d(e,{Lh:()=>m,NM:()=>y,_$:()=>o,tM:()=>C});var i=s(14592),a=s(3566),n=s(11901),r=s(47802),u=s(30685),l=function(){var t=(0,r.K2)(function(t,e,s,i){for(s=s||{},i=t.length;i--;s[t[i]]=e);return s},"o"),e=[1,18],s=[1,19],i=[1,20],a=[1,41],n=[1,42],u=[1,26],l=[1,24],o=[1,25],c=[1,32],h=[1,33],p=[1,34],d=[1,45],A=[1,35],y=[1,36],C=[1,37],g=[1,38],m=[1,27],b=[1,28],E=[1,29],T=[1,30],k=[1,31],f=[1,44],D=[1,46],F=[1,43],B=[1,47],_=[1,9],S=[1,8,9],N=[1,58],L=[1,59],$=[1,60],x=[1,61],O=[1,62],I=[1,63],v=[1,64],w=[1,8,9,41],R=[1,76],P=[1,8,9,12,13,22,39,41,44,66,67,68,69,70,71,72,77,79],K=[1,8,9,12,13,17,20,22,39,41,44,48,58,66,67,68,69,70,71,72,77,79,84,99,101,102],M=[13,58,84,99,101,102],G=[13,58,71,72,84,99,101,102],U=[13,58,66,67,68,69,70,84,99,101,102],Y=[1,98],z=[1,115],Q=[1,107],W=[1,113],X=[1,108],j=[1,109],V=[1,110],q=[1,111],H=[1,112],J=[1,114],Z=[22,58,59,80,84,85,86,87,88,89],tt=[1,8,9,39,41,44],te=[1,8,9,22],ts=[1,143],ti=[1,8,9,59],ta=[1,8,9,22,58,59,80,84,85,86,87,88,89],tn={trace:(0,r.K2)(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mermaidDoc:4,statements:5,graphConfig:6,CLASS_DIAGRAM:7,NEWLINE:8,EOF:9,statement:10,classLabel:11,SQS:12,STR:13,SQE:14,namespaceName:15,alphaNumToken:16,DOT:17,className:18,classLiteralName:19,GENERICTYPE:20,relationStatement:21,LABEL:22,namespaceStatement:23,classStatement:24,memberStatement:25,annotationStatement:26,clickStatement:27,styleStatement:28,cssClassStatement:29,noteStatement:30,classDefStatement:31,direction:32,acc_title:33,acc_title_value:34,acc_descr:35,acc_descr_value:36,acc_descr_multiline_value:37,namespaceIdentifier:38,STRUCT_START:39,classStatements:40,STRUCT_STOP:41,NAMESPACE:42,classIdentifier:43,STYLE_SEPARATOR:44,members:45,CLASS:46,ANNOTATION_START:47,ANNOTATION_END:48,MEMBER:49,SEPARATOR:50,relation:51,NOTE_FOR:52,noteText:53,NOTE:54,CLASSDEF:55,classList:56,stylesOpt:57,ALPHA:58,COMMA:59,direction_tb:60,direction_bt:61,direction_rl:62,direction_lr:63,relationType:64,lineType:65,AGGREGATION:66,EXTENSION:67,COMPOSITION:68,DEPENDENCY:69,LOLLIPOP:70,LINE:71,DOTTED_LINE:72,CALLBACK:73,LINK:74,LINK_TARGET:75,CLICK:76,CALLBACK_NAME:77,CALLBACK_ARGS:78,HREF:79,STYLE:80,CSSCLASS:81,style:82,styleComponent:83,NUM:84,COLON:85,UNIT:86,SPACE:87,BRKT:88,PCT:89,commentToken:90,textToken:91,graphCodeTokens:92,textNoTagsToken:93,TAGSTART:94,TAGEND:95,"==":96,"--":97,DEFAULT:98,MINUS:99,keywords:100,UNICODE_TEXT:101,BQUOTE_STR:102,$accept:0,$end:1},terminals_:{2:"error",7:"CLASS_DIAGRAM",8:"NEWLINE",9:"EOF",12:"SQS",13:"STR",14:"SQE",17:"DOT",20:"GENERICTYPE",22:"LABEL",33:"acc_title",34:"acc_title_value",35:"acc_descr",36:"acc_descr_value",37:"acc_descr_multiline_value",39:"STRUCT_START",41:"STRUCT_STOP",42:"NAMESPACE",44:"STYLE_SEPARATOR",46:"CLASS",47:"ANNOTATION_START",48:"ANNOTATION_END",49:"MEMBER",50:"SEPARATOR",52:"NOTE_FOR",54:"NOTE",55:"CLASSDEF",58:"ALPHA",59:"COMMA",60:"direction_tb",61:"direction_bt",62:"direction_rl",63:"direction_lr",66:"AGGREGATION",67:"EXTENSION",68:"COMPOSITION",69:"DEPENDENCY",70:"LOLLIPOP",71:"LINE",72:"DOTTED_LINE",73:"CALLBACK",74:"LINK",75:"LINK_TARGET",76:"CLICK",77:"CALLBACK_NAME",78:"CALLBACK_ARGS",79:"HREF",80:"STYLE",81:"CSSCLASS",84:"NUM",85:"COLON",86:"UNIT",87:"SPACE",88:"BRKT",89:"PCT",92:"graphCodeTokens",94:"TAGSTART",95:"TAGEND",96:"==",97:"--",98:"DEFAULT",99:"MINUS",100:"keywords",101:"UNICODE_TEXT",102:"BQUOTE_STR"},productions_:[0,[3,1],[3,1],[4,1],[6,4],[5,1],[5,2],[5,3],[11,3],[15,1],[15,3],[15,2],[18,1],[18,3],[18,1],[18,2],[18,2],[18,2],[10,1],[10,2],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,2],[10,2],[10,1],[23,4],[23,5],[38,2],[40,1],[40,2],[40,3],[24,1],[24,3],[24,4],[24,6],[43,2],[43,3],[26,4],[45,1],[45,2],[25,1],[25,2],[25,1],[25,1],[21,3],[21,4],[21,4],[21,5],[30,3],[30,2],[31,3],[56,1],[56,3],[32,1],[32,1],[32,1],[32,1],[51,3],[51,2],[51,2],[51,1],[64,1],[64,1],[64,1],[64,1],[64,1],[65,1],[65,1],[27,3],[27,4],[27,3],[27,4],[27,4],[27,5],[27,3],[27,4],[27,4],[27,5],[27,4],[27,5],[27,5],[27,6],[28,3],[29,3],[57,1],[57,3],[82,1],[82,2],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[90,1],[90,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[93,1],[93,1],[93,1],[93,1],[16,1],[16,1],[16,1],[16,1],[19,1],[53,1]],performAction:(0,r.K2)(function(t,e,s,i,a,n,r){var u=n.length-1;switch(a){case 8:this.$=n[u-1];break;case 9:case 12:case 14:this.$=n[u];break;case 10:case 13:this.$=n[u-2]+"."+n[u];break;case 11:case 15:case 95:this.$=n[u-1]+n[u];break;case 16:case 17:this.$=n[u-1]+"~"+n[u]+"~";break;case 18:i.addRelation(n[u]);break;case 19:n[u-1].title=i.cleanupLabel(n[u]),i.addRelation(n[u-1]);break;case 30:this.$=n[u].trim(),i.setAccTitle(this.$);break;case 31:case 32:this.$=n[u].trim(),i.setAccDescription(this.$);break;case 33:i.addClassesToNamespace(n[u-3],n[u-1]);break;case 34:i.addClassesToNamespace(n[u-4],n[u-1]);break;case 35:this.$=n[u],i.addNamespace(n[u]);break;case 36:case 46:case 59:case 92:this.$=[n[u]];break;case 37:this.$=[n[u-1]];break;case 38:n[u].unshift(n[u-2]),this.$=n[u];break;case 40:i.setCssClass(n[u-2],n[u]);break;case 41:i.addMembers(n[u-3],n[u-1]);break;case 42:i.setCssClass(n[u-5],n[u-3]),i.addMembers(n[u-5],n[u-1]);break;case 43:this.$=n[u],i.addClass(n[u]);break;case 44:this.$=n[u-1],i.addClass(n[u-1]),i.setClassLabel(n[u-1],n[u]);break;case 45:i.addAnnotation(n[u],n[u-2]);break;case 47:n[u].push(n[u-1]),this.$=n[u];break;case 48:case 50:case 51:break;case 49:i.addMember(n[u-1],i.cleanupLabel(n[u]));break;case 52:this.$={id1:n[u-2],id2:n[u],relation:n[u-1],relationTitle1:"none",relationTitle2:"none"};break;case 53:this.$={id1:n[u-3],id2:n[u],relation:n[u-1],relationTitle1:n[u-2],relationTitle2:"none"};break;case 54:this.$={id1:n[u-3],id2:n[u],relation:n[u-2],relationTitle1:"none",relationTitle2:n[u-1]};break;case 55:this.$={id1:n[u-4],id2:n[u],relation:n[u-2],relationTitle1:n[u-3],relationTitle2:n[u-1]};break;case 56:i.addNote(n[u],n[u-1]);break;case 57:i.addNote(n[u]);break;case 58:this.$=n[u-2],i.defineClass(n[u-1],n[u]);break;case 60:this.$=n[u-2].concat([n[u]]);break;case 61:i.setDirection("TB");break;case 62:i.setDirection("BT");break;case 63:i.setDirection("RL");break;case 64:i.setDirection("LR");break;case 65:this.$={type1:n[u-2],type2:n[u],lineType:n[u-1]};break;case 66:this.$={type1:"none",type2:n[u],lineType:n[u-1]};break;case 67:this.$={type1:n[u-1],type2:"none",lineType:n[u]};break;case 68:this.$={type1:"none",type2:"none",lineType:n[u]};break;case 69:this.$=i.relationType.AGGREGATION;break;case 70:this.$=i.relationType.EXTENSION;break;case 71:this.$=i.relationType.COMPOSITION;break;case 72:this.$=i.relationType.DEPENDENCY;break;case 73:this.$=i.relationType.LOLLIPOP;break;case 74:this.$=i.lineType.LINE;break;case 75:this.$=i.lineType.DOTTED_LINE;break;case 76:case 82:this.$=n[u-2],i.setClickEvent(n[u-1],n[u]);break;case 77:case 83:this.$=n[u-3],i.setClickEvent(n[u-2],n[u-1]),i.setTooltip(n[u-2],n[u]);break;case 78:this.$=n[u-2],i.setLink(n[u-1],n[u]);break;case 79:this.$=n[u-3],i.setLink(n[u-2],n[u-1],n[u]);break;case 80:this.$=n[u-3],i.setLink(n[u-2],n[u-1]),i.setTooltip(n[u-2],n[u]);break;case 81:this.$=n[u-4],i.setLink(n[u-3],n[u-2],n[u]),i.setTooltip(n[u-3],n[u-1]);break;case 84:this.$=n[u-3],i.setClickEvent(n[u-2],n[u-1],n[u]);break;case 85:this.$=n[u-4],i.setClickEvent(n[u-3],n[u-2],n[u-1]),i.setTooltip(n[u-3],n[u]);break;case 86:this.$=n[u-3],i.setLink(n[u-2],n[u]);break;case 87:this.$=n[u-4],i.setLink(n[u-3],n[u-1],n[u]);break;case 88:this.$=n[u-4],i.setLink(n[u-3],n[u-1]),i.setTooltip(n[u-3],n[u]);break;case 89:this.$=n[u-5],i.setLink(n[u-4],n[u-2],n[u]),i.setTooltip(n[u-4],n[u-1]);break;case 90:this.$=n[u-2],i.setCssStyle(n[u-1],n[u]);break;case 91:i.setCssClass(n[u-1],n[u]);break;case 93:n[u-2].push(n[u]),this.$=n[u-2]}},"anonymous"),table:[{3:1,4:2,5:3,6:4,7:[1,6],10:5,16:39,18:21,19:40,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,33:e,35:s,37:i,38:22,42:a,43:23,46:n,47:u,49:l,50:o,52:c,54:h,55:p,58:d,60:A,61:y,62:C,63:g,73:m,74:b,76:E,80:T,81:k,84:f,99:D,101:F,102:B},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,3]},t(_,[2,5],{8:[1,48]}),{8:[1,49]},t(S,[2,18],{22:[1,50]}),t(S,[2,20]),t(S,[2,21]),t(S,[2,22]),t(S,[2,23]),t(S,[2,24]),t(S,[2,25]),t(S,[2,26]),t(S,[2,27]),t(S,[2,28]),t(S,[2,29]),{34:[1,51]},{36:[1,52]},t(S,[2,32]),t(S,[2,48],{51:53,64:56,65:57,13:[1,54],22:[1,55],66:N,67:L,68:$,69:x,70:O,71:I,72:v}),{39:[1,65]},t(w,[2,39],{39:[1,67],44:[1,66]}),t(S,[2,50]),t(S,[2,51]),{16:68,58:d,84:f,99:D,101:F},{16:39,18:69,19:40,58:d,84:f,99:D,101:F,102:B},{16:39,18:70,19:40,58:d,84:f,99:D,101:F,102:B},{16:39,18:71,19:40,58:d,84:f,99:D,101:F,102:B},{58:[1,72]},{13:[1,73]},{16:39,18:74,19:40,58:d,84:f,99:D,101:F,102:B},{13:R,53:75},{56:77,58:[1,78]},t(S,[2,61]),t(S,[2,62]),t(S,[2,63]),t(S,[2,64]),t(P,[2,12],{16:39,19:40,18:80,17:[1,79],20:[1,81],58:d,84:f,99:D,101:F,102:B}),t(P,[2,14],{20:[1,82]}),{15:83,16:84,58:d,84:f,99:D,101:F},{16:39,18:85,19:40,58:d,84:f,99:D,101:F,102:B},t(K,[2,118]),t(K,[2,119]),t(K,[2,120]),t(K,[2,121]),t([1,8,9,12,13,20,22,39,41,44,66,67,68,69,70,71,72,77,79],[2,122]),t(_,[2,6],{10:5,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,18:21,38:22,43:23,16:39,19:40,5:86,33:e,35:s,37:i,42:a,46:n,47:u,49:l,50:o,52:c,54:h,55:p,58:d,60:A,61:y,62:C,63:g,73:m,74:b,76:E,80:T,81:k,84:f,99:D,101:F,102:B}),{5:87,10:5,16:39,18:21,19:40,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,33:e,35:s,37:i,38:22,42:a,43:23,46:n,47:u,49:l,50:o,52:c,54:h,55:p,58:d,60:A,61:y,62:C,63:g,73:m,74:b,76:E,80:T,81:k,84:f,99:D,101:F,102:B},t(S,[2,19]),t(S,[2,30]),t(S,[2,31]),{13:[1,89],16:39,18:88,19:40,58:d,84:f,99:D,101:F,102:B},{51:90,64:56,65:57,66:N,67:L,68:$,69:x,70:O,71:I,72:v},t(S,[2,49]),{65:91,71:I,72:v},t(M,[2,68],{64:92,66:N,67:L,68:$,69:x,70:O}),t(G,[2,69]),t(G,[2,70]),t(G,[2,71]),t(G,[2,72]),t(G,[2,73]),t(U,[2,74]),t(U,[2,75]),{8:[1,94],24:95,40:93,43:23,46:n},{16:96,58:d,84:f,99:D,101:F},{45:97,49:Y},{48:[1,99]},{13:[1,100]},{13:[1,101]},{77:[1,102],79:[1,103]},{22:z,57:104,58:Q,80:W,82:105,83:106,84:X,85:j,86:V,87:q,88:H,89:J},{58:[1,116]},{13:R,53:117},t(S,[2,57]),t(S,[2,123]),{22:z,57:118,58:Q,59:[1,119],80:W,82:105,83:106,84:X,85:j,86:V,87:q,88:H,89:J},t(Z,[2,59]),{16:39,18:120,19:40,58:d,84:f,99:D,101:F,102:B},t(P,[2,15]),t(P,[2,16]),t(P,[2,17]),{39:[2,35]},{15:122,16:84,17:[1,121],39:[2,9],58:d,84:f,99:D,101:F},t(tt,[2,43],{11:123,12:[1,124]}),t(_,[2,7]),{9:[1,125]},t(te,[2,52]),{16:39,18:126,19:40,58:d,84:f,99:D,101:F,102:B},{13:[1,128],16:39,18:127,19:40,58:d,84:f,99:D,101:F,102:B},t(M,[2,67],{64:129,66:N,67:L,68:$,69:x,70:O}),t(M,[2,66]),{41:[1,130]},{24:95,40:131,43:23,46:n},{8:[1,132],41:[2,36]},t(w,[2,40],{39:[1,133]}),{41:[1,134]},{41:[2,46],45:135,49:Y},{16:39,18:136,19:40,58:d,84:f,99:D,101:F,102:B},t(S,[2,76],{13:[1,137]}),t(S,[2,78],{13:[1,139],75:[1,138]}),t(S,[2,82],{13:[1,140],78:[1,141]}),{13:[1,142]},t(S,[2,90],{59:ts}),t(ti,[2,92],{83:144,22:z,58:Q,80:W,84:X,85:j,86:V,87:q,88:H,89:J}),t(ta,[2,94]),t(ta,[2,96]),t(ta,[2,97]),t(ta,[2,98]),t(ta,[2,99]),t(ta,[2,100]),t(ta,[2,101]),t(ta,[2,102]),t(ta,[2,103]),t(ta,[2,104]),t(S,[2,91]),t(S,[2,56]),t(S,[2,58],{59:ts}),{58:[1,145]},t(P,[2,13]),{15:146,16:84,58:d,84:f,99:D,101:F},{39:[2,11]},t(tt,[2,44]),{13:[1,147]},{1:[2,4]},t(te,[2,54]),t(te,[2,53]),{16:39,18:148,19:40,58:d,84:f,99:D,101:F,102:B},t(M,[2,65]),t(S,[2,33]),{41:[1,149]},{24:95,40:150,41:[2,37],43:23,46:n},{45:151,49:Y},t(w,[2,41]),{41:[2,47]},t(S,[2,45]),t(S,[2,77]),t(S,[2,79]),t(S,[2,80],{75:[1,152]}),t(S,[2,83]),t(S,[2,84],{13:[1,153]}),t(S,[2,86],{13:[1,155],75:[1,154]}),{22:z,58:Q,80:W,82:156,83:106,84:X,85:j,86:V,87:q,88:H,89:J},t(ta,[2,95]),t(Z,[2,60]),{39:[2,10]},{14:[1,157]},t(te,[2,55]),t(S,[2,34]),{41:[2,38]},{41:[1,158]},t(S,[2,81]),t(S,[2,85]),t(S,[2,87]),t(S,[2,88],{75:[1,159]}),t(ti,[2,93],{83:144,22:z,58:Q,80:W,84:X,85:j,86:V,87:q,88:H,89:J}),t(tt,[2,8]),t(w,[2,42]),t(S,[2,89])],defaultActions:{2:[2,1],3:[2,2],4:[2,3],83:[2,35],122:[2,11],125:[2,4],135:[2,47],146:[2,10],150:[2,38]},parseError:(0,r.K2)(function(t,e){if(e.recoverable)this.trace(t);else{var s=Error(t);throw s.hash=e,s}},"parseError"),parse:(0,r.K2)(function(t){var e=this,s=[0],i=[],a=[null],n=[],u=this.table,l="",o=0,c=0,h=0,p=n.slice.call(arguments,1),d=Object.create(this.lexer),A={yy:{}};for(var y in this.yy)Object.prototype.hasOwnProperty.call(this.yy,y)&&(A.yy[y]=this.yy[y]);d.setInput(t,A.yy),A.yy.lexer=d,A.yy.parser=this,void 0===d.yylloc&&(d.yylloc={});var C=d.yylloc;n.push(C);var g=d.options&&d.options.ranges;function m(){var t;return"number"!=typeof(t=i.pop()||d.lex()||1)&&(t instanceof Array&&(t=(i=t).pop()),t=e.symbols_[t]||t),t}"function"==typeof A.yy.parseError?this.parseError=A.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,(0,r.K2)(function(t){s.length=s.length-2*t,a.length=a.length-t,n.length=n.length-t},"popStack"),(0,r.K2)(m,"lex");for(var b,E,T,k,f,D,F,B,_,S={};;){if(T=s[s.length-1],this.defaultActions[T]?k=this.defaultActions[T]:(null==b&&(b=m()),k=u[T]&&u[T][b]),void 0===k||!k.length||!k[0]){var N="";for(D in _=[],u[T])this.terminals_[D]&&D>2&&_.push("'"+this.terminals_[D]+"'");N=d.showPosition?"Parse error on line "+(o+1)+":\n"+d.showPosition()+"\nExpecting "+_.join(", ")+", got '"+(this.terminals_[b]||b)+"'":"Parse error on line "+(o+1)+": Unexpected "+(1==b?"end of input":"'"+(this.terminals_[b]||b)+"'"),this.parseError(N,{text:d.match,token:this.terminals_[b]||b,line:d.yylineno,loc:C,expected:_})}if(k[0]instanceof Array&&k.length>1)throw Error("Parse Error: multiple actions possible at state: "+T+", token: "+b);switch(k[0]){case 1:s.push(b),a.push(d.yytext),n.push(d.yylloc),s.push(k[1]),b=null,E?(b=E,E=null):(c=d.yyleng,l=d.yytext,o=d.yylineno,C=d.yylloc,h>0&&h--);break;case 2:if(F=this.productions_[k[1]][1],S.$=a[a.length-F],S._$={first_line:n[n.length-(F||1)].first_line,last_line:n[n.length-1].last_line,first_column:n[n.length-(F||1)].first_column,last_column:n[n.length-1].last_column},g&&(S._$.range=[n[n.length-(F||1)].range[0],n[n.length-1].range[1]]),void 0!==(f=this.performAction.apply(S,[l,c,o,A.yy,k[1],a,n].concat(p))))return f;F&&(s=s.slice(0,-1*F*2),a=a.slice(0,-1*F),n=n.slice(0,-1*F)),s.push(this.productions_[k[1]][0]),a.push(S.$),n.push(S._$),B=u[s[s.length-2]][s[s.length-1]],s.push(B);break;case 3:return!0}}return!0},"parse")};function tr(){this.yy={}}return tn.lexer={EOF:1,parseError:(0,r.K2)(function(t,e){if(this.yy.parser)this.yy.parser.parseError(t,e);else throw Error(t)},"parseError"),setInput:(0,r.K2)(function(t,e){return this.yy=e||this.yy||{},this._input=t,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,r.K2)(function(){var t=this._input[0];return this.yytext+=t,this.yyleng++,this.offset++,this.match+=t,this.matched+=t,t.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),t},"input"),unput:(0,r.K2)(function(t){var e=t.length,s=t.split(/(?:\r\n?|\n)/g);this._input=t+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-e),this.offset-=e;var i=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),s.length-1&&(this.yylineno-=s.length-1);var a=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===i.length?this.yylloc.first_column:0)+i[i.length-s.length].length-s[0].length:this.yylloc.first_column-e},this.options.ranges&&(this.yylloc.range=[a[0],a[0]+this.yyleng-e]),this.yyleng=this.yytext.length,this},"unput"),more:(0,r.K2)(function(){return this._more=!0,this},"more"),reject:(0,r.K2)(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:(0,r.K2)(function(t){this.unput(this.match.slice(t))},"less"),pastInput:(0,r.K2)(function(){var t=this.matched.substr(0,this.matched.length-this.match.length);return(t.length>20?"...":"")+t.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,r.K2)(function(){var t=this.match;return t.length<20&&(t+=this._input.substr(0,20-t.length)),(t.substr(0,20)+(t.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,r.K2)(function(){var t=this.pastInput(),e=Array(t.length+1).join("-");return t+this.upcomingInput()+"\n"+e+"^"},"showPosition"),test_match:(0,r.K2)(function(t,e){var s,i,a;if(this.options.backtrack_lexer&&(a={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(a.yylloc.range=this.yylloc.range.slice(0))),(i=t[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=i.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:i?i[i.length-1].length-i[i.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+t[0].length},this.yytext+=t[0],this.match+=t[0],this.matches=t,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(t[0].length),this.matched+=t[0],s=this.performAction.call(this,this.yy,this,e,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),s)return s;if(this._backtrack)for(var n in a)this[n]=a[n];return!1},"test_match"),next:(0,r.K2)(function(){if(this.done)return this.EOF;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var t,e,s,i,a=this._currentRules(),n=0;n<a.length;n++)if((s=this._input.match(this.rules[a[n]]))&&(!e||s[0].length>e[0].length)){if(e=s,i=n,this.options.backtrack_lexer){if(!1!==(t=this.test_match(s,a[n])))return t;if(!this._backtrack)return!1;e=!1;continue}if(!this.options.flex)break}return e?!1!==(t=this.test_match(e,a[i]))&&t:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,r.K2)(function(){var t=this.next();return t||this.lex()},"lex"),begin:(0,r.K2)(function(t){this.conditionStack.push(t)},"begin"),popState:(0,r.K2)(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,r.K2)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,r.K2)(function(t){return(t=this.conditionStack.length-1-Math.abs(t||0))>=0?this.conditionStack[t]:"INITIAL"},"topState"),pushState:(0,r.K2)(function(t){this.begin(t)},"pushState"),stateStackSize:(0,r.K2)(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:(0,r.K2)(function(t,e,s,i){switch(s){case 0:return 60;case 1:return 61;case 2:return 62;case 3:return 63;case 4:case 5:case 14:case 31:case 36:case 40:case 47:break;case 6:return this.begin("acc_title"),33;case 7:return this.popState(),"acc_title_value";case 8:return this.begin("acc_descr"),35;case 9:return this.popState(),"acc_descr_value";case 10:this.begin("acc_descr_multiline");break;case 11:case 19:case 22:case 24:case 58:case 61:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:case 35:return 8;case 15:case 16:return 7;case 17:case 37:case 45:return"EDGE_STATE";case 18:this.begin("callback_name");break;case 20:this.popState(),this.begin("callback_args");break;case 21:return 77;case 23:return 78;case 25:return"STR";case 26:this.begin("string");break;case 27:return 80;case 28:return 55;case 29:return this.begin("namespace"),42;case 30:case 39:return this.popState(),8;case 32:return this.begin("namespace-body"),39;case 33:case 43:return this.popState(),41;case 34:case 44:return"EOF_IN_STRUCT";case 38:return this.begin("class"),46;case 41:return this.popState(),this.popState(),41;case 42:return this.begin("class-body"),39;case 46:return"OPEN_IN_STRUCT";case 48:return"MEMBER";case 49:return 81;case 50:return 73;case 51:return 74;case 52:return 76;case 53:return 52;case 54:return 54;case 55:return 47;case 56:return 48;case 57:return 79;case 59:return"GENERICTYPE";case 60:this.begin("generic");break;case 62:return"BQUOTE_STR";case 63:this.begin("bqstring");break;case 64:case 65:case 66:case 67:return 75;case 68:case 69:return 67;case 70:case 71:return 69;case 72:return 68;case 73:return 66;case 74:return 70;case 75:return 71;case 76:return 72;case 77:return 22;case 78:return 44;case 79:return 99;case 80:return 17;case 81:return"PLUS";case 82:return 85;case 83:return 59;case 84:case 85:return 88;case 86:return 89;case 87:case 88:return"EQUALS";case 89:return 58;case 90:return 12;case 91:return 14;case 92:return"PUNCTUATION";case 93:return 84;case 94:return 101;case 95:case 96:return 87;case 97:return 9}},"anonymous"),rules:[/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:classDiagram-v2\b)/,/^(?:classDiagram\b)/,/^(?:\[\*\])/,/^(?:call[\s]+)/,/^(?:\([\s]*\))/,/^(?:\()/,/^(?:[^(]*)/,/^(?:\))/,/^(?:[^)]*)/,/^(?:["])/,/^(?:[^"]*)/,/^(?:["])/,/^(?:style\b)/,/^(?:classDef\b)/,/^(?:namespace\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:\[\*\])/,/^(?:class\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[}])/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\[\*\])/,/^(?:[{])/,/^(?:[\n])/,/^(?:[^{}\n]*)/,/^(?:cssClass\b)/,/^(?:callback\b)/,/^(?:link\b)/,/^(?:click\b)/,/^(?:note for\b)/,/^(?:note\b)/,/^(?:<<)/,/^(?:>>)/,/^(?:href\b)/,/^(?:[~])/,/^(?:[^~]*)/,/^(?:~)/,/^(?:[`])/,/^(?:[^`]+)/,/^(?:[`])/,/^(?:_self\b)/,/^(?:_blank\b)/,/^(?:_parent\b)/,/^(?:_top\b)/,/^(?:\s*<\|)/,/^(?:\s*\|>)/,/^(?:\s*>)/,/^(?:\s*<)/,/^(?:\s*\*)/,/^(?:\s*o\b)/,/^(?:\s*\(\))/,/^(?:--)/,/^(?:\.\.)/,/^(?::{1}[^:\n;]+)/,/^(?::{3})/,/^(?:-)/,/^(?:\.)/,/^(?:\+)/,/^(?::)/,/^(?:,)/,/^(?:#)/,/^(?:#)/,/^(?:%)/,/^(?:=)/,/^(?:=)/,/^(?:\w+)/,/^(?:\[)/,/^(?:\])/,/^(?:[!"#$%&'*+,-.`?\\/])/,/^(?:[0-9]+)/,/^(?:[\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6]|[\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377]|[\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5]|[\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA]|[\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE]|[\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA]|[\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0]|[\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977]|[\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2]|[\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A]|[\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39]|[\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8]|[\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C]|[\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C]|[\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99]|[\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0]|[\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D]|[\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3]|[\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10]|[\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1]|[\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81]|[\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3]|[\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6]|[\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A]|[\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081]|[\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D]|[\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0]|[\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310]|[\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C]|[\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711]|[\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7]|[\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C]|[\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16]|[\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF]|[\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC]|[\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D]|[\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D]|[\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3]|[\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F]|[\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128]|[\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184]|[\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3]|[\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6]|[\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE]|[\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C]|[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D]|[\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC]|[\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B]|[\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788]|[\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805]|[\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB]|[\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28]|[\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5]|[\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4]|[\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|[\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D]|[\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36]|[\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D]|[\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC]|[\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF]|[\uFFD2-\uFFD7\uFFDA-\uFFDC])/,/^(?:\s)/,/^(?:\s)/,/^(?:$)/],conditions:{"namespace-body":{rules:[26,33,34,35,36,37,38,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},namespace:{rules:[26,29,30,31,32,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},"class-body":{rules:[26,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},class:{rules:[26,39,40,41,42,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_descr_multiline:{rules:[11,12,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_descr:{rules:[9,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_title:{rules:[7,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},callback_args:{rules:[22,23,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},callback_name:{rules:[19,20,21,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},href:{rules:[26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},struct:{rules:[26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},generic:{rules:[26,49,50,51,52,53,54,55,56,57,58,59,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},bqstring:{rules:[26,49,50,51,52,53,54,55,56,57,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},string:{rules:[24,25,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,26,27,28,29,38,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97],inclusive:!0}}},(0,r.K2)(tr,"Parser"),tr.prototype=tn,tn.Parser=tr,new tr}();l.parser=l;var o=l,c=["#","+","~","-",""],h=class{static{(0,r.K2)(this,"ClassMember")}constructor(t,e){this.memberType=e,this.visibility="",this.classifier="",this.text="";let s=(0,r.jZ)(t,(0,r.D7)());this.parseMember(s)}getDisplayDetails(){let t=this.visibility+(0,r.QO)(this.id);return"method"===this.memberType&&(t+=`(${(0,r.QO)(this.parameters.trim())})`,this.returnType&&(t+=" : "+(0,r.QO)(this.returnType))),{displayText:t=t.trim(),cssStyle:this.parseClassifier()}}parseMember(t){let e="";if("method"===this.memberType){let s=/([#+~-])?(.+)\((.*)\)([\s$*])?(.*)([$*])?/.exec(t);if(s){let t=s[1]?s[1].trim():"";if(c.includes(t)&&(this.visibility=t),this.id=s[2],this.parameters=s[3]?s[3].trim():"",e=s[4]?s[4].trim():"",this.returnType=s[5]?s[5].trim():"",""===e){let t=this.returnType.substring(this.returnType.length-1);/[$*]/.exec(t)&&(e=t,this.returnType=this.returnType.substring(0,this.returnType.length-1))}}}else{let s=t.length,i=t.substring(0,1),a=t.substring(s-1);c.includes(i)&&(this.visibility=i),/[$*]/.exec(a)&&(e=a),this.id=t.substring(+(""!==this.visibility),""===e?s:s-1)}this.classifier=e,this.id=this.id.startsWith(" ")?" "+this.id.trim():this.id.trim();let s=`${this.visibility?"\\"+this.visibility:""}${(0,r.QO)(this.id)}${"method"===this.memberType?`(${(0,r.QO)(this.parameters)})${this.returnType?" : "+(0,r.QO)(this.returnType):""}`:""}`;this.text=s.replaceAll("<","&lt;").replaceAll(">","&gt;"),this.text.startsWith("\\&lt;")&&(this.text=this.text.replace("\\&lt;","~"))}parseClassifier(){switch(this.classifier){case"*":return"font-style:italic;";case"$":return"text-decoration:underline;";default:return""}}},p="classId-",d=0,A=(0,r.K2)(t=>r.Y2.sanitizeText(t,(0,r.D7)()),"sanitizeText"),y=class{constructor(){this.relations=[],this.classes=new Map,this.styleClasses=new Map,this.notes=[],this.interfaces=[],this.namespaces=new Map,this.namespaceCounter=0,this.functions=[],this.lineType={LINE:0,DOTTED_LINE:1},this.relationType={AGGREGATION:0,EXTENSION:1,COMPOSITION:2,DEPENDENCY:3,LOLLIPOP:4},this.setupToolTips=(0,r.K2)(t=>{let e=(0,u.Ltv)(".mermaidTooltip");null===(e._groups||e)[0][0]&&(e=(0,u.Ltv)("body").append("div").attr("class","mermaidTooltip").style("opacity",0)),(0,u.Ltv)(t).select("svg").selectAll("g.node").on("mouseover",t=>{let s=(0,u.Ltv)(t.currentTarget);if(null===s.attr("title"))return;let i=this.getBoundingClientRect();e.transition().duration(200).style("opacity",".9"),e.text(s.attr("title")).style("left",window.scrollX+i.left+(i.right-i.left)/2+"px").style("top",window.scrollY+i.top-14+document.body.scrollTop+"px"),e.html(e.html().replace(/&lt;br\/&gt;/g,"<br/>")),s.classed("hover",!0)}).on("mouseout",t=>{e.transition().duration(500).style("opacity",0),(0,u.Ltv)(t.currentTarget).classed("hover",!1)})},"setupToolTips"),this.direction="TB",this.setAccTitle=r.SV,this.getAccTitle=r.iN,this.setAccDescription=r.EI,this.getAccDescription=r.m7,this.setDiagramTitle=r.ke,this.getDiagramTitle=r.ab,this.getConfig=(0,r.K2)(()=>(0,r.D7)().class,"getConfig"),this.functions.push(this.setupToolTips.bind(this)),this.clear(),this.addRelation=this.addRelation.bind(this),this.addClassesToNamespace=this.addClassesToNamespace.bind(this),this.addNamespace=this.addNamespace.bind(this),this.setCssClass=this.setCssClass.bind(this),this.addMembers=this.addMembers.bind(this),this.addClass=this.addClass.bind(this),this.setClassLabel=this.setClassLabel.bind(this),this.addAnnotation=this.addAnnotation.bind(this),this.addMember=this.addMember.bind(this),this.cleanupLabel=this.cleanupLabel.bind(this),this.addNote=this.addNote.bind(this),this.defineClass=this.defineClass.bind(this),this.setDirection=this.setDirection.bind(this),this.setLink=this.setLink.bind(this),this.bindFunctions=this.bindFunctions.bind(this),this.clear=this.clear.bind(this),this.setTooltip=this.setTooltip.bind(this),this.setClickEvent=this.setClickEvent.bind(this),this.setCssStyle=this.setCssStyle.bind(this)}static{(0,r.K2)(this,"ClassDB")}splitClassNameAndType(t){let e=r.Y2.sanitizeText(t,(0,r.D7)()),s="",i=e;if(e.indexOf("~")>0){let t=e.split("~");i=A(t[0]),s=A(t[1])}return{className:i,type:s}}setClassLabel(t,e){let s=r.Y2.sanitizeText(t,(0,r.D7)());e&&(e=A(e));let{className:i}=this.splitClassNameAndType(s);this.classes.get(i).label=e,this.classes.get(i).text=`${e}${this.classes.get(i).type?`<${this.classes.get(i).type}>`:""}`}addClass(t){let e=r.Y2.sanitizeText(t,(0,r.D7)()),{className:s,type:i}=this.splitClassNameAndType(e);if(this.classes.has(s))return;let a=r.Y2.sanitizeText(s,(0,r.D7)());this.classes.set(a,{id:a,type:i,label:a,text:`${a}${i?`&lt;${i}&gt;`:""}`,shape:"classBox",cssClasses:"default",methods:[],members:[],annotations:[],styles:[],domId:p+a+"-"+d}),d++}addInterface(t,e){let s={id:`interface${this.interfaces.length}`,label:t,classId:e};this.interfaces.push(s)}lookUpDomId(t){let e=r.Y2.sanitizeText(t,(0,r.D7)());if(this.classes.has(e))return this.classes.get(e).domId;throw Error("Class not found: "+e)}clear(){this.relations=[],this.classes=new Map,this.notes=[],this.interfaces=[],this.functions=[],this.functions.push(this.setupToolTips.bind(this)),this.namespaces=new Map,this.namespaceCounter=0,this.direction="TB",(0,r.IU)()}getClass(t){return this.classes.get(t)}getClasses(){return this.classes}getRelations(){return this.relations}getNotes(){return this.notes}addRelation(t){r.Rm.debug("Adding relation: "+JSON.stringify(t));let e=[this.relationType.LOLLIPOP,this.relationType.AGGREGATION,this.relationType.COMPOSITION,this.relationType.DEPENDENCY,this.relationType.EXTENSION];t.relation.type1!==this.relationType.LOLLIPOP||e.includes(t.relation.type2)?t.relation.type2!==this.relationType.LOLLIPOP||e.includes(t.relation.type1)?(this.addClass(t.id1),this.addClass(t.id2)):(this.addClass(t.id1),this.addInterface(t.id2,t.id1),t.id2=`interface${this.interfaces.length-1}`):(this.addClass(t.id2),this.addInterface(t.id1,t.id2),t.id1=`interface${this.interfaces.length-1}`),t.id1=this.splitClassNameAndType(t.id1).className,t.id2=this.splitClassNameAndType(t.id2).className,t.relationTitle1=r.Y2.sanitizeText(t.relationTitle1.trim(),(0,r.D7)()),t.relationTitle2=r.Y2.sanitizeText(t.relationTitle2.trim(),(0,r.D7)()),this.relations.push(t)}addAnnotation(t,e){let s=this.splitClassNameAndType(t).className;this.classes.get(s).annotations.push(e)}addMember(t,e){this.addClass(t);let s=this.splitClassNameAndType(t).className,i=this.classes.get(s);if("string"==typeof e){let t=e.trim();t.startsWith("<<")&&t.endsWith(">>")?i.annotations.push(A(t.substring(2,t.length-2))):t.indexOf(")")>0?i.methods.push(new h(t,"method")):t&&i.members.push(new h(t,"attribute"))}}addMembers(t,e){Array.isArray(e)&&(e.reverse(),e.forEach(e=>this.addMember(t,e)))}addNote(t,e){let s={id:`note${this.notes.length}`,class:e,text:t};this.notes.push(s)}cleanupLabel(t){return t.startsWith(":")&&(t=t.substring(1)),A(t.trim())}setCssClass(t,e){t.split(",").forEach(t=>{let s=t;/\d/.exec(t[0])&&(s=p+s);let i=this.classes.get(s);i&&(i.cssClasses+=" "+e)})}defineClass(t,e){for(let s of t){let t=this.styleClasses.get(s);void 0===t&&(t={id:s,styles:[],textStyles:[]},this.styleClasses.set(s,t)),e&&e.forEach(e=>{if(/color/.exec(e)){let s=e.replace("fill","bgFill");t.textStyles.push(s)}t.styles.push(e)}),this.classes.forEach(t=>{t.cssClasses.includes(s)&&t.styles.push(...e.flatMap(t=>t.split(",")))})}}setTooltip(t,e){t.split(",").forEach(t=>{void 0!==e&&(this.classes.get(t).tooltip=A(e))})}getTooltip(t,e){return e&&this.namespaces.has(e)?this.namespaces.get(e).classes.get(t).tooltip:this.classes.get(t).tooltip}setLink(t,e,s){let i=(0,r.D7)();t.split(",").forEach(t=>{let a=t;/\d/.exec(t[0])&&(a=p+a);let r=this.classes.get(a);r&&(r.link=n._K.formatUrl(e,i),"sandbox"===i.securityLevel?r.linkTarget="_top":"string"==typeof s?r.linkTarget=A(s):r.linkTarget="_blank")}),this.setCssClass(t,"clickable")}setClickEvent(t,e,s){t.split(",").forEach(t=>{this.setClickFunc(t,e,s),this.classes.get(t).haveCallback=!0}),this.setCssClass(t,"clickable")}setClickFunc(t,e,s){let i=r.Y2.sanitizeText(t,(0,r.D7)());if("loose"===(0,r.D7)().securityLevel&&void 0!==e&&this.classes.has(i)){let t=this.lookUpDomId(i),a=[];if("string"==typeof s){a=s.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let t=0;t<a.length;t++){let e=a[t].trim();e.startsWith('"')&&e.endsWith('"')&&(e=e.substr(1,e.length-2)),a[t]=e}}0===a.length&&a.push(t),this.functions.push(()=>{let s=document.querySelector(`[id="${t}"]`);null!==s&&s.addEventListener("click",()=>{n._K.runFunc(e,...a)},!1)})}}bindFunctions(t){this.functions.forEach(e=>{e(t)})}getDirection(){return this.direction}setDirection(t){this.direction=t}addNamespace(t){!this.namespaces.has(t)&&(this.namespaces.set(t,{id:t,classes:new Map,children:{},domId:p+t+"-"+this.namespaceCounter}),this.namespaceCounter++)}getNamespace(t){return this.namespaces.get(t)}getNamespaces(){return this.namespaces}addClassesToNamespace(t,e){if(this.namespaces.has(t))for(let s of e){let{className:e}=this.splitClassNameAndType(s);this.classes.get(e).parent=t,this.namespaces.get(t).classes.set(e,this.classes.get(e))}}setCssStyle(t,e){let s=this.classes.get(t);if(e&&s)for(let t of e)t.includes(",")?s.styles.push(...t.split(",")):s.styles.push(t)}getArrowMarker(t){let e;switch(t){case 0:e="aggregation";break;case 1:e="extension";break;case 2:e="composition";break;case 3:e="dependency";break;case 4:e="lollipop";break;default:e="none"}return e}getData(){let t=[],e=[],s=(0,r.D7)();for(let e of this.namespaces.keys()){let i=this.namespaces.get(e);if(i){let e={id:i.id,label:i.id,isGroup:!0,padding:s.class.padding??16,shape:"rect",cssStyles:["fill: none","stroke: black"],look:s.look};t.push(e)}}for(let e of this.classes.keys()){let i=this.classes.get(e);i&&(i.parentId=i.parent,i.look=s.look,t.push(i))}let i=0;for(let a of this.notes){i++;let n={id:a.id,label:a.text,isGroup:!1,shape:"note",padding:s.class.padding??6,cssStyles:["text-align: left","white-space: nowrap",`fill: ${s.themeVariables.noteBkgColor}`,`stroke: ${s.themeVariables.noteBorderColor}`],look:s.look};t.push(n);let r=this.classes.get(a.class)?.id??"";if(r){let t={id:`edgeNote${i}`,start:a.id,end:r,type:"normal",thickness:"normal",classes:"relation",arrowTypeStart:"none",arrowTypeEnd:"none",arrowheadStyle:"",labelStyle:[""],style:["fill: none"],pattern:"dotted",look:s.look};e.push(t)}}for(let e of this.interfaces){let i={id:e.id,label:e.label,isGroup:!1,shape:"rect",cssStyles:["opacity: 0;"],look:s.look};t.push(i)}for(let t of(i=0,this.relations)){i++;let a={id:(0,n.rY)(t.id1,t.id2,{prefix:"id",counter:i}),start:t.id1,end:t.id2,type:"normal",label:t.title,labelpos:"c",thickness:"normal",classes:"relation",arrowTypeStart:this.getArrowMarker(t.relation.type1),arrowTypeEnd:this.getArrowMarker(t.relation.type2),startLabelRight:"none"===t.relationTitle1?"":t.relationTitle1,endLabelLeft:"none"===t.relationTitle2?"":t.relationTitle2,arrowheadStyle:"",labelStyle:["display: inline-block"],style:t.style||"",pattern:1==t.relation.lineType?"dashed":"solid",look:s.look};e.push(a)}return{nodes:t,edges:e,other:{},config:s,direction:this.getDirection()}}},C=(0,r.K2)(t=>`g.classGroup text {
  fill: ${t.nodeBorder||t.classText};
  stroke: none;
  font-family: ${t.fontFamily};
  font-size: 10px;

  .title {
    font-weight: bolder;
  }

}

.nodeLabel, .edgeLabel {
  color: ${t.classText};
}
.edgeLabel .label rect {
  fill: ${t.mainBkg};
}
.label text {
  fill: ${t.classText};
}

.labelBkg {
  background: ${t.mainBkg};
}
.edgeLabel .label span {
  background: ${t.mainBkg};
}

.classTitle {
  font-weight: bolder;
}
.node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${t.mainBkg};
    stroke: ${t.nodeBorder};
    stroke-width: 1px;
  }


.divider {
  stroke: ${t.nodeBorder};
  stroke-width: 1;
}

g.clickable {
  cursor: pointer;
}

g.classGroup rect {
  fill: ${t.mainBkg};
  stroke: ${t.nodeBorder};
}

g.classGroup line {
  stroke: ${t.nodeBorder};
  stroke-width: 1;
}

.classLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: ${t.mainBkg};
  opacity: 0.5;
}

.classLabel .label {
  fill: ${t.nodeBorder};
  font-size: 10px;
}

.relation {
  stroke: ${t.lineColor};
  stroke-width: 1;
  fill: none;
}

.dashed-line{
  stroke-dasharray: 3;
}

.dotted-line{
  stroke-dasharray: 1 2;
}

#compositionStart, .composition {
  fill: ${t.lineColor} !important;
  stroke: ${t.lineColor} !important;
  stroke-width: 1;
}

#compositionEnd, .composition {
  fill: ${t.lineColor} !important;
  stroke: ${t.lineColor} !important;
  stroke-width: 1;
}

#dependencyStart, .dependency {
  fill: ${t.lineColor} !important;
  stroke: ${t.lineColor} !important;
  stroke-width: 1;
}

#dependencyStart, .dependency {
  fill: ${t.lineColor} !important;
  stroke: ${t.lineColor} !important;
  stroke-width: 1;
}

#extensionStart, .extension {
  fill: transparent !important;
  stroke: ${t.lineColor} !important;
  stroke-width: 1;
}

#extensionEnd, .extension {
  fill: transparent !important;
  stroke: ${t.lineColor} !important;
  stroke-width: 1;
}

#aggregationStart, .aggregation {
  fill: transparent !important;
  stroke: ${t.lineColor} !important;
  stroke-width: 1;
}

#aggregationEnd, .aggregation {
  fill: transparent !important;
  stroke: ${t.lineColor} !important;
  stroke-width: 1;
}

#lollipopStart, .lollipop {
  fill: ${t.mainBkg} !important;
  stroke: ${t.lineColor} !important;
  stroke-width: 1;
}

#lollipopEnd, .lollipop {
  fill: ${t.mainBkg} !important;
  stroke: ${t.lineColor} !important;
  stroke-width: 1;
}

.edgeTerminals {
  font-size: 11px;
  line-height: initial;
}

.classTitleText {
  text-anchor: middle;
  font-size: 18px;
  fill: ${t.textColor};
}
`,"getStyles"),g=(0,r.K2)((t,e="TB")=>{if(!t.doc)return e;let s=e;for(let e of t.doc)"dir"===e.stmt&&(s=e.value);return s},"getDir"),m={getClasses:(0,r.K2)(function(t,e){return e.db.getClasses()},"getClasses"),draw:(0,r.K2)(async function(t,e,s,u){r.Rm.info("REF0:"),r.Rm.info("Drawing class diagram (v3)",e);let{securityLevel:l,state:o,layout:c}=(0,r.D7)(),h=u.db.getData(),p=(0,i.A)(e,l);h.type=u.type,h.layoutAlgorithm=(0,a.q7)(c),h.nodeSpacing=o?.nodeSpacing||50,h.rankSpacing=o?.rankSpacing||50,h.markers=["aggregation","extension","composition","dependency","lollipop"],h.diagramId=e,await (0,a.XX)(h,p),n._K.insertTitle(p,"classDiagramTitleText",o?.titleTopMargin??25,u.db.getDiagramTitle()),(0,i.P)(p,8,"classDiagram",o?.useMaxWidth??!0)},"draw"),getDir:g}}}]);