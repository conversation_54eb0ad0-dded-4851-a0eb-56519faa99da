"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4838],{1978:(e,t,n)=>{let i;function r(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}n.d(t,{P:()=>om});let o=e=>Array.isArray(e);function s(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}function a(e){return"string"==typeof e||Array.isArray(e)}function l(e){let t=[{},{}];return null==e||e.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function u(e,t,n,i){if("function"==typeof t){let[r,o]=l(i);t=t(void 0!==n?n:e.custom,r,o)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[r,o]=l(i);t=t(void 0!==n?n:e.custom,r,o)}return t}function c(e,t,n){let i=e.getProps();return u(i,t,void 0!==n?n:i.custom,e)}let d=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],h=["initial",...d];function f(e){let t;return()=>(void 0===t&&(t=e()),t)}let p=f(()=>void 0!==window.ScrollTimeline);class m{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>"finished"in e?e.finished:e))}getAll(e){return this.animations[0][e]}setAll(e,t){for(let n=0;n<this.animations.length;n++)this.animations[n][e]=t}attachTimeline(e,t){let n=this.animations.map(n=>p()&&n.attachTimeline?n.attachTimeline(e):"function"==typeof t?t(n):void 0);return()=>{n.forEach((e,t)=>{e&&e(),this.animations[t].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach(t=>t[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class v extends m{then(e,t){return Promise.all(this.animations).then(e).catch(t)}}function g(e,t){return e?e[t]||e.default||e:void 0}function y(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function x(e){return"function"==typeof e}function w(e,t){e.timeline=t,e.onfinish=null}let b=e=>Array.isArray(e)&&"number"==typeof e[0],P={linearEasing:void 0},A=function(e,t){let n=f(e);return()=>{var e;return null!==(e=P[t])&&void 0!==e?e:n()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),T=(e,t,n)=>{let i=t-e;return 0===i?1:(n-e)/i},E=(e,t,n=10)=>{let i="",r=Math.max(Math.round(t/n),2);for(let t=0;t<r;t++)i+=e(T(0,r-1,t))+", ";return`linear(${i.substring(0,i.length-2)})`},S=([e,t,n,i])=>`cubic-bezier(${e}, ${t}, ${n}, ${i})`,C={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:S([0,.65,.55,1]),circOut:S([.55,0,1,.45]),backIn:S([.31,.01,.66,-.59]),backOut:S([.33,1.53,.69,.99])},M={x:!1,y:!1};function R(e,t){let n=function(e,t,n){var i;if(e instanceof Element)return[e];if("string"==typeof e){let t=document,n=(i=void 0,t.querySelectorAll(e));return n?Array.from(n):[]}return Array.from(e)}(e),i=new AbortController;return[n,{passive:!0,...t,signal:i.signal},()=>i.abort()]}function k(e){return t=>{"touch"===t.pointerType||M.x||M.y||e(t)}}let D=(e,t)=>!!t&&(e===t||D(e,t.parentElement)),L=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary,j=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),V=new WeakSet;function F(e){return t=>{"Enter"===t.key&&e(t)}}function O(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let B=(e,t)=>{let n=e.currentTarget;if(!n)return;let i=F(()=>{if(V.has(n))return;O(n,"down");let e=F(()=>{O(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>O(n,"cancel"),t)});n.addEventListener("keydown",i,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",i),t)};function I(e){return L(e)&&!(M.x||M.y)}let N=e=>1e3*e,U=e=>e/1e3,W=e=>e,_=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],z=new Set(_),H=new Set(["width","height","top","left","right","bottom",..._]),$=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),K=e=>o(e)?e[e.length-1]||0:e,Y={skipAnimations:!1,useManualTiming:!1},X=["read","resolveKeyframes","update","preRender","render","postRender"];function G(e,t){let n=!1,i=!0,r={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,s=X.reduce((e,t)=>(e[t]=function(e){let t=new Set,n=new Set,i=!1,r=!1,o=new WeakSet,s={delta:0,timestamp:0,isProcessing:!1};function a(t){o.has(t)&&(l.schedule(t),e()),t(s)}let l={schedule:(e,r=!1,s=!1)=>{let a=s&&i?t:n;return r&&o.add(e),a.has(e)||a.add(e),e},cancel:e=>{n.delete(e),o.delete(e)},process:e=>{if(s=e,i){r=!0;return}i=!0,[t,n]=[n,t],t.forEach(a),t.clear(),i=!1,r&&(r=!1,l.process(e))}};return l}(o),e),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:d,postRender:h}=s,f=()=>{let o=Y.useManualTiming?r.timestamp:performance.now();n=!1,r.delta=i?1e3/60:Math.max(Math.min(o-r.timestamp,40),1),r.timestamp=o,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),c.process(r),d.process(r),h.process(r),r.isProcessing=!1,n&&t&&(i=!1,e(f))},p=()=>{n=!0,i=!0,r.isProcessing||e(f)};return{schedule:X.reduce((e,t)=>{let i=s[t];return e[t]=(e,t=!1,r=!1)=>(n||p(),i.schedule(e,t,r)),e},{}),cancel:e=>{for(let t=0;t<X.length;t++)s[X[t]].cancel(e)},state:r,steps:s}}let{schedule:q,cancel:Z,state:Q,steps:J}=G("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:W,!0);function ee(){i=void 0}let et={now:()=>(void 0===i&&et.set(Q.isProcessing||Y.useManualTiming?Q.timestamp:performance.now()),i),set:e=>{i=e,queueMicrotask(ee)}};function en(e,t){-1===e.indexOf(t)&&e.push(t)}function ei(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class er{constructor(){this.subscriptions=[]}add(e){return en(this.subscriptions,e),()=>ei(this.subscriptions,e)}notify(e,t,n){let i=this.subscriptions.length;if(i){if(1===i)this.subscriptions[0](e,t,n);else for(let r=0;r<i;r++){let i=this.subscriptions[r];i&&i(e,t,n)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let eo=e=>!isNaN(parseFloat(e)),es={current:void 0};class ea{constructor(e,t={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=et.now();this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=et.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=eo(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new er);let n=this.events[e].add(t);return"change"===e?()=>{n(),q.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return es.current&&es.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=et.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function el(e,t){return new ea(e,t)}let eu=e=>!!(e&&e.getVelocity);function ec(e,t){let n=e.getValue("willChange");if(eu(n)&&n.add)return n.add(t)}let ed=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eh="data-"+ed("framerAppearId"),ef={current:!1},ep=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function em(e,t,n,i){if(e===t&&n===i)return W;let r=t=>(function(e,t,n,i,r){let o,s;let a=0;do(o=ep(s=t+(n-t)/2,i,r)-e)>0?n=s:t=s;while(Math.abs(o)>1e-7&&++a<12);return s})(t,0,1,e,n);return e=>0===e||1===e?e:ep(r(e),t,i)}let ev=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,eg=e=>t=>1-e(1-t),ey=em(.33,1.53,.69,.99),ex=eg(ey),ew=ev(ex),eb=e=>(e*=2)<1?.5*ex(e):.5*(2-Math.pow(2,-10*(e-1))),eP=e=>1-Math.sin(Math.acos(e)),eA=eg(eP),eT=ev(eP),eE=e=>/^0[^.\s]+$/u.test(e),eS=(e,t,n)=>n>t?t:n<e?e:n,eC={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},eM={...eC,transform:e=>eS(0,1,e)},eR={...eC,default:1},ek=e=>Math.round(1e5*e)/1e5,eD=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,eL=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ej=(e,t)=>n=>!!("string"==typeof n&&eL.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),eV=(e,t,n)=>i=>{if("string"!=typeof i)return i;let[r,o,s,a]=i.match(eD);return{[e]:parseFloat(r),[t]:parseFloat(o),[n]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},eF=e=>eS(0,255,e),eO={...eC,transform:e=>Math.round(eF(e))},eB={test:ej("rgb","red"),parse:eV("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:i=1})=>"rgba("+eO.transform(e)+", "+eO.transform(t)+", "+eO.transform(n)+", "+ek(eM.transform(i))+")"},eI={test:ej("#"),parse:function(e){let t="",n="",i="",r="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),i=e.substring(5,7),r=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),i=e.substring(3,4),r=e.substring(4,5),t+=t,n+=n,i+=i,r+=r),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:r?parseInt(r,16)/255:1}},transform:eB.transform},eN=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eU=eN("deg"),eW=eN("%"),e_=eN("px"),ez=eN("vh"),eH=eN("vw"),e$={...eW,parse:e=>eW.parse(e)/100,transform:e=>eW.transform(100*e)},eK={test:ej("hsl","hue"),parse:eV("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:i=1})=>"hsla("+Math.round(e)+", "+eW.transform(ek(t))+", "+eW.transform(ek(n))+", "+ek(eM.transform(i))+")"},eY={test:e=>eB.test(e)||eI.test(e)||eK.test(e),parse:e=>eB.test(e)?eB.parse(e):eK.test(e)?eK.parse(e):eI.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eB.transform(e):eK.transform(e)},eX=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eG="number",eq="color",eZ=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eQ(e){let t=e.toString(),n=[],i={color:[],number:[],var:[]},r=[],o=0,s=t.replace(eZ,e=>(eY.test(e)?(i.color.push(o),r.push(eq),n.push(eY.parse(e))):e.startsWith("var(")?(i.var.push(o),r.push("var"),n.push(e)):(i.number.push(o),r.push(eG),n.push(parseFloat(e))),++o,"${}")).split("${}");return{values:n,split:s,indexes:i,types:r}}function eJ(e){return eQ(e).values}function e0(e){let{split:t,types:n}=eQ(e),i=t.length;return e=>{let r="";for(let o=0;o<i;o++)if(r+=t[o],void 0!==e[o]){let t=n[o];t===eG?r+=ek(e[o]):t===eq?r+=eY.transform(e[o]):r+=e[o]}return r}}let e1=e=>"number"==typeof e?0:e,e2={test:function(e){var t,n;return isNaN(e)&&"string"==typeof e&&((null===(t=e.match(eD))||void 0===t?void 0:t.length)||0)+((null===(n=e.match(eX))||void 0===n?void 0:n.length)||0)>0},parse:eJ,createTransformer:e0,getAnimatableNone:function(e){let t=eJ(e);return e0(e)(t.map(e1))}},e5=new Set(["brightness","contrast","saturate","opacity"]);function e9(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[i]=n.match(eD)||[];if(!i)return e;let r=n.replace(i,""),o=+!!e5.has(t);return i!==n&&(o*=100),t+"("+o+r+")"}let e3=/\b([a-z-]*)\(.*?\)/gu,e6={...e2,getAnimatableNone:e=>{let t=e.match(e3);return t?t.map(e9).join(" "):e}},e4={...eC,transform:Math.round},e8={borderWidth:e_,borderTopWidth:e_,borderRightWidth:e_,borderBottomWidth:e_,borderLeftWidth:e_,borderRadius:e_,radius:e_,borderTopLeftRadius:e_,borderTopRightRadius:e_,borderBottomRightRadius:e_,borderBottomLeftRadius:e_,width:e_,maxWidth:e_,height:e_,maxHeight:e_,top:e_,right:e_,bottom:e_,left:e_,padding:e_,paddingTop:e_,paddingRight:e_,paddingBottom:e_,paddingLeft:e_,margin:e_,marginTop:e_,marginRight:e_,marginBottom:e_,marginLeft:e_,backgroundPositionX:e_,backgroundPositionY:e_,rotate:eU,rotateX:eU,rotateY:eU,rotateZ:eU,scale:eR,scaleX:eR,scaleY:eR,scaleZ:eR,skew:eU,skewX:eU,skewY:eU,distance:e_,translateX:e_,translateY:e_,translateZ:e_,x:e_,y:e_,z:e_,perspective:e_,transformPerspective:e_,opacity:eM,originX:e$,originY:e$,originZ:e_,zIndex:e4,size:e_,fillOpacity:eM,strokeOpacity:eM,numOctaves:e4},e7={...e8,color:eY,backgroundColor:eY,outlineColor:eY,fill:eY,stroke:eY,borderColor:eY,borderTopColor:eY,borderRightColor:eY,borderBottomColor:eY,borderLeftColor:eY,filter:e6,WebkitFilter:e6},te=e=>e7[e];function tt(e,t){let n=te(e);return n!==e6&&(n=e2),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let tn=new Set(["auto","none","0"]),ti=e=>e===eC||e===e_,tr=(e,t)=>parseFloat(e.split(", ")[t]),to=(e,t)=>(n,{transform:i})=>{if("none"===i||!i)return 0;let r=i.match(/^matrix3d\((.+)\)$/u);if(r)return tr(r[1],t);{let t=i.match(/^matrix\((.+)\)$/u);return t?tr(t[1],e):0}},ts=new Set(["x","y","z"]),ta=_.filter(e=>!ts.has(e)),tl={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:to(4,13),y:to(5,14)};tl.translateX=tl.x,tl.translateY=tl.y;let tu=new Set,tc=!1,td=!1;function th(){if(td){let e=Array.from(tu).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return ta.forEach(n=>{let i=e.getValue(n);void 0!==i&&(t.push([n,i.get()]),i.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{var i;null===(i=e.getValue(t))||void 0===i||i.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}td=!1,tc=!1,tu.forEach(e=>e.complete()),tu.clear()}function tf(){tu.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(td=!0)})}class tp{constructor(e,t,n,i,r,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=i,this.element=r,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(tu.add(this),tc||(tc=!0,q.read(tf),q.resolveKeyframes(th))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:i}=this;for(let r=0;r<e.length;r++)if(null===e[r]){if(0===r){let r=null==i?void 0:i.get(),o=e[e.length-1];if(void 0!==r)e[0]=r;else if(n&&t){let i=n.readValue(t,o);null!=i&&(e[0]=i)}void 0===e[0]&&(e[0]=o),i&&void 0===r&&i.set(e[0])}else e[r]=e[r-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),tu.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,tu.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let tm=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),tv=e=>t=>"string"==typeof t&&t.startsWith(e),tg=tv("--"),ty=tv("var(--"),tx=e=>!!ty(e)&&tw.test(e.split("/*")[0].trim()),tw=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,tb=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tP=e=>t=>t.test(e),tA=[eC,e_,eW,eU,eH,ez,{test:e=>"auto"===e,parse:e=>e}],tT=e=>tA.find(tP(e));class tE extends tp{constructor(e,t,n,i,r){super(e,t,n,i,r,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let i=e[n];if("string"==typeof i&&tx(i=i.trim())){let r=function e(t,n,i=1){W(i<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[r,o]=function(e){let t=tb.exec(e);if(!t)return[,];let[,n,i,r]=t;return[`--${null!=n?n:i}`,r]}(t);if(!r)return;let s=window.getComputedStyle(n).getPropertyValue(r);if(s){let e=s.trim();return tm(e)?parseFloat(e):e}return tx(o)?e(o,n,i+1):o}(i,t.current);void 0!==r&&(e[n]=r),n===e.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!H.has(n)||2!==e.length)return;let[i,r]=e,o=tT(i),s=tT(r);if(o!==s){if(ti(o)&&ti(s))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var i;("number"==typeof(i=e[t])?0===i:null===i||"none"===i||"0"===i||eE(i))&&n.push(t)}n.length&&function(e,t,n){let i,r=0;for(;r<e.length&&!i;){let t=e[r];"string"==typeof t&&!tn.has(t)&&eQ(t).values.length&&(i=e[r]),r++}if(i&&n)for(let r of t)e[r]=tt(n,i)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tl[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let i=t[t.length-1];void 0!==i&&e.getValue(n,i).jump(i,!1)}measureEndState(){var e;let{element:t,name:n,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let r=t.getValue(n);r&&r.jump(this.measuredOrigin,!1);let o=i.length-1,s=i[o];i[o]=tl[n](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),(null===(e=this.removedTransforms)||void 0===e?void 0:e.length)&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}let tS=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(e2.test(e)||"0"===e)&&!e.startsWith("url(")),tC=e=>null!==e;function tM(e,{repeat:t,repeatType:n="loop"},i){let r=e.filter(tC),o=t&&"loop"!==n&&t%2==1?0:r.length-1;return o&&void 0!==i?i:r[o]}class tR{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:i=0,repeatDelay:r=0,repeatType:o="loop",...s}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=et.now(),this.options={autoplay:e,delay:t,type:n,repeat:i,repeatDelay:r,repeatType:o,...s},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(tf(),th()),this._resolved}onKeyframesResolved(e,t){this.resolvedAt=et.now(),this.hasAttemptedResolve=!0;let{name:n,type:i,velocity:r,delay:o,onComplete:s,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(e,t,n,i){let r=e[0];if(null===r)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],s=tS(r,t),a=tS(o,t);return W(s===a,`You are trying to animate ${t} from "${r}" to "${o}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${o} via the \`style\` property.`),!!s&&!!a&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||x(n))&&i)}(e,n,i,r)){if(ef.current||!o){a&&a(tM(e,this.options,t)),s&&s(),this.resolveFinishedPromise();return}this.options.duration=0}let u=this.initPlayback(e,t);!1!==u&&(this._resolved={keyframes:e,finalKeyframe:t,...u},this.onPostResolved())}onPostResolved(){}then(e,t){return this.currentFinishedPromise.then(e,t)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}let tk=(e,t,n)=>e+(t-e)*n;function tD(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function tL(e,t){return n=>n>0?t:e}let tj=(e,t,n)=>{let i=e*e,r=n*(t*t-i)+i;return r<0?0:Math.sqrt(r)},tV=[eI,eB,eK],tF=e=>tV.find(t=>t.test(e));function tO(e){let t=tF(e);if(W(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===eK&&(n=function({hue:e,saturation:t,lightness:n,alpha:i}){e/=360,n/=100;let r=0,o=0,s=0;if(t/=100){let i=n<.5?n*(1+t):n+t-n*t,a=2*n-i;r=tD(a,i,e+1/3),o=tD(a,i,e),s=tD(a,i,e-1/3)}else r=o=s=n;return{red:Math.round(255*r),green:Math.round(255*o),blue:Math.round(255*s),alpha:i}}(n)),n}let tB=(e,t)=>{let n=tO(e),i=tO(t);if(!n||!i)return tL(e,t);let r={...n};return e=>(r.red=tj(n.red,i.red,e),r.green=tj(n.green,i.green,e),r.blue=tj(n.blue,i.blue,e),r.alpha=tk(n.alpha,i.alpha,e),eB.transform(r))},tI=(e,t)=>n=>t(e(n)),tN=(...e)=>e.reduce(tI),tU=new Set(["none","hidden"]);function tW(e,t){return n=>tk(e,t,n)}function t_(e){return"number"==typeof e?tW:"string"==typeof e?tx(e)?tL:eY.test(e)?tB:t$:Array.isArray(e)?tz:"object"==typeof e?eY.test(e)?tB:tH:tL}function tz(e,t){let n=[...e],i=n.length,r=e.map((e,n)=>t_(e)(e,t[n]));return e=>{for(let t=0;t<i;t++)n[t]=r[t](e);return n}}function tH(e,t){let n={...e,...t},i={};for(let r in n)void 0!==e[r]&&void 0!==t[r]&&(i[r]=t_(e[r])(e[r],t[r]));return e=>{for(let t in i)n[t]=i[t](e);return n}}let t$=(e,t)=>{let n=e2.createTransformer(t),i=eQ(e),r=eQ(t);return i.indexes.var.length===r.indexes.var.length&&i.indexes.color.length===r.indexes.color.length&&i.indexes.number.length>=r.indexes.number.length?tU.has(e)&&!r.values.length||tU.has(t)&&!i.values.length?function(e,t){return tU.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):tN(tz(function(e,t){var n;let i=[],r={color:0,var:0,number:0};for(let o=0;o<t.values.length;o++){let s=t.types[o],a=e.indexes[s][r[s]],l=null!==(n=e.values[a])&&void 0!==n?n:0;i[o]=l,r[s]++}return i}(i,r),r.values),n):(W(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tL(e,t))};function tK(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?tk(e,t,n):t_(e)(e,t)}function tY(e,t,n){var i,r;let o=Math.max(t-5,0);return i=n-e(o),(r=t-o)?1e3/r*i:0}let tX={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tG(e,t){return e*Math.sqrt(1-t*t)}let tq=["duration","bounce"],tZ=["stiffness","damping","mass"];function tQ(e,t){return t.some(t=>void 0!==e[t])}function tJ(e=tX.visualDuration,t=tX.bounce){let n;let i="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:r,restDelta:o}=i,s=i.keyframes[0],a=i.keyframes[i.keyframes.length-1],l={done:!1,value:s},{stiffness:u,damping:c,mass:d,duration:h,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:tX.velocity,stiffness:tX.stiffness,damping:tX.damping,mass:tX.mass,isResolvedFromDuration:!1,...e};if(!tQ(e,tZ)&&tQ(e,tq)){if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),i=n*n,r=2*eS(.05,1,1-(e.bounce||0))*Math.sqrt(i);t={...t,mass:tX.mass,stiffness:i,damping:r}}else{let n=function({duration:e=tX.duration,bounce:t=tX.bounce,velocity:n=tX.velocity,mass:i=tX.mass}){let r,o;W(e<=N(tX.maxDuration),"Spring duration must be 10 seconds or less");let s=1-t;s=eS(tX.minDamping,tX.maxDamping,s),e=eS(tX.minDuration,tX.maxDuration,U(e)),s<1?(r=t=>{let i=t*s,r=i*e;return .001-(i-n)/tG(t,s)*Math.exp(-r)},o=t=>{let i=t*s*e,o=Math.pow(s,2)*Math.pow(t,2)*e,a=Math.exp(-i),l=tG(Math.pow(t,2),s);return(i*n+n-o)*a*(-r(t)+.001>0?-1:1)/l}):(r=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),o=t=>e*e*(n-t)*Math.exp(-t*e));let a=function(e,t,n){let i=n;for(let n=1;n<12;n++)i-=e(i)/t(i);return i}(r,o,5/e);if(e=N(e),isNaN(a))return{stiffness:tX.stiffness,damping:tX.damping,duration:e};{let t=Math.pow(a,2)*i;return{stiffness:t,damping:2*s*Math.sqrt(i*t),duration:e}}}(e);(t={...t,...n,mass:tX.mass}).isResolvedFromDuration=!0}}return t}({...i,velocity:-U(i.velocity||0)}),m=f||0,v=c/(2*Math.sqrt(u*d)),g=a-s,x=U(Math.sqrt(u/d)),w=5>Math.abs(g);if(r||(r=w?tX.restSpeed.granular:tX.restSpeed.default),o||(o=w?tX.restDelta.granular:tX.restDelta.default),v<1){let e=tG(x,v);n=t=>a-Math.exp(-v*x*t)*((m+v*x*g)/e*Math.sin(e*t)+g*Math.cos(e*t))}else if(1===v)n=e=>a-Math.exp(-x*e)*(g+(m+x*g)*e);else{let e=x*Math.sqrt(v*v-1);n=t=>{let n=Math.exp(-v*x*t),i=Math.min(e*t,300);return a-n*((m+v*x*g)*Math.sinh(i)+e*g*Math.cosh(i))/e}}let b={calculatedDuration:p&&h||null,next:e=>{let t=n(e);if(p)l.done=e>=h;else{let i=0;v<1&&(i=0===e?N(m):tY(n,e,t));let s=Math.abs(a-t)<=o;l.done=Math.abs(i)<=r&&s}return l.value=l.done?a:t,l},toString:()=>{let e=Math.min(y(b),2e4),t=E(t=>b.next(e*t).value,e,30);return e+"ms "+t}};return b}function t0({keyframes:e,velocity:t=0,power:n=.8,timeConstant:i=325,bounceDamping:r=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){let d,h;let f=e[0],p={done:!1,value:f},m=e=>void 0!==a&&e<a||void 0!==l&&e>l,v=e=>void 0===a?l:void 0===l?a:Math.abs(a-e)<Math.abs(l-e)?a:l,g=n*t,y=f+g,x=void 0===s?y:s(y);x!==y&&(g=x-f);let w=e=>-g*Math.exp(-e/i),b=e=>x+w(e),P=e=>{let t=w(e),n=b(e);p.done=Math.abs(t)<=u,p.value=p.done?x:n},A=e=>{m(p.value)&&(d=e,h=tJ({keyframes:[p.value,v(p.value)],velocity:tY(b,e,p.value),damping:r,stiffness:o,restDelta:u,restSpeed:c}))};return A(0),{calculatedDuration:null,next:e=>{let t=!1;return(h||void 0!==d||(t=!0,P(e),A(e)),void 0!==d&&e>=d)?h.next(e-d):(t||P(e),p)}}}let t1=em(.42,0,1,1),t2=em(0,0,.58,1),t5=em(.42,0,.58,1),t9=e=>Array.isArray(e)&&"number"!=typeof e[0],t3={linear:W,easeIn:t1,easeInOut:t5,easeOut:t2,circIn:eP,circInOut:eT,circOut:eA,backIn:ex,backInOut:ew,backOut:ey,anticipate:eb},t6=e=>{if(b(e)){W(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,n,i,r]=e;return em(t,n,i,r)}return"string"==typeof e?(W(void 0!==t3[e],`Invalid easing type '${e}'`),t3[e]):e};function t4({duration:e=300,keyframes:t,times:n,ease:i="easeInOut"}){let r=t9(i)?i.map(t6):t6(i),o={done:!1,value:t[0]},s=function(e,t,{clamp:n=!0,ease:i,mixer:r}={}){let o=e.length;if(W(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];if(2===o&&t[0]===t[1])return()=>t[1];let s=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,n){let i=[],r=n||tK,o=e.length-1;for(let n=0;n<o;n++){let o=r(e[n],e[n+1]);t&&(o=tN(Array.isArray(t)?t[n]||W:t,o)),i.push(o)}return i}(t,i,r),l=a.length,u=n=>{if(s&&n<e[0])return t[0];let i=0;if(l>1)for(;i<e.length-2&&!(n<e[i+1]);i++);let r=T(e[i],e[i+1],n);return a[i](r)};return n?t=>u(eS(e[0],e[o-1],t)):u}((n&&n.length===t.length?n:function(e){let t=[0];return function(e,t){let n=e[e.length-1];for(let i=1;i<=t;i++){let r=T(0,t,i);e.push(tk(n,1,r))}}(t,e.length-1),t}(t)).map(t=>t*e),t,{ease:Array.isArray(r)?r:t.map(()=>r||t5).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(o.value=s(t),o.done=t>=e,o)}}let t8=e=>{let t=({timestamp:t})=>e(t);return{start:()=>q.update(t,!0),stop:()=>Z(t),now:()=>Q.isProcessing?Q.timestamp:et.now()}},t7={decay:t0,inertia:t0,tween:t4,keyframes:t4,spring:tJ},ne=e=>e/100;class nt extends tR{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:e}=this.options;e&&e()};let{name:t,motionValue:n,element:i,keyframes:r}=this.options,o=(null==i?void 0:i.KeyframeResolver)||tp;this.resolver=new o(r,(e,t)=>this.onKeyframesResolved(e,t),t,n,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){let t,n;let{type:i="keyframes",repeat:r=0,repeatDelay:o=0,repeatType:s,velocity:a=0}=this.options,l=x(i)?i:t7[i]||t4;l!==t4&&"number"!=typeof e[0]&&(t=tN(ne,tK(e[0],e[1])),e=[0,100]);let u=l({...this.options,keyframes:e});"mirror"===s&&(n=l({...this.options,keyframes:[...e].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=y(u));let{calculatedDuration:c}=u,d=c+o;return{generator:u,mirroredGenerator:n,mapPercentToKeyframes:t,calculatedDuration:c,resolvedDuration:d,totalDuration:d*(r+1)-o}}onPostResolved(){let{autoplay:e=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&e?this.state=this.pendingPlayState:this.pause()}tick(e,t=!1){let{resolved:n}=this;if(!n){let{keyframes:e}=this.options;return{done:!0,value:e[e.length-1]}}let{finalKeyframe:i,generator:r,mirroredGenerator:o,mapPercentToKeyframes:s,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=n;if(null===this.startTime)return r.next(0);let{delay:d,repeat:h,repeatType:f,repeatDelay:p,onUpdate:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-u/this.speed,this.startTime)),t?this.currentTime=e:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;let v=this.currentTime-d*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=r;if(h){let e=Math.min(this.currentTime,u)/c,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,h+1))%2&&("reverse"===f?(n=1-n,p&&(n-=p/c)):"mirror"===f&&(x=o)),y=eS(0,1,n)*c}let w=g?{done:!1,value:a[0]}:x.next(y);s&&(w.value=s(w.value));let{done:b}=w;g||null===l||(b=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return P&&void 0!==i&&(w.value=tM(a,this.options,i)),m&&m(w.value),P&&this.finish(),w}get duration(){let{resolved:e}=this;return e?U(e.calculatedDuration):0}get time(){return U(this.currentTime)}set time(e){e=N(e),this.currentTime=e,null!==this.holdTime||0===this.speed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=U(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:e=t8,onPlay:t,startTime:n}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),t&&t();let i=this.driver.now();null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=i):this.startTime=null!=n?n:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(e=this.currentTime)&&void 0!==e?e:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:e}=this.options;e&&e()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}let nn=new Set(["opacity","clipPath","filter","transform"]),ni=f(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),nr={anticipate:eb,backInOut:ew,circInOut:eT};class no extends tR{constructor(e){super(e);let{name:t,motionValue:n,element:i,keyframes:r}=this.options;this.resolver=new tE(r,(e,t)=>this.onKeyframesResolved(e,t),t,n,i),this.resolver.scheduleResolve()}initPlayback(e,t){var n;let{duration:i=300,times:r,ease:o,type:s,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof o&&A()&&o in nr&&(o=nr[o]),x((n=this.options).type)||"spring"===n.type||!function e(t){return!!("function"==typeof t&&A()||!t||"string"==typeof t&&(t in C||A())||b(t)||Array.isArray(t)&&t.every(e))}(n.ease)){let{onComplete:t,onUpdate:n,motionValue:a,element:l,...u}=this.options,c=function(e,t){let n=new nt({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0}),i={done:!1,value:e[0]},r=[],o=0;for(;!i.done&&o<2e4;)r.push((i=n.sample(o)).value),o+=10;return{times:void 0,keyframes:r,duration:o-10,ease:"linear"}}(e,u);1===(e=c.keyframes).length&&(e[1]=e[0]),i=c.duration,r=c.times,o=c.ease,s="keyframes"}let c=function(e,t,n,{delay:i=0,duration:r=300,repeat:o=0,repeatType:s="loop",ease:a="easeInOut",times:l}={}){let u={[t]:n};l&&(u.offset=l);let c=function e(t,n){if(t)return"function"==typeof t&&A()?E(t,n):b(t)?S(t):Array.isArray(t)?t.map(t=>e(t,n)||C.easeOut):C[t]}(a,r);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:i,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"})}(a.owner.current,l,e,{...this.options,duration:i,times:r,ease:o});return c.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(w(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{let{onComplete:n}=this.options;a.set(tM(e,this.options,t)),n&&n(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:i,times:r,type:s,ease:o,keyframes:e}}get duration(){let{resolved:e}=this;if(!e)return 0;let{duration:t}=e;return U(t)}get time(){let{resolved:e}=this;if(!e)return 0;let{animation:t}=e;return U(t.currentTime||0)}set time(e){let{resolved:t}=this;if(!t)return;let{animation:n}=t;n.currentTime=N(e)}get speed(){let{resolved:e}=this;if(!e)return 1;let{animation:t}=e;return t.playbackRate}set speed(e){let{resolved:t}=this;if(!t)return;let{animation:n}=t;n.playbackRate=e}get state(){let{resolved:e}=this;if(!e)return"idle";let{animation:t}=e;return t.playState}get startTime(){let{resolved:e}=this;if(!e)return null;let{animation:t}=e;return t.startTime}attachTimeline(e){if(this._resolved){let{resolved:t}=this;if(!t)return W;let{animation:n}=t;w(n,e)}else this.pendingTimeline=e;return W}play(){if(this.isStopped)return;let{resolved:e}=this;if(!e)return;let{animation:t}=e;"finished"===t.playState&&this.updateFinishedPromise(),t.play()}pause(){let{resolved:e}=this;if(!e)return;let{animation:t}=e;t.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:e}=this;if(!e)return;let{animation:t,keyframes:n,duration:i,type:r,ease:o,times:s}=e;if("idle"===t.playState||"finished"===t.playState)return;if(this.time){let{motionValue:e,onUpdate:t,onComplete:a,element:l,...u}=this.options,c=new nt({...u,keyframes:n,duration:i,type:r,ease:o,times:s,isGenerator:!0}),d=N(this.time);e.setWithVelocity(c.sample(d-10).value,c.sample(d).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:e}=this;e&&e.animation.finish()}cancel(){let{resolved:e}=this;e&&e.animation.cancel()}static supports(e){let{motionValue:t,name:n,repeatDelay:i,repeatType:r,damping:o,type:s}=e;if(!t||!t.owner||!(t.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=t.owner.getProps();return ni()&&n&&nn.has(n)&&!a&&!l&&!i&&"mirror"!==r&&0!==o&&"inertia"!==s}}let ns={type:"spring",stiffness:500,damping:25,restSpeed:10},na=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),nl={type:"keyframes",duration:.8},nu={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},nc=(e,{keyframes:t})=>t.length>2?nl:z.has(e)?e.startsWith("scale")?na(t[1]):ns:nu,nd=(e,t,n,i={},r,o)=>s=>{let a=g(i,e)||{},l=a.delay||i.delay||0,{elapsed:u=0}=i;u-=N(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:e=>{t.set(e),a.onUpdate&&a.onUpdate(e)},onComplete:()=>{s(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:o?void 0:r};!function({when:e,delay:t,delayChildren:n,staggerChildren:i,staggerDirection:r,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(a)&&(c={...c,...nc(e,c)}),c.duration&&(c.duration=N(c.duration)),c.repeatDelay&&(c.repeatDelay=N(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let d=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0!==c.delay||(d=!0)),(ef.current||Y.skipAnimations)&&(d=!0,c.duration=0,c.delay=0),d&&!o&&void 0!==t.get()){let e=tM(c.keyframes,a);if(void 0!==e)return q.update(()=>{c.onUpdate(e),c.onComplete()}),new v([])}return!o&&no.supports(c)?new no(c):new nt(c)};function nh(e,t,{delay:n=0,transitionOverride:i,type:r}={}){var o;let{transition:s=e.getDefaultTransition(),transitionEnd:a,...l}=t;i&&(s=i);let u=[],d=r&&e.animationState&&e.animationState.getState()[r];for(let t in l){let i=e.getValue(t,null!==(o=e.latestValues[t])&&void 0!==o?o:null),r=l[t];if(void 0===r||d&&function({protectedKeys:e,needsAnimating:t},n){let i=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,i}(d,t))continue;let a={delay:n,...g(s||{},t)},c=!1;if(window.MotionHandoffAnimation){let n=e.props[eh];if(n){let e=window.MotionHandoffAnimation(n,t,q);null!==e&&(a.startTime=e,c=!0)}}ec(e,t),i.start(nd(t,i,r,e.shouldReduceMotion&&H.has(t)?{type:!1}:a,e,c));let h=i.animation;h&&u.push(h)}return a&&Promise.all(u).then(()=>{q.update(()=>{a&&function(e,t){let{transitionEnd:n={},transition:i={},...r}=c(e,t)||{};for(let t in r={...r,...n}){let n=K(r[t]);e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,el(n))}}(e,a)})}),u}function nf(e,t,n={}){var i;let r=c(e,t,"exit"===n.type?null===(i=e.presenceContext)||void 0===i?void 0:i.custom:void 0),{transition:o=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(o=n.transitionOverride);let s=r?()=>Promise.all(nh(e,r,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(i=0)=>{let{delayChildren:r=0,staggerChildren:s,staggerDirection:a}=o;return function(e,t,n=0,i=0,r=1,o){let s=[],a=(e.variantChildren.size-1)*i,l=1===r?(e=0)=>e*i:(e=0)=>a-e*i;return Array.from(e.variantChildren).sort(np).forEach((e,i)=>{e.notify("AnimationStart",t),s.push(nf(e,t,{...o,delay:n+l(i)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(s)}(e,t,r+i,s,a,n)}:()=>Promise.resolve(),{when:l}=o;if(!l)return Promise.all([s(),a(n.delay)]);{let[e,t]="beforeChildren"===l?[s,a]:[a,s];return e().then(()=>t())}}function np(e,t){return e.sortNodePosition(t)}let nm=h.length,nv=[...d].reverse(),ng=d.length;function ny(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nx(){return{animate:ny(!0),whileInView:ny(),whileHover:ny(),whileTap:ny(),whileDrag:ny(),whileFocus:ny(),exit:ny()}}class nw{constructor(e){this.isMounted=!1,this.node=e}update(){}}class nb extends nw{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let i;if(e.notify("AnimationStart",t),Array.isArray(t))i=Promise.all(t.map(t=>nf(e,t,n)));else if("string"==typeof t)i=nf(e,t,n);else{let r="function"==typeof t?c(e,t,n.custom):t;i=Promise.all(nh(e,r,n))}return i.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=nx(),i=!0,l=t=>(n,i)=>{var r;let o=c(e,i,"exit"===t?null===(r=e.presenceContext)||void 0===r?void 0:r.custom:void 0);if(o){let{transition:e,transitionEnd:t,...i}=o;n={...n,...i,...t}}return n};function u(u){let{props:c}=e,d=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<nm;e++){let i=h[e],r=t.props[i];(a(r)||!1===r)&&(n[i]=r)}return n}(e.parent)||{},f=[],p=new Set,m={},v=1/0;for(let t=0;t<ng;t++){var g,y;let h=nv[t],x=n[h],w=void 0!==c[h]?c[h]:d[h],b=a(w),P=h===u?x.isActive:null;!1===P&&(v=t);let A=w===d[h]&&w!==c[h]&&b;if(A&&i&&e.manuallyAnimateOnMount&&(A=!1),x.protectedKeys={...m},!x.isActive&&null===P||!w&&!x.prevProp||r(w)||"boolean"==typeof w)continue;let T=(g=x.prevProp,"string"==typeof(y=w)?y!==g:!!Array.isArray(y)&&!s(y,g)),E=T||h===u&&x.isActive&&!A&&b||t>v&&b,S=!1,C=Array.isArray(w)?w:[w],M=C.reduce(l(h),{});!1===P&&(M={});let{prevResolvedValues:R={}}=x,k={...R,...M},D=t=>{E=!0,p.has(t)&&(S=!0,p.delete(t)),x.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in k){let t=M[e],n=R[e];if(m.hasOwnProperty(e))continue;let i=!1;(o(t)&&o(n)?s(t,n):t===n)?void 0!==t&&p.has(e)?D(e):x.protectedKeys[e]=!0:null!=t?D(e):p.add(e)}x.prevProp=w,x.prevResolvedValues=M,x.isActive&&(m={...m,...M}),i&&e.blockInitialAnimation&&(E=!1);let L=!(A&&T)||S;E&&L&&f.push(...C.map(e=>({animation:e,options:{type:h}})))}if(p.size){let t={};p.forEach(n=>{let i=e.getBaseTarget(n),r=e.getValue(n);r&&(r.liveStyle=!0),t[n]=null!=i?i:null}),f.push({animation:t})}let x=!!f.length;return i&&(!1===c.initial||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(x=!1),i=!1,x?t(f):Promise.resolve()}return{animateChanges:u,setActive:function(t,i){var r;if(n[t].isActive===i)return Promise.resolve();null===(r=e.variantChildren)||void 0===r||r.forEach(e=>{var n;return null===(n=e.animationState)||void 0===n?void 0:n.setActive(t,i)}),n[t].isActive=i;let o=u(t);for(let e in n)n[e].protectedKeys={};return o},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=nx(),i=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();r(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null===(e=this.unmountControls)||void 0===e||e.call(this)}}let nP=0;class nA extends nw{constructor(){super(...arguments),this.id=nP++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let i=this.node.animationState.setActive("exit",!e);t&&!e&&i.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}function nT(e,t,n,i={passive:!0}){return e.addEventListener(t,n,i),()=>e.removeEventListener(t,n)}function nE(e){return{point:{x:e.pageX,y:e.pageY}}}let nS=e=>t=>L(t)&&e(t,nE(t));function nC(e,t,n,i){return nT(e,t,nS(n),i)}let nM=(e,t)=>Math.abs(e-t);class nR{constructor(e,t,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nL(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(nM(e.x,t.x)**2+nM(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;let{point:i}=e,{timestamp:r}=Q;this.history.push({...i,timestamp:r});let{onStart:o,onMove:s}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=nk(t,this.transformPagePoint),q.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:i,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=nL("pointercancel"===e.type?this.lastMoveEventInfo:nk(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),i&&i(e,o)},!L(e))return;this.dragSnapToOrigin=r,this.handlers=t,this.transformPagePoint=n,this.contextWindow=i||window;let o=nk(nE(e),this.transformPagePoint),{point:s}=o,{timestamp:a}=Q;this.history=[{...s,timestamp:a}];let{onSessionStart:l}=t;l&&l(e,nL(o,this.history)),this.removeListeners=tN(nC(this.contextWindow,"pointermove",this.handlePointerMove),nC(this.contextWindow,"pointerup",this.handlePointerUp),nC(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),Z(this.updatePoint)}}function nk(e,t){return t?{point:t(e.point)}:e}function nD(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nL({point:e},t){return{point:e,delta:nD(e,nj(t)),offset:nD(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,i=null,r=nj(e);for(;n>=0&&(i=e[n],!(r.timestamp-i.timestamp>N(.1)));)n--;if(!i)return{x:0,y:0};let o=U(r.timestamp-i.timestamp);if(0===o)return{x:0,y:0};let s={x:(r.x-i.x)/o,y:(r.y-i.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}(t,.1)}}function nj(e){return e[e.length-1]}function nV(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function nF(e){return e.max-e.min}function nO(e,t,n,i=.5){e.origin=i,e.originPoint=tk(t.min,t.max,e.origin),e.scale=nF(n)/nF(t),e.translate=tk(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nB(e,t,n,i){nO(e.x,t.x,n.x,i?i.originX:void 0),nO(e.y,t.y,n.y,i?i.originY:void 0)}function nI(e,t,n){e.min=n.min+t.min,e.max=e.min+nF(t)}function nN(e,t,n){e.min=t.min-n.min,e.max=e.min+nF(t)}function nU(e,t,n){nN(e.x,t.x,n.x),nN(e.y,t.y,n.y)}function nW(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function n_(e,t){let n=t.min-e.min,i=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,i]=[i,n]),{min:n,max:i}}function nz(e,t,n){return{min:nH(e,t),max:nH(e,n)}}function nH(e,t){return"number"==typeof e?e:e[t]||0}let n$=()=>({translate:0,scale:1,origin:0,originPoint:0}),nK=()=>({x:n$(),y:n$()}),nY=()=>({min:0,max:0}),nX=()=>({x:nY(),y:nY()});function nG(e){return[e("x"),e("y")]}function nq({top:e,left:t,right:n,bottom:i}){return{x:{min:t,max:n},y:{min:e,max:i}}}function nZ(e){return void 0===e||1===e}function nQ({scale:e,scaleX:t,scaleY:n}){return!nZ(e)||!nZ(t)||!nZ(n)}function nJ(e){return nQ(e)||n0(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function n0(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function n1(e,t,n,i,r){return void 0!==r&&(e=i+r*(e-i)),i+n*(e-i)+t}function n2(e,t=0,n=1,i,r){e.min=n1(e.min,t,n,i,r),e.max=n1(e.max,t,n,i,r)}function n5(e,{x:t,y:n}){n2(e.x,t.translate,t.scale,t.originPoint),n2(e.y,n.translate,n.scale,n.originPoint)}function n9(e,t){e.min=e.min+t,e.max=e.max+t}function n3(e,t,n,i,r=.5){let o=tk(e.min,e.max,r);n2(e,t,n,o,i)}function n6(e,t){n3(e.x,t.x,t.scaleX,t.scale,t.originX),n3(e.y,t.y,t.scaleY,t.scale,t.originY)}function n4(e,t){return nq(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),i=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(e.getBoundingClientRect(),t))}let n8=({current:e})=>e?e.ownerDocument.defaultView:null,n7=new WeakMap;class ie{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nX(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new nR(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(nE(e).point)},onStart:(e,t)=>{var n;let{drag:i,dragPropagation:r,onDragStart:o}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(n=i)||"y"===n?M[n]?null:(M[n]=!0,()=>{M[n]=!1}):M.x||M.y?null:(M.x=M.y=!0,()=>{M.x=M.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nG(e=>{let t=this.getAxisMotionValue(e).get()||0;if(eW.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let i=n.layout.layoutBox[e];i&&(t=nF(i)*(parseFloat(t)/100))}}this.originPoint[e]=t}),o&&q.postRender(()=>o(e,t)),ec(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:n,dragDirectionLock:i,onDirectionLock:r,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:s}=t;if(i&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(s),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>nG(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:n8(this.visualElement)})}stop(e,t){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:i}=t;this.startAnimation(i);let{onDragEnd:r}=this.getProps();r&&q.postRender(()=>r(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:i}=this.getProps();if(!n||!it(e,i,this.currentDirection))return;let r=this.getAxisMotionValue(e),o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:n},i){return void 0!==t&&e<t?e=i?tk(t,e,i.min):Math.max(e,t):void 0!==n&&e>n&&(e=i?tk(n,e,i.max):Math.min(e,n)),e}(o,this.constraints[e],this.elastic[e])),r.set(o)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:n}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,r=this.constraints;t&&nV(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(e,{top:t,left:n,bottom:i,right:r}){return{x:nW(e.x,n,r),y:nW(e.y,t,i)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:nz(e,"left","right"),y:nz(e,"top","bottom")}}(n),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&nG(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(i.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!nV(t))return!1;let i=t.current;W(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let o=function(e,t,n){let i=n4(e,n),{scroll:r}=t;return r&&(n9(i.x,r.offset.x),n9(i.y,r.offset.y)),i}(i,r.root,this.visualElement.getTransformPagePoint()),s={x:n_((e=r.layout.layoutBox).x,o.x),y:n_(e.y,o.y)};if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(s));this.hasMutatedConstraints=!!e,e&&(s=nq(e))}return s}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:i,dragTransition:r,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{};return Promise.all(nG(s=>{if(!it(s,t,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[s]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(s,u)})).then(s)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return ec(this.visualElement,e),n.start(nd(e,n,0,t,this.visualElement,!1))}stopAnimation(){nG(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nG(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){nG(t=>{let{drag:n}=this.getProps();if(!it(t,n,this.currentDirection))return;let{projection:i}=this.visualElement,r=this.getAxisMotionValue(t);if(i&&i.layout){let{min:n,max:o}=i.layout.layoutBox[t];r.set(e[t]-tk(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!nV(t)||!n||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};nG(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();i[e]=function(e,t){let n=.5,i=nF(e),r=nF(t);return r>i?n=T(t.min,t.max-i,e.min):i>r&&(n=T(e.min,e.max-r,t.min)),eS(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),nG(t=>{if(!it(t,e,null))return;let n=this.getAxisMotionValue(t),{min:r,max:o}=this.constraints[t];n.set(tk(r,o,i[t]))})}addListeners(){if(!this.visualElement.current)return;n7.set(this.visualElement,this);let e=nC(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();nV(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),q.read(t);let r=nT(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(nG(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{r(),e(),i(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:r=!1,dragElastic:o=.35,dragMomentum:s=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:i,dragConstraints:r,dragElastic:o,dragMomentum:s}}}function it(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class ii extends nw{constructor(e){super(e),this.removeGroupControls=W,this.removeListeners=W,this.controls=new ie(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||W}unmount(){this.removeGroupControls(),this.removeListeners()}}let ir=e=>(t,n)=>{e&&q.postRender(()=>e(t,n))};class io extends nw{constructor(){super(...arguments),this.removePointerDownListener=W}onPointerDown(e){this.session=new nR(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:n8(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:ir(e),onStart:ir(t),onMove:n,onEnd:(e,t)=>{delete this.session,i&&q.postRender(()=>i(e,t))}}}mount(){this.removePointerDownListener=nC(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var is,ia,il=n(95155),iu=n(12115),ic=n(32082),id=n(90869);let ih=(0,iu.createContext)({}),ip={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function im(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let iv={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!e_.test(e))return e;e=parseFloat(e)}let n=im(e,t.target.x),i=im(e,t.target.y);return`${n}% ${i}%`}},ig={},{schedule:iy,cancel:ix}=G(queueMicrotask,!1);class iw extends iu.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:i}=this.props,{projection:r}=e;Object.assign(ig,iP),r&&(t.group&&t.group.add(r),n&&n.register&&i&&n.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),ip.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:i,isPresent:r}=this.props,o=n.projection;return o&&(o.isPresent=r,i||e.layoutDependency!==t||void 0===t?o.willUpdate():this.safeToRemove(),e.isPresent===r||(r?o.promote():o.relegate()||q.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),iy.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function ib(e){let[t,n]=(0,ic.xQ)(),i=(0,iu.useContext)(id.L);return(0,il.jsx)(iw,{...e,layoutGroup:i,switchLayoutGroup:(0,iu.useContext)(ih),isPresent:t,safeToRemove:n})}let iP={borderRadius:{...iv,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:iv,borderTopRightRadius:iv,borderBottomLeftRadius:iv,borderBottomRightRadius:iv,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let i=e2.parse(e);if(i.length>5)return e;let r=e2.createTransformer(e),o=+("number"!=typeof i[0]),s=n.x.scale*t.x,a=n.y.scale*t.y;i[0+o]/=s,i[1+o]/=a;let l=tk(s,a,.5);return"number"==typeof i[2+o]&&(i[2+o]/=l),"number"==typeof i[3+o]&&(i[3+o]/=l),r(i)}}},iA=(e,t)=>e.depth-t.depth;class iT{constructor(){this.children=[],this.isDirty=!1}add(e){en(this.children,e),this.isDirty=!0}remove(e){ei(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(iA),this.isDirty=!1,this.children.forEach(e)}}function iE(e){let t=eu(e)?e.get():e;return $(t)?t.toValue():t}let iS=["TopLeft","TopRight","BottomLeft","BottomRight"],iC=iS.length,iM=e=>"string"==typeof e?parseFloat(e):e,iR=e=>"number"==typeof e||e_.test(e);function ik(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let iD=ij(0,.5,eA),iL=ij(.5,.95,W);function ij(e,t,n){return i=>i<e?0:i>t?1:n(T(e,t,i))}function iV(e,t){e.min=t.min,e.max=t.max}function iF(e,t){iV(e.x,t.x),iV(e.y,t.y)}function iO(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function iB(e,t,n,i,r){return e-=t,e=i+1/n*(e-i),void 0!==r&&(e=i+1/r*(e-i)),e}function iI(e,t,[n,i,r],o,s){!function(e,t=0,n=1,i=.5,r,o=e,s=e){if(eW.test(t)&&(t=parseFloat(t),t=tk(s.min,s.max,t/100)-s.min),"number"!=typeof t)return;let a=tk(o.min,o.max,i);e===o&&(a-=t),e.min=iB(e.min,t,n,a,r),e.max=iB(e.max,t,n,a,r)}(e,t[n],t[i],t[r],t.scale,o,s)}let iN=["x","scaleX","originX"],iU=["y","scaleY","originY"];function iW(e,t,n,i){iI(e.x,t,iN,n?n.x:void 0,i?i.x:void 0),iI(e.y,t,iU,n?n.y:void 0,i?i.y:void 0)}function i_(e){return 0===e.translate&&1===e.scale}function iz(e){return i_(e.x)&&i_(e.y)}function iH(e,t){return e.min===t.min&&e.max===t.max}function i$(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function iK(e,t){return i$(e.x,t.x)&&i$(e.y,t.y)}function iY(e){return nF(e.x)/nF(e.y)}function iX(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class iG{constructor(){this.members=[]}add(e){en(this.members,e),e.scheduleRender()}remove(e){if(ei(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:i}=e.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let iq={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},iZ="undefined"!=typeof window&&void 0!==window.MotionDebug,iQ=["","X","Y","Z"],iJ={visibility:"hidden"},i0=0;function i1(e,t,n,i){let{latestValues:r}=t;r[e]&&(n[e]=r[e],t.setStaticValue(e,0),i&&(i[e]=0))}function i2({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:i,resetTransform:r}){return class{constructor(e={},n=null==t?void 0:t()){this.id=i0++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,iZ&&(iq.totalNodes=iq.resolvedTargetDeltas=iq.recalculatedProjection=0),this.nodes.forEach(i3),this.nodes.forEach(rn),this.nodes.forEach(ri),this.nodes.forEach(i6),iZ&&window.MotionDebug.record(iq)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new iT)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new er),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,n=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:i,layout:r,visualElement:o}=this.options;if(o&&!o.current&&o.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(r||i)&&(this.isLayoutDirty=!0),e){let n;let i=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=et.now(),i=({timestamp:r})=>{let o=r-n;o>=250&&(Z(i),e(o-t))};return q.read(i,!0),()=>Z(i)}(i,250),ip.hasAnimatedSinceResize&&(ip.hasAnimatedSinceResize=!1,this.nodes.forEach(rt))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||r)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||o.getDefaultTransition()||ru,{onLayoutAnimationStart:s,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!iK(this.targetLayout,i)||n,u=!t&&n;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,u);let t={...g(r,"layout"),onPlay:s,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||rt(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Z(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rr),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let i=n.props[eh];if(window.MotionHasOptimisedAnimation(i,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(i,"transform",q,!(e||n))}let{parent:r}=t;r&&!r.hasCheckedOptimisedAppear&&e(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(i8);return}this.isUpdating||this.nodes.forEach(i7),this.isUpdating=!1,this.nodes.forEach(re),this.nodes.forEach(i5),this.nodes.forEach(i9),this.clearAllSnapshots();let e=et.now();Q.delta=eS(0,1e3/60,e-Q.timestamp),Q.timestamp=e,Q.isProcessing=!0,J.update.process(Q),J.preRender.process(Q),J.render.process(Q),Q.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,iy.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(i4),this.sharedNodes.forEach(ro)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,q.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){q.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nX(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t){let t=i(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!r)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!iz(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;e&&(t||nJ(this.latestValues)||o)&&(r(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),i=this.removeElementScroll(n);return e&&(i=this.removeTransform(i)),rh((t=i).x),rh(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){var e;let{visualElement:t}=this.options;if(!t)return nX();let n=t.measureViewportBox();if(!((null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)||this.path.some(rp))){let{scroll:e}=this.root;e&&(n9(n.x,e.offset.x),n9(n.y,e.offset.y))}return n}removeElementScroll(e){var t;let n=nX();if(iF(n,e),null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)return n;for(let t=0;t<this.path.length;t++){let i=this.path[t],{scroll:r,options:o}=i;i!==this.root&&r&&o.layoutScroll&&(r.wasRoot&&iF(n,e),n9(n.x,r.offset.x),n9(n.y,r.offset.y))}return n}applyTransform(e,t=!1){let n=nX();iF(n,e);for(let e=0;e<this.path.length;e++){let i=this.path[e];!t&&i.options.layoutScroll&&i.scroll&&i!==i.root&&n6(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),nJ(i.latestValues)&&n6(n,i.latestValues)}return nJ(this.latestValues)&&n6(n,this.latestValues),n}removeTransform(e){let t=nX();iF(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!nJ(n.latestValues))continue;nQ(n.latestValues)&&n.updateSnapshot();let i=nX();iF(i,n.measurePageBox()),iW(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,i)}return nJ(this.latestValues)&&iW(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Q.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,n,i,r;let o=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=o.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=o.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=o.isSharedProjectionDirty);let s=!!this.resumingFrom||this!==o;if(!(e||s&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=Q.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nX(),this.relativeTargetOrigin=nX(),nU(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),iF(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=nX(),this.targetWithTransforms=nX()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),n=this.target,i=this.relativeTarget,r=this.relativeParent.target,nI(n.x,i.x,r.x),nI(n.y,i.y,r.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iF(this.target,this.layout.layoutBox),n5(this.target,this.targetDelta)):iF(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nX(),this.relativeTargetOrigin=nX(),nU(this.relativeTargetOrigin,this.target,e.target),iF(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}iZ&&iq.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||nQ(this.parent.latestValues)||n0(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),n=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(i=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===Q.timestamp&&(i=!1),i)return;let{layout:r,layoutId:o}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||o))return;iF(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;!function(e,t,n,i=!1){let r,o;let s=n.length;if(s){t.x=t.y=1;for(let a=0;a<s;a++){o=(r=n[a]).projectionDelta;let{visualElement:s}=r.options;(!s||!s.props.style||"contents"!==s.props.style.display)&&(i&&r.options.layoutScroll&&r.scroll&&r!==r.root&&n6(e,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,n5(e,o)),i&&nJ(r.latestValues)&&n6(e,r.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,n),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=nX());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(iO(this.prevProjectionDelta.x,this.projectionDelta.x),iO(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nB(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&iX(this.projectionDelta.x,this.prevProjectionDelta.x)&&iX(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),iZ&&iq.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){var t;if(null===(t=this.options.visualElement)||void 0===t||t.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=nK(),this.projectionDelta=nK(),this.projectionDeltaWithTransform=nK()}setAnimationOrigin(e,t=!1){let n;let i=this.snapshot,r=i?i.latestValues:{},o={...this.latestValues},s=nK();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=nX(),l=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(rl));this.animationProgress=0,this.mixTargetDelta=t=>{let i=t/1e3;if(rs(s.x,e.x,i),rs(s.y,e.y,i),this.setTargetDelta(s),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,h,f,p,m,v;if(nU(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=a,v=i,ra(f.x,p.x,m.x,v),ra(f.y,p.y,m.y,v),n&&(u=this.relativeTarget,h=n,iH(u.x,h.x)&&iH(u.y,h.y)))this.isProjectionDirty=!1;n||(n=nX()),iF(n,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,n,i,r,o){r?(e.opacity=tk(0,void 0!==n.opacity?n.opacity:1,iD(i)),e.opacityExit=tk(void 0!==t.opacity?t.opacity:1,0,iL(i))):o&&(e.opacity=tk(void 0!==t.opacity?t.opacity:1,void 0!==n.opacity?n.opacity:1,i));for(let r=0;r<iC;r++){let o=`border${iS[r]}Radius`,s=ik(t,o),a=ik(n,o);(void 0!==s||void 0!==a)&&(s||(s=0),a||(a=0),0===s||0===a||iR(s)===iR(a)?(e[o]=Math.max(tk(iM(s),iM(a),i),0),(eW.test(a)||eW.test(s))&&(e[o]+="%")):e[o]=a)}(t.rotate||n.rotate)&&(e.rotate=tk(t.rotate||0,n.rotate||0,i))}(o,r,this.latestValues,i,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Z(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=q.update(()=>{ip.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,n){let i=eu(0)?0:el(e);return i.start(nd("",i,1e3,n)),i.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:i,latestValues:r}=e;if(t&&n&&i){if(this!==e&&this.layout&&i&&rf(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||nX();let t=nF(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let i=nF(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+i}iF(t,n),n6(t,r),nB(this.projectionDeltaWithTransform,this.layoutCorrected,t,r)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new iG),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let i=this.getStack();i&&i.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let i={};n.z&&i1("z",e,i,this.animationValues);for(let t=0;t<iQ.length;t++)i1(`rotate${iQ[t]}`,e,i,this.animationValues),i1(`skew${iQ[t]}`,e,i,this.animationValues);for(let t in e.render(),i)e.setStaticValue(t,i[t]),this.animationValues&&(this.animationValues[t]=i[t]);e.scheduleRender()}getProjectionStyles(e){var t,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return iJ;let i={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=iE(null==e?void 0:e.pointerEvents)||"",i.transform=r?r(this.latestValues,""):"none",i;let o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=iE(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!nJ(this.latestValues)&&(t.transform=r?r({},""):"none",this.hasProjected=!1),t}let s=o.animationValues||o.latestValues;this.applyTransformsToTarget(),i.transform=function(e,t,n){let i="",r=e.x.translate/t.x,o=e.y.translate/t.y,s=(null==n?void 0:n.z)||0;if((r||o||s)&&(i=`translate3d(${r}px, ${o}px, ${s}px) `),(1!==t.x||1!==t.y)&&(i+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:r,rotateY:o,skewX:s,skewY:a}=n;e&&(i=`perspective(${e}px) ${i}`),t&&(i+=`rotate(${t}deg) `),r&&(i+=`rotateX(${r}deg) `),o&&(i+=`rotateY(${o}deg) `),s&&(i+=`skewX(${s}deg) `),a&&(i+=`skewY(${a}deg) `)}let a=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==a||1!==l)&&(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),r&&(i.transform=r(s,i.transform));let{x:a,y:l}=this.projectionDelta;for(let e in i.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?i.opacity=o===this?null!==(n=null!==(t=s.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:i.opacity=o===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,ig){if(void 0===s[e])continue;let{correct:t,applyTo:n}=ig[e],r="none"===i.transform?s[e]:t(s[e],o);if(n){let e=n.length;for(let t=0;t<e;t++)i[n[t]]=r}else i[e]=r}return this.options.layoutId&&(i.pointerEvents=o===this?iE(null==e?void 0:e.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(i8),this.root.sharedNodes.clear()}}}function i5(e){e.updateLayout()}function i9(e){var t;let n=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:i}=e.layout,{animationType:r}=e.options,o=n.source!==e.layout.source;"size"===r?nG(e=>{let i=o?n.measuredBox[e]:n.layoutBox[e],r=nF(i);i.min=t[e].min,i.max=i.min+r}):rf(r,n.layoutBox,t)&&nG(i=>{let r=o?n.measuredBox[i]:n.layoutBox[i],s=nF(t[i]);r.max=r.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[i].max=e.relativeTarget[i].min+s)});let s=nK();nB(s,t,n.layoutBox);let a=nK();o?nB(a,e.applyTransform(i,!0),n.measuredBox):nB(a,t,n.layoutBox);let l=!iz(s),u=!1;if(!e.resumeFrom){let i=e.getClosestProjectingParent();if(i&&!i.resumeFrom){let{snapshot:r,layout:o}=i;if(r&&o){let s=nX();nU(s,n.layoutBox,r.layoutBox);let a=nX();nU(a,t,o.layoutBox),iK(s,a)||(u=!0),i.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=s,e.relativeParent=i)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:n,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function i3(e){iZ&&iq.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function i6(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function i4(e){e.clearSnapshot()}function i8(e){e.clearMeasurements()}function i7(e){e.isLayoutDirty=!1}function re(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rt(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rn(e){e.resolveTargetDelta()}function ri(e){e.calcProjection()}function rr(e){e.resetSkewAndRotation()}function ro(e){e.removeLeadSnapshot()}function rs(e,t,n){e.translate=tk(t.translate,0,n),e.scale=tk(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function ra(e,t,n,i){e.min=tk(t.min,n.min,i),e.max=tk(t.max,n.max,i)}function rl(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let ru={duration:.45,ease:[.4,0,.1,1]},rc=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),rd=rc("applewebkit/")&&!rc("chrome/")?Math.round:W;function rh(e){e.min=rd(e.min),e.max=rd(e.max)}function rf(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(iY(t)-iY(n)))}function rp(e){var t;return e!==e.root&&(null===(t=e.scroll)||void 0===t?void 0:t.wasRoot)}let rm=i2({attachResizeListener:(e,t)=>nT(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rv={current:void 0},rg=i2({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!rv.current){let e=new rm({});e.mount(window),e.setOptions({layoutScroll:!0}),rv.current=e}return rv.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function ry(e,t,n){let{props:i}=e;e.animationState&&i.whileHover&&e.animationState.setActive("whileHover","Start"===n);let r=i["onHover"+n];r&&q.postRender(()=>r(t,nE(t)))}class rx extends nw{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[i,r,o]=R(e,n),s=k(e=>{let{target:n}=e,i=t(e);if("function"!=typeof i||!n)return;let o=k(e=>{i(e),n.removeEventListener("pointerleave",o)});n.addEventListener("pointerleave",o,r)});return i.forEach(e=>{e.addEventListener("pointerenter",s,r)}),o}(e,e=>(ry(this.node,e,"Start"),e=>ry(this.node,e,"End"))))}unmount(){}}class rw extends nw{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tN(nT(this.node.current,"focus",()=>this.onFocus()),nT(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function rb(e,t,n){let{props:i}=e;e.animationState&&i.whileTap&&e.animationState.setActive("whileTap","Start"===n);let r=i["onTap"+("End"===n?"":n)];r&&q.postRender(()=>r(t,nE(t)))}class rP extends nw{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[i,r,o]=R(e,n),s=e=>{let i=e.currentTarget;if(!I(e)||V.has(i))return;V.add(i);let o=t(e),s=(e,t)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),I(e)&&V.has(i)&&(V.delete(i),"function"==typeof o&&o(e,{success:t}))},a=e=>{s(e,n.useGlobalTarget||D(i,e.target))},l=e=>{s(e,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return i.forEach(e=>{!j.has(e.tagName)&&-1===e.tabIndex&&null===e.getAttribute("tabindex")&&(e.tabIndex=0),(n.useGlobalTarget?window:e).addEventListener("pointerdown",s,r),e.addEventListener("focus",e=>B(e,r),r)}),o}(e,e=>(rb(this.node,e,"Start"),(e,{success:t})=>rb(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rA=new WeakMap,rT=new WeakMap,rE=e=>{let t=rA.get(e.target);t&&t(e)},rS=e=>{e.forEach(rE)},rC={some:0,all:1};class rM extends nw{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:i="some",once:r}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:rC[i]};return function(e,t,n){let i=function({root:e,...t}){let n=e||document;rT.has(n)||rT.set(n,{});let i=rT.get(n),r=JSON.stringify(t);return i[r]||(i[r]=new IntersectionObserver(rS,{root:e,...t})),i[r]}(t);return rA.set(e,n),i.observe(e),()=>{rA.delete(e),i.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,r&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=t?n:i;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let rR=(0,iu.createContext)({strict:!1});var rk=n(51508);let rD=(0,iu.createContext)({});function rL(e){return r(e.animate)||h.some(t=>a(e[t]))}function rj(e){return!!(rL(e)||e.variants)}function rV(e){return Array.isArray(e)?e.join(" "):e}var rF=n(68972);let rO={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rB={};for(let e in rO)rB[e]={isEnabled:t=>rO[e].some(e=>!!t[e])};let rI=Symbol.for("motionComponentSymbol");var rN=n(80845),rU=n(97494);let rW=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r_(e){if("string"!=typeof e||e.includes("-"));else if(rW.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var rz=n(82885);let rH=e=>(t,n)=>{let i=(0,iu.useContext)(rD),o=(0,iu.useContext)(rN.t),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},i,o,s){let a={latestValues:function(e,t,n,i){let o={},s=i(e,{});for(let e in s)o[e]=iE(s[e]);let{initial:a,animate:l}=e,c=rL(e),d=rj(e);t&&d&&!c&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===l&&(l=t.animate));let h=!!n&&!1===n.initial,f=(h=h||!1===a)?l:a;if(f&&"boolean"!=typeof f&&!r(f)){let t=Array.isArray(f)?f:[f];for(let n=0;n<t.length;n++){let i=u(e,t[n]);if(i){let{transitionEnd:e,transition:t,...n}=i;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=h?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(i,o,s,e),renderState:t()};return n&&(a.onMount=e=>n({props:i,current:e,...a}),a.onUpdate=e=>n(e)),a})(e,t,i,o);return n?s():(0,rz.M)(s)},r$=(e,t)=>t&&"number"==typeof e?t.transform(e):e,rK={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rY=_.length;function rX(e,t,n){let{style:i,vars:r,transformOrigin:o}=e,s=!1,a=!1;for(let e in t){let n=t[e];if(z.has(e)){s=!0;continue}if(tg(e)){r[e]=n;continue}{let t=r$(n,e8[e]);e.startsWith("origin")?(a=!0,o[e]=t):i[e]=t}}if(!t.transform&&(s||n?i.transform=function(e,t,n){let i="",r=!0;for(let o=0;o<rY;o++){let s=_[o],a=e[s];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!s.startsWith("scale"):0===parseFloat(a))||n){let e=r$(a,e8[s]);if(!l){r=!1;let t=rK[s]||s;i+=`${t}(${e}) `}n&&(t[s]=e)}}return i=i.trim(),n?i=n(t,r?"":i):r&&(i="none"),i}(t,e.transform,n):i.transform&&(i.transform="none")),a){let{originX:e="50%",originY:t="50%",originZ:n=0}=o;i.transformOrigin=`${e} ${t} ${n}`}}let rG={offset:"stroke-dashoffset",array:"stroke-dasharray"},rq={offset:"strokeDashoffset",array:"strokeDasharray"};function rZ(e,t,n){return"string"==typeof e?e:e_.transform(t+n*e)}function rQ(e,{attrX:t,attrY:n,attrScale:i,originX:r,originY:o,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},c,d){if(rX(e,u,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:h,style:f,dimensions:p}=e;h.transform&&(p&&(f.transform=h.transform),delete h.transform),p&&(void 0!==r||void 0!==o||f.transform)&&(f.transformOrigin=function(e,t,n){let i=rZ(t,e.x,e.width),r=rZ(n,e.y,e.height);return`${i} ${r}`}(p,void 0!==r?r:.5,void 0!==o?o:.5)),void 0!==t&&(h.x=t),void 0!==n&&(h.y=n),void 0!==i&&(h.scale=i),void 0!==s&&function(e,t,n=1,i=0,r=!0){e.pathLength=1;let o=r?rG:rq;e[o.offset]=e_.transform(-i);let s=e_.transform(t),a=e_.transform(n);e[o.array]=`${s} ${a}`}(h,s,a,l,!1)}let rJ=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),r0=()=>({...rJ(),attrs:{}}),r1=e=>"string"==typeof e&&"svg"===e.toLowerCase();function r2(e,{style:t,vars:n},i,r){for(let o in Object.assign(e.style,t,r&&r.getProjectionStyles(i)),n)e.style.setProperty(o,n[o])}let r5=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function r9(e,t,n,i){for(let n in r2(e,t,void 0,i),t.attrs)e.setAttribute(r5.has(n)?n:ed(n),t.attrs[n])}function r3(e,{layout:t,layoutId:n}){return z.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!ig[e]||"opacity"===e)}function r6(e,t,n){var i;let{style:r}=e,o={};for(let s in r)(eu(r[s])||t.style&&eu(t.style[s])||r3(s,e)||(null===(i=null==n?void 0:n.getValue(s))||void 0===i?void 0:i.liveStyle)!==void 0)&&(o[s]=r[s]);return o}function r4(e,t,n){let i=r6(e,t,n);for(let n in e)(eu(e[n])||eu(t[n]))&&(i[-1!==_.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return i}let r8=["x","y","width","height","cx","cy","r"],r7={useVisualState:rH({scrapeMotionValuesFromProps:r4,createRenderState:r0,onUpdate:({props:e,prevProps:t,current:n,renderState:i,latestValues:r})=>{if(!n)return;let o=!!e.drag;if(!o){for(let e in r)if(z.has(e)){o=!0;break}}if(!o)return;let s=!t;if(t)for(let n=0;n<r8.length;n++){let i=r8[n];e[i]!==t[i]&&(s=!0)}s&&q.read(()=>{!function(e,t){try{t.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(e){t.dimensions={x:0,y:0,width:0,height:0}}}(n,i),q.render(()=>{rQ(i,r,r1(n.tagName),e.transformTemplate),r9(n,i)})})}})},oe={useVisualState:rH({scrapeMotionValuesFromProps:r6,createRenderState:rJ})};function ot(e,t,n){for(let i in t)eu(t[i])||r3(i,n)||(e[i]=t[i])}let on=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function oi(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||on.has(e)}let or=e=>!oi(e);try{!function(e){e&&(or=t=>t.startsWith("on")?!oi(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}let oo={current:null},os={current:!1},oa=[...tA,eY,e2],ol=e=>oa.find(tP(e)),ou=new WeakMap,oc=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class od{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:r,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tp,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=et.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,q.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=o;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.blockInitialAnimation=!!r,this.isControllingVariants=rL(t),this.isVariantNode=rj(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:c,...d}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in d){let t=d[e];void 0!==a[e]&&eu(t)&&t.set(a[e],!1)}}mount(e){this.current=e,ou.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),os.current||function(){if(os.current=!0,rF.B){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>oo.current=e.matches;e.addListener(t),t()}else oo.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||oo.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in ou.delete(this.current),this.projection&&this.projection.unmount(),Z(this.notifyUpdate),Z(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let i=z.has(e),r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&q.preRender(this.notifyUpdate),i&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{r(),o(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in rB){let t=rB[e];if(!t)continue;let{isEnabled:n,Feature:i}=t;if(!this.features[e]&&i&&n(this.props)&&(this.features[e]=new i(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nX()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<oc.length;t++){let n=oc[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let i=e["on"+n];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=function(e,t,n){for(let i in t){let r=t[i],o=n[i];if(eu(r))e.addValue(i,r);else if(eu(o))e.addValue(i,el(r,{owner:e}));else if(o!==r){if(e.hasValue(i)){let t=e.getValue(i);!0===t.liveStyle?t.jump(r):t.hasAnimated||t.set(r)}else{let t=e.getStaticValue(i);e.addValue(i,el(void 0!==t?t:r,{owner:e}))}}}for(let i in n)void 0===t[i]&&e.removeValue(i);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=el(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){var n;let i=void 0===this.latestValues[e]&&this.current?null!==(n=this.getBaseTargetFromProps(this.props,e))&&void 0!==n?n:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=i&&("string"==typeof i&&(tm(i)||eE(i))?i=parseFloat(i):!ol(i)&&e2.test(t)&&(i=tt(e,t)),this.setBaseTarget(e,eu(i)?i.get():i)),eu(i)?i.get():i}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let n;let{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=u(this.props,i,null===(t=this.presenceContext)||void 0===t?void 0:t.custom);r&&(n=r[e])}if(i&&void 0!==n)return n;let r=this.getBaseTargetFromProps(this.props,e);return void 0===r||eu(r)?void 0!==this.initialValues[e]&&void 0===n?void 0:this.baseTarget[e]:r}on(e,t){return this.events[e]||(this.events[e]=new er),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class oh extends od{constructor(){super(...arguments),this.KeyframeResolver=tE}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;eu(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}class of extends oh{constructor(){super(...arguments),this.type="html",this.renderInstance=r2}readValueFromInstance(e,t){if(z.has(t)){let e=te(t);return e&&e.default||0}{let n=window.getComputedStyle(e),i=(tg(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof i?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:t}){return n4(e,t)}build(e,t,n){rX(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return r6(e,t,n)}}class op extends oh{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nX}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(z.has(t)){let e=te(t);return e&&e.default||0}return t=r5.has(t)?t:ed(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return r4(e,t,n)}build(e,t,n){rQ(e,t,this.isSVGTag,n.transformTemplate)}renderInstance(e,t,n,i){r9(e,t,n,i)}mount(e){this.isSVGTag=r1(e.tagName),super.mount(e)}}let om=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,i)=>"create"===i?e:(t.has(i)||t.set(i,e(i)),t.get(i))})}((is={animation:{Feature:nb},exit:{Feature:nA},inView:{Feature:rM},tap:{Feature:rP},focus:{Feature:rw},hover:{Feature:rx},pan:{Feature:io},drag:{Feature:ii,ProjectionNode:rg,MeasureLayout:ib},layout:{ProjectionNode:rg,MeasureLayout:ib}},ia=(e,t)=>r_(e)?new op(t):new of(t,{allowProjection:e!==iu.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function(e){var t,n;let{preloadedFeatures:i,createVisualElement:r,useRender:o,useVisualState:s,Component:l}=e;function u(e,t){var n,i,u;let c;let d={...(0,iu.useContext)(rk.Q),...e,layoutId:function(e){let{layoutId:t}=e,n=(0,iu.useContext)(id.L).id;return n&&void 0!==t?n+"-"+t:t}(e)},{isStatic:h}=d,f=function(e){let{initial:t,animate:n}=function(e,t){if(rL(e)){let{initial:t,animate:n}=e;return{initial:!1===t||a(t)?t:void 0,animate:a(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,iu.useContext)(rD));return(0,iu.useMemo)(()=>({initial:t,animate:n}),[rV(t),rV(n)])}(e),p=s(e,h);if(!h&&rF.B){i=0,u=0,(0,iu.useContext)(rR).strict;let e=function(e){let{drag:t,layout:n}=rB;if(!t&&!n)return{};let i={...t,...n};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==n?void 0:n.isEnabled(e))?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(d);c=e.MeasureLayout,f.visualElement=function(e,t,n,i,r){var o,s;let{visualElement:a}=(0,iu.useContext)(rD),l=(0,iu.useContext)(rR),u=(0,iu.useContext)(rN.t),c=(0,iu.useContext)(rk.Q).reducedMotion,d=(0,iu.useRef)(null);i=i||l.renderer,!d.current&&i&&(d.current=i(e,{visualState:t,parent:a,props:n,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:c}));let h=d.current,f=(0,iu.useContext)(ih);h&&!h.projection&&r&&("html"===h.type||"svg"===h.type)&&function(e,t,n,i){let{layoutId:r,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:r,layout:o,alwaysMeasureLayout:!!s||a&&nV(a),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,layoutScroll:l,layoutRoot:u})}(d.current,n,r,f);let p=(0,iu.useRef)(!1);(0,iu.useInsertionEffect)(()=>{h&&p.current&&h.update(n,u)});let m=n[eh],v=(0,iu.useRef)(!!m&&!(null===(o=window.MotionHandoffIsComplete)||void 0===o?void 0:o.call(window,m))&&(null===(s=window.MotionHasOptimisedAnimation)||void 0===s?void 0:s.call(window,m)));return(0,rU.E)(()=>{h&&(p.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),iy.render(h.render),v.current&&h.animationState&&h.animationState.animateChanges())}),(0,iu.useEffect)(()=>{h&&(!v.current&&h.animationState&&h.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var e;null===(e=window.MotionHandoffMarkAsComplete)||void 0===e||e.call(window,m)}),v.current=!1))}),h}(l,p,d,r,e.ProjectionNode)}return(0,il.jsxs)(rD.Provider,{value:f,children:[c&&f.visualElement?(0,il.jsx)(c,{visualElement:f.visualElement,...d}):null,o(l,e,(n=f.visualElement,(0,iu.useCallback)(e=>{e&&p.onMount&&p.onMount(e),n&&(e?n.mount(e):n.unmount()),t&&("function"==typeof t?t(e):nV(t)&&(t.current=e))},[n])),p,h,f.visualElement)]})}i&&function(e){for(let t in e)rB[t]={...rB[t],...e[t]}}(i),u.displayName="motion.".concat("string"==typeof l?l:"create(".concat(null!==(n=null!==(t=l.displayName)&&void 0!==t?t:l.name)&&void 0!==n?n:"",")"));let c=(0,iu.forwardRef)(u);return c[rI]=l,c}({...r_(e)?r7:oe,preloadedFeatures:is,useRender:function(e=!1){return(t,n,i,{latestValues:r},o)=>{let s=(r_(t)?function(e,t,n,i){let r=(0,iu.useMemo)(()=>{let n=r0();return rQ(n,t,r1(i),e.transformTemplate),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};ot(t,e.style,e),r.style={...t,...r.style}}return r}:function(e,t){let n={},i=function(e,t){let n=e.style||{},i={};return ot(i,n,e),Object.assign(i,function({transformTemplate:e},t){return(0,iu.useMemo)(()=>{let n=rJ();return rX(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),i}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=i,n})(n,r,o,t),a=function(e,t,n){let i={};for(let r in e)("values"!==r||"object"!=typeof e.values)&&(or(r)||!0===n&&oi(r)||!t&&!oi(r)||e.draggable&&r.startsWith("onDrag"))&&(i[r]=e[r]);return i}(n,"string"==typeof t,e),l=t!==iu.Fragment?{...a,...s,ref:i}:{},{children:u}=n,c=(0,iu.useMemo)(()=>eu(u)?u.get():u,[u]);return(0,iu.createElement)(t,{...l,children:c})}}(t),createVisualElement:ia,Component:e})}))},5040:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5196:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},9428:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},13052:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},14186:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17580:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},17951:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},19178:(e,t,n)=>{n.d(t,{qW:()=>h});var i,r=n(12115),o=n(85185),s=n(63655),a=n(6101),l=n(39033),u=n(95155),c="dismissableLayer.update",d=r.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),h=r.forwardRef((e,t)=>{var n,h;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:x,onDismiss:w,...b}=e,P=r.useContext(d),[A,T]=r.useState(null),E=null!==(h=null==A?void 0:A.ownerDocument)&&void 0!==h?h:null===(n=globalThis)||void 0===n?void 0:n.document,[,S]=r.useState({}),C=(0,a.s)(t,e=>T(e)),M=Array.from(P.layers),[R]=[...P.layersWithOutsidePointerEventsDisabled].slice(-1),k=M.indexOf(R),D=A?M.indexOf(A):-1,L=P.layersWithOutsidePointerEventsDisabled.size>0,j=D>=k,V=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,i=(0,l.c)(e),o=r.useRef(!1),s=r.useRef(()=>{});return r.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){p("dismissableLayer.pointerDownOutside",i,r,{discrete:!0})},r={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",s.current),s.current=t,n.addEventListener("click",s.current,{once:!0})):t()}else n.removeEventListener("click",s.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",s.current)}},[n,i]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...P.branches].some(e=>e.contains(t));!j||n||(null==g||g(e),null==x||x(e),e.defaultPrevented||null==w||w())},E),F=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,i=(0,l.c)(e),o=r.useRef(!1);return r.useEffect(()=>{let e=e=>{e.target&&!o.current&&p("dismissableLayer.focusOutside",i,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,i]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...P.branches].some(e=>e.contains(t))||(null==y||y(e),null==x||x(e),e.defaultPrevented||null==w||w())},E);return!function(e,t=globalThis?.document){let n=(0,l.c)(e);r.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{D===P.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},E),r.useEffect(()=>{if(A)return m&&(0===P.layersWithOutsidePointerEventsDisabled.size&&(i=E.body.style.pointerEvents,E.body.style.pointerEvents="none"),P.layersWithOutsidePointerEventsDisabled.add(A)),P.layers.add(A),f(),()=>{m&&1===P.layersWithOutsidePointerEventsDisabled.size&&(E.body.style.pointerEvents=i)}},[A,E,m,P]),r.useEffect(()=>()=>{A&&(P.layers.delete(A),P.layersWithOutsidePointerEventsDisabled.delete(A),f())},[A,P]),r.useEffect(()=>{let e=()=>S({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(s.sG.div,{...b,ref:C,style:{pointerEvents:L?j?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,F.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,F.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,V.onPointerDownCapture)})});function f(){let e=new CustomEvent(c);document.dispatchEvent(e)}function p(e,t,n,i){let{discrete:r}=i,o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,s.hO)(o,a):o.dispatchEvent(a)}h.displayName="DismissableLayer",r.forwardRef((e,t)=>{let n=r.useContext(d),i=r.useRef(null),o=(0,a.s)(t,i);return r.useEffect(()=>{let e=i.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(s.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch"},25519:(e,t,n)=>{n.d(t,{n:()=>d});var i=n(12115),r=n(6101),o=n(63655),s=n(39033),a=n(95155),l="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=i.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[x,w]=i.useState(null),b=(0,s.c)(v),P=(0,s.c)(g),A=i.useRef(null),T=(0,r.s)(t,e=>w(e)),E=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(d){let e=function(e){if(E.paused||!x)return;let t=e.target;x.contains(t)?A.current=t:p(A.current,{select:!0})},t=function(e){if(E.paused||!x)return;let t=e.relatedTarget;null===t||x.contains(t)||p(A.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&p(x)});return x&&n.observe(x,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,x,E.paused]),i.useEffect(()=>{if(x){m.add(E);let e=document.activeElement;if(!x.contains(e)){let t=new CustomEvent(l,c);x.addEventListener(l,b),x.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let i of e)if(p(i,{select:t}),document.activeElement!==n)return}(h(x).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&p(x))}return()=>{x.removeEventListener(l,b),setTimeout(()=>{let t=new CustomEvent(u,c);x.addEventListener(u,P),x.dispatchEvent(t),t.defaultPrevented||p(null!=e?e:document.body,{select:!0}),x.removeEventListener(u,P),m.remove(E)},0)}}},[x,b,P,E]);let S=i.useCallback(e=>{if(!n&&!d||E.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,i=document.activeElement;if(t&&i){let t=e.currentTarget,[r,o]=function(e){let t=h(e);return[f(t,e),f(t.reverse(),e)]}(t);r&&o?e.shiftKey||i!==o?e.shiftKey&&i===r&&(e.preventDefault(),n&&p(o,{select:!0})):(e.preventDefault(),n&&p(r,{select:!0})):i===t&&e.preventDefault()}},[n,d,E.paused]);return(0,a.jsx)(o.sG.div,{tabIndex:-1,...y,ref:T,onKeyDown:S})});function h(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function f(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function p(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let i=document.activeElement;e.focus({preventScroll:!0}),e!==i&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=v(e,t)).unshift(t)},remove(t){var n;null===(n=(e=v(e,t))[0])||void 0===n||n.resume()}}}();function v(e,t){let n=[...e],i=n.indexOf(t);return -1!==i&&n.splice(i,1),n}},32082:(e,t,n)=>{n.d(t,{xQ:()=>o});var i=n(12115),r=n(80845);function o(e=!0){let t=(0,i.useContext)(r.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:s,register:a}=t,l=(0,i.useId)();(0,i.useEffect)(()=>{e&&a(l)},[e]);let u=(0,i.useCallback)(()=>e&&s&&s(l),[l,s,e]);return!n&&s?[!1,u]:[!0]}},34378:(e,t,n)=>{n.d(t,{Z:()=>l});var i=n(12115),r=n(47650),o=n(63655),s=n(52712),a=n(95155),l=i.forwardRef((e,t)=>{var n,l;let{container:u,...c}=e,[d,h]=i.useState(!1);(0,s.N)(()=>h(!0),[]);let f=u||d&&(null===(l=globalThis)||void 0===l?void 0:null===(n=l.document)||void 0===n?void 0:n.body);return f?r.createPortal((0,a.jsx)(o.sG.div,{...c,ref:t}),f):null});l.displayName="Portal"},34835:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},38168:(e,t,n)=>{n.d(t,{Eq:()=>c});var i=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},r=new WeakMap,o=new WeakMap,s={},a=0,l=function(e){return e&&(e.host||l(e.parentNode))},u=function(e,t,n,i){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});s[n]||(s[n]=new WeakMap);var c=s[n],d=[],h=new Set,f=new Set(u),p=function(e){!(!e||h.has(e))&&(h.add(e),p(e.parentNode))};u.forEach(p);var m=function(e){!(!e||f.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(h.has(e))m(e);else try{var t=e.getAttribute(i),s=null!==t&&"false"!==t,a=(r.get(e)||0)+1,l=(c.get(e)||0)+1;r.set(e,a),c.set(e,l),d.push(e),1===a&&s&&o.set(e,!0),1===l&&e.setAttribute(n,"true"),s||e.setAttribute(i,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),h.clear(),a++,function(){d.forEach(function(e){var t=r.get(e)-1,s=c.get(e)-1;r.set(e,t),c.set(e,s),t||(o.has(e)||e.removeAttribute(i),o.delete(e)),s||e.removeAttribute(n)}),--a||(r=new WeakMap,r=new WeakMap,o=new WeakMap,s={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||i(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),u(r,o,n,"aria-hidden")):function(){return null}}},38795:(e,t,n)=>{n.d(t,{Mz:()=>eG,i3:()=>eZ,UC:()=>eq,bL:()=>eX,Bk:()=>eL});var i=n(12115);let r=["top","right","bottom","left"],o=Math.min,s=Math.max,a=Math.round,l=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function h(e,t){return"function"==typeof e?e(t):e}function f(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(f(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>d[e])}function x(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:i,height:r}=e;return{width:i,height:r,top:n,left:t,right:t+i,bottom:n+r,x:t,y:n}}function P(e,t,n){let i,{reference:r,floating:o}=e,s=g(t),a=m(g(t)),l=v(a),u=f(t),c="y"===s,d=r.x+r.width/2-o.width/2,h=r.y+r.height/2-o.height/2,y=r[l]/2-o[l]/2;switch(u){case"top":i={x:d,y:r.y-o.height};break;case"bottom":i={x:d,y:r.y+r.height};break;case"right":i={x:r.x+r.width,y:h};break;case"left":i={x:r.x-o.width,y:h};break;default:i={x:r.x,y:r.y}}switch(p(t)){case"start":i[a]-=y*(n&&c?-1:1);break;case"end":i[a]+=y*(n&&c?-1:1)}return i}let A=async(e,t,n)=>{let{placement:i="bottom",strategy:r="absolute",middleware:o=[],platform:s}=n,a=o.filter(Boolean),l=await (null==s.isRTL?void 0:s.isRTL(t)),u=await s.getElementRects({reference:e,floating:t,strategy:r}),{x:c,y:d}=P(u,i,l),h=i,f={},p=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:v,y:g,data:y,reset:x}=await m({x:c,y:d,initialPlacement:i,placement:h,strategy:r,middlewareData:f,rects:u,platform:s,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,f={...f,[o]:{...f[o],...y}},x&&p<=50&&(p++,"object"==typeof x&&(x.placement&&(h=x.placement),x.rects&&(u=!0===x.rects?await s.getElementRects({reference:e,floating:t,strategy:r}):x.rects),{x:c,y:d}=P(u,h,l)),n=-1)}return{x:c,y:d,placement:h,strategy:r,middlewareData:f}};async function T(e,t){var n;void 0===t&&(t={});let{x:i,y:r,platform:o,rects:s,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=h(t,e),m=w(p),v=a[f?"floating"===d?"reference":"floating":d],g=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(v)))||n?v:v.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:l})),y="floating"===d?{x:i,y:r,width:s.floating.width,height:s.floating.height}:s.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),P=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},A=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:x,strategy:l}):y);return{top:(g.top-A.top+m.top)/P.y,bottom:(A.bottom-g.bottom+m.bottom)/P.y,left:(g.left-A.left+m.left)/P.x,right:(A.right-g.right+m.right)/P.x}}function E(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function S(e){return r.some(t=>e[t]>=0)}async function C(e,t){let{placement:n,platform:i,elements:r}=e,o=await (null==i.isRTL?void 0:i.isRTL(r.floating)),s=f(n),a=p(n),l="y"===g(n),u=["left","top"].includes(s)?-1:1,c=o&&l?-1:1,d=h(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof y&&(v="end"===a?-1*y:y),l?{x:v*c,y:m*u}:{x:m*u,y:v*c}}function M(){return"undefined"!=typeof window}function R(e){return L(e)?(e.nodeName||"").toLowerCase():"#document"}function k(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function D(e){var t;return null==(t=(L(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function L(e){return!!M()&&(e instanceof Node||e instanceof k(e).Node)}function j(e){return!!M()&&(e instanceof Element||e instanceof k(e).Element)}function V(e){return!!M()&&(e instanceof HTMLElement||e instanceof k(e).HTMLElement)}function F(e){return!!M()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof k(e).ShadowRoot)}function O(e){let{overflow:t,overflowX:n,overflowY:i,display:r}=W(e);return/auto|scroll|overlay|hidden|clip/.test(t+i+n)&&!["inline","contents"].includes(r)}function B(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function I(e){let t=N(),n=j(e)?W(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function N(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function U(e){return["html","body","#document"].includes(R(e))}function W(e){return k(e).getComputedStyle(e)}function _(e){return j(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function z(e){if("html"===R(e))return e;let t=e.assignedSlot||e.parentNode||F(e)&&e.host||D(e);return F(t)?t.host:t}function H(e,t,n){var i;void 0===t&&(t=[]),void 0===n&&(n=!0);let r=function e(t){let n=z(t);return U(n)?t.ownerDocument?t.ownerDocument.body:t.body:V(n)&&O(n)?n:e(n)}(e),o=r===(null==(i=e.ownerDocument)?void 0:i.body),s=k(r);if(o){let e=$(s);return t.concat(s,s.visualViewport||[],O(r)?r:[],e&&n?H(e):[])}return t.concat(r,H(r,[],n))}function $(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function K(e){let t=W(e),n=parseFloat(t.width)||0,i=parseFloat(t.height)||0,r=V(e),o=r?e.offsetWidth:n,s=r?e.offsetHeight:i,l=a(n)!==o||a(i)!==s;return l&&(n=o,i=s),{width:n,height:i,$:l}}function Y(e){return j(e)?e:e.contextElement}function X(e){let t=Y(e);if(!V(t))return u(1);let n=t.getBoundingClientRect(),{width:i,height:r,$:o}=K(t),s=(o?a(n.width):n.width)/i,l=(o?a(n.height):n.height)/r;return s&&Number.isFinite(s)||(s=1),l&&Number.isFinite(l)||(l=1),{x:s,y:l}}let G=u(0);function q(e){let t=k(e);return N()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:G}function Z(e,t,n,i){var r;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),s=Y(e),a=u(1);t&&(i?j(i)&&(a=X(i)):a=X(e));let l=(void 0===(r=n)&&(r=!1),i&&(!r||i===k(s))&&r)?q(s):u(0),c=(o.left+l.x)/a.x,d=(o.top+l.y)/a.y,h=o.width/a.x,f=o.height/a.y;if(s){let e=k(s),t=i&&j(i)?k(i):i,n=e,r=$(n);for(;r&&i&&t!==n;){let e=X(r),t=r.getBoundingClientRect(),i=W(r),o=t.left+(r.clientLeft+parseFloat(i.paddingLeft))*e.x,s=t.top+(r.clientTop+parseFloat(i.paddingTop))*e.y;c*=e.x,d*=e.y,h*=e.x,f*=e.y,c+=o,d+=s,r=$(n=k(r))}}return b({width:h,height:f,x:c,y:d})}function Q(e,t){let n=_(e).scrollLeft;return t?t.left+n:Z(D(e)).left+n}function J(e,t,n){void 0===n&&(n=!1);let i=e.getBoundingClientRect();return{x:i.left+t.scrollLeft-(n?0:Q(e,i)),y:i.top+t.scrollTop}}function ee(e,t,n){let i;if("viewport"===t)i=function(e,t){let n=k(e),i=D(e),r=n.visualViewport,o=i.clientWidth,s=i.clientHeight,a=0,l=0;if(r){o=r.width,s=r.height;let e=N();(!e||e&&"fixed"===t)&&(a=r.offsetLeft,l=r.offsetTop)}return{width:o,height:s,x:a,y:l}}(e,n);else if("document"===t)i=function(e){let t=D(e),n=_(e),i=e.ownerDocument.body,r=s(t.scrollWidth,t.clientWidth,i.scrollWidth,i.clientWidth),o=s(t.scrollHeight,t.clientHeight,i.scrollHeight,i.clientHeight),a=-n.scrollLeft+Q(e),l=-n.scrollTop;return"rtl"===W(i).direction&&(a+=s(t.clientWidth,i.clientWidth)-r),{width:r,height:o,x:a,y:l}}(D(e));else if(j(t))i=function(e,t){let n=Z(e,!0,"fixed"===t),i=n.top+e.clientTop,r=n.left+e.clientLeft,o=V(e)?X(e):u(1),s=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:s,height:a,x:r*o.x,y:i*o.y}}(t,n);else{let n=q(e);i={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(i)}function et(e){return"static"===W(e).position}function en(e,t){if(!V(e)||"fixed"===W(e).position)return null;if(t)return t(e);let n=e.offsetParent;return D(e)===n&&(n=n.ownerDocument.body),n}function ei(e,t){let n=k(e);if(B(e))return n;if(!V(e)){let t=z(e);for(;t&&!U(t);){if(j(t)&&!et(t))return t;t=z(t)}return n}let i=en(e,t);for(;i&&["table","td","th"].includes(R(i))&&et(i);)i=en(i,t);return i&&U(i)&&et(i)&&!I(i)?n:i||function(e){let t=z(e);for(;V(t)&&!U(t);){if(I(t))return t;if(B(t))break;t=z(t)}return null}(e)||n}let er=async function(e){let t=this.getOffsetParent||ei,n=this.getDimensions,i=await n(e.floating);return{reference:function(e,t,n){let i=V(t),r=D(t),o="fixed"===n,s=Z(e,!0,o,t),a={scrollLeft:0,scrollTop:0},l=u(0);if(i||!i&&!o){if(("body"!==R(t)||O(r))&&(a=_(t)),i){let e=Z(t,!0,o,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else r&&(l.x=Q(r))}o&&!i&&r&&(l.x=Q(r));let c=!r||i||o?u(0):J(r,a);return{x:s.left+a.scrollLeft-l.x-c.x,y:s.top+a.scrollTop-l.y-c.y,width:s.width,height:s.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:i,strategy:r}=e,o="fixed"===r,s=D(i),a=!!t&&B(t.floating);if(i===s||a&&o)return n;let l={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),h=V(i);if((h||!h&&!o)&&(("body"!==R(i)||O(s))&&(l=_(i)),V(i))){let e=Z(i);c=X(i),d.x=e.x+i.clientLeft,d.y=e.y+i.clientTop}let f=!s||h||o?u(0):J(s,l,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+d.x+f.x,y:n.y*c.y-l.scrollTop*c.y+d.y+f.y}},getDocumentElement:D,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:i,strategy:r}=e,a=[..."clippingAncestors"===n?B(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let i=H(e,[],!1).filter(e=>j(e)&&"body"!==R(e)),r=null,o="fixed"===W(e).position,s=o?z(e):e;for(;j(s)&&!U(s);){let t=W(s),n=I(s);n||"fixed"!==t.position||(r=null),(o?!n&&!r:!n&&"static"===t.position&&!!r&&["absolute","fixed"].includes(r.position)||O(s)&&!n&&function e(t,n){let i=z(t);return!(i===n||!j(i)||U(i))&&("fixed"===W(i).position||e(i,n))}(e,s))?i=i.filter(e=>e!==s):r=t,s=z(s)}return t.set(e,i),i}(t,this._c):[].concat(n),i],l=a[0],u=a.reduce((e,n)=>{let i=ee(t,n,r);return e.top=s(i.top,e.top),e.right=o(i.right,e.right),e.bottom=o(i.bottom,e.bottom),e.left=s(i.left,e.left),e},ee(t,l,r));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:ei,getElementRects:er,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=K(e);return{width:t,height:n}},getScale:X,isElement:j,isRTL:function(e){return"rtl"===W(e).direction}};function es(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:i,placement:r,rects:a,platform:l,elements:u,middlewareData:c}=t,{element:d,padding:f=0}=h(e,t)||{};if(null==d)return{};let y=w(f),x={x:n,y:i},b=m(g(r)),P=v(b),A=await l.getDimensions(d),T="y"===b,E=T?"clientHeight":"clientWidth",S=a.reference[P]+a.reference[b]-x[b]-a.floating[P],C=x[b]-a.reference[b],M=await (null==l.getOffsetParent?void 0:l.getOffsetParent(d)),R=M?M[E]:0;R&&await (null==l.isElement?void 0:l.isElement(M))||(R=u.floating[E]||a.floating[P]);let k=R/2-A[P]/2-1,D=o(y[T?"top":"left"],k),L=o(y[T?"bottom":"right"],k),j=R-A[P]-L,V=R/2-A[P]/2+(S/2-C/2),F=s(D,o(V,j)),O=!c.arrow&&null!=p(r)&&V!==F&&a.reference[P]/2-(V<D?D:L)-A[P]/2<0,B=O?V<D?V-D:V-j:0;return{[b]:x[b]+B,data:{[b]:F,centerOffset:V-F-B,...O&&{alignmentOffset:B}},reset:O}}}),el=(e,t,n)=>{let i=new Map,r={platform:eo,...n},o={...r.platform,_c:i};return A(e,t,{...r,platform:o})};var eu=n(47650),ec="undefined"!=typeof document?i.useLayoutEffect:i.useEffect;function ed(e,t){let n,i,r;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(i=n;0!=i--;)if(!ed(e[i],t[i]))return!1;return!0}if((n=(r=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(i=n;0!=i--;)if(!({}).hasOwnProperty.call(t,r[i]))return!1;for(i=n;0!=i--;){let n=r[i];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eh(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ef(e,t){let n=eh(e);return Math.round(t*n)/n}function ep(e){let t=i.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:i}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:i}).fn(t):{}:n?ea({element:n,padding:i}).fn(t):{}}}),ev=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,i;let{x:r,y:o,placement:s,middlewareData:a}=t,l=await C(t,e);return s===(null==(n=a.offset)?void 0:n.placement)&&null!=(i=a.arrow)&&i.alignmentOffset?{}:{x:r+l.x,y:o+l.y,data:{...l,placement:s}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:i,placement:r}=t,{mainAxis:a=!0,crossAxis:l=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=h(e,t),d={x:n,y:i},p=await T(t,c),v=g(f(r)),y=m(v),x=d[y],w=d[v];if(a){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=x+p[e],i=x-p[t];x=s(n,o(x,i))}if(l){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=w+p[e],i=w-p[t];w=s(n,o(w,i))}let b=u.fn({...t,[y]:x,[v]:w});return{...b,data:{x:b.x-n,y:b.y-i,enabled:{[y]:a,[v]:l}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:i,placement:r,rects:o,middlewareData:s}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=h(e,t),c={x:n,y:i},d=g(r),p=m(d),v=c[p],y=c[d],x=h(a,t),w="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(l){let e="y"===p?"height":"width",t=o.reference[p]-o.floating[e]+w.mainAxis,n=o.reference[p]+o.reference[e]-w.mainAxis;v<t?v=t:v>n&&(v=n)}if(u){var b,P;let e="y"===p?"width":"height",t=["top","left"].includes(f(r)),n=o.reference[d]-o.floating[e]+(t&&(null==(b=s.offset)?void 0:b[d])||0)+(t?0:w.crossAxis),i=o.reference[d]+o.reference[e]+(t?0:(null==(P=s.offset)?void 0:P[d])||0)-(t?w.crossAxis:0);y<n?y=n:y>i&&(y=i)}return{[p]:v,[d]:y}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,i,r,o,s,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:d,platform:w,elements:b}=t,{mainAxis:P=!0,crossAxis:A=!0,fallbackPlacements:E,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:C="none",flipAlignment:M=!0,...R}=h(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let k=f(l),D=g(d),L=f(d)===d,j=await (null==w.isRTL?void 0:w.isRTL(b.floating)),V=E||(L||!M?[x(d)]:function(e){let t=x(e);return[y(e),t,y(t)]}(d)),F="none"!==C;!E&&F&&V.push(...function(e,t,n,i){let r=p(e),o=function(e,t,n){let i=["left","right"],r=["right","left"];switch(e){case"top":case"bottom":if(n)return t?r:i;return t?i:r;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(f(e),"start"===n,i);return r&&(o=o.map(e=>e+"-"+r),t&&(o=o.concat(o.map(y)))),o}(d,M,C,j));let O=[d,...V],B=await T(t,R),I=[],N=(null==(i=u.flip)?void 0:i.overflows)||[];if(P&&I.push(B[k]),A){let e=function(e,t,n){void 0===n&&(n=!1);let i=p(e),r=m(g(e)),o=v(r),s="x"===r?i===(n?"end":"start")?"right":"left":"start"===i?"bottom":"top";return t.reference[o]>t.floating[o]&&(s=x(s)),[s,x(s)]}(l,c,j);I.push(B[e[0]],B[e[1]])}if(N=[...N,{placement:l,overflows:I}],!I.every(e=>e<=0)){let e=((null==(r=u.flip)?void 0:r.index)||0)+1,t=O[e];if(t){let n="alignment"===A&&D!==g(t),i=(null==(s=N[0])?void 0:s.overflows[0])>0;if(!n||i)return{data:{index:e,overflows:N},reset:{placement:t}}}let n=null==(o=N.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(S){case"bestFit":{let e=null==(a=N.filter(e=>{if(F){let t=g(e.placement);return t===D||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=d}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,i;let r,a;let{placement:l,rects:u,platform:c,elements:d}=t,{apply:m=()=>{},...v}=h(e,t),y=await T(t,v),x=f(l),w=p(l),b="y"===g(l),{width:P,height:A}=u.floating;"top"===x||"bottom"===x?(r=x,a=w===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(a=x,r="end"===w?"top":"bottom");let E=A-y.top-y.bottom,S=P-y.left-y.right,C=o(A-y[r],E),M=o(P-y[a],S),R=!t.middlewareData.shift,k=C,D=M;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(D=S),null!=(i=t.middlewareData.shift)&&i.enabled.y&&(k=E),R&&!w){let e=s(y.left,0),t=s(y.right,0),n=s(y.top,0),i=s(y.bottom,0);b?D=P-2*(0!==e||0!==t?e+t:s(y.left,y.right)):k=A-2*(0!==n||0!==i?n+i:s(y.top,y.bottom))}await m({...t,availableWidth:D,availableHeight:k});let L=await c.getDimensions(d.floating);return P!==L.width||A!==L.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:i="referenceHidden",...r}=h(e,t);switch(i){case"referenceHidden":{let e=E(await T(t,{...r,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:S(e)}}}case"escaped":{let e=E(await T(t,{...r,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:S(e)}}}default:return{}}}}}(e),options:[e,t]}),eP=(e,t)=>({...em(e),options:[e,t]});var eA=n(63655),eT=n(95155),eE=i.forwardRef((e,t)=>{let{children:n,width:i=10,height:r=5,...o}=e;return(0,eT.jsx)(eA.sG.svg,{...o,ref:t,width:i,height:r,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eT.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eE.displayName="Arrow";var eS=n(6101),eC=n(46081),eM=n(39033),eR=n(52712),ek="Popper",[eD,eL]=(0,eC.A)(ek),[ej,eV]=eD(ek),eF=e=>{let{__scopePopper:t,children:n}=e,[r,o]=i.useState(null);return(0,eT.jsx)(ej,{scope:t,anchor:r,onAnchorChange:o,children:n})};eF.displayName=ek;var eO="PopperAnchor",eB=i.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...o}=e,s=eV(eO,n),a=i.useRef(null),l=(0,eS.s)(t,a);return i.useEffect(()=>{s.onAnchorChange((null==r?void 0:r.current)||a.current)}),r?null:(0,eT.jsx)(eA.sG.div,{...o,ref:l})});eB.displayName=eO;var eI="PopperContent",[eN,eU]=eD(eI),eW=i.forwardRef((e,t)=>{var n,r,a,u,c,d,h,f;let{__scopePopper:p,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:x=0,avoidCollisions:w=!0,collisionBoundary:b=[],collisionPadding:P=0,sticky:A="partial",hideWhenDetached:T=!1,updatePositionStrategy:E="optimized",onPlaced:S,...C}=e,M=eV(eI,p),[R,k]=i.useState(null),L=(0,eS.s)(t,e=>k(e)),[j,V]=i.useState(null),F=function(e){let[t,n]=i.useState(void 0);return(0,eR.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let i,r;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;i=t.inlineSize,r=t.blockSize}else i=e.offsetWidth,r=e.offsetHeight;n({width:i,height:r})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(j),O=null!==(h=null==F?void 0:F.width)&&void 0!==h?h:0,B=null!==(f=null==F?void 0:F.height)&&void 0!==f?f:0,I="number"==typeof P?P:{top:0,right:0,bottom:0,left:0,...P},N=Array.isArray(b)?b:[b],U=N.length>0,W={padding:I,boundary:N.filter(e$),altBoundary:U},{refs:_,floatingStyles:z,placement:$,isPositioned:K,middlewareData:X}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:a}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[d,h]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[f,p]=i.useState(r);ed(f,r)||p(r);let[m,v]=i.useState(null),[g,y]=i.useState(null),x=i.useCallback(e=>{e!==A.current&&(A.current=e,v(e))},[]),w=i.useCallback(e=>{e!==T.current&&(T.current=e,y(e))},[]),b=s||m,P=a||g,A=i.useRef(null),T=i.useRef(null),E=i.useRef(d),S=null!=u,C=ep(u),M=ep(o),R=ep(c),k=i.useCallback(()=>{if(!A.current||!T.current)return;let e={placement:t,strategy:n,middleware:f};M.current&&(e.platform=M.current),el(A.current,T.current,e).then(e=>{let t={...e,isPositioned:!1!==R.current};D.current&&!ed(E.current,t)&&(E.current=t,eu.flushSync(()=>{h(t)}))})},[f,t,n,M,R]);ec(()=>{!1===c&&E.current.isPositioned&&(E.current.isPositioned=!1,h(e=>({...e,isPositioned:!1})))},[c]);let D=i.useRef(!1);ec(()=>(D.current=!0,()=>{D.current=!1}),[]),ec(()=>{if(b&&(A.current=b),P&&(T.current=P),b&&P){if(C.current)return C.current(b,P,k);k()}},[b,P,k,C,S]);let L=i.useMemo(()=>({reference:A,floating:T,setReference:x,setFloating:w}),[x,w]),j=i.useMemo(()=>({reference:b,floating:P}),[b,P]),V=i.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=ef(j.floating,d.x),i=ef(j.floating,d.y);return l?{...e,transform:"translate("+t+"px, "+i+"px)",...eh(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:i}},[n,l,j.floating,d.x,d.y]);return i.useMemo(()=>({...d,update:k,refs:L,elements:j,floatingStyles:V}),[d,k,L,j,V])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,i){let r;void 0===i&&(i={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:h=!1}=i,f=Y(e),p=a||u?[...f?H(f):[],...H(t)]:[];p.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=f&&d?function(e,t){let n,i=null,r=D(e);function a(){var e;clearTimeout(n),null==(e=i)||e.disconnect(),i=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),a();let h=e.getBoundingClientRect(),{left:f,top:p,width:m,height:v}=h;if(c||t(),!m||!v)return;let g=l(p),y=l(r.clientWidth-(f+m)),x={rootMargin:-g+"px "+-y+"px "+-l(r.clientHeight-(p+v))+"px "+-l(f)+"px",threshold:s(0,o(1,d))||1},w=!0;function b(t){let i=t[0].intersectionRatio;if(i!==d){if(!w)return u();i?u(!1,i):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==i||es(h,e.getBoundingClientRect())||u(),w=!1}try{i=new IntersectionObserver(b,{...x,root:r.ownerDocument})}catch(e){i=new IntersectionObserver(b,x)}i.observe(e)}(!0),a}(f,n):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{let[i]=e;i&&i.target===f&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),f&&!h&&g.observe(f),g.observe(t));let y=h?Z(e):null;return h&&function t(){let i=Z(e);y&&!es(y,i)&&n(),y=i,r=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,h&&cancelAnimationFrame(r)}}(...t,{animationFrame:"always"===E})},elements:{reference:M.anchor},middleware:[ev({mainAxis:v+B,alignmentAxis:y}),w&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===A?ey():void 0,...W}),w&&ex({...W}),ew({...W,apply:e=>{let{elements:t,rects:n,availableWidth:i,availableHeight:r}=e,{width:o,height:s}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(i,"px")),a.setProperty("--radix-popper-available-height","".concat(r,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(s,"px"))}}),j&&eP({element:j,padding:x}),eK({arrowWidth:O,arrowHeight:B}),T&&eb({strategy:"referenceHidden",...W})]}),[G,q]=eY($),Q=(0,eM.c)(S);(0,eR.N)(()=>{K&&(null==Q||Q())},[K,Q]);let J=null===(n=X.arrow)||void 0===n?void 0:n.x,ee=null===(r=X.arrow)||void 0===r?void 0:r.y,et=(null===(a=X.arrow)||void 0===a?void 0:a.centerOffset)!==0,[en,ei]=i.useState();return(0,eR.N)(()=>{R&&ei(window.getComputedStyle(R).zIndex)},[R]),(0,eT.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...z,transform:K?z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null===(u=X.transformOrigin)||void 0===u?void 0:u.x,null===(c=X.transformOrigin)||void 0===c?void 0:c.y].join(" "),...(null===(d=X.hide)||void 0===d?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eT.jsx)(eN,{scope:p,placedSide:G,onArrowChange:V,arrowX:J,arrowY:ee,shouldHideArrow:et,children:(0,eT.jsx)(eA.sG.div,{"data-side":G,"data-align":q,...C,ref:L,style:{...C.style,animation:K?void 0:"none"}})})})});eW.displayName=eI;var e_="PopperArrow",ez={top:"bottom",right:"left",bottom:"top",left:"right"},eH=i.forwardRef(function(e,t){let{__scopePopper:n,...i}=e,r=eU(e_,n),o=ez[r.placedSide];return(0,eT.jsx)("span",{ref:r.onArrowChange,style:{position:"absolute",left:r.arrowX,top:r.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[r.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[r.placedSide],visibility:r.shouldHideArrow?"hidden":void 0},children:(0,eT.jsx)(eE,{...i,ref:t,style:{...i.style,display:"block"}})})});function e$(e){return null!==e}eH.displayName=e_;var eK=e=>({name:"transformOrigin",options:e,fn(t){var n,i,r,o,s;let{placement:a,rects:l,middlewareData:u}=t,c=(null===(n=u.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,h=c?0:e.arrowHeight,[f,p]=eY(a),m={start:"0%",center:"50%",end:"100%"}[p],v=(null!==(o=null===(i=u.arrow)||void 0===i?void 0:i.x)&&void 0!==o?o:0)+d/2,g=(null!==(s=null===(r=u.arrow)||void 0===r?void 0:r.y)&&void 0!==s?s:0)+h/2,y="",x="";return"bottom"===f?(y=c?m:"".concat(v,"px"),x="".concat(-h,"px")):"top"===f?(y=c?m:"".concat(v,"px"),x="".concat(l.floating.height+h,"px")):"right"===f?(y="".concat(-h,"px"),x=c?m:"".concat(g,"px")):"left"===f&&(y="".concat(l.floating.width+h,"px"),x=c?m:"".concat(g,"px")),{data:{x:y,y:x}}}});function eY(e){let[t,n="center"]=e.split("-");return[t,n]}var eX=eF,eG=eB,eq=eW,eZ=eH},48698:(e,t,n)=>{n.d(t,{H_:()=>e6,UC:()=>e2,YJ:()=>e5,q7:()=>e3,VF:()=>e7,JU:()=>e9,ZL:()=>e1,z6:()=>e4,hN:()=>e8,bL:()=>eJ,wv:()=>te,Pb:()=>tt,G5:()=>ti,ZP:()=>tn,l9:()=>e0});var i=n(12115),r=n(85185),o=n(6101),s=n(46081),a=n(5845),l=n(63655),u=n(82284),c=n(94315),d=n(19178),h=n(92293),f=n(25519),p=n(61285),m=n(38795),v=n(34378),g=n(28905),y=n(89196),x=n(99708),w=n(39033),b=n(38168),P=n(93795),A=n(95155),T=["Enter"," "],E=["ArrowUp","PageDown","End"],S=["ArrowDown","PageUp","Home",...E],C={ltr:[...T,"ArrowRight"],rtl:[...T,"ArrowLeft"]},M={ltr:["ArrowLeft"],rtl:["ArrowRight"]},R="Menu",[k,D,L]=(0,u.N)(R),[j,V]=(0,s.A)(R,[L,m.Bk,y.RG]),F=(0,m.Bk)(),O=(0,y.RG)(),[B,I]=j(R),[N,U]=j(R),W=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:s,modal:a=!0}=e,l=F(t),[u,d]=i.useState(null),h=i.useRef(!1),f=(0,w.c)(s),p=(0,c.jH)(o);return i.useEffect(()=>{let e=()=>{h.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>h.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,A.jsx)(m.bL,{...l,children:(0,A.jsx)(B,{scope:t,open:n,onOpenChange:f,content:u,onContentChange:d,children:(0,A.jsx)(N,{scope:t,onClose:i.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:h,dir:p,modal:a,children:r})})})};W.displayName=R;var _=i.forwardRef((e,t)=>{let{__scopeMenu:n,...i}=e,r=F(n);return(0,A.jsx)(m.Mz,{...r,...i,ref:t})});_.displayName="MenuAnchor";var z="MenuPortal",[H,$]=j(z,{forceMount:void 0}),K=e=>{let{__scopeMenu:t,forceMount:n,children:i,container:r}=e,o=I(z,t);return(0,A.jsx)(H,{scope:t,forceMount:n,children:(0,A.jsx)(g.C,{present:n||o.open,children:(0,A.jsx)(v.Z,{asChild:!0,container:r,children:i})})})};K.displayName=z;var Y="MenuContent",[X,G]=j(Y),q=i.forwardRef((e,t)=>{let n=$(Y,e.__scopeMenu),{forceMount:i=n.forceMount,...r}=e,o=I(Y,e.__scopeMenu),s=U(Y,e.__scopeMenu);return(0,A.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,A.jsx)(g.C,{present:i||o.open,children:(0,A.jsx)(k.Slot,{scope:e.__scopeMenu,children:s.modal?(0,A.jsx)(Z,{...r,ref:t}):(0,A.jsx)(Q,{...r,ref:t})})})})}),Z=i.forwardRef((e,t)=>{let n=I(Y,e.__scopeMenu),s=i.useRef(null),a=(0,o.s)(t,s);return i.useEffect(()=>{let e=s.current;if(e)return(0,b.Eq)(e)},[]),(0,A.jsx)(J,{...e,ref:a,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=i.forwardRef((e,t)=>{let n=I(Y,e.__scopeMenu);return(0,A.jsx)(J,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),J=i.forwardRef((e,t)=>{let{__scopeMenu:n,loop:s=!1,trapFocus:a,onOpenAutoFocus:l,onCloseAutoFocus:u,disableOutsidePointerEvents:c,onEntryFocus:p,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:T,disableOutsideScroll:C,...M}=e,R=I(Y,n),k=U(Y,n),L=F(n),j=O(n),V=D(n),[B,N]=i.useState(null),W=i.useRef(null),_=(0,o.s)(t,W,R.onContentChange),z=i.useRef(0),H=i.useRef(""),$=i.useRef(0),K=i.useRef(null),G=i.useRef("right"),q=i.useRef(0),Z=C?P.A:i.Fragment,Q=C?{as:x.DX,allowPinchZoom:!0}:void 0,J=e=>{var t,n;let i=H.current+e,r=V().filter(e=>!e.disabled),o=document.activeElement,s=null===(t=r.find(e=>e.ref.current===o))||void 0===t?void 0:t.textValue,a=function(e,t,n){var i;let r=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=(i=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(i+n)%e.length]));1===r.length&&(o=o.filter(e=>e!==n));let s=o.find(e=>e.toLowerCase().startsWith(r.toLowerCase()));return s!==n?s:void 0}(r.map(e=>e.textValue),i,s),l=null===(n=r.find(e=>e.textValue===a))||void 0===n?void 0:n.ref.current;!function e(t){H.current=t,window.clearTimeout(z.current),""!==t&&(z.current=window.setTimeout(()=>e(""),1e3))}(i),l&&setTimeout(()=>l.focus())};i.useEffect(()=>()=>window.clearTimeout(z.current),[]),(0,h.Oh)();let ee=i.useCallback(e=>{var t,n;return G.current===(null===(t=K.current)||void 0===t?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:i}=e,r=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let s=t[e].x,a=t[e].y,l=t[o].x,u=t[o].y;a>i!=u>i&&n<(l-s)*(i-a)/(u-a)+s&&(r=!r)}return r}({x:e.clientX,y:e.clientY},t)}(e,null===(n=K.current)||void 0===n?void 0:n.area)},[]);return(0,A.jsx)(X,{scope:n,searchRef:H,onItemEnter:i.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),onItemLeave:i.useCallback(e=>{var t;ee(e)||(null===(t=W.current)||void 0===t||t.focus(),N(null))},[ee]),onTriggerLeave:i.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),pointerGraceTimerRef:$,onPointerGraceIntentChange:i.useCallback(e=>{K.current=e},[]),children:(0,A.jsx)(Z,{...Q,children:(0,A.jsx)(f.n,{asChild:!0,trapped:a,onMountAutoFocus:(0,r.m)(l,e=>{var t;e.preventDefault(),null===(t=W.current)||void 0===t||t.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,A.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:T,children:(0,A.jsx)(y.bL,{asChild:!0,...j,dir:k.dir,orientation:"vertical",loop:s,currentTabStopId:B,onCurrentTabStopIdChange:N,onEntryFocus:(0,r.m)(p,e=>{k.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,A.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eC(R.open),"data-radix-menu-content":"",dir:k.dir,...L,...M,ref:_,style:{outline:"none",...M.style},onKeyDown:(0,r.m)(M.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,i=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&i&&J(e.key));let r=W.current;if(e.target!==r||!S.includes(e.key))return;e.preventDefault();let o=V().filter(e=>!e.disabled).map(e=>e.ref.current);E.includes(e.key)&&o.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(o)}),onBlur:(0,r.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(z.current),H.current="")}),onPointerMove:(0,r.m)(e.onPointerMove,ek(e=>{let t=e.target,n=q.current!==e.clientX;e.currentTarget.contains(t)&&n&&(G.current=e.clientX>q.current?"right":"left",q.current=e.clientX)}))})})})})})})});q.displayName=Y;var ee=i.forwardRef((e,t)=>{let{__scopeMenu:n,...i}=e;return(0,A.jsx)(l.sG.div,{role:"group",...i,ref:t})});ee.displayName="MenuGroup";var et=i.forwardRef((e,t)=>{let{__scopeMenu:n,...i}=e;return(0,A.jsx)(l.sG.div,{...i,ref:t})});et.displayName="MenuLabel";var en="MenuItem",ei="menu.itemSelect",er=i.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:s,...a}=e,u=i.useRef(null),c=U(en,e.__scopeMenu),d=G(en,e.__scopeMenu),h=(0,o.s)(t,u),f=i.useRef(!1);return(0,A.jsx)(eo,{...a,ref:h,disabled:n,onClick:(0,r.m)(e.onClick,()=>{let e=u.current;if(!n&&e){let t=new CustomEvent(ei,{bubbles:!0,cancelable:!0});e.addEventListener(ei,e=>null==s?void 0:s(e),{once:!0}),(0,l.hO)(e,t),t.defaultPrevented?f.current=!1:c.onClose()}}),onPointerDown:t=>{var n;null===(n=e.onPointerDown)||void 0===n||n.call(e,t),f.current=!0},onPointerUp:(0,r.m)(e.onPointerUp,e=>{var t;f.current||null===(t=e.currentTarget)||void 0===t||t.click()}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;!n&&(!t||" "!==e.key)&&T.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});er.displayName=en;var eo=i.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:s=!1,textValue:a,...u}=e,c=G(en,n),d=O(n),h=i.useRef(null),f=(0,o.s)(t,h),[p,m]=i.useState(!1),[v,g]=i.useState("");return i.useEffect(()=>{let e=h.current;if(e){var t;g((null!==(t=e.textContent)&&void 0!==t?t:"").trim())}},[u.children]),(0,A.jsx)(k.ItemSlot,{scope:n,disabled:s,textValue:null!=a?a:v,children:(0,A.jsx)(y.q7,{asChild:!0,...d,focusable:!s,children:(0,A.jsx)(l.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":s||void 0,"data-disabled":s?"":void 0,...u,ref:f,onPointerMove:(0,r.m)(e.onPointerMove,ek(e=>{s?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,r.m)(e.onPointerLeave,ek(e=>c.onItemLeave(e))),onFocus:(0,r.m)(e.onFocus,()=>m(!0)),onBlur:(0,r.m)(e.onBlur,()=>m(!1))})})})}),es=i.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:i,...o}=e;return(0,A.jsx)(ep,{scope:e.__scopeMenu,checked:n,children:(0,A.jsx)(er,{role:"menuitemcheckbox","aria-checked":eM(n)?"mixed":n,...o,ref:t,"data-state":eR(n),onSelect:(0,r.m)(o.onSelect,()=>null==i?void 0:i(!!eM(n)||!n),{checkForDefaultPrevented:!1})})})});es.displayName="MenuCheckboxItem";var ea="MenuRadioGroup",[el,eu]=j(ea,{value:void 0,onValueChange:()=>{}}),ec=i.forwardRef((e,t)=>{let{value:n,onValueChange:i,...r}=e,o=(0,w.c)(i);return(0,A.jsx)(el,{scope:e.__scopeMenu,value:n,onValueChange:o,children:(0,A.jsx)(ee,{...r,ref:t})})});ec.displayName=ea;var ed="MenuRadioItem",eh=i.forwardRef((e,t)=>{let{value:n,...i}=e,o=eu(ed,e.__scopeMenu),s=n===o.value;return(0,A.jsx)(ep,{scope:e.__scopeMenu,checked:s,children:(0,A.jsx)(er,{role:"menuitemradio","aria-checked":s,...i,ref:t,"data-state":eR(s),onSelect:(0,r.m)(i.onSelect,()=>{var e;return null===(e=o.onValueChange)||void 0===e?void 0:e.call(o,n)},{checkForDefaultPrevented:!1})})})});eh.displayName=ed;var ef="MenuItemIndicator",[ep,em]=j(ef,{checked:!1}),ev=i.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:i,...r}=e,o=em(ef,n);return(0,A.jsx)(g.C,{present:i||eM(o.checked)||!0===o.checked,children:(0,A.jsx)(l.sG.span,{...r,ref:t,"data-state":eR(o.checked)})})});ev.displayName=ef;var eg=i.forwardRef((e,t)=>{let{__scopeMenu:n,...i}=e;return(0,A.jsx)(l.sG.div,{role:"separator","aria-orientation":"horizontal",...i,ref:t})});eg.displayName="MenuSeparator";var ey=i.forwardRef((e,t)=>{let{__scopeMenu:n,...i}=e,r=F(n);return(0,A.jsx)(m.i3,{...r,...i,ref:t})});ey.displayName="MenuArrow";var ex="MenuSub",[ew,eb]=j(ex),eP=e=>{let{__scopeMenu:t,children:n,open:r=!1,onOpenChange:o}=e,s=I(ex,t),a=F(t),[l,u]=i.useState(null),[c,d]=i.useState(null),h=(0,w.c)(o);return i.useEffect(()=>(!1===s.open&&h(!1),()=>h(!1)),[s.open,h]),(0,A.jsx)(m.bL,{...a,children:(0,A.jsx)(B,{scope:t,open:r,onOpenChange:h,content:c,onContentChange:d,children:(0,A.jsx)(ew,{scope:t,contentId:(0,p.B)(),triggerId:(0,p.B)(),trigger:l,onTriggerChange:u,children:n})})})};eP.displayName=ex;var eA="MenuSubTrigger",eT=i.forwardRef((e,t)=>{let n=I(eA,e.__scopeMenu),s=U(eA,e.__scopeMenu),a=eb(eA,e.__scopeMenu),l=G(eA,e.__scopeMenu),u=i.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=l,h={__scopeMenu:e.__scopeMenu},f=i.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return i.useEffect(()=>f,[f]),i.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,A.jsx)(_,{asChild:!0,...h,children:(0,A.jsx)(eo,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":a.contentId,"data-state":eC(n.open),...e,ref:(0,o.t)(t,a.onTriggerChange),onClick:t=>{var i;null===(i=e.onClick)||void 0===i||i.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,r.m)(e.onPointerMove,ek(t=>{l.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||u.current||(l.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:(0,r.m)(e.onPointerLeave,ek(e=>{var t,i;f();let r=null===(t=n.content)||void 0===t?void 0:t.getBoundingClientRect();if(r){let t=null===(i=n.content)||void 0===i?void 0:i.dataset.side,o="right"===t,s=r[o?"left":"right"],a=r[o?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:s,y:r.top},{x:a,y:r.top},{x:a,y:r.bottom},{x:s,y:r.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,r.m)(e.onKeyDown,t=>{let i=""!==l.searchRef.current;if(!e.disabled&&(!i||" "!==t.key)&&C[s.dir].includes(t.key)){var r;n.onOpenChange(!0),null===(r=n.content)||void 0===r||r.focus(),t.preventDefault()}})})})});eT.displayName=eA;var eE="MenuSubContent",eS=i.forwardRef((e,t)=>{let n=$(Y,e.__scopeMenu),{forceMount:s=n.forceMount,...a}=e,l=I(Y,e.__scopeMenu),u=U(Y,e.__scopeMenu),c=eb(eE,e.__scopeMenu),d=i.useRef(null),h=(0,o.s)(t,d);return(0,A.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,A.jsx)(g.C,{present:s||l.open,children:(0,A.jsx)(k.Slot,{scope:e.__scopeMenu,children:(0,A.jsx)(J,{id:c.contentId,"aria-labelledby":c.triggerId,...a,ref:h,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;u.isUsingKeyboardRef.current&&(null===(t=d.current)||void 0===t||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,r.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=M[u.dir].includes(e.key);if(t&&n){var i;l.onOpenChange(!1),null===(i=c.trigger)||void 0===i||i.focus(),e.preventDefault()}})})})})})});function eC(e){return e?"open":"closed"}function eM(e){return"indeterminate"===e}function eR(e){return eM(e)?"indeterminate":e?"checked":"unchecked"}function ek(e){return t=>"mouse"===t.pointerType?e(t):void 0}eS.displayName=eE;var eD="DropdownMenu",[eL,ej]=(0,s.A)(eD,[V]),eV=V(),[eF,eO]=eL(eD),eB=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:s,onOpenChange:l,modal:u=!0}=e,c=eV(t),d=i.useRef(null),[h=!1,f]=(0,a.i)({prop:o,defaultProp:s,onChange:l});return(0,A.jsx)(eF,{scope:t,triggerId:(0,p.B)(),triggerRef:d,contentId:(0,p.B)(),open:h,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(e=>!e),[f]),modal:u,children:(0,A.jsx)(W,{...c,open:h,onOpenChange:f,dir:r,modal:u,children:n})})};eB.displayName=eD;var eI="DropdownMenuTrigger",eN=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:i=!1,...s}=e,a=eO(eI,n),u=eV(n);return(0,A.jsx)(_,{asChild:!0,...u,children:(0,A.jsx)(l.sG.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":i?"":void 0,disabled:i,...s,ref:(0,o.t)(t,a.triggerRef),onPointerDown:(0,r.m)(e.onPointerDown,e=>{i||0!==e.button||!1!==e.ctrlKey||(a.onOpenToggle(),a.open||e.preventDefault())}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{!i&&(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eN.displayName=eI;var eU=e=>{let{__scopeDropdownMenu:t,...n}=e,i=eV(t);return(0,A.jsx)(K,{...i,...n})};eU.displayName="DropdownMenuPortal";var eW="DropdownMenuContent",e_=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,s=eO(eW,n),a=eV(n),l=i.useRef(!1);return(0,A.jsx)(q,{id:s.contentId,"aria-labelledby":s.triggerId,...a,...o,ref:t,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{var t;l.current||null===(t=s.triggerRef.current)||void 0===t||t.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,r.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,i=2===t.button||n;(!s.modal||i)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e_.displayName=eW;var ez=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,r=eV(n);return(0,A.jsx)(ee,{...r,...i,ref:t})});ez.displayName="DropdownMenuGroup";var eH=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,r=eV(n);return(0,A.jsx)(et,{...r,...i,ref:t})});eH.displayName="DropdownMenuLabel";var e$=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,r=eV(n);return(0,A.jsx)(er,{...r,...i,ref:t})});e$.displayName="DropdownMenuItem";var eK=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,r=eV(n);return(0,A.jsx)(es,{...r,...i,ref:t})});eK.displayName="DropdownMenuCheckboxItem";var eY=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,r=eV(n);return(0,A.jsx)(ec,{...r,...i,ref:t})});eY.displayName="DropdownMenuRadioGroup";var eX=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,r=eV(n);return(0,A.jsx)(eh,{...r,...i,ref:t})});eX.displayName="DropdownMenuRadioItem";var eG=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,r=eV(n);return(0,A.jsx)(ev,{...r,...i,ref:t})});eG.displayName="DropdownMenuItemIndicator";var eq=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,r=eV(n);return(0,A.jsx)(eg,{...r,...i,ref:t})});eq.displayName="DropdownMenuSeparator",i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,r=eV(n);return(0,A.jsx)(ey,{...r,...i,ref:t})}).displayName="DropdownMenuArrow";var eZ=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,r=eV(n);return(0,A.jsx)(eT,{...r,...i,ref:t})});eZ.displayName="DropdownMenuSubTrigger";var eQ=i.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,r=eV(n);return(0,A.jsx)(eS,{...r,...i,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eQ.displayName="DropdownMenuSubContent";var eJ=eB,e0=eN,e1=eU,e2=e_,e5=ez,e9=eH,e3=e$,e6=eK,e4=eY,e8=eX,e7=eG,te=eq,tt=e=>{let{__scopeDropdownMenu:t,children:n,open:i,onOpenChange:r,defaultOpen:o}=e,s=eV(t),[l=!1,u]=(0,a.i)({prop:i,defaultProp:o,onChange:r});return(0,A.jsx)(eP,{...s,open:l,onOpenChange:u,children:n})},tn=eZ,ti=eQ},51508:(e,t,n)=>{n.d(t,{Q:()=>i});let i=(0,n(12115).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},54416:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},60760:(e,t,n)=>{n.d(t,{N:()=>g});var i=n(95155),r=n(12115),o=n(90869),s=n(82885),a=n(80845),l=n(51508);class u extends r.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function c(e){let{children:t,isPresent:n}=e,o=(0,r.useId)(),s=(0,r.useRef)(null),a=(0,r.useRef)({width:0,height:0,top:0,left:0}),{nonce:c}=(0,r.useContext)(l.Q);return(0,r.useInsertionEffect)(()=>{let{width:e,height:t,top:i,left:r}=a.current;if(n||!s.current||!e||!t)return;s.current.dataset.motionPopId=o;let l=document.createElement("style");return c&&(l.nonce=c),document.head.appendChild(l),l.sheet&&l.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            top: ").concat(i,"px !important;\n            left: ").concat(r,"px !important;\n          }\n        ")),()=>{document.head.removeChild(l)}},[n]),(0,i.jsx)(u,{isPresent:n,childRef:s,sizeRef:a,children:r.cloneElement(t,{ref:s})})}let d=e=>{let{children:t,initial:n,isPresent:o,onExitComplete:l,custom:u,presenceAffectsLayout:d,mode:f}=e,p=(0,s.M)(h),m=(0,r.useId)(),v=(0,r.useCallback)(e=>{for(let t of(p.set(e,!0),p.values()))if(!t)return;l&&l()},[p,l]),g=(0,r.useMemo)(()=>({id:m,initial:n,isPresent:o,custom:u,onExitComplete:v,register:e=>(p.set(e,!1),()=>p.delete(e))}),d?[Math.random(),v]:[o,v]);return(0,r.useMemo)(()=>{p.forEach((e,t)=>p.set(t,!1))},[o]),r.useEffect(()=>{o||p.size||!l||l()},[o]),"popLayout"===f&&(t=(0,i.jsx)(c,{isPresent:o,children:t})),(0,i.jsx)(a.t.Provider,{value:g,children:t})};function h(){return new Map}var f=n(32082);let p=e=>e.key||"";function m(e){let t=[];return r.Children.forEach(e,e=>{(0,r.isValidElement)(e)&&t.push(e)}),t}var v=n(97494);let g=e=>{let{children:t,custom:n,initial:a=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:c="sync",propagate:h=!1}=e,[g,y]=(0,f.xQ)(h),x=(0,r.useMemo)(()=>m(t),[t]),w=h&&!g?[]:x.map(p),b=(0,r.useRef)(!0),P=(0,r.useRef)(x),A=(0,s.M)(()=>new Map),[T,E]=(0,r.useState)(x),[S,C]=(0,r.useState)(x);(0,v.E)(()=>{b.current=!1,P.current=x;for(let e=0;e<S.length;e++){let t=p(S[e]);w.includes(t)?A.delete(t):!0!==A.get(t)&&A.set(t,!1)}},[S,w.length,w.join("-")]);let M=[];if(x!==T){let e=[...x];for(let t=0;t<S.length;t++){let n=S[t],i=p(n);w.includes(i)||(e.splice(t,0,n),M.push(n))}"wait"===c&&M.length&&(e=M),C(m(e)),E(x);return}let{forceRender:R}=(0,r.useContext)(o.L);return(0,i.jsx)(i.Fragment,{children:S.map(e=>{let t=p(e),r=(!h||!!g)&&(x===S||w.includes(t));return(0,i.jsx)(d,{isPresent:r,initial:(!b.current||!!a)&&void 0,custom:r?void 0:n,presenceAffectsLayout:u,mode:c,onExitComplete:r?void 0:()=>{if(!A.has(t))return;A.set(t,!0);let e=!0;A.forEach(t=>{t||(e=!1)}),e&&(null==R||R(),C(P.current),h&&(null==y||y()),l&&l())},children:e},t)})})}},62098:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},68972:(e,t,n)=>{n.d(t,{B:()=>i});let i="undefined"!=typeof window},70333:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]])},74783:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},80845:(e,t,n)=>{n.d(t,{t:()=>i});let i=(0,n(12115).createContext)(null)},82885:(e,t,n)=>{n.d(t,{M:()=>r});var i=n(12115);function r(e){let t=(0,i.useRef)(null);return null===t.current&&(t.current=e()),t.current}},84616:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},90869:(e,t,n)=>{n.d(t,{L:()=>i});let i=(0,n(12115).createContext)({})},92293:(e,t,n)=>{n.d(t,{Oh:()=>o});var i=n(12115),r=0;function o(){i.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:s()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:s()),r++,()=>{1===r&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),r--}},[])}function s(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},93509:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},93795:(e,t,n)=>{n.d(t,{A:()=>$});var i,r=function(){return(r=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};function o(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++)0>t.indexOf(i[r])&&Object.prototype.propertyIsEnumerable.call(e,i[r])&&(n[i[r]]=e[i[r]]);return n}Object.create;Object.create;var s=("function"==typeof SuppressedError&&SuppressedError,n(12115)),a="right-scroll-bar-position",l="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?s.useLayoutEffect:s.useEffect,d=new WeakMap;function h(e){return e}var f=function(e){void 0===e&&(e={});var t,n,i,o,s=(t=null,void 0===n&&(n=h),i=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return i.length?i[i.length-1]:null},useMedium:function(e){var t=n(e,o);return i.push(t),function(){i=i.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;i.length;){var t=i;i=[],t.forEach(e)}i={push:function(t){return e(t)},filter:function(){return i}}},assignMedium:function(e){o=!0;var t=[];if(i.length){var n=i;i=[],n.forEach(e),t=i}var r=function(){var n=t;t=[],n.forEach(e)},s=function(){return Promise.resolve().then(r)};s(),i={push:function(e){t.push(e),s()},filter:function(e){return t=t.filter(e),i}}}});return s.options=r({async:!0,ssr:!1},e),s}(),p=function(){},m=s.forwardRef(function(e,t){var n,i,a,l,h=s.useRef(null),m=s.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),v=m[0],g=m[1],y=e.forwardProps,x=e.children,w=e.className,b=e.removeScrollBar,P=e.enabled,A=e.shards,T=e.sideCar,E=e.noIsolation,S=e.inert,C=e.allowPinchZoom,M=e.as,R=e.gapMode,k=o(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),D=(n=[h,t],i=function(e){return n.forEach(function(t){return u(t,e)})},(a=(0,s.useState)(function(){return{value:null,callback:i,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=i,l=a.facade,c(function(){var e=d.get(l);if(e){var t=new Set(e),i=new Set(n),r=l.current;t.forEach(function(e){i.has(e)||u(e,null)}),i.forEach(function(e){t.has(e)||u(e,r)})}d.set(l,n)},[n]),l),L=r(r({},k),v);return s.createElement(s.Fragment,null,P&&s.createElement(T,{sideCar:f,removeScrollBar:b,shards:A,noIsolation:E,inert:S,setCallbacks:g,allowPinchZoom:!!C,lockRef:h,gapMode:R}),y?s.cloneElement(s.Children.only(x),r(r({},L),{ref:D})):s.createElement(void 0===M?"div":M,r({},L,{className:w,ref:D}),x))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:l,zeroRight:a};var v=function(e){var t=e.sideCar,n=o(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var i=t.read();if(!i)throw Error("Sidecar medium not found");return s.createElement(i,r({},n))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=i||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,s;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),s=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(s)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){s.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},x=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},b=function(e){return parseInt(e||"",10)||0},P=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],i=t["padding"===e?"paddingTop":"marginTop"],r=t["padding"===e?"paddingRight":"marginRight"];return[b(n),b(i),b(r)]},A=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=P(e),n=document.documentElement.clientWidth,i=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,i-n+t[2]-t[0])}},T=x(),E="data-scroll-locked",S=function(e,t,n,i){var r=e.left,o=e.top,s=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(i,";\n   padding-right: ").concat(u,"px ").concat(i,";\n  }\n  body[").concat(E,"] {\n    overflow: hidden ").concat(i,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(i,";"),"margin"===n&&"\n    padding-left: ".concat(r,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(s,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(i,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(i,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(u,"px ").concat(i,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(u,"px ").concat(i,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(i,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(i,";\n  }\n  \n  body[").concat(E,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},C=function(){var e=parseInt(document.body.getAttribute(E)||"0",10);return isFinite(e)?e:0},M=function(){s.useEffect(function(){return document.body.setAttribute(E,(C()+1).toString()),function(){var e=C()-1;e<=0?document.body.removeAttribute(E):document.body.setAttribute(E,e.toString())}},[])},R=function(e){var t=e.noRelative,n=e.noImportant,i=e.gapMode,r=void 0===i?"margin":i;M();var o=s.useMemo(function(){return A(r)},[r]);return s.createElement(T,{styles:S(o,!t,r,n?"":"!important")})},k=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return k=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){k=!1}var L=!!k&&{passive:!1},j=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},V=function(e,t){var n=t.ownerDocument,i=t;do{if("undefined"!=typeof ShadowRoot&&i instanceof ShadowRoot&&(i=i.host),F(e,i)){var r=O(e,i);if(r[1]>r[2])return!0}i=i.parentNode}while(i&&i!==n.body);return!1},F=function(e,t){return"v"===e?j(t,"overflowY"):j(t,"overflowX")},O=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},B=function(e,t,n,i,r){var o,s=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),a=s*i,l=n.target,u=t.contains(l),c=!1,d=a>0,h=0,f=0;do{var p=O(e,l),m=p[0],v=p[1]-p[2]-s*m;(m||v)&&F(e,l)&&(h+=v,f+=m),l=l instanceof ShadowRoot?l.host:l.parentNode}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return d&&(r&&1>Math.abs(h)||!r&&a>h)?c=!0:!d&&(r&&1>Math.abs(f)||!r&&-a>f)&&(c=!0),c},I=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},N=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},W=0,_=[];let z=(f.useMedium(function(e){var t=s.useRef([]),n=s.useRef([0,0]),i=s.useRef(),r=s.useState(W++)[0],o=s.useState(x)[0],a=s.useRef(e);s.useEffect(function(){a.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(r));var t=(function(e,t,n){if(n||2==arguments.length)for(var i,r=0,o=t.length;r<o;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(r))}),function(){document.body.classList.remove("block-interactivity-".concat(r)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(r))})}}},[e.inert,e.lockRef.current,e.shards]);var l=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var r,o=I(e),s=n.current,l="deltaX"in e?e.deltaX:s[0]-o[0],u="deltaY"in e?e.deltaY:s[1]-o[1],c=e.target,d=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var h=V(d,c);if(!h)return!0;if(h?r=d:(r="v"===d?"h":"v",h=V(d,c)),!h)return!1;if(!i.current&&"changedTouches"in e&&(l||u)&&(i.current=r),!r)return!0;var f=i.current||r;return B(f,t,e,"h"===f?l:u,!0)},[]),u=s.useCallback(function(e){if(_.length&&_[_.length-1]===o){var n="deltaY"in e?N(e):I(e),i=t.current.filter(function(t){var i;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(i=t.delta)[0]===n[0]&&i[1]===n[1]})[0];if(i&&i.should){e.cancelable&&e.preventDefault();return}if(!i){var r=(a.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(r.length>0?l(e,r[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=s.useCallback(function(e,n,i,r){var o={name:e,delta:n,target:i,should:r,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(i)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),d=s.useCallback(function(e){n.current=I(e),i.current=void 0},[]),h=s.useCallback(function(t){c(t.type,N(t),t.target,l(t,e.lockRef.current))},[]),f=s.useCallback(function(t){c(t.type,I(t),t.target,l(t,e.lockRef.current))},[]);s.useEffect(function(){return _.push(o),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:f}),document.addEventListener("wheel",u,L),document.addEventListener("touchmove",u,L),document.addEventListener("touchstart",d,L),function(){_=_.filter(function(e){return e!==o}),document.removeEventListener("wheel",u,L),document.removeEventListener("touchmove",u,L),document.removeEventListener("touchstart",d,L)}},[]);var p=e.removeScrollBar,m=e.inert;return s.createElement(s.Fragment,null,m?s.createElement(o,{styles:"\n  .block-interactivity-".concat(r," {pointer-events: none;}\n  .allow-interactivity-").concat(r," {pointer-events: all;}\n")}):null,p?s.createElement(R,{gapMode:e.gapMode}):null)}),v);var H=s.forwardRef(function(e,t){return s.createElement(m,r({},e,{ref:t,sideCar:z}))});H.classNames=m.classNames;let $=H},96766:(e,t,n)=>{n.d(t,{A:()=>i});let i=(0,n(19946).A)("Cuboid",[["path",{d:"m21.12 6.4-6.05-4.06a2 2 0 0 0-2.17-.05L2.95 8.41a2 2 0 0 0-.95 1.7v5.82a2 2 0 0 0 .88 1.66l6.05 4.07a2 2 0 0 0 2.17.05l9.95-6.12a2 2 0 0 0 .95-1.7V8.06a2 2 0 0 0-.88-1.66Z",key:"1u2ovd"}],["path",{d:"M10 22v-8L2.25 9.15",key:"11pn4q"}],["path",{d:"m10 14 11.77-6.87",key:"1kt1wh"}]])},97494:(e,t,n)=>{n.d(t,{E:()=>r});var i=n(12115);let r=n(68972).B?i.useLayoutEffect:i.useEffect}}]);