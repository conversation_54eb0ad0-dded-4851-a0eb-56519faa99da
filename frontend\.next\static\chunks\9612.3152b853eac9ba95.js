"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9612],{29612:(e,t,n)=>{n.r(t),n.d(t,{Document:()=>_,Outline:()=>W,Page:()=>eo,PasswordResponses:()=>E,Thumbnail:()=>ea,pdfjs:()=>r,useDocumentContext:()=>V,useOutlineContext:()=>F,usePageContext:()=>Q});var r=n(13824),o=n(95155),i=n(12115);let a=["onCopy","onCut","onPaste","onCompositionEnd","onCompositionStart","onCompositionUpdate","onFocus","onBlur","onInput","onInvalid","onReset","onSubmit","onLoad","onError","onKeyDown","onKeyPress","onKeyUp","onAbort","onCanPlay","onCanPlayThrough","onDurationChange","onEmptied","onEncrypted","onEnded","onError","onLoadedData","onLoadedMetadata","onLoadStart","onPause","onPlay","onPlaying","onProgress","onRateChange","onSeeked","onSeeking","onStalled","onSuspend","onTimeUpdate","onVolumeChange","onWaiting","onClick","onContextMenu","onDoubleClick","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onSelect","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onPointerDown","onPointerMove","onPointerUp","onPointerCancel","onGotPointerCapture","onLostPointerCapture","onPointerEnter","onPointerLeave","onPointerOver","onPointerOut","onScroll","onWheel","onAnimationStart","onAnimationEnd","onAnimationIteration","onTransitionEnd","onChange","onToggle"];function s(e,t){let n={};for(let r of a){let o=e[r];o&&(t?n[r]=e=>o(e,t(r)):n[r]=o)}return n}function l(e){let t=!1;return{promise:new Promise((n,r)=>{e.then(e=>!t&&n(e)).catch(e=>!t&&r(e))}),cancel(){t=!0}}}var u=n(52596),c=n(93179),d=n(94274),f=Object.prototype.hasOwnProperty;function p(e,t,n){for(n of e.keys())if(g(n,t))return n}function g(e,t){var n,r,o;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((r=e.length)===t.length)for(;r--&&g(e[r],t[r]););return -1===r}if(n===Set){if(e.size!==t.size)return!1;for(r of e)if((o=r)&&"object"==typeof o&&!(o=p(t,o))||!t.has(o))return!1;return!0}if(n===Map){if(e.size!==t.size)return!1;for(r of e)if((o=r[0])&&"object"==typeof o&&!(o=p(t,o))||!g(r[1],t.get(o)))return!1;return!0}if(n===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(n===DataView){if((r=e.byteLength)===t.byteLength)for(;r--&&e.getInt8(r)===t.getInt8(r););return -1===r}if(ArrayBuffer.isView(e)){if((r=e.byteLength)===t.byteLength)for(;r--&&e[r]===t[r];);return -1===r}if(!n||"object"==typeof e){for(n in r=0,e)if(f.call(e,n)&&++r&&!f.call(t,n)||!(n in t)||!g(e[n],t[n]))return!1;return Object.keys(t).length===r}}return e!=e&&t!=t}let h=(0,i.createContext)(null);function m({children:e,type:t}){return(0,o.jsx)("div",{className:`react-pdf__message react-pdf__message--${t}`,children:e})}class y{constructor(){this.externalLinkEnabled=!0,this.externalLinkRel=void 0,this.externalLinkTarget=void 0,this.isInPresentationMode=!1,this.pdfDocument=void 0,this.pdfViewer=void 0}setDocument(e){this.pdfDocument=e}setViewer(e){this.pdfViewer=e}setExternalLinkRel(e){this.externalLinkRel=e}setExternalLinkTarget(e){this.externalLinkTarget=e}setHistory(){}get pagesCount(){return this.pdfDocument?this.pdfDocument.numPages:0}get page(){return(0,c.A)(this.pdfViewer,"PDF viewer is not initialized."),this.pdfViewer.currentPageNumber||0}set page(e){(0,c.A)(this.pdfViewer,"PDF viewer is not initialized."),this.pdfViewer.currentPageNumber=e}get rotation(){return 0}set rotation(e){}goToDestination(e){return new Promise(t=>{(0,c.A)(this.pdfDocument,"PDF document not loaded."),(0,c.A)(e,"Destination is not specified."),"string"==typeof e?this.pdfDocument.getDestination(e).then(t):Array.isArray(e)?t(e):e.then(t)}).then(e=>{(0,c.A)(Array.isArray(e),`"${e}" is not a valid destination array.`);let t=e[0];new Promise(e=>{(0,c.A)(this.pdfDocument,"PDF document not loaded."),t instanceof Object?this.pdfDocument.getPageIndex(t).then(t=>{e(t)}).catch(()=>{(0,c.A)(!1,`"${t}" is not a valid page reference.`)}):"number"==typeof t?e(t):(0,c.A)(!1,`"${t}" is not a valid destination reference.`)}).then(t=>{let n=t+1;(0,c.A)(this.pdfViewer,"PDF viewer is not initialized."),(0,c.A)(n>=1&&n<=this.pagesCount,`"${n}" is not a valid page number.`),this.pdfViewer.scrollPageIntoView({dest:e,pageIndex:t,pageNumber:n})})})}navigateTo(e){this.goToDestination(e)}goToPage(e){(0,c.A)(this.pdfViewer,"PDF viewer is not initialized."),(0,c.A)(e>=1&&e<=this.pagesCount,`"${e}" is not a valid page number.`),this.pdfViewer.scrollPageIntoView({pageIndex:e-1,pageNumber:e})}addLinkAttributes(e,t,n){e.href=t,e.rel=this.externalLinkRel||"noopener noreferrer nofollow",e.target=n?"_blank":this.externalLinkTarget||""}getDestinationHash(){return"#"}getAnchorUrl(){return"#"}setHash(){}executeNamedAction(){}cachePageRef(){}isPageVisible(){return!0}isPageCached(){return!0}executeSetOCGState(){}}let E={NEED_PASSWORD:1,INCORRECT_PASSWORD:2},b="undefined"!=typeof window,v=b&&"file:"===window.location.protocol;function w(e){return null!=e}function S(e){return"string"==typeof e&&/^data:/.test(e)}function x(e){(0,c.A)(S(e),"Invalid data URI.");let[t="",n=""]=e.split(",");return -1!==t.split(";").indexOf("base64")?atob(n):unescape(n)}let O="On Chromium based browsers, you can use --allow-file-access-from-files flag for debugging purposes.";function P(){d(!v,`Loading PDF as base64 strings/URLs may not work on protocols other than HTTP/HTTPS. ${O}`)}function R(e){(null==e?void 0:e.cancel)&&e.cancel()}function A(e,t){return Object.defineProperty(e,"width",{get(){return this.view[2]*t},configurable:!0}),Object.defineProperty(e,"height",{get(){return this.view[3]*t},configurable:!0}),Object.defineProperty(e,"originalWidth",{get(){return this.view[2]},configurable:!0}),Object.defineProperty(e,"originalHeight",{get(){return this.view[3]},configurable:!0}),e}function T(e,t){switch(t.type){case"RESOLVE":return{value:t.value,error:void 0};case"REJECT":return{value:!1,error:t.error};case"RESET":return{value:void 0,error:void 0};default:return e}}function j(){return(0,i.useReducer)(T,{value:void 0,error:void 0})}var L=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};let{PDFDataRangeTransport:C}=r,D=(e,t)=>{switch(t){case E.NEED_PASSWORD:e(prompt("Enter the password to open this PDF file."));break;case E.INCORRECT_PASSWORD:e(prompt("Invalid password. Please try again."))}};function k(e){return"object"==typeof e&&null!==e&&("data"in e||"range"in e||"url"in e)}let _=(0,i.forwardRef)(function(e,t){var{children:n,className:a,error:f="Failed to load PDF file.",externalLinkRel:p,externalLinkTarget:E,file:v,inputRef:w,imageResourcesPath:O,loading:A="Loading PDF…",noData:T="No PDF file specified.",onItemClick:_,onLoadError:M,onLoadProgress:I,onLoadSuccess:N,onPassword:V=D,onSourceError:F,onSourceSuccess:G,options:U,renderMode:H,rotate:W,scale:B}=e,z=L(e,["children","className","error","externalLinkRel","externalLinkTarget","file","inputRef","imageResourcesPath","loading","noData","onItemClick","onLoadError","onLoadProgress","onLoadSuccess","onPassword","onSourceError","onSourceSuccess","options","renderMode","rotate","scale"]);let[$,J]=j(),{value:q,error:K}=$,[Y,Q]=j(),{value:X,error:Z}=Y,ee=(0,i.useRef)(new y),et=(0,i.useRef)([]),en=(0,i.useRef)(void 0),er=(0,i.useRef)(void 0);v&&v!==en.current&&k(v)&&(d(!g(v,en.current),'File prop passed to <Document /> changed, but it\'s equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to "file" prop.'),en.current=v),U&&U!==er.current&&(d(!g(U,er.current),'Options prop passed to <Document /> changed, but it\'s equal to previous one. This might result in unnecessary reloads. Consider memoizing the value passed to "options" prop.'),er.current=U);let eo=(0,i.useRef)({scrollPageIntoView:e=>{let{dest:t,pageNumber:n,pageIndex:r=n-1}=e;if(_){_({dest:t,pageIndex:r,pageNumber:n});return}let o=et.current[r];if(o){o.scrollIntoView();return}d(!1,"An internal link leading to page ".concat(n," was clicked, but neither <Document> was provided with onItemClick nor it was able to find the page within itself. Either provide onItemClick to <Document> and handle navigating by yourself or ensure that all pages are rendered within <Document>."))}});(0,i.useImperativeHandle)(t,()=>({linkService:ee,pages:et,viewer:eo}),[]),(0,i.useEffect)(function(){J({type:"RESET"})},[v,J]);let ei=(0,i.useCallback)(async()=>{if(!v)return null;if("string"==typeof v)return S(v)?{data:x(v)}:(P(),{url:v});if(v instanceof C)return{range:v};if(v instanceof ArrayBuffer)return{data:v};if(b){if((0,c.A)(b,"isBlob can only be used in a browser environment"),v instanceof Blob)return{data:await new Promise((e,t)=>{let n=new FileReader;n.onload=()=>{if(!n.result)return t(Error("Error while reading a file."));e(n.result)},n.onerror=e=>{if(!e.target)return t(Error("Error while reading a file."));let{error:n}=e.target;if(!n)return t(Error("Error while reading a file."));switch(n.code){case n.NOT_FOUND_ERR:return t(Error("Error while reading a file: File not found."));case n.SECURITY_ERR:return t(Error("Error while reading a file: Security error."));case n.ABORT_ERR:return t(Error("Error while reading a file: Aborted."));default:return t(Error("Error while reading a file."))}},n.readAsArrayBuffer(v)})}}if((0,c.A)("object"==typeof v,"Invalid parameter in file, need either Uint8Array, string or a parameter object"),(0,c.A)(k(v),"Invalid parameter object: need either .data, .range or .url"),"url"in v&&"string"==typeof v.url){if(S(v.url)){let{url:e}=v,t=L(v,["url"]);return Object.assign({data:x(e)},t)}P()}return v},[v]);(0,i.useEffect)(()=>{let e=l(ei());return e.promise.then(e=>{J({type:"RESOLVE",value:e})}).catch(e=>{J({type:"REJECT",error:e})}),()=>{R(e)}},[ei,J]),(0,i.useEffect)(()=>{if(void 0!==q){if(!1===q){K&&(d(!1,K.toString()),F&&F(K));return}G&&G()}},[q]),(0,i.useEffect)(function(){Q({type:"RESET"})},[Q,q]),(0,i.useEffect)(function(){if(!q)return;let e=U?Object.assign(Object.assign({},q),U):q,t=r.getDocument(e);I&&(t.onProgress=I),V&&(t.onPassword=V);let n=t.promise.then(e=>{Q({type:"RESOLVE",value:e})}).catch(e=>{!t.destroyed&&Q({type:"REJECT",error:e})});return()=>{n.finally(()=>t.destroy())}},[U,Q,q]),(0,i.useEffect)(()=>{if(void 0!==X){if(!1===X){Z&&(d(!1,Z.toString()),M&&M(Z));return}!X||(N&&N(X),et.current=Array(X.numPages),ee.current.setDocument(X))}},[X]),(0,i.useEffect)(function(){ee.current.setViewer(eo.current),ee.current.setExternalLinkRel(p),ee.current.setExternalLinkTarget(E)},[p,E]);let ea=(0,i.useCallback)((e,t)=>{et.current[e]=t},[]),es=(0,i.useCallback)(e=>{delete et.current[e]},[]),el=(0,i.useMemo)(()=>({imageResourcesPath:O,linkService:ee.current,onItemClick:_,pdf:X,registerPage:ea,renderMode:H,rotate:W,scale:B,unregisterPage:es}),[O,_,X,ea,H,W,B,es]),eu=(0,i.useMemo)(()=>s(z,()=>X),[z,X]);return(0,o.jsx)("div",Object.assign({className:(0,u.A)("react-pdf__Document",a),ref:w},eu,{children:v?null==X?(0,o.jsx)(m,{type:"loading",children:"function"==typeof A?A():A}):!1===X?(0,o.jsx)(m,{type:"error",children:"function"==typeof f?f():f}):(0,o.jsx)(h.Provider,{value:el,children:n}):(0,o.jsx)(m,{type:"no-data",children:"function"==typeof T?T():T})}))}),M=(0,i.createContext)(null);class I{constructor({num:e,gen:t}){this.num=e,this.gen=t}toString(){let e=`${this.num}R`;return 0!==this.gen&&(e+=this.gen),e}}function N(e){let t=(0,i.useRef)(void 0),n=t.current;return void 0!==n?()=>n:()=>{let n=e();return t.current=n,n}}function V(){return(0,i.useContext)(h)}function F(){return(0,i.useContext)(M)}var G=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function U(e){let t=V(),n=F();(0,c.A)(n,"Unable to find Outline context.");let r=Object.assign(Object.assign(Object.assign({},t),n),e),{item:i,linkService:a,onItemClick:s,pdf:l}=r,u=G(r,["item","linkService","onItemClick","pdf"]);(0,c.A)(l,"Attempted to load an outline, but no document was specified. Wrap <Outline /> in a <Document /> or pass explicit `pdf` prop.");let d=N(()=>"string"==typeof i.dest?l.getDestination(i.dest):i.dest),f=N(async()=>{let e=await d();if(!e)throw Error("Destination not found.");let[t]=e;return l.getPageIndex(new I(t))}),p=N(async()=>await f()+1);return(0,o.jsxs)("li",{children:[(0,o.jsx)("a",{href:"#",onClick:function(e){e.preventDefault(),(0,c.A)(s||a,"Either onItemClick callback or linkService must be defined in order to navigate to an outline item."),s?Promise.all([d(),f(),p()]).then(([e,t,n])=>{s({dest:e,pageIndex:t,pageNumber:n})}):a&&a.goToDestination(i.dest)},children:i.title}),function(){if(!i.items||!i.items.length)return null;let{items:e}=i;return(0,o.jsx)("ul",{children:e.map((e,t)=>(0,o.jsx)(U,Object.assign({item:e,pdf:l},u),"string"==typeof e.dest?e.dest:t))})}()]})}var H=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function W(e){let t=Object.assign(Object.assign({},V()),e),{className:n,inputRef:r,onItemClick:a,onLoadError:f,onLoadSuccess:p,pdf:g}=t,h=H(t,["className","inputRef","onItemClick","onLoadError","onLoadSuccess","pdf"]);(0,c.A)(g,"Attempted to load an outline, but no document was specified. Wrap <Outline /> in a <Document /> or pass explicit `pdf` prop.");let[m,y]=j(),{value:E,error:b}=m;(0,i.useEffect)(function(){y({type:"RESET"})},[y,g]),(0,i.useEffect)(function(){if(!g)return;let e=l(g.getOutline());return e.promise.then(e=>{y({type:"RESOLVE",value:e})}).catch(e=>{y({type:"REJECT",error:e})}),()=>R(e)},[y,g]),(0,i.useEffect)(()=>{if(void 0!==E){if(!1===E){b&&(d(!1,b.toString()),f&&f(b));return}void 0!==E&&!1!==E&&p&&p(E)}},[E]);let v=(0,i.useMemo)(()=>({onItemClick:a}),[a]),w=(0,i.useMemo)(()=>s(h,()=>E),[h,E]);return E?(0,o.jsx)("div",Object.assign({className:(0,u.A)("react-pdf__Outline",n),ref:r},w,{children:(0,o.jsx)(M.Provider,{value:v,children:E?(0,o.jsx)("ul",{children:E.map((e,t)=>(0,o.jsx)(U,{item:e,pdf:g},"string"==typeof e.dest?e.dest:t))}):null})})):null}function B(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=e.filter(Boolean);return n.length<=1?n[0]||null:function(e){for(var t=0;t<n.length;t++){var r=n[t];"function"==typeof r?r(e):r&&(r.current=e)}}}let z=(0,i.createContext)(null),$={Document:null,DocumentFragment:null,Part:"group",Sect:"group",Div:"group",Aside:"note",NonStruct:"none",P:null,H:"heading",Title:null,FENote:"note",Sub:"group",Lbl:null,Span:null,Em:null,Strong:null,Link:"link",Annot:"note",Form:"form",Ruby:null,RB:null,RT:null,RP:null,Warichu:null,WT:null,WP:null,L:"list",LI:"listitem",LBody:null,Table:"table",TR:"row",TH:"columnheader",TD:"cell",THead:"columnheader",TBody:null,TFoot:null,Caption:null,Figure:"figure",Formula:null,Artifact:null},J=/^H(\d+)$/;function q(e){return"children"in e}function K(e){return!!q(e)&&1===e.children.length&&0 in e.children&&"id"in e.children[0]}function Y({className:e,node:t}){let n=(0,i.useMemo)(()=>t?Object.assign(Object.assign({},function(e){let t={};if(q(e)){let{role:n}=e,r=n.match(J);if(r)t.role="heading",t["aria-level"]=Number(r[1]);else if(n in $){let e=$[n];e&&(t.role=e)}}return t}(t)),function e(t){let n={};if(q(t)){if(void 0!==t.alt&&(n["aria-label"]=t.alt),void 0!==t.lang&&(n.lang=t.lang),K(t)){let[r]=t.children;if(r){let t=e(r);return Object.assign(Object.assign({},n),t)}}}else"id"in t&&(n["aria-owns"]=t.id);return n}(t)):null,[t]),r=(0,i.useMemo)(()=>!q(t)||K(t)?null:t.children.map((e,t)=>(0,o.jsx)(Y,{node:e},t)),[t]);return(0,o.jsx)("span",Object.assign({className:e},n,{children:r}))}function Q(){return(0,i.useContext)(z)}function X(){let e=Q();(0,c.A)(e,"Unable to find Page context.");let{onGetStructTreeError:t,onGetStructTreeSuccess:n}=e,[r,a]=j(),{value:s,error:u}=r,{customTextRenderer:f,page:p}=e;return((0,i.useEffect)(function(){a({type:"RESET"})},[a,p]),(0,i.useEffect)(function(){if(f||!p)return;let e=l(p.getStructTree());return e.promise.then(e=>{a({type:"RESOLVE",value:e})}).catch(e=>{a({type:"REJECT",error:e})}),()=>R(e)},[f,p,a]),(0,i.useEffect)(()=>{if(void 0!==s){if(!1===s){u&&(d(!1,u.toString()),t&&t(u));return}s&&n&&n(s)}},[s]),s)?(0,o.jsx)(Y,{className:"react-pdf__Page__structTree structTree",node:s}):null}let Z=r.AnnotationMode;function ee(e){let t=Q();(0,c.A)(t,"Unable to find Page context.");let{_className:n,canvasBackground:r,devicePixelRatio:a=b&&window.devicePixelRatio||1,onRenderError:s,onRenderSuccess:l,page:u,renderForms:f,renderTextLayer:p,rotate:g,scale:h}=Object.assign(Object.assign({},t),e),{canvasRef:m}=e;(0,c.A)(u,"Attempted to render page canvas, but no page was specified.");let y=(0,i.useRef)(null);function E(e){"RenderingCancelledException"!==e.name&&(d(!1,e.toString()),s&&s(e))}let v=(0,i.useMemo)(()=>u.getViewport({scale:h*a,rotation:g}),[a,u,g,h]),w=(0,i.useMemo)(()=>u.getViewport({scale:h,rotation:g}),[u,g,h]);(0,i.useEffect)(function(){if(!u)return;u.cleanup();let{current:e}=y;if(!e)return;e.width=v.width,e.height=v.height,e.style.width="".concat(Math.floor(w.width),"px"),e.style.height="".concat(Math.floor(w.height),"px"),e.style.visibility="hidden";let t={annotationMode:f?Z.ENABLE_FORMS:Z.ENABLE,canvasContext:e.getContext("2d",{alpha:!1}),viewport:v};r&&(t.background=r);let n=u.render(t);return n.promise.then(()=>{e.style.visibility="",u&&l&&l(A(u,h))}).catch(E),()=>R(n)},[r,u,f,v,w]);let S=(0,i.useCallback)(()=>{let{current:e}=y;e&&(e.width=0,e.height=0)},[]);return(0,i.useEffect)(()=>S,[S]),(0,o.jsx)("canvas",{className:"".concat(n,"__canvas"),dir:"ltr",ref:B(m,y),style:{display:"block",userSelect:"none"},children:p?(0,o.jsx)(X,{}):null})}function et(){let e=Q();(0,c.A)(e,"Unable to find Page context.");let{customTextRenderer:t,onGetTextError:n,onGetTextSuccess:a,onRenderTextLayerError:s,onRenderTextLayerSuccess:f,page:p,pageIndex:g,pageNumber:h,rotate:m,scale:y}=e;(0,c.A)(p,"Attempted to load page text content, but no page was specified.");let[E,b]=j(),{value:v,error:w}=E,S=(0,i.useRef)(null);d(1===Number.parseInt(window.getComputedStyle(document.body).getPropertyValue("--react-pdf-text-layer"),10),"TextLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-text-layer"),(0,i.useEffect)(function(){b({type:"RESET"})},[p,b]),(0,i.useEffect)(function(){if(!p)return;let e=l(p.getTextContent());return e.promise.then(e=>{b({type:"RESOLVE",value:e})}).catch(e=>{b({type:"REJECT",error:e})}),()=>R(e)},[p,b]),(0,i.useEffect)(()=>{if(void 0!==v){if(!1===v){w&&(d(!1,w.toString()),n&&n(w));return}v&&a&&a(v)}},[v]);let x=(0,i.useCallback)(()=>{f&&f()},[f]),O=(0,i.useCallback)(e=>{d(!1,e.toString()),s&&s(e)},[s]),P=(0,i.useMemo)(()=>p.getViewport({scale:y,rotation:m}),[p,m,y]);return(0,i.useLayoutEffect)(function(){if(!p||!v)return;let{current:e}=S;if(!e)return;e.innerHTML="";let n=p.streamTextContent({includeMarkedContent:!0}),o=new r.TextLayer({container:e,textContentSource:n,viewport:P});return o.render().then(()=>{let n=document.createElement("div");n.className="endOfContent",e.append(n);let r=e.querySelectorAll('[role="presentation"]');if(t){let e=0;v.items.forEach((n,o)=>{if(!("str"in n))return;let i=r[e];i&&(i.innerHTML=t(Object.assign({pageIndex:g,pageNumber:h,itemIndex:o},n)),e+=n.str&&n.hasEOL?2:1)})}x()}).catch(O),()=>R(o)},[t,O,x,p,g,h,v,P]),(0,o.jsx)("div",{className:(0,u.A)("react-pdf__Page__textContent","textLayer"),onMouseUp:function(){let e=S.current;e&&e.classList.remove("selecting")},onMouseDown:function(){let e=S.current;e&&e.classList.add("selecting")},ref:S})}function en(){let e=V(),t=Q();(0,c.A)(t,"Unable to find Page context.");let{imageResourcesPath:n,linkService:a,onGetAnnotationsError:s,onGetAnnotationsSuccess:f,onRenderAnnotationLayerError:p,onRenderAnnotationLayerSuccess:g,page:h,pdf:m,renderForms:y,rotate:E,scale:b=1}=Object.assign(Object.assign({},e),t);(0,c.A)(m,"Attempted to load page annotations, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop."),(0,c.A)(h,"Attempted to load page annotations, but no page was specified."),(0,c.A)(a,"Attempted to load page annotations, but no linkService was specified.");let[v,w]=j(),{value:S,error:x}=v,O=(0,i.useRef)(null);d(1===Number.parseInt(window.getComputedStyle(document.body).getPropertyValue("--react-pdf-annotation-layer"),10),"AnnotationLayer styles not found. Read more: https://github.com/wojtekmaj/react-pdf#support-for-annotations"),(0,i.useEffect)(function(){w({type:"RESET"})},[w,h]),(0,i.useEffect)(function(){if(!h)return;let e=l(h.getAnnotations());return e.promise.then(e=>{w({type:"RESOLVE",value:e})}).catch(e=>{w({type:"REJECT",error:e})}),()=>{R(e)}},[w,h]),(0,i.useEffect)(()=>{if(void 0!==S){if(!1===S){x&&(d(!1,x.toString()),s&&s(x));return}S&&f&&f(S)}},[S]);let P=(0,i.useMemo)(()=>h.getViewport({scale:b,rotation:E}),[h,E,b]);return(0,i.useEffect)(function(){if(!m||!h||!a||!S)return;let{current:e}=O;if(!e)return;let t=P.clone({dontFlip:!0}),o={annotations:S,annotationStorage:m.annotationStorage,div:e,imageResourcesPath:n,linkService:a,page:h,renderForms:y,viewport:t};e.innerHTML="";try{new r.AnnotationLayer({accessibilityManager:null,annotationCanvasMap:null,annotationEditorUIManager:null,div:e,l10n:null,page:h,structTreeLayer:null,viewport:t}).render(o),g&&g()}catch(e){d(!1,"".concat(e)),p&&p(e)}return()=>{}},[S,n,a,h,m,y,P]),(0,o.jsx)("div",{className:(0,u.A)("react-pdf__Page__annotations","annotationLayer"),ref:O})}var er=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function eo(e){let t=Object.assign(Object.assign({},V()),e),{_className:n="react-pdf__Page",_enableRegisterUnregisterPage:r=!0,canvasBackground:a,canvasRef:f,children:p,className:g,customRenderer:h,customTextRenderer:y,devicePixelRatio:E,error:b="Failed to load the page.",height:v,inputRef:S,loading:x="Loading page…",noData:O="No page specified.",onGetAnnotationsError:P,onGetAnnotationsSuccess:T,onGetStructTreeError:L,onGetStructTreeSuccess:C,onGetTextError:D,onGetTextSuccess:k,onLoadError:_,onLoadSuccess:M,onRenderAnnotationLayerError:I,onRenderAnnotationLayerSuccess:N,onRenderError:F,onRenderSuccess:G,onRenderTextLayerError:U,onRenderTextLayerSuccess:H,pageIndex:W,pageNumber:$,pdf:J,registerPage:q,renderAnnotationLayer:K=!0,renderForms:Y=!1,renderMode:Q="canvas",renderTextLayer:X=!0,rotate:Z,scale:eo=1,unregisterPage:ei,width:ea}=t,es=er(t,["_className","_enableRegisterUnregisterPage","canvasBackground","canvasRef","children","className","customRenderer","customTextRenderer","devicePixelRatio","error","height","inputRef","loading","noData","onGetAnnotationsError","onGetAnnotationsSuccess","onGetStructTreeError","onGetStructTreeSuccess","onGetTextError","onGetTextSuccess","onLoadError","onLoadSuccess","onRenderAnnotationLayerError","onRenderAnnotationLayerSuccess","onRenderError","onRenderSuccess","onRenderTextLayerError","onRenderTextLayerSuccess","pageIndex","pageNumber","pdf","registerPage","renderAnnotationLayer","renderForms","renderMode","renderTextLayer","rotate","scale","unregisterPage","width"]),[el,eu]=j(),{value:ec,error:ed}=el,ef=(0,i.useRef)(null);(0,c.A)(J,"Attempted to load a page, but no document was specified. Wrap <Page /> in a <Document /> or pass explicit `pdf` prop.");let ep=w($)?$-1:null!=W?W:null,eg=null!=$?$:w(W)?W+1:null,eh=null!=Z?Z:ec?ec.rotate:null,em=(0,i.useMemo)(()=>{if(!ec)return null;let e=1,t=null!=eo?eo:1;if(ea||v){let t=ec.getViewport({scale:1,rotation:eh});ea?e=ea/t.width:v&&(e=v/t.height)}return t*e},[v,ec,eh,eo,ea]);(0,i.useEffect)(function(){return()=>{w(ep)&&r&&ei&&ei(ep)}},[r,J,ep,ei]),(0,i.useEffect)(function(){eu({type:"RESET"})},[eu,J,ep]),(0,i.useEffect)(function(){if(!J||!eg)return;let e=l(J.getPage(eg));return e.promise.then(e=>{eu({type:"RESOLVE",value:e})}).catch(e=>{eu({type:"REJECT",error:e})}),()=>R(e)},[eu,J,eg]),(0,i.useEffect)(()=>{if(void 0!==ec){if(!1===ec){ed&&(d(!1,ed.toString()),_&&_(ed));return}!function(){if(M){if(!ec||!em)return;M(A(ec,em))}if(r&&q){if(!w(ep)||!ef.current)return;q(ep,ef.current)}}()}},[ec,em]);let ey=(0,i.useMemo)(()=>ec&&w(ep)&&eg&&w(eh)&&w(em)?{_className:n,canvasBackground:a,customTextRenderer:y,devicePixelRatio:E,onGetAnnotationsError:P,onGetAnnotationsSuccess:T,onGetStructTreeError:L,onGetStructTreeSuccess:C,onGetTextError:D,onGetTextSuccess:k,onRenderAnnotationLayerError:I,onRenderAnnotationLayerSuccess:N,onRenderError:F,onRenderSuccess:G,onRenderTextLayerError:U,onRenderTextLayerSuccess:H,page:ec,pageIndex:ep,pageNumber:eg,renderForms:Y,renderTextLayer:X,rotate:eh,scale:em}:null,[n,a,y,E,P,T,L,C,D,k,I,N,F,G,U,H,ec,ep,eg,Y,X,eh,em]),eE=(0,i.useMemo)(()=>s(es,()=>ec?em?A(ec,em):void 0:ec),[es,ec,em]),eb="".concat(ep,"@").concat(em,"/").concat(eh);return(0,o.jsx)("div",Object.assign({className:(0,u.A)(n,g),"data-page-number":eg,ref:B(S,ef),style:{"--scale-round-x":"1px","--scale-round-y":"1px","--scale-factor":"1","--user-unit":"".concat(em),"--total-scale-factor":"calc(var(--scale-factor) * var(--user-unit))",backgroundColor:a||"white",position:"relative",minWidth:"min-content",minHeight:"min-content"}},eE,{children:eg?null===J||null==ec?(0,o.jsx)(m,{type:"loading",children:"function"==typeof x?x():x}):!1===J||!1===ec?(0,o.jsx)(m,{type:"error",children:"function"==typeof b?b():b}):(0,o.jsxs)(z.Provider,{value:ey,children:[function(){switch(Q){case"custom":return(0,c.A)(h,'renderMode was set to "custom", but no customRenderer was passed.'),(0,o.jsx)(h,{},"".concat(eb,"_custom"));case"none":return null;default:return(0,o.jsx)(ee,{canvasRef:f},"".concat(eb,"_canvas"))}}(),X?(0,o.jsx)(et,{},"".concat(eb,"_text")):null,K?(0,o.jsx)(en,{},"".concat(eb,"_annotations")):null,p]}):(0,o.jsx)(m,{type:"no-data",children:"function"==typeof O?O():O})}))}var ei=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function ea(e){let{className:t,linkService:n,onItemClick:r,pageIndex:i,pageNumber:a,pdf:s}=Object.assign(Object.assign({},V()),e);(0,c.A)(s,"Attempted to load a thumbnail, but no document was specified. Wrap <Thumbnail /> in a <Document /> or pass explicit `pdf` prop.");let l=w(a)?a-1:null!=i?i:null,d=null!=a?a:w(i)?i+1:null,{className:f,onItemClick:p}=e,g=ei(e,["className","onItemClick"]);return(0,o.jsx)("a",{className:(0,u.A)("react-pdf__Thumbnail",t),href:d?"#":void 0,onClick:function(e){e.preventDefault(),w(l)&&d&&((0,c.A)(r||n,"Either onItemClick callback or linkService must be defined in order to navigate to an outline item."),r?r({pageIndex:l,pageNumber:d}):n&&n.goToPage(d))},children:(0,o.jsx)(eo,Object.assign({},g,{_className:"react-pdf__Thumbnail__page",_enableRegisterUnregisterPage:!1,renderAnnotationLayer:!1,renderTextLayer:!1}))})}d(!v,`Loading PDF.js worker may not work on protocols other than HTTP/HTTPS. ${O}`),r.GlobalWorkerOptions.workerSrc="pdf.worker.mjs"},94274:e=>{e.exports=function(){}}}]);