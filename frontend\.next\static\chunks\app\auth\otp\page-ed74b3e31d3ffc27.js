(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9463],{6501:(e,t,r)=>{Promise.resolve().then(r.bind(r,40918))},40918:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var a=r(95155),s=r(12115),l=r(97168),n=r(89852),i=r(88482),o=r(35695),c=r(66766),d=r(31886),u=r(56671);function f(){let e=(0,o.useRouter)(),[t,r]=(0,s.useState)(Array(6).fill("")),f=(0,s.useRef)([]),[m,g]=(0,s.useState)(60),[p,h]=(0,s.useState)(!1),[x,v]=(0,s.useState)(!1),[b,y]=(0,s.useState)("");(0,s.useEffect)(()=>{let t=localStorage.getItem("email");if(!t){u.o.error("No email found. Please register again."),e.push("/auth");return}y(t);let r=f.current[0];r&&r.focus();let a=setInterval(()=>{g(e=>e<=1?(clearInterval(a),0):e-1)},1e3);return()=>clearInterval(a)},[e]);let N=(e,a)=>{if(!/^\d*$/.test(a))return;let s=[...t];if(s[e]=a.substring(0,1),r(s),a&&e<5&&f.current[e+1]){let t=f.current[e+1];t&&t.focus()}},j=(e,a)=>{if("ArrowRight"===a.key&&e<5){let t=f.current[e+1];t&&t.focus()}else if("ArrowLeft"===a.key&&e>0){let t=f.current[e-1];t&&t.focus()}if("Backspace"===a.key){if(t[e]){let a=[...t];a[e]="",r(a)}else if(e>0){let t=f.current[e-1];t&&t.focus()}}},w=e=>{e.preventDefault();let a=e.clipboardData.getData("text/plain").trim();if(!/^\d+$/.test(a))return;let s=[...t];for(let e=0;e<Math.min(a.length,6);e++)s[e]=a[e];r(s),a.length<6&&f.current[a.length]&&f.current[a.length].focus()},P=async()=>{if(0===m){h(!0);try{let t=localStorage.getItem("username");if(!t){u.o.error("No username found. Please register again."),e.push("/auth");return}await d.authApi.register({email:b,username:t,password:"",confirm_password:"",first_name:"",last_name:""}),g(60),u.o.success("OTP resent successfully!")}catch(e){var t,r;u.o.error((null===(r=e.response)||void 0===r?void 0:null===(t=r.data)||void 0===t?void 0:t.message)||"Failed to resend OTP. Please try again.")}finally{h(!1)}}},k=async()=>{let r=t.join("");if(6!==r.length){u.o.error("Please enter a valid OTP");return}v(!0);try{await d.authApi.verifyOTP({email:b,otp:r});try{if(!localStorage.getItem("username")){u.o.error("No username found. Please register again."),e.push("/auth");return}u.o.success("Email verified successfully! Please sign in with your credentials."),e.push("/auth")}catch(t){u.o.error("Account verified but failed to sign in automatically. Please sign in manually."),e.push("/auth")}}catch(e){var a,s;u.o.error((null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.message)||"Failed to verify OTP. Please try again.")}finally{v(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsx)("div",{className:"flex justify-center mb-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"relative h-10 w-10",children:(0,a.jsx)(c.default,{src:"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png",alt:"Cognimosity Logo",fill:!0,className:"object-contain"})}),(0,a.jsx)("span",{className:"text-2xl font-bold",children:"Cognimosity"})]})}),(0,a.jsxs)(i.Zp,{className:"border-border bg-card",children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"OTP Verification"}),(0,a.jsx)(i.BT,{children:"We've sent a verification code to your email. Please enter the code below."})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"flex justify-between mb-6",children:t.map((e,t)=>(0,a.jsx)(n.p,{ref:e=>{f.current[t]=e},type:"text",inputMode:"numeric",maxLength:1,value:e,onChange:e=>N(t,e.target.value),onKeyDown:e=>j(t,e),onPaste:0===t?w:void 0,className:"w-12 h-12 text-center text-lg"},t))}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:"Didn't receive the code?"}),(0,a.jsx)(l.$,{variant:"link",className:"p-0 h-auto text-sm ".concat(0===m?"text-purple-500":"text-muted-foreground cursor-not-allowed"),onClick:P,disabled:m>0||p,children:p?"Resending...":m>0?"Resend in ".concat((e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(m)):"Resend OTP"})]})]}),(0,a.jsx)(i.wL,{children:(0,a.jsx)(l.$,{className:"w-full bg-purple-600 hover:bg-purple-700",onClick:k,disabled:6!==t.join("").length||x,children:x?"Verifying...":"Verify"})})]})]})})}},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>u});var a=r(95155),s=r(12115),l=r(53999);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});n.displayName="Card";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...s})});i.displayName="CardHeader";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});o.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",r),...s})});d.displayName="CardContent";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",r),...s})});u.displayName="CardFooter"},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(95155),s=r(12115),l=r(53999);let n=s.forwardRef((e,t)=>{let{className:r,type:s,...n}=e;return(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...n})});n.displayName="Input"}},e=>{var t=t=>e(e.s=t);e.O(0,[9749,3464,6671,1455,8441,1684,7358],()=>t(6501)),_N_E=e.O()}]);