(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{19324:()=>{},20946:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,19324,23)),Promise.resolve().then(s.bind(s,99304)),Promise.resolve().then(s.t.bind(s,29840,23)),Promise.resolve().then(s.bind(s,56671))},29840:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},99304:(e,t,s)=>{"use strict";s.d(t,{D:()=>i,ThemeProvider:()=>a});var r=s(95155),o=s(12115);let n=(0,o.createContext)({theme:"system",setTheme:()=>null});function a(e){let{children:t,defaultTheme:s="system",storageKey:a="ui-theme",enableSystem:i=!0,disableTransitionOnChange:l=!1,...m}=e,[d,c]=(0,o.useState)(s);return(0,o.useEffect)(()=>{window.document.documentElement;let e=localStorage.getItem(a);e?c(e):i&&c(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light")},[a,i]),(0,o.useEffect)(()=>{let e=window.document.documentElement;if(l&&(e.classList.add("transition-none"),window.setTimeout(()=>{e.classList.remove("transition-none")},0)),"system"===d&&i){let t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";e.classList.remove("light","dark"),e.classList.add(t);return}e.classList.remove("light","dark"),e.classList.add(d),localStorage.setItem(a,d)},[d,l,i,a]),(0,r.jsx)(n.Provider,{...m,value:{theme:d,setTheme:e=>{c(e)}},children:t})}let i=()=>{let e=(0,o.useContext)(n);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[7385,6671,8441,1684,7358],()=>t(20946)),_N_E=e.O()}]);