(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{34964:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>c,av:()=>d,j7:()=>o,tU:()=>i});var a=s(95155),r=s(12115),l=s(60704),n=s(53999);let i=l.bL,o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.B8,{ref:t,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...r})});o.displayName=l.B8.displayName;let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.l9,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...r})});c.displayName=l.l9.displayName;let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.UC,{ref:t,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...r})});d.displayName=l.UC.displayName},82714:(e,t,s)=>{"use strict";s.d(t,{J:()=>c});var a=s(95155),r=s(12115),l=s(40968),n=s(74466),i=s(53999);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.b,{ref:t,className:(0,i.cn)(o(),s),...r})});c.displayName=l.b.displayName},86450:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(95155);s(12115);var r=s(60760),l=s(1978);function n(e){let{isVisible:t,message:s="Personalizing your learning journey. Just a moment..."}=e;return(0,a.jsx)(r.N,{children:t&&(0,a.jsxs)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},className:"fixed inset-0 z-[9999] flex flex-col items-center justify-center",style:{background:"rgba(255, 255, 255, 0)",backdropFilter:"blur(8px)",WebkitBackdropFilter:"blur(8px)"},onClick:e=>e.preventDefault(),children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-8",children:"Cognimosity"}),(0,a.jsxs)("div",{className:"flex gap-5 mb-8",children:[(0,a.jsxs)("div",{className:"relative w-12 h-12",children:[(0,a.jsx)("svg",{viewBox:"0 0 80 80",className:"w-full h-full",children:(0,a.jsx)("circle",{cx:"40",cy:"40",r:"32",fill:"none",stroke:"#d3d3d3",strokeWidth:"10",strokeLinecap:"round",strokeLinejoin:"round",strokeDasharray:"150 50 150 50",strokeDashoffset:"75",style:{animation:"pathCircle 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"}})}),(0,a.jsx)("div",{className:"absolute w-2 h-2 bg-purple-600 rounded-full top-[37px] left-[19px] transform -translate-x-[18px] -translate-y-[18px]",style:{animation:"dotRect 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"}})]}),(0,a.jsxs)("div",{className:"relative w-12 h-12",children:[(0,a.jsx)("svg",{viewBox:"0 0 86 80",className:"w-full h-full",children:(0,a.jsx)("polygon",{points:"43 8 79 72 7 72",fill:"none",stroke:"#d3d3d3",strokeWidth:"10",strokeLinecap:"round",strokeLinejoin:"round",strokeDasharray:"145 76 145 76",strokeDashoffset:"0",style:{animation:"pathTriangle 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"}})}),(0,a.jsx)("div",{className:"absolute w-2 h-2 bg-purple-600 rounded-full top-[37px] left-[21px] transform -translate-x-[10px] -translate-y-[18px]",style:{animation:"dotTriangle 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"}})]}),(0,a.jsxs)("div",{className:"relative w-12 h-12",children:[(0,a.jsx)("svg",{viewBox:"0 0 80 80",className:"w-full h-full",children:(0,a.jsx)("rect",{x:"8",y:"8",width:"64",height:"64",fill:"none",stroke:"#d3d3d3",strokeWidth:"10",strokeLinecap:"round",strokeLinejoin:"round",strokeDasharray:"192 64 192 64",strokeDashoffset:"0",style:{animation:"pathRect 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"}})}),(0,a.jsx)("div",{className:"absolute w-2 h-2 bg-purple-600 rounded-full top-[37px] left-[19px] transform -translate-x-[18px] -translate-y-[18px]",style:{animation:"dotRect 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"}})]})]}),(0,a.jsx)("div",{className:"text-base text-gray-700 text-center max-w-md px-4",children:s})]})})}},88402:(e,t,s)=>{"use strict";s.d(t,{CogniUI:()=>K});var a=s(95155),r=s(12115),l=s(74783),n=s(34835),i=s(17951),o=s(62098),c=s(93509),d=s(29869),u=s(38164),m=s(97207),x=s(84616),p=s(96766),h=s(57434),g=s(40646),b=s(14186),f=s(85339),j=s(97168),v=s(9685),N=s(35695),y=s(66766),w=s(99304),k=s(15452),C=s(54416),S=s(53999);let A=k.bL;k.l9;let I=k.ZL;k.bm;let R=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(k.hJ,{ref:t,className:(0,S.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r})});R.displayName=k.hJ.displayName;let _=r.forwardRef((e,t)=>{let{className:s,children:r,...l}=e;return(0,a.jsxs)(I,{children:[(0,a.jsx)(R,{}),(0,a.jsxs)(k.UC,{ref:t,className:(0,S.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...l,children:[r,(0,a.jsxs)(k.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(C.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});_.displayName=k.UC.displayName;let D=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,S.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};D.displayName="DialogHeader";let O=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(k.hE,{ref:t,className:(0,S.cn)("text-lg font-semibold leading-none tracking-tight",s),...r})});O.displayName=k.hE.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(k.VY,{ref:t,className:(0,S.cn)("text-sm text-muted-foreground",s),...r})}).displayName=k.VY.displayName;var P=s(89852),U=s(82714);function L(e){let{open:t,setOpen:l,onCreated:n}=e,[i,o]=(0,r.useState)(""),[c,d]=(0,r.useState)(!1),[u,m]=(0,r.useState)(null),x=async()=>{if(i.trim()){d(!0),m(null);try{await Promise.resolve().then(s.bind(s,31886)).then(e=>{let{documentApi:t}=e;return t.createGroup({name:i})}),l(!1),o(""),null==n||n()}catch(s){var e,t;m((null==s?void 0:null===(t=s.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.error)||"Failed to create space")}finally{d(!1)}}};return(0,a.jsx)(A,{open:t,onOpenChange:l,children:(0,a.jsxs)(_,{className:"sm:max-w-md bg-background border-border",children:[(0,a.jsx)(D,{children:(0,a.jsx)(O,{children:"Create New Space"})}),(0,a.jsx)("div",{className:"py-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(U.J,{htmlFor:"name",children:"Space Name"}),(0,a.jsx)(P.p,{id:"name",placeholder:"Enter space name",value:i,onChange:e=>o(e.target.value),className:"bg-background border-border",disabled:c})]}),u&&(0,a.jsx)("div",{className:"text-red-500 text-sm",children:u}),(0,a.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,a.jsx)(j.$,{variant:"outline",onClick:()=>l(!1),disabled:c,children:"Cancel"}),(0,a.jsx)(j.$,{className:"bg-purple-600 hover:bg-purple-700",onClick:x,disabled:!i.trim()||c,children:c?"Creating...":"Create Space"})]})]})})]})})}var T=s(99890),$=s(1978),F=s(56671),z=s(31886),E=s(86450);function J(e){let{isOpen:t,setIsOpen:s,onUploadSuccess:l,groupId:n,groups:i=[]}=e,[o,c]=(0,r.useState)(!1),[u,m]=(0,r.useState)([]),[x,p]=(0,r.useState)(!1),[h,g]=(0,r.useState)(null!=n?n:null),b=(0,r.useRef)(null),f=(0,N.useRouter)();(0,r.useEffect)(()=>{g(null!=n?n:null)},[n,t]);let v=e=>{m(t=>t.filter((t,s)=>s!==e))},y=async()=>{if(0!==u.length){p(!0);try{let e=await z.documentApi.uploadDocument(u[0],null!=h?h:void 0);if(e.message){F.o.success(e.message),localStorage.removeItem("uploadedFiles"),localStorage.removeItem("filePreviewUrls"),localStorage.removeItem("currentDocumentId"),e.document_id&&(localStorage.setItem("currentDocumentId",e.document_id.toString()),localStorage.setItem("uploadedFiles",JSON.stringify(u.map(t=>({name:t.name,size:t.size,type:t.type,lastModified:t.lastModified,documentId:e.document_id}))))),s(!1),null==l||l();let t=e.document_id;t?f.push("/process?type=upload&documentId=".concat(t)):f.push("/process?type=upload")}}catch(s){var e,t;F.o.error((null===(t=s.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.error)||"Failed to upload file. Please try again."),p(!1)}}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(E.p,{isVisible:x,message:"Uploading and processing your file. This may take a few moments..."}),(0,a.jsx)(A,{open:t,onOpenChange:s,children:(0,a.jsxs)(_,{className:"sm:max-w-md bg-neutral-900 border-neutral-800",children:[(0,a.jsx)(D,{children:(0,a.jsx)(O,{className:"text-center",children:"Upload Files"})}),i.length>0&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{htmlFor:"group-select",className:"block text-sm font-medium mb-1 text-neutral-300",children:"Select Space/Group"}),(0,a.jsxs)("select",{id:"group-select",className:"w-full p-2 rounded border border-neutral-700 bg-neutral-800 text-neutral-100",value:null!=h?h:"",onChange:e=>g(e.target.value?Number(e.target.value):null),disabled:x,children:[(0,a.jsx)("option",{value:"",children:"No Space (default)"}),i.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{className:"mt-4 border-2 border-dashed rounded-lg p-8 text-center transition-colors ".concat(o?"border-purple-500 bg-purple-500/10":"border-neutral-700"),onDragOver:e=>{e.preventDefault(),c(!0)},onDragLeave:()=>{c(!1)},onDrop:e=>{if(e.preventDefault(),c(!1),e.dataTransfer.files&&e.dataTransfer.files.length>0){let t=Array.from(e.dataTransfer.files);m(e=>[...e,...t])}},children:[(0,a.jsx)("input",{type:"file",ref:b,onChange:e=>{if(e.target.files&&e.target.files.length>0){let t=Array.from(e.target.files);m(e=>[...e,...t])}},className:"hidden",multiple:!0}),(0,a.jsxs)($.P.div,{initial:{scale:1},animate:{scale:o?1.05:1},className:"flex flex-col items-center",children:[(0,a.jsx)("div",{className:"p-3 bg-neutral-800 rounded-full mb-3",children:(0,a.jsx)(d.A,{className:"h-6 w-6 text-purple-500"})}),(0,a.jsx)("p",{className:"text-neutral-300 mb-2",children:"Drag and drop files here"}),(0,a.jsx)("p",{className:"text-neutral-500 text-sm mb-4",children:"or"}),(0,a.jsx)(j.$,{onClick:()=>{var e;null===(e=b.current)||void 0===e||e.click()},className:"bg-purple-600 hover:bg-purple-700",children:"Browse Files"}),(0,a.jsx)("p",{className:"text-neutral-500 text-xs mt-4",children:"Supported formats: PDF, PPT, DOC, TXT, MP3, WAV"})]})]}),u.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h3",{className:"text-sm font-medium mb-2",children:"Selected Files"}),(0,a.jsx)("div",{className:"max-h-40 overflow-y-auto space-y-2",children:u.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-neutral-800 p-2 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(T.A,{className:"h-4 w-4 mr-2 text-purple-500"}),(0,a.jsx)("span",{className:"text-sm truncate max-w-[200px]",children:e.name})]}),(0,a.jsx)(j.$,{variant:"ghost",size:"icon",className:"h-6 w-6",onClick:()=>v(t),children:(0,a.jsx)(C.A,{className:"h-4 w-4"})})]},t))}),(0,a.jsxs)("div",{className:"mt-4 flex justify-end gap-2",children:[(0,a.jsx)(j.$,{variant:"outline",onClick:()=>m([]),children:"Clear All"}),(0,a.jsxs)(j.$,{className:"bg-purple-600 hover:bg-purple-700",disabled:0===u.length,onClick:y,children:["Upload ",u.length," ",1===u.length?"File":"Files"]})]})]})]})})]})}var B=s(2925),V=s(34869),W=s(34964),Y=s(99474);function M(e){let{isOpen:t,setIsOpen:s}=e,[l,n]=(0,r.useState)(""),[i,o]=(0,r.useState)(""),[c,d]=(0,r.useState)(""),[u,m]=(0,r.useState)("youtube"),x=(0,N.useRouter)();return(0,a.jsx)(A,{open:t,onOpenChange:s,children:(0,a.jsxs)(_,{className:"sm:max-w-md bg-neutral-900 border-neutral-800",children:[(0,a.jsx)(D,{children:(0,a.jsx)(O,{className:"text-center",children:"Paste Content"})}),(0,a.jsxs)(W.tU,{defaultValue:"youtube",className:"mt-4",onValueChange:m,children:[(0,a.jsxs)(W.j7,{className:"grid grid-cols-3 mb-4",children:[(0,a.jsxs)(W.Xi,{value:"youtube",className:"data-[state=active]:bg-purple-600",children:[(0,a.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"YouTube"]}),(0,a.jsxs)(W.Xi,{value:"website",className:"data-[state=active]:bg-purple-600",children:[(0,a.jsx)(V.A,{className:"h-4 w-4 mr-2"}),"Website"]}),(0,a.jsxs)(W.Xi,{value:"text",className:"data-[state=active]:bg-purple-600",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Text"]})]}),(0,a.jsxs)(W.av,{value:"youtube",className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm text-neutral-400 mb-2 block",children:"Paste YouTube URL"}),(0,a.jsx)(P.p,{placeholder:"https://www.youtube.com/watch?v=...",value:l,onChange:e=>n(e.target.value),className:"bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500"})]}),(0,a.jsx)("p",{className:"text-xs text-neutral-500",children:"Enter a YouTube video URL to extract and analyze its content"})]}),(0,a.jsxs)(W.av,{value:"website",className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm text-neutral-400 mb-2 block",children:"Paste Website URL"}),(0,a.jsx)(P.p,{placeholder:"https://example.com",value:i,onChange:e=>o(e.target.value),className:"bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500"})]}),(0,a.jsx)("p",{className:"text-xs text-neutral-500",children:"Enter a website URL to extract and analyze its content"})]}),(0,a.jsx)(W.av,{value:"text",className:"space-y-4",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm text-neutral-400 mb-2 block",children:"Paste Text Content"}),(0,a.jsx)(Y.T,{placeholder:"Paste or type your text here...",value:c,onChange:e=>d(e.target.value),className:"min-h-[150px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500"})]})})]}),(0,a.jsxs)("div",{className:"mt-4 flex justify-end gap-2",children:[(0,a.jsx)(j.$,{variant:"outline",onClick:()=>s(!1),children:"Cancel"}),(0,a.jsx)(j.$,{className:"bg-purple-600 hover:bg-purple-700",onClick:()=>{"youtube"===u&&l?localStorage.setItem("pastedContent",JSON.stringify({type:"youtube",url:l})):"website"===u&&i?localStorage.setItem("pastedContent",JSON.stringify({type:"website",url:i})):"text"===u&&c&&localStorage.setItem("pastedContent",JSON.stringify({type:"text",content:c})),s(!1),x.push("/process?type=paste")},disabled:"youtube"===u&&!l||"website"===u&&!i||"text"===u&&!c,children:"Process Content"})]})]})})}var X=s(18979),G=s(85690),H=s(82178),Q=s(4229);function Z(e){let{isOpen:t,setIsOpen:s}=e,[l,n]=(0,r.useState)(!1),[i,o]=(0,r.useState)(!1),[c,d]=(0,r.useState)(0),[u,x]=(0,r.useState)(null),p=(0,r.useRef)(null),h=(0,r.useRef)([]),g=(0,r.useRef)(null),b=(0,r.useRef)(null),f=(0,N.useRouter)();(0,r.useEffect)(()=>()=>{g.current&&clearInterval(g.current),u&&URL.revokeObjectURL(u)},[u]);let v=async()=>{try{let e=await navigator.mediaDevices.getUserMedia({audio:!0}),t=new MediaRecorder(e);p.current=t,h.current=[],t.ondataavailable=e=>{e.data.size>0&&h.current.push(e.data)},t.onstop=()=>{let e=new Blob(h.current,{type:"audio/wav"}),t=URL.createObjectURL(e);x(t)},t.start(),n(!0),o(!1),g.current=setInterval(()=>{d(e=>e+1)},1e3)}catch(e){console.error("Error accessing microphone:",e)}},y=()=>{p.current&&l&&(p.current.stop(),n(!1),g.current&&(clearInterval(g.current),g.current=null),p.current.stream.getTracks().forEach(e=>e.stop()))},w=e=>{let t=Math.floor(e/60);return"".concat(t.toString().padStart(2,"0"),":").concat((e%60).toString().padStart(2,"0"))};return(0,a.jsx)(A,{open:t,onOpenChange:e=>{!e&&l&&y(),s(e)},children:(0,a.jsxs)(_,{className:"sm:max-w-md bg-neutral-900 border-neutral-800",children:[(0,a.jsx)(D,{children:(0,a.jsx)(O,{className:"text-center",children:"Record Audio"})}),(0,a.jsxs)("div",{className:"mt-4 flex flex-col items-center",children:[(0,a.jsxs)($.P.div,{initial:{scale:1},animate:{scale:l&&!i?[1,1.1,1]:1,transition:{repeat:l&&!i?Number.POSITIVE_INFINITY:0,duration:1.5}},className:"relative mb-6",children:[(0,a.jsx)("div",{className:"p-8 rounded-full ".concat(l?"bg-red-500/20":"bg-neutral-800"),children:(0,a.jsx)(m.A,{className:"h-10 w-10 ".concat(l?"text-red-500":"text-purple-500")})}),l&&!i&&(0,a.jsx)($.P.div,{className:"absolute inset-0 rounded-full border-2 border-red-500",initial:{scale:1,opacity:1},animate:{scale:[1,1.2,1.2,1],opacity:[1,.8,.2,0]},transition:{duration:2,repeat:Number.POSITIVE_INFINITY,ease:"easeOut"}})]}),(0,a.jsx)("div",{className:"text-2xl font-mono mb-6",children:w(c)}),(0,a.jsx)("div",{className:"flex gap-4 mb-6",children:l?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j.$,{onClick:y,className:"bg-red-600 hover:bg-red-700 rounded-full h-12 w-12 p-0",children:(0,a.jsx)(X.A,{className:"h-5 w-5"})}),i?(0,a.jsx)(j.$,{onClick:()=>{p.current&&l&&i&&(p.current.resume(),o(!1),g.current=setInterval(()=>{d(e=>e+1)},1e3))},className:"bg-purple-600 hover:bg-purple-700 rounded-full h-12 w-12 p-0",children:(0,a.jsx)(G.A,{className:"h-5 w-5"})}):(0,a.jsx)(j.$,{onClick:()=>{p.current&&l&&!i&&(p.current.pause(),o(!0),g.current&&(clearInterval(g.current),g.current=null))},className:"bg-purple-600 hover:bg-purple-700 rounded-full h-12 w-12 p-0",children:(0,a.jsx)(H.A,{className:"h-5 w-5"})})]}):(0,a.jsx)(j.$,{onClick:v,className:"bg-purple-600 hover:bg-purple-700 rounded-full h-12 w-12 p-0",children:(0,a.jsx)(m.A,{className:"h-5 w-5"})})}),u&&(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("audio",{ref:b,src:u,controls:!0,className:"w-full mb-4"}),(0,a.jsxs)(j.$,{onClick:()=>{u&&localStorage.setItem("recordedAudio",JSON.stringify({duration:w(c),timestamp:new Date().toISOString()})),s(!1),f.push("/process?type=record")},className:"w-full bg-purple-600 hover:bg-purple-700",children:[(0,a.jsx)(Q.A,{className:"h-4 w-4 mr-2"}),"Process Recording"]})]})]})]})})}var q=s(67133);function K(){var e;let t=(0,N.useRouter)(),{theme:s,setTheme:k}=(0,w.D)(),[C,S]=(0,r.useState)(!1),[A,I]=(0,r.useState)(!1),[R,_]=(0,r.useState)(!1),[D,O]=(0,r.useState)(!1),[P,U]=(0,r.useState)(!1),[T,$]=(0,r.useState)(!1),[F,E]=(0,r.useState)(""),[B,V]=(0,r.useState)([]),[W,Y]=(0,r.useState)(!1),[X,G]=(0,r.useState)(null),[H,Q]=(0,r.useState)([]),[K,ee]=(0,r.useState)(null),et=()=>{localStorage.removeItem("isLoggedIn"),localStorage.removeItem("username"),localStorage.removeItem("token"),$(!1),E(""),V([]),t.push("/")},es=async()=>{if(T){Y(!0),G(null);try{let e=await z.documentApi.getDocuments();V(e.results||e||[])}catch(e){console.error("Error fetching documents:",e),G("Failed to load documents")}finally{Y(!1)}}},ea=e=>{"completed"===e.processing_status&&t.push("/process?documentId=".concat(e.id,"&type=upload"))};r.useEffect(()=>{let e="true"===localStorage.getItem("isLoggedIn"),t=localStorage.getItem("username");e&&t&&($(!0),E(t))},[]),(0,r.useEffect)(()=>{T&&es()},[T]),(0,r.useEffect)(()=>{let e=async()=>{try{let e=await z.documentApi.getGroups();Q(Array.isArray(e)?e:e.results||[])}catch(e){console.error("Failed to fetch groups:",e),Q([])}};T&&e()},[T]),(0,r.useEffect)(()=>{let e=async()=>{Y(!0),G(null);try{let e=await z.documentApi.getDocuments(K||void 0);V(e.results||e||[])}catch(e){console.error("Error fetching documents:",e),G("Failed to load documents")}finally{Y(!1)}};T&&e()},[T,K]);let er=()=>{I(!0)};return(0,a.jsxs)("div",{className:"flex h-screen bg-background text-foreground overflow-hidden",children:[(0,a.jsx)(v.B,{isOpen:C,setIsOpen:S,isLoggedIn:T,username:F,onLogout:et}),(0,a.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between p-4",children:[(0,a.jsx)(j.$,{variant:"ghost",size:"icon",className:"h-10 w-10 rounded-full",onClick:()=>S(!0),children:(0,a.jsx)(l.A,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[T?(0,a.jsxs)(q.rI,{children:[(0,a.jsx)(q.ty,{asChild:!0,children:(0,a.jsx)(j.$,{variant:"outline",className:"border-2 border-gray-300 hover:border-gray-500 bg-white hover:bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100 dark:border-gray-600 dark:hover:border-gray-400 dark:hover:bg-gray-800",children:F})}),(0,a.jsx)(q.SQ,{align:"end",children:(0,a.jsxs)(q._2,{onClick:et,children:[(0,a.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Log out"})]})})]}):(0,a.jsx)(j.$,{className:"bg-purple-600 hover:bg-purple-700",onClick:()=>{t.push("/auth")},children:"Sign In / Register"}),(0,a.jsxs)(j.$,{className:"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0",size:"sm",onClick:()=>{t.push("/subscription")},children:[(0,a.jsx)(i.A,{className:"h-4 w-4 mr-2"}),"Upgrade"]}),(0,a.jsx)(j.$,{variant:"ghost",size:"icon",className:"h-10 w-10 rounded-full",onClick:()=>k("dark"===s?"light":"dark"),children:"dark"===s?(0,a.jsx)(o.A,{className:"h-5 w-5"}):(0,a.jsx)(c.A,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{className:"max-w-5xl mx-auto px-4 py-8",children:[(0,a.jsx)("div",{className:"flex justify-center mb-8",children:(0,a.jsx)("div",{className:"relative h-16 w-16",children:(0,a.jsx)(y.default,{src:"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png",alt:"Cognimosity Logo",fill:!0,className:"object-contain"})})}),(0,a.jsx)("h1",{className:"text-4xl font-bold text-center mb-12",children:"Ready to unlock something new today?"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-16",children:[(0,a.jsxs)("button",{onClick:()=>_(!0),className:"border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group relative ".concat("light"===s?"border-black bg-white":"border-neutral-800"),children:[(0,a.jsx)("div",{className:"absolute top-2 right-2 bg-purple-500 text-xs px-2 py-0.5 rounded-full",children:"Popular"}),(0,a.jsx)("div",{className:"mb-4 p-3 rounded-full transition-all duration-300 ".concat("light"===s?"bg-white border border-black":"bg-neutral-900"," group-hover:bg-purple-500/20"),children:(0,a.jsx)(d.A,{className:"h-6 w-6 group-hover:text-purple-500 transition-all duration-300 ".concat("light"===s?"text-black":"")})}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-1",children:"Upload"}),(0,a.jsx)("p",{className:"text-sm text-center ".concat("light"===s?"text-neutral-600":"text-neutral-400"),children:"PDF, PPT, DOC, TXT, AUDIO"})]}),(0,a.jsxs)("button",{onClick:()=>O(!0),className:"border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group ".concat("light"===s?"border-black bg-white":"border-neutral-800"),children:[(0,a.jsx)("div",{className:"mb-4 p-3 rounded-full transition-all duration-300 ".concat("light"===s?"bg-white border border-black":"bg-neutral-900"," group-hover:bg-purple-500/20"),children:(0,a.jsx)(u.A,{className:"h-6 w-6 group-hover:text-purple-500 transition-all duration-300 ".concat("light"===s?"text-black":"")})}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-1",children:"Paste"}),(0,a.jsx)("p",{className:"text-sm text-center ".concat("light"===s?"text-neutral-600":"text-neutral-400"),children:"YouTube, Website, Text"})]}),(0,a.jsxs)("button",{onClick:()=>U(!0),className:"border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group ".concat("light"===s?"border-black bg-white":"border-neutral-800"),children:[(0,a.jsx)("div",{className:"mb-4 p-3 rounded-full transition-all duration-300 ".concat("light"===s?"bg-white border border-black":"bg-neutral-900"," group-hover:bg-purple-500/20"),children:(0,a.jsx)(m.A,{className:"h-6 w-6 group-hover:text-purple-500 transition-all duration-300 ".concat("light"===s?"text-black":"")})}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-1",children:"Record"}),(0,a.jsx)("p",{className:"text-sm text-center ".concat("light"===s?"text-neutral-600":"text-neutral-400"),children:"Record Your Lecture"})]})]}),(0,a.jsxs)("div",{className:"mb-12",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold",children:"My spaces"}),(0,a.jsxs)(j.$,{variant:"outline",className:"gap-2 border-dashed hover:border-purple-500 transition-all duration-300",onClick:er,children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),"Add space"]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:H.length>0?H.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4 flex items-center justify-between hover:border-purple-500 transition-all duration-300 cursor-pointer ".concat(K===e.id?"border-purple-500 bg-purple-50":"light"===s?"border-black bg-white":"border-neutral-800"),onClick:()=>ee(e.id),children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(p.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"font-medium",children:e.name})]}),(0,a.jsx)("span",{className:"text-xs text-gray-400",children:new Date(e.created_at).toLocaleDateString()})]},e.id)):(0,a.jsx)("div",{className:"border rounded-lg p-4 text-center ".concat("light"===s?"border-gray-200 bg-gray-50":"border-neutral-700 bg-neutral-800"),children:"No spaces yet. Create one to get started!"})})]}),T&&(0,a.jsx)("div",{className:"mb-12",children:(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)(j.$,{variant:null===K?"default":"outline",onClick:()=>ee(null),className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),"All Documents"]}),H.map(e=>(0,a.jsxs)(j.$,{variant:K===e.id?"default":"outline",onClick:()=>ee(e.id),className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),e.name]},e.id)),(0,a.jsxs)(j.$,{variant:"outline",className:"gap-2 border-dashed hover:border-purple-500 transition-all duration-300",onClick:er,children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),"Add space"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold",children:K?"Documents in ".concat(null===(e=H.find(e=>e.id===K))||void 0===e?void 0:e.name):"All Documents"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(j.$,{variant:"ghost",size:"sm",onClick:es,disabled:W,children:W?"Loading...":"Refresh"}),(0,a.jsxs)(j.$,{className:"bg-purple-600 hover:bg-purple-700",onClick:()=>_(!0),children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-1"})," Upload Document"]})]})]}),X&&(0,a.jsx)("div",{className:"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700",children:X}),W?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[1,2,3].map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4 animate-pulse ".concat("light"===s?"border-gray-200 bg-gray-50":"border-neutral-700 bg-neutral-800"),children:[(0,a.jsx)("div",{className:"h-4 bg-gray-300 rounded mb-2"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded mb-1"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-2/3"})]},e))}):B.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:B.map(e=>{var t;return(0,a.jsxs)("div",{onClick:()=>ea(e),className:"border rounded-lg p-4 transition-all duration-300 hover:shadow-lg ".concat("completed"===e.processing_status?"cursor-pointer hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)]":"cursor-not-allowed opacity-60"," ").concat("light"===s?"border-black bg-white":"border-neutral-800"),children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 text-purple-500"}),(0,a.jsx)("span",{className:"font-medium text-sm truncate",children:e.title})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.group&&!K&&(0,a.jsx)("span",{className:"text-xs px-2 py-0.5 rounded-full bg-purple-100 text-purple-800",children:(null===(t=H.find(t=>t.id===Number(e.group)))||void 0===t?void 0:t.name)||"Group"}),"completed"===e.processing_status&&(0,a.jsx)(g.A,{className:"h-4 w-4 text-green-500"}),"processing"===e.processing_status&&(0,a.jsx)(b.A,{className:"h-4 w-4 text-yellow-500 animate-spin"}),"failed"===e.processing_status&&(0,a.jsx)(f.A,{className:"h-4 w-4 text-red-500"})]})]}),(0,a.jsxs)("p",{className:"text-xs mb-2 ".concat("light"===s?"text-gray-600":"text-gray-400"),children:["Uploaded: ",new Date(e.uploaded_at).toLocaleDateString()]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat("completed"===e.processing_status?"bg-green-100 text-green-800":"processing"===e.processing_status?"bg-yellow-100 text-yellow-800":"failed"===e.processing_status?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"),children:e.processing_status.charAt(0).toUpperCase()+e.processing_status.slice(1)}),"completed"===e.processing_status&&(0,a.jsx)("span",{className:"text-xs ".concat("light"===s?"text-gray-500":"text-gray-400"),children:"Click to learn"})]})]},e.id)})}):(0,a.jsxs)("div",{className:"border rounded-lg p-8 text-center ".concat("light"===s?"border-gray-200 bg-gray-50":"border-neutral-700 bg-neutral-800"),children:[(0,a.jsx)(h.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-400"}),(0,a.jsxs)("h3",{className:"text-lg font-medium mb-2",children:["No documents ",K?"in this space":"yet"]}),(0,a.jsx)("p",{className:"text-sm mb-4 ".concat("light"===s?"text-gray-600":"text-gray-400"),children:"Upload your first document to get started with learning"}),(0,a.jsx)(j.$,{onClick:()=>_(!0),className:"bg-purple-600 hover:bg-purple-700",children:"Upload Document"})]})]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold",children:"Continue learning"}),(0,a.jsx)(j.$,{variant:"link",className:"text-neutral-400",children:"View all"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)("div",{className:"border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 ".concat("light"===s?"border-black bg-white":"border-neutral-800"),children:(0,a.jsx)("div",{className:"h-32 bg-purple-900 flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-4xl font-bold text-white",children:"MAP"})})}),(0,a.jsx)("div",{className:"border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 ".concat("light"===s?"border-black bg-white":"border-neutral-800"),children:(0,a.jsx)("div",{className:"h-32 ".concat("light"===s?"bg-gray-100":"bg-neutral-900")})}),(0,a.jsx)("div",{className:"border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 ".concat("light"===s?"border-black bg-white":"border-neutral-800"),children:(0,a.jsx)("div",{className:"h-32 ".concat("light"===s?"bg-gray-100":"bg-neutral-900")})})]})]})]})]}),(0,a.jsx)(J,{isOpen:R,setIsOpen:_,onUploadSuccess:es,groupId:K,groups:H}),(0,a.jsx)(M,{isOpen:D,setIsOpen:O}),(0,a.jsx)(Z,{isOpen:P,setIsOpen:U}),(0,a.jsx)(L,{open:A,setOpen:I,onCreated:()=>{(async()=>{try{let e=await z.documentApi.getGroups();Q(Array.isArray(e)?e:e.results||[])}catch(e){Q([])}})()}})]})}},89323:(e,t,s)=>{Promise.resolve().then(s.bind(s,88402))},89852:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(95155),r=s(12115),l=s(53999);let n=r.forwardRef((e,t)=>{let{className:s,type:r,...n}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...n})});n.displayName="Input"},99474:(e,t,s)=>{"use strict";s.d(t,{T:()=>n});var a=s(95155),r=s(12115),l=s(53999);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),ref:t,...r})});n.displayName="Textarea"}},e=>{var t=t=>e(e.s=t);e.O(0,[9749,3464,2707,4838,6671,5220,1455,7609,8441,1684,7358],()=>t(89323)),_N_E=e.O()}]);