(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1168],{16891:(e,s,t)=>{"use strict";t.d(s,{F:()=>i});var r=t(95155),l=t(12115),a=t(28577),n=t(53999);let i=l.forwardRef((e,s)=>{let{className:t,children:l,...i}=e;return(0,r.jsxs)(a.bL,{ref:s,className:(0,n.cn)("relative overflow-hidden",t),...i,children:[(0,r.jsx)(a.LM,{className:"h-full w-full rounded-[inherit]",children:l}),(0,r.jsx)(c,{}),(0,r.jsx)(a.OK,{})]})});i.displayName="ScrollArea";let c=l.forwardRef((e,s)=>{let{className:t,orientation:l="vertical",...i}=e;return(0,r.jsx)(a.VM,{ref:s,orientation:l,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===l&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===l&&"h-2.5 border-t border-t-transparent p-[1px]",t),...i,children:(0,r.jsx)(a.lr,{className:"relative flex-1 rounded-full bg-border"})})});c.displayName="ScrollBar"},37125:(e,s,t)=>{Promise.resolve().then(t.bind(t,83442))},79750:(e,s,t)=>{"use strict";t.d(s,{U:()=>n});var r=t(12115),l=t(35695),a=t(49509);function n(){let{documentId:e,enabled:s=!0,isProcessingComplete:t=!1,onQuizStart:n,onQuizEnd:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},c=(0,r.useRef)(null),o=(0,r.useRef)(!1),d=(0,r.useRef)(!1),u=(0,r.useRef)(e),m=(0,r.useRef)(null);(0,l.useRouter)();let x=a.env.NEXT_PUBLIC_API_URL||"http://localhost:8000/api",p=()=>{let e=localStorage.getItem("token");return{"Content-Type":"application/json",Authorization:"Token ".concat(e)}},h=(0,r.useCallback)(async()=>{if(s&&e&&t&&!o.current)try{if(!localStorage.getItem("token"))return;let s=await fetch("".concat(x,"/users/platform-time/start/"),{method:"POST",headers:p(),body:JSON.stringify({document_id:e})});if(s.ok){let t=await s.json();c.current=t.session_id,o.current=!0,d.current=!1,u.current=e,m.current=new Date,console.log("Document time session started:",t.session_id,t.message)}}catch(e){console.error("Error starting document time session:",e)}},[e,s,t]),f=(0,r.useCallback)(async function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"manual";if(e&&o.current&&!d.current)try{if(!localStorage.getItem("token"))return;(await fetch("".concat(x,"/users/platform-time/pause/"),{method:"POST",headers:p(),body:JSON.stringify({document_id:e,reason:s})})).ok&&(d.current=!0,console.log("Document time session paused: ".concat(s)))}catch(e){console.error("Error pausing document time session:",e)}},[e]),g=(0,r.useCallback)(async()=>{if(e&&o.current&&d.current)try{if(!localStorage.getItem("token"))return;(await fetch("".concat(x,"/users/platform-time/resume/"),{method:"POST",headers:p(),body:JSON.stringify({document_id:e})})).ok&&(d.current=!1,console.log("Document time session resumed"))}catch(e){console.error("Error resuming document time session:",e)}},[e]),j=(0,r.useCallback)(async()=>{if(e&&o.current)try{if(!localStorage.getItem("token"))return;await fetch("".concat(x,"/users/platform-time/end/"),{method:"POST",headers:p(),body:JSON.stringify({document_id:e})}),c.current=null,o.current=!1,d.current=!1,m.current=null,console.log("Document time session ended")}catch(e){console.error("Error ending document time session:",e)}},[e]),b=(0,r.useCallback)(()=>{f("quiz"),null==n||n()},[f,n]),N=(0,r.useCallback)(()=>{g(),null==i||i()},[g,i]);return(0,r.useEffect)(()=>{if(!s||!e||!t)return;u.current!==e&&(o.current&&j(),u.current=e),h();let r=()=>{j()},l=()=>{"/process"!==window.location.pathname&&j()};return window.addEventListener("beforeunload",r),window.addEventListener("popstate",l),()=>{window.removeEventListener("beforeunload",r),window.removeEventListener("popstate",l),j()}},[e,s,t,h,j]),{sessionId:c.current,isActive:o.current,isPaused:d.current,sessionStartTime:m.current,startQuiz:b,endQuiz:N,pauseSession:f,resumeSession:g,endSession:j}}},82714:(e,s,t)=>{"use strict";t.d(s,{J:()=>o});var r=t(95155),l=t(12115),a=t(40968),n=t(74466),i=t(53999);let c=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)(a.b,{ref:s,className:(0,i.cn)(c(),t),...l})});o.displayName=a.b.displayName},83442:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>se});var r=t(95155),l=t(12115),a=t(35695),n=t(34964),i=t(49730),c=t(48021),o=t(53999);let d=e=>{let{className:s,...t}=e;return(0,r.jsx)(i.YZ,{className:(0,o.cn)("flex h-full w-full data-[panel-group-direction=vertical]:flex-col",s),...t})},u=i.Zk,m=e=>{let{withHandle:s,className:t,...l}=e;return(0,r.jsx)(i.TW,{className:(0,o.cn)("relative flex w-px items-center justify-center bg-border after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-1 data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90",t),...l,children:s&&(0,r.jsx)("div",{className:"z-10 flex h-4 w-3 items-center justify-center rounded-sm border bg-border",children:(0,r.jsx)(c.A,{className:"h-2.5 w-2.5"})})})};var x=t(81497),p=t(74876),h=t(57434),f=t(1194),g=t(5040),j=t(88495),b=t(97168),N=t(51154),w=t(29869),v=t(99890),y=t(54416),k=t(1978),S=t(99304);function C(){let[e,s]=(0,l.useState)(!1),[t,a]=(0,l.useState)([]),n=(0,l.useRef)(null),{theme:i}=(0,S.D)(),[c,o]=(0,l.useState)(!1),[d,u]=(0,l.useState)(!1),m=e=>{a(s=>s.filter((s,t)=>t!==e))};return(0,r.jsxs)("div",{className:"h-full flex flex-col",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Upload Files"}),(0,r.jsxs)("div",{className:"border-2 border-dashed rounded-lg p-8 text-center transition-colors mb-4 ".concat(e?"border-purple-500 bg-purple-500/10":"light"===i?"border-black":"border-neutral-700"),onDragOver:e=>{e.preventDefault(),s(!0)},onDragLeave:()=>{s(!1)},onDrop:e=>{if(e.preventDefault(),s(!1),e.dataTransfer.files&&e.dataTransfer.files.length>0){let s=Array.from(e.dataTransfer.files);a(e=>[...e,...s]),u(!0),setTimeout(()=>{u(!1)},1500)}},children:[(0,r.jsx)("input",{type:"file",ref:n,onChange:e=>{if(e.target.files&&e.target.files.length>0){let s=Array.from(e.target.files);a(e=>[...e,...s]),u(!0),setTimeout(()=>{u(!1)},1500)}},className:"hidden",multiple:!0}),(0,r.jsxs)(k.P.div,{initial:{scale:1},animate:{scale:e?1.05:1},className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"p-3 rounded-full mb-3 ".concat("light"===i?"bg-white border border-black":"bg-neutral-800"),children:d?(0,r.jsx)(N.A,{className:"h-6 w-6 animate-spin ".concat("light"===i?"text-black":"text-purple-500")}):(0,r.jsx)(w.A,{className:"h-6 w-6 ".concat("light"===i?"text-black":"text-purple-500")})}),(0,r.jsx)("p",{className:"text-neutral-300 mb-2",children:"Drag and drop files here"}),(0,r.jsx)("p",{className:"text-neutral-500 text-sm mb-4",children:"or"}),(0,r.jsx)(b.$,{onClick:()=>{var e;null===(e=n.current)||void 0===e||e.click()},className:"bg-purple-600 hover:bg-purple-700",disabled:d,children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(N.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Uploading..."]}):"Browse Files"}),(0,r.jsx)("p",{className:"text-neutral-500 text-xs mt-4",children:"Supported formats: PDF, PPT, DOC, TXT, MP3, WAV"})]})]}),t.length>0&&(0,r.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,r.jsx)("h3",{className:"text-sm font-medium mb-2",children:"Selected Files"}),(0,r.jsx)("div",{className:"space-y-2",children:t.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 rounded ".concat("light"===i?"bg-white border border-black":"bg-neutral-800"),children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2 ".concat("light"===i?"text-black":"text-purple-500")}),(0,r.jsx)("span",{className:"text-sm truncate max-w-[200px]",children:e.name})]}),(0,r.jsx)(b.$,{variant:"ghost",size:"icon",className:"h-6 w-6",onClick:()=>m(s),children:(0,r.jsx)(y.A,{className:"h-4 w-4"})})]},s))}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(b.$,{className:"bg-purple-600 hover:bg-purple-700 w-full",disabled:0===t.length||c,onClick:()=>{if(t.length>0){o(!0);let e=t.map(e=>{var s;return{name:e.name,size:"".concat((e.size/1048576).toFixed(2)," MB"),type:e.type||(null===(s=e.name.split(".").pop())||void 0===s?void 0:s.toLowerCase()),lastModified:e.lastModified}});localStorage.setItem("uploadedFiles",JSON.stringify(e));let s=t.map(e=>URL.createObjectURL(e));localStorage.setItem("filePreviewUrls",JSON.stringify(s)),setTimeout(()=>{s.forEach(e=>URL.revokeObjectURL(e))},3e5),setTimeout(()=>{window.location.href="/process?type=upload"},2e3)}},children:c?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 mr-2 animate-spin"}),(0,r.jsx)("div",{children:"Processing..."})]}):(0,r.jsxs)(r.Fragment,{children:["Process ",t.length," ",1===t.length?"File":"Files"]})})})]})]})}var A=t(89852),P=t(99474),F=t(2925),T=t(34869);function I(){let[e,s]=(0,l.useState)(""),[t,a]=(0,l.useState)(""),[i,c]=(0,l.useState)(""),[o,d]=(0,l.useState)("youtube"),{theme:u}=(0,S.D)(),[m,x]=(0,l.useState)(!1),p=()=>{"youtube"===o&&e?(x(!0),localStorage.setItem("pastedContent",JSON.stringify({type:"youtube",url:e})),setTimeout(()=>{window.location.href="/process?type=paste"},2e3)):"website"===o&&t?(x(!0),localStorage.setItem("pastedContent",JSON.stringify({type:"website",url:t})),setTimeout(()=>{window.location.href="/process?type=paste"},2e3)):"text"===o&&i&&(x(!0),localStorage.setItem("pastedContent",JSON.stringify({type:"text",content:i})),setTimeout(()=>{window.location.href="/process?type=paste"},2e3))};return(0,r.jsxs)("div",{className:"h-full flex flex-col",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Paste Content"}),(0,r.jsxs)(n.tU,{defaultValue:"youtube",className:"flex-1",onValueChange:d,children:[(0,r.jsxs)(n.j7,{className:"grid grid-cols-3 mb-4",children:[(0,r.jsxs)(n.Xi,{value:"youtube",className:"data-[state=active]:bg-purple-600",children:[(0,r.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"YouTube"]}),(0,r.jsxs)(n.Xi,{value:"website",className:"data-[state=active]:bg-purple-600",children:[(0,r.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Website"]}),(0,r.jsxs)(n.Xi,{value:"text",className:"data-[state=active]:bg-purple-600",children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Text"]})]}),(0,r.jsxs)(n.av,{value:"youtube",className:"space-y-4 flex-1",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm text-neutral-400 mb-2 block",children:"Paste YouTube URL"}),(0,r.jsx)(A.p,{placeholder:"https://www.youtube.com/watch?v=...",value:e,onChange:e=>s(e.target.value),className:"focus-visible:ring-purple-500 ".concat("light"===u?"bg-white border-black":"bg-neutral-800 border-neutral-700")})]}),(0,r.jsx)("p",{className:"text-xs text-neutral-500",children:"Enter a YouTube video URL to extract and analyze its content"}),(0,r.jsx)("div",{className:"mt-auto pt-4",children:(0,r.jsx)(b.$,{className:"bg-purple-600 hover:bg-purple-700 w-full",disabled:!e||m,onClick:p,children:m?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 mr-2 animate-spin"}),(0,r.jsx)("div",{children:"Processing..."})]}):(0,r.jsx)(r.Fragment,{children:"Process YouTube Video"})})})]}),(0,r.jsxs)(n.av,{value:"website",className:"space-y-4 flex-1",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm text-neutral-400 mb-2 block",children:"Paste Website URL"}),(0,r.jsx)(A.p,{placeholder:"https://example.com",value:t,onChange:e=>a(e.target.value),className:"focus-visible:ring-purple-500 ".concat("light"===u?"bg-white border-black":"bg-neutral-800 border-neutral-700")})]}),(0,r.jsx)("p",{className:"text-xs text-neutral-500",children:"Enter a website URL to extract and analyze its content"}),(0,r.jsx)("div",{className:"mt-auto pt-4",children:(0,r.jsx)(b.$,{className:"bg-purple-600 hover:bg-purple-700 w-full",disabled:!t||m,onClick:p,children:m?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 mr-2 animate-spin"}),(0,r.jsx)("div",{children:"Processing..."})]}):(0,r.jsx)(r.Fragment,{children:"Process Website"})})})]}),(0,r.jsxs)(n.av,{value:"text",className:"space-y-4 flex-1 flex flex-col",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"text-sm text-neutral-400 mb-2 block",children:"Paste Text Content"}),(0,r.jsx)(P.T,{placeholder:"Paste or type your text here...",value:i,onChange:e=>c(e.target.value),className:"min-h-[200px] focus-visible:ring-purple-500 h-full ".concat("light"===u?"bg-white border-black":"bg-neutral-800 border-neutral-700")})]}),(0,r.jsx)("div",{className:"pt-4",children:(0,r.jsx)(b.$,{className:"bg-purple-600 hover:bg-purple-700 w-full",disabled:!i||m,onClick:p,children:m?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 mr-2 animate-spin"}),(0,r.jsx)("div",{children:"Processing..."})]}):(0,r.jsx)(r.Fragment,{children:"Process Text"})})})]})]})]})}var R=t(97207),D=t(18979),E=t(85690),L=t(82178),B=t(4229);function U(){let[e,s]=(0,l.useState)(!1),[t,a]=(0,l.useState)(!1),[n,i]=(0,l.useState)(0),[c,o]=(0,l.useState)(null),d=(0,l.useRef)(null),u=(0,l.useRef)([]),m=(0,l.useRef)(null),x=(0,l.useRef)(null),{theme:p}=(0,S.D)(),[h,f]=(0,l.useState)(!1);(0,l.useEffect)(()=>()=>{m.current&&clearInterval(m.current),c&&URL.revokeObjectURL(c)},[c]);let g=async()=>{try{let e=await navigator.mediaDevices.getUserMedia({audio:!0}),t=new MediaRecorder(e);d.current=t,u.current=[],t.ondataavailable=e=>{e.data.size>0&&u.current.push(e.data)},t.onstop=()=>{let e=new Blob(u.current,{type:"audio/wav"}),s=URL.createObjectURL(e);o(s)},t.start(),s(!0),a(!1),m.current=setInterval(()=>{i(e=>e+1)},1e3)}catch(e){console.error("Error accessing microphone:",e)}},j=e=>{let s=Math.floor(e/60);return"".concat(s.toString().padStart(2,"0"),":").concat((e%60).toString().padStart(2,"0"))};return(0,r.jsxs)("div",{className:"h-full flex flex-col items-center justify-center",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-8",children:"Record Audio"}),(0,r.jsxs)(k.P.div,{initial:{scale:1},animate:{scale:e&&!t?[1,1.1,1]:1,transition:{repeat:e&&!t?Number.POSITIVE_INFINITY:0,duration:1.5}},className:"relative mb-6",children:[(0,r.jsx)("div",{className:"p-12 rounded-full ".concat(e?"bg-red-500/20":"light"===p?"bg-white border border-black":"bg-neutral-800"),children:(0,r.jsx)(R.A,{className:"h-12 w-12 ".concat(e?"text-red-500":"light"===p?"text-black":"text-purple-500")})}),e&&!t&&(0,r.jsx)(k.P.div,{className:"absolute inset-0 rounded-full border-2 border-red-500",initial:{scale:1,opacity:1},animate:{scale:[1,1.2,1.2,1],opacity:[1,.8,.2,0]},transition:{duration:2,repeat:Number.POSITIVE_INFINITY,ease:"easeOut"}})]}),(0,r.jsx)("div",{className:"text-3xl font-mono mb-8",children:j(n)}),(0,r.jsx)("div",{className:"flex gap-6 mb-8",children:e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(b.$,{onClick:()=>{d.current&&e&&(d.current.stop(),s(!1),m.current&&(clearInterval(m.current),m.current=null),d.current.stream.getTracks().forEach(e=>e.stop()))},className:"bg-red-600 hover:bg-red-700 rounded-full h-14 w-14 p-0",children:(0,r.jsx)(D.A,{className:"h-6 w-6"})}),t?(0,r.jsx)(b.$,{onClick:()=>{d.current&&e&&t&&(d.current.resume(),a(!1),m.current=setInterval(()=>{i(e=>e+1)},1e3))},className:"bg-purple-600 hover:bg-purple-700 rounded-full h-14 w-14 p-0",children:(0,r.jsx)(E.A,{className:"h-6 w-6"})}):(0,r.jsx)(b.$,{onClick:()=>{d.current&&e&&!t&&(d.current.pause(),a(!0),m.current&&(clearInterval(m.current),m.current=null))},className:"bg-purple-600 hover:bg-purple-700 rounded-full h-14 w-14 p-0",children:(0,r.jsx)(L.A,{className:"h-6 w-6"})})]}):(0,r.jsx)(b.$,{onClick:g,className:"bg-purple-600 hover:bg-purple-700 rounded-full h-14 w-14 p-0",children:(0,r.jsx)(R.A,{className:"h-6 w-6"})})}),c&&(0,r.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,r.jsx)("audio",{ref:x,src:c,controls:!0,className:"w-full max-w-md"}),(0,r.jsx)(b.$,{className:"bg-purple-600 hover:bg-purple-700",onClick:()=>{c&&(f(!0),localStorage.setItem("recordedAudio",JSON.stringify({duration:j(n),timestamp:new Date().toISOString(),audioURL:c})),setTimeout(()=>{window.location.href="/process?type=record"},2e3))},disabled:h,children:h?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 mr-2 animate-spin"}),(0,r.jsx)("div",{children:"Processing..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"Process Recording"]})})]})]})}var z=t(87449),O=t(66474),$=t(47863),_=t(5196);let Z=z.bL;z.YJ;let M=z.WT,W=l.forwardRef((e,s)=>{let{className:t,children:l,...a}=e;return(0,r.jsxs)(z.l9,{ref:s,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...a,children:[l,(0,r.jsx)(z.In,{asChild:!0,children:(0,r.jsx)(O.A,{className:"h-4 w-4 opacity-50"})})]})});W.displayName=z.l9.displayName;let q=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)(z.PP,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,r.jsx)($.A,{className:"h-4 w-4"})})});q.displayName=z.PP.displayName;let G=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)(z.wn,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...l,children:(0,r.jsx)(O.A,{className:"h-4 w-4"})})});G.displayName=z.wn.displayName;let J=l.forwardRef((e,s)=>{let{className:t,children:l,position:a="popper",...n}=e;return(0,r.jsx)(z.ZL,{children:(0,r.jsxs)(z.UC,{ref:s,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:a,...n,children:[(0,r.jsx)(q,{}),(0,r.jsx)(z.LM,{className:(0,o.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,r.jsx)(G,{})]})})});J.displayName=z.UC.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)(z.JU,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...l})}).displayName=z.JU.displayName;let V=l.forwardRef((e,s)=>{let{className:t,children:l,...a}=e;return(0,r.jsxs)(z.q7,{ref:s,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...a,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(z.VF,{children:(0,r.jsx)(_.A,{className:"h-4 w-4"})})}),(0,r.jsx)(z.p4,{children:l})]})});V.displayName=z.q7.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)(z.wv,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),...l})}).displayName=z.wv.displayName;var Y=t(71007),X=t(50492),H=t(27213),K=t(90105),Q=t(85977);let ee=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)(Q.bL,{ref:s,className:(0,o.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),...l})});ee.displayName=Q.bL.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)(Q._V,{ref:s,className:(0,o.cn)("aspect-square h-full w-full",t),...l})}).displayName=Q._V.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)(Q.H4,{ref:s,className:(0,o.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),...l})}).displayName=Q.H4.displayName;var es=t(66766),et=t(88106);let er=et.bL,el=et.R6,ea=et.Ke;var en=t(31886),ei=t(88240),ec=t(15405);function eo(e){let{content:s,className:t}=e;return(0,r.jsx)(ei.oz,{className:(0,o.cn)("prose prose-invert prose-sm max-w-none","prose-headings:text-neutral-100 prose-headings:font-semibold","prose-h1:text-xl prose-h1:mb-4 prose-h1:mt-6","prose-h2:text-lg prose-h2:mb-3 prose-h2:mt-5","prose-h3:text-base prose-h3:mb-2 prose-h3:mt-4","prose-p:text-neutral-200 prose-p:leading-relaxed prose-p:mb-3","prose-ul:text-neutral-200 prose-ul:mb-3","prose-ol:text-neutral-200 prose-ol:mb-3","prose-li:text-neutral-200 prose-li:mb-1","prose-strong:text-neutral-100 prose-strong:font-semibold","prose-em:text-neutral-300 prose-em:italic","prose-code:text-purple-400 prose-code:bg-neutral-800 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-sm","prose-pre:bg-neutral-900 prose-pre:border prose-pre:border-neutral-700 prose-pre:rounded-lg prose-pre:p-4","prose-blockquote:border-l-4 prose-blockquote:border-purple-500 prose-blockquote:pl-4 prose-blockquote:italic prose-blockquote:text-neutral-300","prose-a:text-purple-400 prose-a:underline prose-a:decoration-purple-400/50 hover:prose-a:decoration-purple-400","prose-table:border-collapse prose-table:border prose-table:border-neutral-700","prose-th:border prose-th:border-neutral-700 prose-th:bg-neutral-800 prose-th:p-2 prose-th:text-left prose-th:font-semibold","prose-td:border prose-td:border-neutral-700 prose-td:p-2",t),remarkPlugins:[ec.A],components:{h1:e=>{let{children:s}=e;return(0,r.jsx)("h1",{className:"text-xl font-semibold text-neutral-100 mb-4 mt-6 first:mt-0",children:s})},h2:e=>{let{children:s}=e;return(0,r.jsx)("h2",{className:"text-lg font-semibold text-neutral-100 mb-3 mt-5 first:mt-0",children:s})},h3:e=>{let{children:s}=e;return(0,r.jsx)("h3",{className:"text-base font-semibold text-neutral-100 mb-2 mt-4 first:mt-0",children:s})},p:e=>{let{children:s}=e;return(0,r.jsx)("p",{className:"text-neutral-200 leading-relaxed mb-3 last:mb-0",children:s})},ul:e=>{let{children:s}=e;return(0,r.jsx)("ul",{className:"text-neutral-200 mb-3 pl-6 space-y-1",children:s})},ol:e=>{let{children:s}=e;return(0,r.jsx)("ol",{className:"text-neutral-200 mb-3 pl-6 space-y-1",children:s})},li:e=>{let{children:s}=e;return(0,r.jsx)("li",{className:"text-neutral-200",children:s})},strong:e=>{let{children:s}=e;return(0,r.jsx)("strong",{className:"text-neutral-100 font-semibold",children:s})},em:e=>{let{children:s}=e;return(0,r.jsx)("em",{className:"text-neutral-300 italic",children:s})},code:e=>{let{children:s,className:t}=e;return(null==t?void 0:t.includes("language-"))?(0,r.jsx)("code",{className:t,children:s}):(0,r.jsx)("code",{className:"text-purple-400 bg-neutral-800 px-1 py-0.5 rounded text-sm",children:s})},pre:e=>{let{children:s}=e;return(0,r.jsx)("pre",{className:"bg-neutral-900 border border-neutral-700 rounded-lg p-4 overflow-x-auto mb-3",children:s})},blockquote:e=>{let{children:s}=e;return(0,r.jsx)("blockquote",{className:"border-l-4 border-purple-500 pl-4 italic text-neutral-300 mb-3",children:s})},a:e=>{let{children:s,href:t}=e;return(0,r.jsx)("a",{href:t,className:"text-purple-400 underline decoration-purple-400/50 hover:decoration-purple-400 transition-colors",target:"_blank",rel:"noopener noreferrer",children:s})},table:e=>{let{children:s}=e;return(0,r.jsx)("div",{className:"overflow-x-auto mb-3",children:(0,r.jsx)("table",{className:"border-collapse border border-neutral-700 w-full",children:s})})},th:e=>{let{children:s}=e;return(0,r.jsx)("th",{className:"border border-neutral-700 bg-neutral-800 p-2 text-left font-semibold text-neutral-100",children:s})},td:e=>{let{children:s}=e;return(0,r.jsx)("td",{className:"border border-neutral-700 p-2 text-neutral-200",children:s})}},children:s})}let ed=["Explain the main concepts in this document","Create a summary of the key points","Generate practice questions about this content","How does this relate to [topic]?","What are the practical applications of this?"];function eu(e){let{state:s,setState:t,documentId:a,selectedModel:n="openai",onModelChange:i}=e,[c,o]=(0,l.useState)(s||[]),[d,u]=(0,l.useState)(""),[m,p]=(0,l.useState)(!1),[h,f]=(0,l.useState)(n),g=(0,l.useRef)(null),j=(0,l.useRef)(null),N=(0,l.useRef)(null),[w,v]=(0,l.useState)(!1),y=(0,l.useRef)(null);(0,l.useEffect)(()=>{f(n)},[n]),(0,l.useEffect)(()=>{t&&t(c)},[c,t]),(0,l.useEffect)(()=>{k()},[c]);let k=()=>{var e;null===(e=g.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})},S=async()=>{if(!d.trim())return;let e={id:Date.now().toString(),content:d,role:"user",timestamp:new Date};o(s=>[...s,e]),u(""),p(!0);try{let e=await en.chatApi.sendMessage(d,null==a?void 0:a.toString(),h),s={id:(Date.now()+1).toString(),content:e.message||e.response||"I received your message but couldn't generate a response.",role:"assistant",timestamp:new Date};o(e=>[...e,s])}catch(s){console.error("Error sending message:",s);let e={id:(Date.now()+1).toString(),content:"I'm sorry, I'm having trouble responding right now. Please try again.",role:"assistant",timestamp:new Date};o(s=>[...s,e])}finally{p(!1)}},C=e=>{if(e.target.files&&e.target.files.length>0){let s=e.target.files[0],t={id:Date.now().toString(),content:"I'd like to upload and discuss this file: ".concat(s.name),role:"user",timestamp:new Date};o(e=>[...e,t]),j.current&&(j.current.value=""),p(!0),en.chatApi.sendMessage("I'd like to upload and discuss this file: ".concat(s.name),null==a?void 0:a.toString(),h).then(e=>{let s={id:(Date.now()+1).toString(),content:e.message||e.response||"I understand you'd like to discuss a file. Please describe what you'd like to know about it.",role:"assistant",timestamp:new Date};o(e=>[...e,s])}).catch(e=>{console.error("Error sending file message:",e);let s={id:(Date.now()+1).toString(),content:"I received your file reference, but I'm having trouble responding right now. Please try asking a question about it.",role:"assistant",timestamp:new Date};o(e=>[...e,s])}).finally(()=>{p(!1)})}},A=e=>{u(e),v(!1)},F=0===c.length&&!m;return(0,r.jsxs)("div",{className:"flex flex-col h-full bg-black",ref:y,children:[(0,r.jsxs)("div",{className:"p-4 border-b border-neutral-800",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("h2",{className:"text-xl font-medium",children:"Chat with the AI Tutor"}),(0,r.jsxs)(Z,{value:h,onValueChange:e=>{f(e),i&&i(e)},children:[(0,r.jsx)(W,{className:"w-32 bg-neutral-800 border-neutral-700",children:(0,r.jsx)(M,{})}),(0,r.jsxs)(J,{children:[(0,r.jsx)(V,{value:"openai",children:"OpenAI"}),(0,r.jsx)(V,{value:"gemini",children:"Gemini"})]})]})]}),F&&(0,r.jsx)("p",{className:"text-center text-sm text-muted-foreground",children:"Ask anything or use the suggestions below"}),a&&(0,r.jsxs)("p",{className:"text-center text-xs text-purple-400",children:["Connected to document #",a]})]}),(0,r.jsx)("div",{className:"flex-1 overflow-auto",children:F?(0,r.jsx)("div",{className:"h-full flex flex-col items-center justify-center",children:(0,r.jsx)("div",{className:"p-6 rounded-full bg-neutral-800 mb-4",children:(0,r.jsx)(x.A,{className:"h-12 w-12 text-muted-foreground"})})}):(0,r.jsxs)("div",{className:"space-y-4 p-4",children:[c.map(e=>(0,r.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,r.jsxs)("div",{className:"flex gap-3 max-w-[80%] ".concat("user"===e.role?"flex-row-reverse":"flex-row"),children:["assistant"===e.role?(0,r.jsx)(ee,{className:"bg-purple-600",children:(0,r.jsx)("div",{className:"relative h-5 w-5",children:(0,r.jsx)(es.default,{src:"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png",alt:"Cognimosity Logo",fill:!0,className:"object-contain"})})}):(0,r.jsx)(ee,{className:"bg-neutral-700",children:(0,r.jsx)(Y.A,{className:"h-5 w-5"})}),(0,r.jsx)("div",{className:"rounded-lg p-3 ".concat("user"===e.role?"bg-purple-600 text-white":"bg-neutral-800 text-neutral-100"),children:"assistant"===e.role?(0,r.jsx)(eo,{content:e.content,className:"text-sm"}):(0,r.jsx)("p",{className:"text-sm",children:e.content})})]})},e.id)),m&&(0,r.jsx)("div",{className:"flex justify-start",children:(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)(ee,{className:"bg-purple-600",children:(0,r.jsx)("div",{className:"relative h-5 w-5",children:(0,r.jsx)(es.default,{src:"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png",alt:"Cognimosity Logo",fill:!0,className:"object-contain"})})}),(0,r.jsx)("div",{className:"rounded-lg p-3 bg-neutral-800 text-neutral-100",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("div",{className:"h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.3s]"}),(0,r.jsx)("div",{className:"h-2 w-2 rounded-full bg-neutral-400 animate-bounce [animation-delay:-0.15s]"}),(0,r.jsx)("div",{className:"h-2 w-2 rounded-full bg-neutral-400 animate-bounce"})]})})]})}),(0,r.jsx)("div",{ref:g})]})}),(0,r.jsx)("div",{className:"p-4 border-t border-neutral-800",children:(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,r.jsxs)(er,{open:w,onOpenChange:v,children:[(0,r.jsx)(el,{asChild:!0,children:(0,r.jsxs)(b.$,{variant:"outline",className:"w-full justify-between",children:[(0,r.jsx)("span",{children:"Learning Suggestions"}),(0,r.jsx)(O.A,{className:"h-4 w-4 transition-transform ".concat(w?"rotate-180":"")})]})}),(0,r.jsx)(ea,{className:"space-y-2 pt-2",children:ed.map((e,s)=>(0,r.jsx)(b.$,{variant:"ghost",className:"w-full justify-start text-sm",onClick:()=>A(e),children:e},s))})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("input",{type:"file",ref:j,className:"hidden",onChange:C,accept:".pdf,.doc,.docx,.txt"}),(0,r.jsx)("input",{type:"file",ref:N,className:"hidden",onChange:C,accept:"image/*"}),(0,r.jsx)(b.$,{variant:"outline",size:"icon",onClick:()=>{var e;null===(e=j.current)||void 0===e||e.click()},children:(0,r.jsx)(X.A,{className:"h-4 w-4"})}),(0,r.jsx)(b.$,{variant:"outline",size:"icon",onClick:()=>{var e;null===(e=N.current)||void 0===e||e.click()},children:(0,r.jsx)(H.A,{className:"h-4 w-4"})}),(0,r.jsx)(P.T,{value:d,onChange:e=>u(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),S())},placeholder:"Type your message...",className:"min-h-[60px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500"}),(0,r.jsx)(b.$,{onClick:S,disabled:!d.trim()||m,children:(0,r.jsx)(K.A,{className:"h-4 w-4"})})]})]})})]})}var em=t(65749),ex=t(35789),ep=t(88482);function eh(e){let{className:s,...t}=e;return(0,r.jsx)("div",{className:(0,o.cn)("animate-pulse rounded-md bg-muted",s),...t})}var ef=t(16785),eg=t(40646),ej=t(53904),eb=t(53311),eN=t(56671);function ew(e){let{documentId:s}=e,[t,a]=(0,l.useState)(""),[n,i]=(0,l.useState)([]),[c,o]=(0,l.useState)(!1),[d,u]=(0,l.useState)(!1),[m,x]=(0,l.useState)(null),[p,h]=(0,l.useState)(!1),[f,g]=(0,l.useState)(""),[j,N]=(0,l.useState)(!1),[w,v]=(0,l.useState)(!1),[y,k]=(0,l.useState)(""),[S,C]=(0,l.useState)(null),[A,F]=(0,l.useState)(""),[T,I]=(0,l.useState)(!1),[R,D]=(0,l.useState)(null),[E,L]=(0,l.useState)(""),[B,U]=(0,l.useState)(!1),[z,O]=(0,l.useState)(null);(0,l.useEffect)(()=>{s&&$()},[s]),(0,l.useEffect)(()=>{F(t)},[t]);let $=async()=>{if(!s){x("No document selected");return}o(!0),x(null),N(!1);try{let e=await en.blueprintApi.getBlueprint(s);if(e&&e.blueprint){a(e.blueprint);try{let e=await en.blueprintApi.getBlueprintTopics(s);i(e.map(e=>({...e,id:e.id,weightage:"string"==typeof e.weightage?parseFloat(e.weightage):e.weightage})))}catch(e){i([])}N(!1);return}N(!0),a(""),i([]);return}catch(e){N(!0),a(""),i([]);return}finally{o(!1)}},_=async()=>{if(!s||!S&&!y.trim()){eN.o.error("Please select a file or enter blueprint text.");return}v(!0),x(null);try{S?await en.blueprintApi.uploadBlueprint(s,S):await en.blueprintApi.uploadBlueprint(s,y),eN.o.success("Blueprint uploaded!"),N(!1),k(""),C(null),$()}catch(e){x(e.message||"Failed to upload blueprint"),eN.o.error(e.message||"Failed to upload blueprint")}finally{v(!1)}},Z=async()=>{if(!s){eN.o.error("No document selected for blueprint generation");return}u(!0),x(null);try{console.log("Generating new blueprint for document:",s,"focus areas:",f);let e=await en.blueprintApi.generateBlueprint(s,f);a(e.blueprint),i(e.topics||[]),eN.o.success("New blueprint generated successfully!")}catch(e){console.error("Error generating blueprint:",e),x("Failed to generate new blueprint. Please try again."),eN.o.error("Failed to generate new blueprint")}finally{u(!1)}},M=async()=>{if(!s||!A.trim()){eN.o.error("Blueprint cannot be empty.");return}I(!0),x(null);try{await en.blueprintApi.uploadBlueprint(s,A),a(A),eN.o.success("Blueprint saved!")}catch(e){x(e.message||"Failed to save blueprint"),eN.o.error(e.message||"Failed to save blueprint")}finally{I(!1)}},W=async e=>{if(e.id){U(!0),O(null),L(""),D(e);try{let s=await en.blueprintApi.getBlueprintTopicContent(e.id);s.content_details&&s.content_details.length>0?L(s.content_details.map(e=>e.text).join("\n\n")):L("No content found for this topic.")}catch(e){O(e.message||"Failed to load topic content."),L("")}finally{U(!1)}}};return s?c?(0,r.jsx)("div",{className:"p-6 space-y-4",children:(0,r.jsxs)(ep.Zp,{children:[(0,r.jsxs)(ep.aR,{children:[(0,r.jsxs)(ep.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(ef.A,{className:"h-5 w-5"}),"Learning Blueprint"]}),(0,r.jsx)(ep.BT,{children:t?"Generating new blueprint...":"Loading blueprint..."})]}),(0,r.jsx)(ep.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(eh,{className:"h-4 w-full"}),(0,r.jsx)(eh,{className:"h-4 w-full"}),(0,r.jsx)(eh,{className:"h-4 w-3/4"}),(0,r.jsx)(eh,{className:"h-4 w-full"}),(0,r.jsx)(eh,{className:"h-4 w-2/3"})]})})]})}):m?(0,r.jsx)("div",{className:"p-6 space-y-4",children:(0,r.jsxs)(ep.Zp,{children:[(0,r.jsxs)(ep.aR,{children:[(0,r.jsxs)(ep.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(ef.A,{className:"h-5 w-5"}),"Learning Blueprint"]}),(0,r.jsx)(ep.BT,{className:"text-red-500",children:m})]}),(0,r.jsx)(ep.Wu,{children:(0,r.jsx)(b.$,{onClick:$,className:"w-full",children:"Try Again"})})]})}):j?(0,r.jsx)("div",{className:"p-6 space-y-4",children:(0,r.jsxs)(ep.Zp,{children:[(0,r.jsxs)(ep.aR,{children:[(0,r.jsxs)(ep.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(ef.A,{className:"h-5 w-5"}),"Upload or Enter Blueprint"]}),(0,r.jsx)(ep.BT,{children:"No blueprint found for this document. Upload a file or enter blueprint text manually."})]}),(0,r.jsxs)(ep.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Upload Blueprint File:"}),(0,r.jsx)("input",{type:"file",accept:".txt,.pdf,.docx",onChange:e=>{e.target.files&&e.target.files.length>0&&C(e.target.files[0])},className:"block w-full text-sm"}),S&&(0,r.jsxs)("div",{className:"text-xs text-neutral-400",children:["Selected: ",S.name]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Or Enter Blueprint Text:"}),(0,r.jsx)(P.T,{value:y,onChange:e=>k(e.target.value),placeholder:"Paste or type your blueprint here...",className:"min-h-[80px] bg-neutral-900 border-neutral-700"})]}),(0,r.jsx)(b.$,{onClick:_,className:"w-full",disabled:w||!S&&!y.trim(),children:w?"Uploading...":"Submit Blueprint"})]})]})}):(0,r.jsx)("div",{className:"p-6 space-y-4",children:(0,r.jsxs)(ep.Zp,{children:[(0,r.jsxs)(ep.aR,{children:[(0,r.jsxs)(ep.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(ef.A,{className:"h-5 w-5"}),"Learning Blueprint"]}),(0,r.jsx)(ep.BT,{children:"AI-generated learning blueprint for your document content"})]}),(0,r.jsxs)(ep.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Focus Areas (Optional):"}),(0,r.jsx)(P.T,{value:f,onChange:e=>g(e.target.value),placeholder:"Specify topics or areas you want to emphasize (e.g., machine learning, data science, algorithms)...",className:"min-h-[60px] bg-neutral-900 border-neutral-700"})]}),n.length>0&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h3",{className:"text-sm font-medium",children:"Key Topics Identified:"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:n.map((e,s)=>(0,r.jsxs)("button",{type:"button",className:"flex items-center justify-between p-2 rounded transition-colors w-full text-left ".concat((null==R?void 0:R.title)===e.title?"bg-purple-800/60 border border-purple-500":"bg-neutral-800 hover:bg-neutral-700"),onClick:()=>W(e),children:[(0,r.jsx)("span",{className:"text-sm",children:e.title}),(0,r.jsxs)("span",{className:"text-xs text-purple-400 font-medium",children:[e.weightage.toFixed(0),"%"]})]},s))}),R&&(0,r.jsxs)("div",{className:"mt-4 p-3 rounded bg-neutral-900 border border-purple-700",children:[(0,r.jsx)("h4",{className:"font-semibold text-purple-400 mb-1",children:R.title}),(0,r.jsx)("div",{className:"text-sm text-neutral-200 whitespace-pre-line",children:B?(0,r.jsx)("span",{children:"Loading..."}):z?(0,r.jsx)("span",{className:"text-red-400",children:z}):E||"No additional content for this topic."})]})]}),t&&(0,r.jsxs)("div",{className:"border rounded-lg p-4 bg-neutral-900/50 space-y-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Edit Blueprint:"}),(0,r.jsx)(P.T,{value:A,onChange:e=>F(e.target.value),className:"min-h-[120px] bg-neutral-900 border-neutral-700"})]}),(0,r.jsxs)("div",{className:"flex gap-2 pt-4 border-t",children:[(0,r.jsx)(b.$,{onClick:M,variant:"outline",className:"flex-1",disabled:!A||T||c||d,children:T?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(eg.A,{className:"h-4 w-4 mr-2 text-green-500"}),"Saving..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(eg.A,{className:"h-4 w-4 mr-2"}),"Save"]})}),(0,r.jsxs)(b.$,{onClick:$,variant:"outline",className:"flex-1",disabled:c||d,children:[(0,r.jsx)(ej.A,{className:"h-4 w-4 mr-2"}),"Reload"]}),(0,r.jsxs)(b.$,{onClick:Z,className:"flex-1",disabled:c||d,children:[(0,r.jsx)(eb.A,{className:"h-4 w-4 mr-2"}),d?"Generating...":"Generate New"]})]})]})]})}):(0,r.jsx)("div",{className:"p-6 space-y-4",children:(0,r.jsxs)(ep.Zp,{children:[(0,r.jsxs)(ep.aR,{children:[(0,r.jsxs)(ep.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(ef.A,{className:"h-5 w-5"}),"Learning Blueprint"]}),(0,r.jsx)(ep.BT,{children:"Select a document to view or generate its learning blueprint"})]}),(0,r.jsx)(ep.Wu,{children:(0,r.jsxs)("div",{className:"rounded-lg p-4 bg-purple-600/10 border border-purple-600/30",children:[(0,r.jsx)("h3",{className:"font-medium text-purple-500 mb-2",children:"What is a Blueprint?"}),(0,r.jsx)("p",{className:"text-sm text-neutral-300",children:"A Blueprint helps Cognimosity understand which topics or areas you want to prioritize. This will influence how content is analyzed, summarized, and presented across all features."})]})})]})})}var ev=t(6262),ey=t(40133),ek=t(54481),eS=t(57918),eC=t(91788),eA=t(16891),eP=t(85280);function eF(e){let{documentId:s}=e,[t,a]=(0,l.useState)(1),[n,i]=(0,l.useState)(!1),[c,o]=(0,l.useState)(!1),[d,u]=(0,l.useState)(null),[m,p]=(0,l.useState)(""),h=(0,l.useRef)(null),f=(0,l.useRef)(null),{theme:g}=(0,S.D)(),j=(0,l.useRef)(null);(0,l.useEffect)(()=>{eP.A.initialize({startOnLoad:!1,theme:"dark"===g?"dark":"default",themeVariables:"dark"===g?{primaryColor:"#6366f1",primaryTextColor:"#ffffff",primaryBorderColor:"#4f46e5",lineColor:"#8b5cf6",secondaryColor:"#7c3aed",tertiaryColor:"#a855f7",background:"#1e1b4b",mainBkg:"#312e81",secondBkg:"#3730a3",tertiaryBkg:"#4338ca"}:{primaryColor:"#818cf8",primaryTextColor:"#ffffff",primaryBorderColor:"#6366f1",lineColor:"#6366f1",secondaryColor:"#c7d2fe",tertiaryColor:"#e0e7ff",background:"#ffffff",mainBkg:"#f3f4f6",secondBkg:"#e5e7eb",tertiaryBkg:"#f9fafb"},securityLevel:"loose",fontFamily:"inherit"}),N()},[g,s]);let N=async()=>{if(!s){console.warn("No document ID provided for flowchart");return}try{o(!0),u(null),console.log("Generating new RAG-based flowchart for document:",s);let e=(await en.flowchartApi.generateFlowchartEnhanced(s)).flowchart;if(e)p(e),await w(e);else throw Error("No flowchart data received")}catch(e){console.error("Failed to load flowchart:",e),u("Failed to load flowchart visualization. Please try again later."),p("")}finally{o(!1)}},w=async e=>{if(h.current&&e)try{if(h.current.innerHTML="",!e.trim())throw Error("Empty mermaid code");let s="mermaid-".concat(Date.now()),{svg:t}=await eP.A.render(s,e);h.current.innerHTML=t,u(null)}catch(t){console.error("Error rendering flowchart:",t);let s="Error rendering flowchart.";t instanceof Error&&(s=t.message.includes("Parse error")||t.message.includes("syntax")?"Invalid flowchart syntax. The generated code may contain errors.":t.message.includes("Empty")?"No flowchart content was generated.":"Rendering error: ".concat(t.message)),u(s),h.current&&(h.current.innerHTML='\n            <div class="p-4 bg-red-900/20 border border-red-500/30 rounded-lg">\n              <h4 class="text-red-400 font-medium mb-2">Flowchart Rendering Error</h4>\n              <p class="text-red-300 text-sm mb-3">'.concat(s,'</p>\n              <details class="text-xs">\n                <summary class="text-red-400 cursor-pointer">Show raw code</summary>\n                <pre class="mt-2 p-2 bg-black/30 rounded text-gray-300 overflow-auto">').concat(e,"</pre>\n              </details>\n            </div>\n          "))}},v=async()=>{let e=!n;i(e),e&&m&&f.current&&setTimeout(async()=>{try{let e="mermaid-fullscreen-".concat(Date.now()),{svg:s}=await eP.A.render(e,m);f.current&&(f.current.innerHTML=s)}catch(e){console.error("Error rendering fullscreen flowchart:",e)}},100)};return(0,r.jsxs)("div",{className:"flex flex-col h-full bg-black",ref:j,children:[(0,r.jsxs)("div",{className:"p-4 border-b border-neutral-800",children:[(0,r.jsx)("h2",{className:"text-xl font-medium text-center",children:"Flowchart"}),(0,r.jsx)("p",{className:"text-center text-sm text-muted-foreground mt-1",children:"Machine Learning Process Visualization"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between px-4 py-2 border-b border-neutral-800",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Zoom: ",Math.round(100*t),"%"]})}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(b.$,{onClick:()=>{a(e=>Math.max(e-.2,.5))},size:"sm",variant:"outline",className:"h-8 w-8 p-0",disabled:t<=.5,children:(0,r.jsx)(ev.A,{className:"h-4 w-4"})}),(0,r.jsx)(b.$,{onClick:()=>{a(1)},size:"sm",variant:"outline",className:"h-8 w-8 p-0",children:(0,r.jsx)(ey.A,{className:"h-4 w-4"})}),(0,r.jsx)(b.$,{onClick:()=>{a(e=>Math.min(e+.2,3))},size:"sm",variant:"outline",className:"h-8 w-8 p-0",disabled:t>=3,children:(0,r.jsx)(ek.A,{className:"h-4 w-4"})}),(0,r.jsx)(b.$,{onClick:v,size:"sm",variant:"outline",className:"h-8 w-8 p-0",children:(0,r.jsx)(eS.A,{className:"h-4 w-4"})}),(0,r.jsxs)(b.$,{onClick:()=>{console.log("Download flowchart")},size:"sm",className:"bg-purple-600 hover:bg-purple-700",children:[(0,r.jsx)(eC.A,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,r.jsx)(eA.F,{className:"flex-1",children:c?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:s?"Checking for existing flowchart or generating new one...":"No document selected"})]})}):d||!s?(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md p-6",children:[(0,r.jsx)(x.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-red-500 mb-2",children:s?"Error Loading Flowchart":"No Document Selected"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:s?d:"Please select a document to view its flowchart."}),s&&d&&(0,r.jsx)("button",{onClick:N,className:"mt-3 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-md transition-colors",children:"Try Again"})]})}):m?(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)(ep.Zp,{className:"bg-neutral-800 border-neutral-700 p-6 overflow-hidden",children:(0,r.jsx)("div",{className:"overflow-auto",children:(0,r.jsx)(k.P.div,{ref:h,className:"flex items-center justify-center min-h-[500px]",style:{transform:"scale(".concat(t,")"),transformOrigin:"center top"},transition:{duration:.3}})})})}):(0,r.jsx)("div",{className:"h-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center max-w-md p-6",children:[(0,r.jsx)(x.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-muted-foreground mb-2",children:"No Flowchart Available"}),(0,r.jsx)("p",{className:"text-muted-foreground text-sm",children:s?"Unable to load or generate flowchart for this document.":"Please select a document to view its flowchart."})]})})}),n&&(0,r.jsx)("div",{className:"fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-6",onClick:v,children:(0,r.jsxs)("div",{className:"w-full h-full max-w-6xl max-h-[90vh] bg-neutral-800 border border-neutral-700 rounded-lg overflow-auto p-6",onClick:e=>e.stopPropagation(),children:[(0,r.jsx)("div",{className:"flex justify-between items-center mb-4",children:(0,r.jsx)("h3",{className:"text-xl font-medium",children:"Machine Learning Process Flowchart"})}),(0,r.jsx)("div",{className:"overflow-auto",children:(0,r.jsx)(k.P.div,{ref:f,className:"flex items-center justify-center min-h-[500px]",style:{transform:"scale(".concat(t,")"),transformOrigin:"center top"},transition:{duration:.3}})})]})})]})}var eT=t(11518),eI=t.n(eT),eR=t(13052),eD=t(84616),eE=t(62525),eL=t(82714);function eB(e){let{documentId:s}=e,[t,a]=(0,l.useState)([]),[n,i]=(0,l.useState)(0),[c,o]=(0,l.useState)(!1),[d,u]=(0,l.useState)(!1),[m,p]=(0,l.useState)(""),[h,f]=(0,l.useState)(""),[g,j]=(0,l.useState)(!1),{theme:N}=(0,S.D)(),w=(0,l.useRef)(null);(0,l.useEffect)(()=>{v()},[s]);let v=async()=>{if(!s){console.warn("No document ID provided for flashcards");return}try{j(!0);try{let e=await en.flashcardsApi.getFlashcards(s),t=e.results||e;if(t&&Array.isArray(t)&&t.length>0){let e=t.map(e=>({id:e.id.toString(),question:e.front,answer:e.back}));a(e);return}console.log("No existing flashcards found, will generate new ones")}catch(s){var e;if((null===(e=s.response)||void 0===e?void 0:e.status)===404)console.log("No existing flashcards found, will generate new ones");else throw s}console.log("Generating new flashcards for document:",s),await en.flashcardsApi.generateFlashcardsEnhanced(s);let t=await en.flashcardsApi.getFlashcards(s),r=t.results||t;if(r&&Array.isArray(r)&&r.length>0){let e=r.map(e=>({id:e.id.toString(),question:e.front,answer:e.back}));a(e)}else console.warn("No flashcards were generated"),a([])}catch(e){console.error("Error loading flashcards:",e),a([])}finally{j(!1)}},y=t[n],C=()=>{o(!c)},A=0===t.length;return(0,r.jsxs)("div",{ref:w,className:"jsx-1417f6e2293e1f4f flex flex-col h-full bg-black",children:[(0,r.jsxs)("div",{className:"jsx-1417f6e2293e1f4f p-4 border-b border-neutral-800",children:[(0,r.jsx)("h2",{className:"jsx-1417f6e2293e1f4f text-xl font-medium text-center",children:"Flashcards"}),!A&&!d&&(0,r.jsxs)("p",{className:"jsx-1417f6e2293e1f4f text-center text-sm text-muted-foreground mt-1",children:["Card ",n+1," of ",t.length]})]}),(0,r.jsx)(eA.F,{className:"flex-1",children:g?(0,r.jsxs)("div",{className:"jsx-1417f6e2293e1f4f h-full flex flex-col items-center justify-center p-4",children:[(0,r.jsx)("div",{className:"jsx-1417f6e2293e1f4f p-6 rounded-full bg-neutral-800 mb-4",children:(0,r.jsx)("div",{className:"jsx-1417f6e2293e1f4f h-12 w-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"})}),(0,r.jsxs)("div",{className:"jsx-1417f6e2293e1f4f rounded-lg p-4 bg-purple-600/10 border border-purple-600/30 max-w-md text-center",children:[(0,r.jsx)("h3",{className:"jsx-1417f6e2293e1f4f font-medium text-purple-500 mb-2",children:"Loading Flashcards"}),(0,r.jsx)("p",{className:"jsx-1417f6e2293e1f4f text-sm",children:s?"Checking for existing flashcards or generating new ones...":"No document selected"})]})]}):A?(0,r.jsxs)("div",{className:"jsx-1417f6e2293e1f4f h-full flex flex-col items-center justify-center p-4",children:[(0,r.jsx)("div",{className:"jsx-1417f6e2293e1f4f p-6 rounded-full bg-neutral-800 mb-4",children:(0,r.jsx)(x.A,{className:"h-12 w-12 text-muted-foreground"})}),(0,r.jsxs)("div",{className:"jsx-1417f6e2293e1f4f rounded-lg p-4 bg-purple-600/10 border border-purple-600/30 max-w-md text-center",children:[(0,r.jsx)("h3",{className:"jsx-1417f6e2293e1f4f font-medium text-purple-500 mb-2",children:"No Flashcards Available"}),(0,r.jsx)("p",{className:"jsx-1417f6e2293e1f4f text-sm",children:s?"Unable to load or generate flashcards for this document. Please try again or create flashcards manually.":"Please select a document to view flashcards."}),s&&(0,r.jsx)("button",{onClick:v,className:"jsx-1417f6e2293e1f4f mt-3 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-md transition-colors",children:"Try Again"})]})]}):d?(0,r.jsx)("div",{className:"jsx-1417f6e2293e1f4f p-6",children:(0,r.jsxs)("div",{className:"jsx-1417f6e2293e1f4f max-w-lg mx-auto",children:[(0,r.jsx)("h3",{className:"jsx-1417f6e2293e1f4f text-lg font-medium mb-4",children:"Add New Flashcard"}),(0,r.jsxs)("div",{className:"jsx-1417f6e2293e1f4f space-y-4",children:[(0,r.jsxs)("div",{className:"jsx-1417f6e2293e1f4f space-y-2",children:[(0,r.jsx)(eL.J,{htmlFor:"question",children:"Question"}),(0,r.jsx)(P.T,{id:"question",placeholder:"Enter the flashcard question",value:m,onChange:e=>p(e.target.value),className:"min-h-[100px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500"})]}),(0,r.jsxs)("div",{className:"jsx-1417f6e2293e1f4f space-y-2",children:[(0,r.jsx)(eL.J,{htmlFor:"answer",children:"Answer"}),(0,r.jsx)(P.T,{id:"answer",placeholder:"Enter the flashcard answer",value:h,onChange:e=>f(e.target.value),className:"min-h-[150px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500"})]}),(0,r.jsxs)("div",{className:"jsx-1417f6e2293e1f4f flex justify-end gap-2",children:[(0,r.jsx)(b.$,{variant:"outline",onClick:()=>u(!1),children:"Cancel"}),(0,r.jsx)(b.$,{className:"bg-purple-600 hover:bg-purple-700",onClick:()=>{m.trim()&&h.trim()&&(a([...t,{id:Date.now().toString(),question:m.trim(),answer:h.trim()}]),p(""),f(""),u(!1))},disabled:!m.trim()||!h.trim(),children:"Add Flashcard"})]})]})]})}):(0,r.jsxs)("div",{className:"jsx-1417f6e2293e1f4f p-6 flex flex-col items-center",children:[(0,r.jsx)("div",{className:"jsx-1417f6e2293e1f4f w-full max-w-lg mb-8",children:(0,r.jsx)("div",{className:"jsx-1417f6e2293e1f4f relative w-full perspective-1000",children:(0,r.jsxs)(k.P.div,{className:"w-full relative preserve-3d",animate:{rotateY:180*!!c},transition:{duration:.6,type:"spring"},style:{transformStyle:"preserve-3d"},children:[(0,r.jsx)(ep.Zp,{className:"absolute inset-0 backface-hidden bg-neutral-800 border-neutral-700 p-6 cursor-pointer",onClick:C,children:(0,r.jsxs)("div",{className:"jsx-1417f6e2293e1f4f flex flex-col h-full",children:[(0,r.jsx)("div",{className:"jsx-1417f6e2293e1f4f text-sm text-neutral-400 mb-2",children:"QUESTION"}),(0,r.jsx)("div",{className:"jsx-1417f6e2293e1f4f flex-1 flex items-center justify-center",children:(0,r.jsx)("p",{className:"jsx-1417f6e2293e1f4f text-lg",children:null==y?void 0:y.question})}),(0,r.jsx)("div",{className:"jsx-1417f6e2293e1f4f text-xs text-neutral-500 mt-4 text-center",children:"Click to reveal answer"})]})}),(0,r.jsx)(ep.Zp,{className:"absolute inset-0 backface-hidden bg-neutral-800 border-neutral-700 p-6 cursor-pointer",onClick:C,style:{transform:"rotateY(180deg)"},children:(0,r.jsxs)("div",{className:"jsx-1417f6e2293e1f4f flex flex-col h-full",children:[(0,r.jsx)("div",{className:"jsx-1417f6e2293e1f4f text-sm text-neutral-400 mb-2",children:"ANSWER"}),(0,r.jsx)("div",{className:"jsx-1417f6e2293e1f4f flex-1 flex items-center justify-center",children:(0,r.jsx)("p",{className:"jsx-1417f6e2293e1f4f text-lg",children:null==y?void 0:y.answer})}),(0,r.jsx)("div",{className:"jsx-1417f6e2293e1f4f text-xs text-neutral-500 mt-4 text-center",children:"Click to see question"})]})})]})})}),(0,r.jsxs)("div",{className:"jsx-1417f6e2293e1f4f flex flex-wrap justify-center gap-3",children:[(0,r.jsxs)(b.$,{onClick:()=>{i(e=>(e+1)%t.length),o(!1)},className:"bg-purple-600 hover:bg-purple-700",children:["Next Card",(0,r.jsx)(eR.A,{className:"h-4 w-4 ml-2"})]}),(0,r.jsxs)(b.$,{onClick:()=>u(!0),variant:"outline",children:[(0,r.jsx)(eD.A,{className:"h-4 w-4 mr-2"}),"Add Card"]}),(0,r.jsxs)(b.$,{onClick:()=>{if(t.length>1){let e=t.filter((e,s)=>s!==n);a(e),i(s=>s>=e.length?0:s),o(!1)}},variant:"outline",disabled:t.length<=1,children:[(0,r.jsx)(eE.A,{className:"h-4 w-4 mr-2"}),"Delete Card"]}),(0,r.jsxs)(b.$,{onClick:()=>{i(0),o(!1)},variant:"outline",children:[(0,r.jsx)(ey.A,{className:"h-4 w-4 mr-2"}),"Reset"]})]})]})}),(0,r.jsx)(eI(),{id:"1417f6e2293e1f4f",children:".perspective-1000{-webkit-perspective:1e3px;-moz-perspective:1e3px;perspective:1e3px}.preserve-3d{-webkit-transform-style:preserve-3d;-moz-transform-style:preserve-3d;transform-style:preserve-3d;min-height:300px}.backface-hidden{-webkit-backface-visibility:hidden;-moz-backface-visibility:hidden;backface-visibility:hidden}"})]})}var eU=t(24357);function ez(e){let{documentId:s}=e,[t,a]=(0,l.useState)(""),[n,i]=(0,l.useState)(!1),[c,o]=(0,l.useState)(null),[d,u]=(0,l.useState)(!1),[m,x]=(0,l.useState)("comprehensive"),[p,f]=(0,l.useState)(!1);(0,l.useEffect)(()=>{s&&g()},[s]);let g=async()=>{if(!s){o("No document selected");return}i(!0),o(null);try{try{let e=await en.summaryApi.getSummary(s);if(e&&e.summary){a(e.summary);return}}catch(e){console.log("No existing summary found, will generate new one")}console.log("Generating new summary for document:",s);let e=await en.summaryApi.generateSummary(s,m);a(e.summary)}catch(e){console.error("Error loading summary:",e),o("Failed to load summary. Please try again.")}finally{i(!1)}},j=async()=>{if(!s){eN.o.error("No document selected for summary generation");return}f(!0),o(null);try{console.log("Generating new summary for document:",s,"type:",m);let e=await en.summaryApi.generateSummary(s,m);a(e.summary),eN.o.success("New summary generated successfully!")}catch(e){console.error("Error generating summary:",e),o("Failed to generate new summary. Please try again."),eN.o.error("Failed to generate new summary")}finally{f(!1)}},N=async()=>{if(t)try{await navigator.clipboard.writeText(t),u(!0),eN.o.success("Summary copied to clipboard!"),setTimeout(()=>u(!1),2e3)}catch(e){console.error("Failed to copy:",e),eN.o.error("Failed to copy summary")}};return s?n?(0,r.jsx)("div",{className:"p-6 space-y-4",children:(0,r.jsxs)(ep.Zp,{children:[(0,r.jsxs)(ep.aR,{children:[(0,r.jsxs)(ep.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-5 w-5"}),"Document Summary"]}),(0,r.jsx)(ep.BT,{children:t?"Generating new summary...":"Loading summary..."})]}),(0,r.jsx)(ep.Wu,{className:"space-y-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(eh,{className:"h-4 w-full"}),(0,r.jsx)(eh,{className:"h-4 w-full"}),(0,r.jsx)(eh,{className:"h-4 w-3/4"}),(0,r.jsx)(eh,{className:"h-4 w-full"}),(0,r.jsx)(eh,{className:"h-4 w-2/3"})]})})]})}):c?(0,r.jsx)("div",{className:"p-6 space-y-4",children:(0,r.jsxs)(ep.Zp,{children:[(0,r.jsxs)(ep.aR,{children:[(0,r.jsxs)(ep.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-5 w-5"}),"Document Summary"]}),(0,r.jsx)(ep.BT,{className:"text-red-500",children:c})]}),(0,r.jsx)(ep.Wu,{children:(0,r.jsx)(b.$,{onClick:g,className:"w-full",children:"Try Again"})})]})}):(0,r.jsx)("div",{className:"p-6 space-y-4",children:(0,r.jsxs)(ep.Zp,{children:[(0,r.jsxs)(ep.aR,{children:[(0,r.jsxs)(ep.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-5 w-5"}),"Document Summary"]}),(0,r.jsx)(ep.BT,{children:"AI-generated summary of your document content"})]}),(0,r.jsxs)(ep.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Summary Type:"}),(0,r.jsxs)(Z,{value:m,onValueChange:x,children:[(0,r.jsx)(W,{className:"w-48",children:(0,r.jsx)(M,{})}),(0,r.jsxs)(J,{children:[(0,r.jsx)(V,{value:"comprehensive",children:"Comprehensive"}),(0,r.jsx)(V,{value:"brief",children:"Brief"}),(0,r.jsx)(V,{value:"key_points",children:"Key Points"})]})]})]}),(0,r.jsx)("div",{className:"border rounded-lg p-4 bg-neutral-900/50",children:(0,r.jsx)(eo,{content:t})}),(0,r.jsxs)("div",{className:"flex gap-2 pt-4 border-t",children:[(0,r.jsx)(b.$,{onClick:N,variant:"outline",className:"flex-1",disabled:!t,children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(eg.A,{className:"h-4 w-4 mr-2 text-green-500"}),"Copied!"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(eU.A,{className:"h-4 w-4 mr-2"}),"Copy Summary"]})}),(0,r.jsxs)(b.$,{onClick:g,variant:"outline",className:"flex-1",disabled:n||p,children:[(0,r.jsx)(ej.A,{className:"h-4 w-4 mr-2"}),"Reload"]}),(0,r.jsxs)(b.$,{onClick:j,className:"flex-1",disabled:n||p,children:[(0,r.jsx)(eb.A,{className:"h-4 w-4 mr-2"}),p?"Generating...":"Generate New"]})]})]})]})}):(0,r.jsx)("div",{className:"p-6 space-y-4",children:(0,r.jsxs)(ep.Zp,{children:[(0,r.jsxs)(ep.aR,{children:[(0,r.jsxs)(ep.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-5 w-5"}),"Document Summary"]}),(0,r.jsx)(ep.BT,{children:"Select a document to view or generate its summary"})]}),(0,r.jsx)(ep.Wu,{children:(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Please select a document to generate a summary."})})]})})}var eO=t(52292);let e$=eO.bL,e_=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)(eO.q7,{ref:s,className:(0,o.cn)("border-b",t),...l})});e_.displayName="AccordionItem";let eZ=l.forwardRef((e,s)=>{let{className:t,children:l,...a}=e;return(0,r.jsx)(eO.Y9,{className:"flex",children:(0,r.jsxs)(eO.l9,{ref:s,className:(0,o.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",t),...a,children:[l,(0,r.jsx)(O.A,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})});eZ.displayName=eO.l9.displayName;let eM=l.forwardRef((e,s)=>{let{className:t,children:l,...a}=e;return(0,r.jsx)(eO.UC,{ref:s,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...a,children:(0,r.jsx)("div",{className:(0,o.cn)("pb-4 pt-0",t),children:l})})});function eW(e){let{documentId:s}=e,[t,a]=(0,l.useState)([]),[n,i]=(0,l.useState)(!1),[c,o]=(0,l.useState)(null),[d,u]=(0,l.useState)(null);(0,l.useEffect)(()=>{s&&m()},[s]);let m=async()=>{if(!s){o("No document selected");return}i(!0),o(null);try{let e=await en.chapterApi.getOrGenerateChapters(s);if(e.chapters&&e.chapters.length>0){let s=e.chapters.map((e,s)=>{var t;return{id:(null===(t=e.id)||void 0===t?void 0:t.toString())||(s+1).toString(),title:e.title||"Chapter ".concat(s+1),content:e.content||"",subsections:e.subsections||[]}});a(s),eN.o.success("Generated ".concat(s.length," chapters successfully!"))}else o("No chapters were generated. Please try again.")}catch(e){console.error("Error loading chapters:",e),o("Failed to load chapters. Please try again."),eN.o.error("Failed to generate chapters")}finally{i(!1)}},x=async()=>{if(!s){eN.o.error("No document selected for chapter generation");return}i(!0),o(null);try{let e=await en.chapterApi.generateChapters(s,5,"gemini");if(e.chapters&&e.chapters.length>0){let s=e.chapters.map((e,s)=>{var t;return{id:(null===(t=e.id)||void 0===t?void 0:t.toString())||(s+1).toString(),title:e.title||"Chapter ".concat(s+1),content:e.content||"",subsections:e.subsections||[]}});a(s),eN.o.success("Generated ".concat(s.length," new chapters successfully!"))}else o("No chapters were generated. Please try again."),eN.o.error("Failed to generate new chapters")}catch(e){console.error("Error generating chapters:",e),o("Failed to generate new chapters. Please try again."),eN.o.error("Failed to generate new chapters")}finally{i(!1)}},p=async e=>{let s="# ".concat(e.title,"\n\n").concat(e.content,"\n\n");e.subsections&&e.subsections.forEach(e=>{s+="## ".concat(e.title,"\n").concat(e.content,"\n\n")});try{await navigator.clipboard.writeText(s),u(e.id),eN.o.success('Chapter "'.concat(e.title,'" copied to clipboard!')),setTimeout(()=>u(null),2e3)}catch(e){console.error("Failed to copy:",e),eN.o.error("Failed to copy chapter")}},h=async()=>{if(0===t.length)return;let e="";t.forEach((s,t)=>{e+="# Chapter ".concat(t+1,": ").concat(s.title,"\n\n").concat(s.content,"\n\n"),s.subsections&&s.subsections.forEach(s=>{e+="## ".concat(s.title,"\n").concat(s.content,"\n\n")}),e+="---\n\n"});try{await navigator.clipboard.writeText(e),eN.o.success("All chapters copied to clipboard!")}catch(e){console.error("Failed to copy:",e),eN.o.error("Failed to copy all chapters")}};return s?n?(0,r.jsx)("div",{className:"p-6 space-y-4",children:(0,r.jsxs)(ep.Zp,{children:[(0,r.jsxs)(ep.aR,{children:[(0,r.jsxs)(ep.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-5 w-5"}),"Document Chapters"]}),(0,r.jsx)(ep.BT,{children:t.length>0?"Generating new chapters...":"Loading chapters..."})]}),(0,r.jsx)(ep.Wu,{className:"space-y-4",children:[1,2,3].map(e=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(eh,{className:"h-6 w-3/4"}),(0,r.jsx)(eh,{className:"h-4 w-full"}),(0,r.jsx)(eh,{className:"h-4 w-2/3"})]},e))})]})}):c?(0,r.jsx)("div",{className:"p-6 space-y-4",children:(0,r.jsxs)(ep.Zp,{children:[(0,r.jsxs)(ep.aR,{children:[(0,r.jsxs)(ep.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-5 w-5"}),"Document Chapters"]}),(0,r.jsx)(ep.BT,{className:"text-red-500",children:c})]}),(0,r.jsx)(ep.Wu,{children:(0,r.jsx)(b.$,{onClick:m,className:"w-full",children:"Try Again"})})]})}):(0,r.jsx)("div",{className:"p-6 space-y-4",children:(0,r.jsxs)(ep.Zp,{children:[(0,r.jsxs)(ep.aR,{children:[(0,r.jsxs)(ep.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-5 w-5"}),"Document Chapters"]}),(0,r.jsx)(ep.BT,{children:"AI-generated chapter breakdown of your document"})]}),(0,r.jsxs)(ep.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex gap-2 mb-4",children:[(0,r.jsxs)(b.$,{onClick:h,variant:"outline",className:"flex-1",disabled:0===t.length,children:[(0,r.jsx)(eU.A,{className:"h-4 w-4 mr-2"}),"Copy All Chapters"]}),(0,r.jsxs)(b.$,{onClick:x,className:"flex-1",disabled:n,children:[(0,r.jsx)(eb.A,{className:"h-4 w-4 mr-2"}),"Generate New Chapters"]})]}),(0,r.jsx)(e$,{type:"single",collapsible:!0,className:"w-full",children:t.map((e,s)=>(0,r.jsxs)(e_,{value:e.id,children:[(0,r.jsx)(eZ,{className:"text-left",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-muted-foreground",children:["Chapter ",s+1]}),(0,r.jsx)(eR.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:e.title})]})}),(0,r.jsx)(eM,{children:(0,r.jsxs)("div",{className:"space-y-4 pt-2",children:[(0,r.jsx)("p",{className:"text-sm leading-relaxed",children:e.content}),e.subsections&&e.subsections.length>0&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"font-medium text-sm",children:"Subsections:"}),e.subsections.map((e,s)=>(0,r.jsxs)("div",{className:"pl-4 border-l-2 border-muted",children:[(0,r.jsx)("h5",{className:"font-medium text-sm mb-1",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.content})]},s))]}),(0,r.jsx)(b.$,{onClick:()=>p(e),variant:"outline",size:"sm",className:"mt-3",children:d===e.id?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(eg.A,{className:"h-4 w-4 mr-2 text-green-500"}),"Copied!"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(eU.A,{className:"h-4 w-4 mr-2"}),"Copy Chapter"]})})]})})]},e.id))})]})]})}):(0,r.jsx)("div",{className:"p-6 space-y-4",children:(0,r.jsxs)(ep.Zp,{children:[(0,r.jsxs)(ep.aR,{children:[(0,r.jsxs)(ep.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(g.A,{className:"h-5 w-5"}),"Document Chapters"]}),(0,r.jsx)(ep.BT,{children:"Select a document to view its chapter breakdown"})]}),(0,r.jsx)(ep.Wu,{children:(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Please select a document to generate chapters."})})]})})}eM.displayName=eO.UC.displayName;var eq=t(86450),eG=t(9803),eJ=t(30227),eV=t(64261),eY=t(48264),eX=t(39022),eH=t(85339),eK=t(92657),eQ=t(76435);let e0=null,e1=null,e4=null;function e2(e){let{documentId:s,fileName:t,fileType:a,className:n=""}=e,[i,c]=(0,l.useState)(null),[o,d]=(0,l.useState)(!0),[u,m]=(0,l.useState)(null),[x,p]=(0,l.useState)(0),[f,g]=(0,l.useState)(1),[j,b]=(0,l.useState)(1),{theme:N}=(0,S.D)(),w=(0,l.useRef)(null);(0,l.useEffect)(()=>{y()},[s]);let y=async()=>{try{d(!0),m(null);let e=a.toLowerCase(),t=await en.documentApi.getDocumentFileBlob(s);if("pdf"===e){let e=URL.createObjectURL(t);c(e)}else if(["doc","docx"].includes(e)){let e=await t.arrayBuffer(),s=await eQ.convertToHtml({arrayBuffer:e});c(s.value)}else if(["txt","csv","json","xml","html","css","js","py","java","cpp","c"].includes(e)){let e=await t.text();c(e)}else if(["jpg","jpeg","png","gif","bmp","webp","svg"].includes(e)){let e=URL.createObjectURL(t);c(e)}else m("Preview not available for this file type")}catch(e){console.error("Error loading file preview:",e),m("Failed to load file preview. The file may be corrupted or too large.")}finally{d(!1)}},k=e=>{let s=e.toLowerCase();if(["jpg","jpeg","png","gif","bmp","webp","svg"].includes(s))return(0,r.jsx)(H.A,{className:"h-8 w-8 text-blue-500"});if(["mp4","avi","mov","wmv","flv","webm"].includes(s))return(0,r.jsx)(eG.A,{className:"h-8 w-8 text-red-500"});if(["mp3","wav","flac","aac","ogg"].includes(s))return(0,r.jsx)(eJ.A,{className:"h-8 w-8 text-green-500"});if(["pdf","doc","docx","txt","rtf"].includes(s))return(0,r.jsx)(h.A,{className:"h-8 w-8 text-orange-500"});if(["xls","xlsx","csv"].includes(s))return(0,r.jsx)(eV.A,{className:"h-8 w-8 text-green-600"});else if(["ppt","pptx"].includes(s))return(0,r.jsx)(eY.A,{className:"h-8 w-8 text-red-600"});else if(["zip","rar","7z","tar","gz"].includes(s))return(0,r.jsx)(eX.A,{className:"h-8 w-8 text-purple-500"});else return(0,r.jsx)(v.A,{className:"h-8 w-8 text-gray-500"})},C=e=>{let{numPages:s}=e;p(s),g(1)},A=e=>{g(s=>s+e)},P=()=>A(-1),F=()=>A(1),T=()=>b(e=>Math.min(e+.2,3)),I=()=>b(e=>Math.max(e-.2,.5)),R=()=>b(1),D=()=>{let e=en.documentApi.getDocumentFileUrl(s);window.open(e,"_blank")};return o?(0,r.jsx)("div",{className:"flex items-center justify-center h-full ".concat(n),children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Loading preview..."})]})}):u?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-8 ".concat(n),children:[(0,r.jsx)(eH.A,{className:"h-16 w-16 text-destructive mb-4"}),(0,r.jsx)("p",{className:"text-lg font-medium mb-2",children:t}),(0,r.jsx)("p",{className:"text-sm text-destructive mb-4",children:u}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsxs)("button",{onClick:y,className:"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2",children:[(0,r.jsx)(eK.A,{className:"h-4 w-4"}),"Retry Preview"]}),(0,r.jsxs)("button",{onClick:D,className:"px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors flex items-center gap-2",children:[(0,r.jsx)(eC.A,{className:"h-4 w-4"}),"Download File"]})]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-4",children:["File type: ",a.toUpperCase()]})]}):(0,r.jsx)("div",{className:"h-full w-full bg-background border rounded-lg ".concat(n),children:(()=>{let e=a.toLowerCase();return"pdf"===e&&i&&e0&&e1?(0,r.jsxs)("div",{className:"h-full flex flex-col",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b bg-muted/50",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("button",{onClick:P,disabled:f<=1,className:"px-3 py-1 bg-primary text-primary-foreground rounded disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,r.jsxs)("span",{className:"text-sm",children:["Page ",f," of ",x]}),(0,r.jsx)("button",{onClick:F,disabled:f>=x,className:"px-3 py-1 bg-primary text-primary-foreground rounded disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("button",{onClick:I,className:"px-2 py-1 border rounded",children:"-"}),(0,r.jsxs)("span",{className:"text-sm",children:[Math.round(100*j),"%"]}),(0,r.jsx)("button",{onClick:T,className:"px-2 py-1 border rounded",children:"+"}),(0,r.jsx)("button",{onClick:R,className:"px-2 py-1 border rounded text-xs",children:"Reset"})]})]}),(0,r.jsx)("div",{className:"flex-1 overflow-auto flex justify-center p-4",ref:w,children:(0,r.jsx)(e0,{file:i,onLoadSuccess:C,loading:(0,r.jsx)("div",{className:"text-center",children:"Loading PDF..."}),error:(0,r.jsx)("div",{className:"text-center text-destructive",children:"Failed to load PDF"}),children:(0,r.jsx)(e1,{pageNumber:f,scale:j,renderTextLayer:!1,renderAnnotationLayer:!1})})})]}):"pdf"!==e||!i||e0&&e1?["doc","docx"].includes(e)&&i?(0,r.jsx)("div",{className:"h-full overflow-auto p-6",children:(0,r.jsx)("div",{className:"prose prose-sm max-w-none dark:prose-invert",dangerouslySetInnerHTML:{__html:i}})}):["txt","csv","json","xml","html","css","js","py","java","cpp","c"].includes(e)&&i?(0,r.jsx)("div",{className:"h-full overflow-auto p-4",children:(0,r.jsx)("pre",{className:"text-sm font-mono whitespace-pre-wrap break-words",children:i})}):["jpg","jpeg","png","gif","bmp","webp","svg"].includes(e)&&i?(0,r.jsx)("div",{className:"h-full flex items-center justify-center p-4",children:(0,r.jsx)("img",{src:i,alt:t,className:"max-w-full max-h-full object-contain rounded-lg shadow-lg",style:{transform:"scale(".concat(j,")")}})}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-8",children:[k(a),(0,r.jsx)("p",{className:"text-lg font-medium mt-4",children:t}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground mt-2",children:["File type: ",a.toUpperCase()]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-4 text-center max-w-md",children:"This file type is not supported for preview. You can download the file to view it in the appropriate application."}),(0,r.jsxs)("button",{onClick:D,className:"mt-4 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2",children:[(0,r.jsx)(eC.A,{className:"h-4 w-4"}),"Download File"]})]}):(0,r.jsx)("div",{className:"h-full flex flex-col items-center justify-center p-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Loading PDF viewer..."}),(0,r.jsxs)("button",{onClick:D,className:"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2",children:[(0,r.jsx)(eC.A,{className:"h-4 w-4"}),"Download PDF"]})]})})})()})}Promise.all([t.e(2084),t.e(9612)]).then(t.bind(t,29612)).then(e=>{e0=e.Document,e1=e.Page,(e4=e.pdfjs).GlobalWorkerOptions.workerSrc="//cdnjs.cloudflare.com/ajax/libs/pdf.js/".concat(e4.version,"/pdf.worker.min.js")});var e6=t(33786);function e3(e){let{documentId:s,fileName:t,className:a=""}=e,[n,i]=(0,l.useState)(!0),[c,o]=(0,l.useState)(null),[d,u]=(0,l.useState)(null),[m,x]=(0,l.useState)("office365");(0,l.useEffect)(()=>{p()},[s]);let p=async()=>{try{i(!0),o(null);let e=en.documentApi.getDocumentFileUrl(s);u(e)}catch(e){console.error("Error loading file info:",e),o("Failed to load file information.")}finally{i(!1)}},h=()=>{d&&window.open(d,"_blank")},f=()=>{if(d){let e="https://view.officeapps.live.com/op/embed.aspx?src=".concat(encodeURIComponent(d));window.open(e,"_blank")}},g=()=>{if(d){let e="https://docs.google.com/gview?url=".concat(encodeURIComponent(d),"&embedded=true");window.open(e,"_blank")}};return n?(0,r.jsx)("div",{className:"flex items-center justify-center h-full ".concat(a),children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Loading PowerPoint preview..."})]})}):(0,r.jsxs)("div",{className:"h-full w-full bg-background border rounded-lg ".concat(a),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b bg-muted/50",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(eY.A,{className:"h-6 w-6 text-red-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium",children:t}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"PowerPoint Presentation"})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("select",{value:m,onChange:e=>x(e.target.value),className:"px-3 py-1 border rounded text-sm",children:[(0,r.jsx)("option",{value:"office365",children:"Office 365 Viewer"}),(0,r.jsx)("option",{value:"google",children:"Google Docs Viewer"}),(0,r.jsx)("option",{value:"download",children:"Download Only"})]}),(0,r.jsxs)("button",{onClick:h,className:"px-3 py-1 bg-primary text-primary-foreground rounded text-sm hover:bg-primary/90 transition-colors flex items-center gap-1",children:[(0,r.jsx)(eC.A,{className:"h-3 w-3"}),"Download"]})]})]}),(0,r.jsx)("div",{className:"flex-1 h-full",children:c?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-8",children:[(0,r.jsx)(eH.A,{className:"h-16 w-16 text-destructive mb-4"}),(0,r.jsx)("p",{className:"text-lg font-medium mb-2",children:"Preview Error"}),(0,r.jsx)("p",{className:"text-sm text-destructive mb-6 text-center max-w-md",children:c}),(0,r.jsxs)("div",{className:"flex flex-col gap-3 w-full max-w-sm",children:[(0,r.jsxs)("button",{onClick:f,className:"px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2",children:[(0,r.jsx)(e6.A,{className:"h-4 w-4"}),"Open in Office 365 Viewer"]}),(0,r.jsxs)("button",{onClick:g,className:"px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-2",children:[(0,r.jsx)(e6.A,{className:"h-4 w-4"}),"Open in Google Docs Viewer"]}),(0,r.jsxs)("button",{onClick:h,className:"px-4 py-3 border border-border rounded-lg hover:bg-muted transition-colors flex items-center justify-center gap-2",children:[(0,r.jsx)(eC.A,{className:"h-4 w-4"}),"Download File"]})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-4 text-center",children:"PowerPoint files require external viewers or desktop applications for full functionality."})]}):"download"===m?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-8",children:[(0,r.jsx)(eY.A,{className:"h-24 w-24 text-red-600 mb-6"}),(0,r.jsx)("p",{className:"text-xl font-medium mb-2",children:t}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-6 text-center max-w-md",children:"PowerPoint presentations are best viewed in PowerPoint or compatible applications. Download the file to view all slides, animations, and interactive elements."}),(0,r.jsxs)("div",{className:"flex flex-col gap-3 w-full max-w-sm",children:[(0,r.jsxs)("button",{onClick:h,className:"px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center justify-center gap-2",children:[(0,r.jsx)(eC.A,{className:"h-4 w-4"}),"Download PowerPoint File"]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("button",{onClick:f,className:"flex-1 px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors flex items-center justify-center gap-1",children:[(0,r.jsx)(e6.A,{className:"h-3 w-3"}),"Office 365"]}),(0,r.jsxs)("button",{onClick:g,className:"flex-1 px-3 py-2 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors flex items-center justify-center gap-1",children:[(0,r.jsx)(e6.A,{className:"h-3 w-3"}),"Google Docs"]})]})]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-6 text-center",children:"File type: PPTX • Size: Unknown"})]}):(0,r.jsx)("div",{className:"h-full p-4",children:(e=>{if(!d)return null;let s="office365"===e?"https://view.officeapps.live.com/op/embed.aspx?src=".concat(encodeURIComponent(d)):"https://docs.google.com/gview?url=".concat(encodeURIComponent(d),"&embedded=true");return(0,r.jsx)("iframe",{src:s,className:"w-full h-full border-0 rounded-lg",title:t,onError:()=>o("".concat(e," preview failed. Try downloading the file instead.")),onLoad:()=>o(null)})})(m)})})]})}function e5(e){let{documentId:s,fileName:t,fileType:a,className:n=""}=e,[i,c]=(0,l.useState)(null),[o,d]=(0,l.useState)(!0),[u,m]=(0,l.useState)(null),{theme:x}=(0,S.D)();(0,l.useEffect)(()=>{p()},[s]);let p=async()=>{try{if(d(!0),m(null),f(a)){let e=await en.documentApi.getDocumentFileBlob(s),t=URL.createObjectURL(e);c(t)}else{let e=en.documentApi.getDocumentFileUrl(s);c(e)}}catch(e){console.error("Error loading file preview:",e),m("Failed to load file preview. The file may not be accessible or the server may be down.")}finally{d(!1)}},f=e=>["jpg","jpeg","png","gif","bmp","webp","svg","pdf"].includes(e.toLowerCase()),g=e=>{let s=e.toLowerCase();if(["jpg","jpeg","png","gif","bmp","webp","svg"].includes(s))return(0,r.jsx)(H.A,{className:"h-8 w-8 text-blue-500"});if(["mp4","avi","mov","wmv","flv","webm"].includes(s))return(0,r.jsx)(eG.A,{className:"h-8 w-8 text-red-500"});if(["mp3","wav","flac","aac","ogg"].includes(s))return(0,r.jsx)(eJ.A,{className:"h-8 w-8 text-green-500"});if(["pdf","doc","docx","txt","rtf"].includes(s))return(0,r.jsx)(h.A,{className:"h-8 w-8 text-orange-500"});if(["xls","xlsx","csv"].includes(s))return(0,r.jsx)(eV.A,{className:"h-8 w-8 text-green-600"});else if(["ppt","pptx"].includes(s))return(0,r.jsx)(eY.A,{className:"h-8 w-8 text-red-600"});else if(["zip","rar","7z","tar","gz"].includes(s))return(0,r.jsx)(eX.A,{className:"h-8 w-8 text-purple-500"});else return(0,r.jsx)(v.A,{className:"h-8 w-8 text-gray-500"})};(0,l.useEffect)(()=>()=>{i&&i.startsWith("blob:")&&URL.revokeObjectURL(i)},[i]);let j=a.toLowerCase();return["ppt","pptx"].includes(j)?(0,r.jsx)("div",{className:"h-full w-full ".concat(n),children:(0,r.jsx)(e3,{documentId:s,fileName:t,className:"h-full"})}):["pdf","doc","docx","txt","csv","json","xml","html","css","js","py","java","cpp","c","jpg","jpeg","png","gif","bmp","webp","svg"].includes(j)?(0,r.jsx)("div",{className:"h-full w-full ".concat(n),children:(0,r.jsx)(e2,{documentId:s,fileName:t,fileType:a,className:"h-full"})}):(0,r.jsx)("div",{className:"h-full w-full ".concat("light"===x?"bg-white border border-gray-200":"bg-neutral-800 border border-neutral-700"," rounded-lg overflow-hidden ").concat(n),children:(()=>{if(o)return(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Loading preview..."})]});if(u||!i)return(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-8",children:[g(a),(0,r.jsx)("p",{className:"text-lg font-medium mt-4",children:t}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground mt-2",children:["File type: ",a.toUpperCase()]}),u&&(0,r.jsx)("p",{className:"text-sm text-destructive mt-2",children:u}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-4 text-center",children:u?"Preview failed to load":"Preview not available for this file type"}),u&&(0,r.jsxs)("div",{className:"mt-6 flex gap-3",children:[(0,r.jsx)("button",{onClick:p,className:"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",children:"Retry"}),(0,r.jsx)("button",{onClick:()=>window.open(en.documentApi.getDocumentFileUrl(s),"_blank"),className:"px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors",children:"Download File"})]})]});let e=a.toLowerCase();return["jpg","jpeg","png","gif","bmp","webp"].includes(e)?(0,r.jsx)("div",{className:"flex items-center justify-center h-full p-4",children:(0,r.jsx)("img",{src:i,alt:t,className:"max-w-full max-h-full object-contain rounded-lg shadow-lg",onError:()=>m("Failed to load image")})}):"svg"===e?(0,r.jsx)("div",{className:"flex items-center justify-center h-full p-4",children:(0,r.jsx)("div",{className:"max-w-full max-h-full",dangerouslySetInnerHTML:{__html:i}})}):"pdf"===e?(0,r.jsx)("div",{className:"h-full w-full",children:(0,r.jsx)("iframe",{src:i,title:t,className:"w-full h-full border-0 rounded-lg",onError:()=>m("Failed to load PDF")})}):["mp4","webm","ogg"].includes(e)?(0,r.jsx)("div",{className:"flex items-center justify-center h-full p-4",children:(0,r.jsxs)("video",{controls:!0,className:"max-w-full max-h-full rounded-lg shadow-lg",onError:()=>m("Failed to load video"),children:[(0,r.jsx)("source",{src:i,type:"video/".concat(e)}),"Your browser does not support the video tag."]})}):["mp3","wav","ogg","flac","aac"].includes(e)?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-8",children:[(0,r.jsx)(eJ.A,{className:"h-16 w-16 text-green-500 mb-4"}),(0,r.jsx)("p",{className:"text-lg font-medium mb-4",children:t}),(0,r.jsxs)("audio",{controls:!0,className:"w-full max-w-md",onError:()=>m("Failed to load audio"),children:[(0,r.jsx)("source",{src:i,type:"audio/".concat(e)}),"Your browser does not support the audio tag."]})]}):["txt","csv","json","xml","html","css","js","py","java","cpp","c"].includes(e)?(0,r.jsx)("div",{className:"h-full p-4",children:(0,r.jsx)("iframe",{src:i,title:t,className:"w-full h-full border-0 rounded-lg bg-white",onError:()=>m("Failed to load text file")})}):["ppt","pptx"].includes(e)?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-8",children:[(0,r.jsx)(eY.A,{className:"h-24 w-24 text-red-600 mb-6"}),(0,r.jsx)("p",{className:"text-xl font-medium mb-2",children:t}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-6 text-center max-w-md",children:"PowerPoint presentations cannot be previewed directly in the browser. You can download the file to view it in PowerPoint or a compatible application."}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{onClick:()=>window.open(i,"_blank"),className:"px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",children:"Download File"}),(0,r.jsx)("button",{onClick:()=>{let e=document.createElement("a");e.href=i,e.download=t,e.click()},className:"px-6 py-3 border border-border rounded-lg hover:bg-muted transition-colors",children:"Save As..."})]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-4",children:["File type: ",a.toUpperCase()]})]}):["doc","docx","xls","xlsx"].includes(e)?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-8",children:[e.includes("doc")?(0,r.jsx)(h.A,{className:"h-24 w-24 text-blue-600 mb-6"}):(0,r.jsx)(eV.A,{className:"h-24 w-24 text-green-600 mb-6"}),(0,r.jsx)("p",{className:"text-xl font-medium mb-2",children:t}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-6 text-center max-w-md",children:"Office documents cannot be previewed directly in the browser. You can download the file to view it in the appropriate application."}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{onClick:()=>window.open(i,"_blank"),className:"px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",children:"Download File"}),(0,r.jsx)("button",{onClick:()=>{let e=document.createElement("a");e.href=i,e.download=t,e.click()},className:"px-6 py-3 border border-border rounded-lg hover:bg-muted transition-colors",children:"Save As..."})]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground mt-4",children:["File type: ",a.toUpperCase()]})]}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-8",children:[g(a),(0,r.jsx)("p",{className:"text-lg font-medium mt-4",children:t}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground mt-2",children:["File type: ",a.toUpperCase()]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-4 text-center",children:"Preview not available for this file type"}),(0,r.jsx)("button",{onClick:()=>window.open(i,"_blank"),className:"mt-4 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:"Download File"})]})})()})}var e8=t(14186);function e7(e){let{isActive:s,isPaused:t,sessionStartTime:a,className:n=""}=e,[i,c]=(0,l.useState)("00:00:00");return((0,l.useEffect)(()=>{if(!s||!a){c("00:00:00");return}let e=()=>{if(t)return;let e=new Date,s=new Date(a),r=Math.floor((e.getTime()-s.getTime())/1e3),l=Math.floor(r/3600),n=Math.floor(r%3600/60);c("".concat(l.toString().padStart(2,"0"),":").concat(n.toString().padStart(2,"0"),":").concat((r%60).toString().padStart(2,"0")))};e();let r=setInterval(e,1e3);return()=>clearInterval(r)},[s,t,a]),s)?(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm ".concat(n),children:[(0,r.jsx)(e8.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"font-mono",children:i}),t&&(0,r.jsx)("span",{className:"text-yellow-500 text-xs",children:"(Paused)"})]}):null}var e9=t(79750);function se(){let e=(0,a.useSearchParams)();(0,a.useRouter)();let s=e.get("type")||"upload",[t,i]=(0,l.useState)("chat"),[c,o]=(0,l.useState)(!0),[b,N]=(0,l.useState)(null),{theme:w}=(0,S.D)(),v=(0,l.useRef)(null),[y,A]=(0,l.useState)([]),[P]=(0,l.useState)(""),[F]=(0,l.useState)(""),[T,R]=(0,l.useState)(!1),[D,E]=(0,l.useState)(null);(0,l.useEffect)(()=>{document.body.classList.add("page-transition");let t=e.get("documentId");if(t){let e=parseInt(t);"upload"===s&&(R(!0),B(e)),L(e)}let r=localStorage.getItem("uploadedFiles");if(r&&"upload"===s)try{let e=JSON.parse(r);if(e.length>0){var l;let s=e[0],t=localStorage.getItem("currentDocumentId");N({type:"file",name:s.name,size:s.size,fileType:s.type||(null===(l=s.name.split(".").pop())||void 0===l?void 0:l.toLowerCase()),documentId:s.documentId||(t?parseInt(t):void 0)}),o(!1)}}catch(e){console.error("Error parsing uploaded files:",e)}else if("paste"===s){let e=localStorage.getItem("pastedContent");if(e)try{let s=JSON.parse(e);N({type:"text",content:s.content||s.url||"Pasted content"}),o(!1)}catch(e){console.error("Error parsing pasted content:",e)}}else if("record"===s){let e=localStorage.getItem("recordedAudio");if(e)try{let s=JSON.parse(e);N({type:"audio",name:"Recording-"+new Date().toISOString().split("T")[0]+".wav",duration:s.duration||"00:00"}),o(!1)}catch(e){console.error("Error parsing recorded audio:",e)}}return()=>{document.body.classList.remove("page-transition")}},[s,e]);let L=async e=>{try{let t=await en.documentApi.getDocumentDetails(e);if(E(t),t&&t.file){var s;let r=null===(s=t.title.split(".").pop())||void 0===s?void 0:s.toLowerCase(),l={type:"file",name:t.title,size:"Unknown",fileType:r||"unknown",documentId:e};N(l),o(!1)}}catch(e){console.error("Error loading document details:",e)}},B=async e=>{try{let s=await en.documentApi.getDocumentStatus(e);"completed"===s.processing_status?R(!1):"failed"===s.processing_status?R(!1):setTimeout(()=>B(e),2e3)}catch(e){console.error("Error checking document status:",e),R(!1)}},z=()=>{if(e.get("documentId")&&!b&&!T)return(0,r.jsx)("div",{className:"p-4 h-full flex flex-col items-center justify-center",children:(0,r.jsxs)("div",{className:"rounded-lg p-8 text-center ".concat("light"===w?"bg-white border border-black":"bg-neutral-800"),children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-lg mb-2",children:"Loading document..."}),(0,r.jsx)("p",{className:"text-sm ".concat("light"===w?"text-neutral-600":"text-neutral-500"),children:"Please wait while we load your document for preview."})]})});if(b){if("file"===b.type)return(0,r.jsxs)("div",{className:"p-4 h-full flex flex-col",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:b.name}),(0,r.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,r.jsx)(e5,{documentId:b.documentId,fileName:b.name,fileType:b.fileType,className:"h-full"})})]});if("text"===b.type)return(0,r.jsxs)("div",{className:"p-4 h-full flex flex-col",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Pasted Content"}),(0,r.jsx)("div",{className:"flex-1 overflow-auto",children:(0,r.jsx)("div",{className:"rounded-lg p-4 ".concat("light"===w?"bg-white border border-black":"bg-neutral-800"),children:(0,r.jsx)("p",{children:b.content})})})]});if("audio"===b.type)return(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:b.name}),(0,r.jsxs)("div",{className:"rounded-lg p-4 flex flex-col items-center ".concat("light"===w?"bg-white border border-black":"bg-neutral-800"),children:[(0,r.jsx)("div",{className:"w-full h-24 rounded-md mb-4 ".concat("light"===w?"bg-gray-200":"bg-neutral-700")}),(0,r.jsx)("audio",{controls:!0,className:"w-full",children:(0,r.jsx)("source",{src:"#",type:"audio/wav"})}),(0,r.jsxs)("p",{className:"text-sm mt-2 ".concat("light"===w?"text-neutral-600":"text-neutral-500"),children:["Duration: ",b.duration]})]})]})}switch(s){case"upload":default:return(0,r.jsx)(C,{});case"paste":return(0,r.jsx)(I,{});case"record":return(0,r.jsx)(U,{})}},O=e.get("documentId")?parseInt(e.get("documentId")):void 0,{isActive:$,isPaused:_,sessionStartTime:Z}=(0,e9.U)({documentId:O,enabled:!!O,isProcessingComplete:!T});return(0,r.jsxs)(ex.b,{showQuizButton:!0,showUpgradeButton:!0,documentId:O,children:[(0,r.jsx)(eq.p,{isVisible:T,message:"Processing your document. This may take a few moments..."}),(0,r.jsx)(k.P.div,{className:"h-[calc(100vh-65px)] overflow-hidden bg-background text-foreground flex flex-col",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},children:c?(0,r.jsx)(k.P.div,{className:"flex-1 p-4",children:z()}):(0,r.jsxs)(d,{direction:"horizontal",className:"flex-1 overflow-hidden",children:[(0,r.jsx)(u,{defaultSize:40,minSize:30,children:(0,r.jsx)("div",{className:"h-full overflow-auto",children:z()})}),(0,r.jsx)(m,{withHandle:!0}),(0,r.jsx)(u,{defaultSize:60,minSize:30,children:(0,r.jsx)("div",{className:"h-full flex flex-col",children:(0,r.jsxs)(n.tU,{defaultValue:"chat",onValueChange:i,value:t,className:"w-full h-full",children:[(0,r.jsx)("div",{className:"border-b border-border sticky top-0 bg-background z-10",children:(0,r.jsxs)("div",{className:"px-4 overflow-x-auto scrollbar-hide flex items-center justify-between",ref:v,children:[(0,r.jsxs)(n.j7,{className:"h-12 w-max",children:[(0,r.jsxs)(n.Xi,{value:"chat",className:"gap-2",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),"Chat"]}),(0,r.jsxs)(n.Xi,{value:"blueprint",className:"gap-2",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),"Blueprint"]}),(0,r.jsxs)(n.Xi,{value:"summary",className:"gap-2",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),"Summary"]}),(0,r.jsxs)(n.Xi,{value:"flowchart",className:"gap-2",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),"Flowchart"]}),(0,r.jsxs)(n.Xi,{value:"flashcards",className:"gap-2",children:[(0,r.jsx)(f.A,{className:"h-4 w-4"}),"Flashcards"]}),(0,r.jsxs)(n.Xi,{value:"chapters",className:"gap-2",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),"Chapters"]}),(0,r.jsxs)(n.Xi,{value:"transcript",className:"gap-2",children:[(0,r.jsx)(j.A,{className:"h-4 w-4"}),"Performance"]})]}),(0,r.jsx)("div",{className:"flex items-center gap-2 ml-4",children:(0,r.jsx)(e7,{isActive:$,isPaused:_,sessionStartTime:Z,className:"text-muted-foreground"})})]})}),(0,r.jsx)(n.av,{value:t,className:"flex-1 p-0 m-0 overflow-hidden h-full",children:(0,r.jsx)("div",{className:"h-full overflow-y-auto pb-8 scrollbar-thin scrollbar-thumb-neutral-500 scrollbar-track-transparent hover:scrollbar-thumb-neutral-400",children:(()=>{let s=e.get("documentId")?parseInt(e.get("documentId")):void 0;if(!s){let e=localStorage.getItem("currentDocumentId");e&&(s=parseInt(e))}switch(t){case"chat":default:return(0,r.jsx)(eu,{state:y,setState:A,documentId:s});case"summary":return(0,r.jsx)(ez,{documentId:s});case"flowchart":return(0,r.jsx)(eF,{documentId:s});case"flashcards":return(0,r.jsx)(eB,{documentId:s});case"chapters":return(0,r.jsx)(eW,{documentId:s});case"transcript":return(0,r.jsx)(em.Z,{documentId:s});case"blueprint":return(0,r.jsx)(ew,{documentId:s})}})()})})]})})})]})})]})}},86450:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var r=t(95155);t(12115);var l=t(60760),a=t(1978);function n(e){let{isVisible:s,message:t="Personalizing your learning journey. Just a moment..."}=e;return(0,r.jsx)(l.N,{children:s&&(0,r.jsxs)(a.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},className:"fixed inset-0 z-[9999] flex flex-col items-center justify-center",style:{background:"rgba(255, 255, 255, 0)",backdropFilter:"blur(8px)",WebkitBackdropFilter:"blur(8px)"},onClick:e=>e.preventDefault(),children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-600 mb-8",children:"Cognimosity"}),(0,r.jsxs)("div",{className:"flex gap-5 mb-8",children:[(0,r.jsxs)("div",{className:"relative w-12 h-12",children:[(0,r.jsx)("svg",{viewBox:"0 0 80 80",className:"w-full h-full",children:(0,r.jsx)("circle",{cx:"40",cy:"40",r:"32",fill:"none",stroke:"#d3d3d3",strokeWidth:"10",strokeLinecap:"round",strokeLinejoin:"round",strokeDasharray:"150 50 150 50",strokeDashoffset:"75",style:{animation:"pathCircle 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"}})}),(0,r.jsx)("div",{className:"absolute w-2 h-2 bg-purple-600 rounded-full top-[37px] left-[19px] transform -translate-x-[18px] -translate-y-[18px]",style:{animation:"dotRect 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"}})]}),(0,r.jsxs)("div",{className:"relative w-12 h-12",children:[(0,r.jsx)("svg",{viewBox:"0 0 86 80",className:"w-full h-full",children:(0,r.jsx)("polygon",{points:"43 8 79 72 7 72",fill:"none",stroke:"#d3d3d3",strokeWidth:"10",strokeLinecap:"round",strokeLinejoin:"round",strokeDasharray:"145 76 145 76",strokeDashoffset:"0",style:{animation:"pathTriangle 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"}})}),(0,r.jsx)("div",{className:"absolute w-2 h-2 bg-purple-600 rounded-full top-[37px] left-[21px] transform -translate-x-[10px] -translate-y-[18px]",style:{animation:"dotTriangle 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"}})]}),(0,r.jsxs)("div",{className:"relative w-12 h-12",children:[(0,r.jsx)("svg",{viewBox:"0 0 80 80",className:"w-full h-full",children:(0,r.jsx)("rect",{x:"8",y:"8",width:"64",height:"64",fill:"none",stroke:"#d3d3d3",strokeWidth:"10",strokeLinecap:"round",strokeLinejoin:"round",strokeDasharray:"192 64 192 64",strokeDashoffset:"0",style:{animation:"pathRect 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"}})}),(0,r.jsx)("div",{className:"absolute w-2 h-2 bg-purple-600 rounded-full top-[37px] left-[19px] transform -translate-x-[18px] -translate-y-[18px]",style:{animation:"dotRect 3s cubic-bezier(0.785, 0.135, 0.15, 0.86) infinite"}})]})]}),(0,r.jsx)("div",{className:"text-base text-gray-700 text-center max-w-md px-4",children:t})]})})}},89852:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var r=t(95155),l=t(12115),a=t(53999);let n=l.forwardRef((e,s)=>{let{className:t,type:l,...n}=e;return(0,r.jsx)("input",{type:l,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:s,...n})});n.displayName="Input"},99474:(e,s,t)=>{"use strict";t.d(s,{T:()=>n});var r=t(95155),l=t(12115),a=t(53999);let n=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,r.jsx)("textarea",{className:(0,a.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:s,...l})});n.displayName="Textarea"}},e=>{var s=s=>e(e.s=s);e.O(0,[5661,9749,3464,2707,4838,6671,4871,3653,6821,1455,7609,2913,8441,1684,7358],()=>s(37125)),_N_E=e.O()}]);