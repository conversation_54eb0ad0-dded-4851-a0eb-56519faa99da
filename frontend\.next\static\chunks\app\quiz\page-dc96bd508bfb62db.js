(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6602],{15603:(e,t,r)=>{Promise.resolve().then(r.bind(r,29310))},16785:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},16891:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var a=r(95155),s=r(12115),i=r(28577),n=r(53999);let l=s.forwardRef((e,t)=>{let{className:r,children:s,...l}=e;return(0,a.jsxs)(i.bL,{ref:t,className:(0,n.cn)("relative overflow-hidden",r),...l,children:[(0,a.jsx)(i.LM,{className:"h-full w-full rounded-[inherit]",children:s}),(0,a.jsx)(o,{}),(0,a.jsx)(i.OK,{})]})});l.displayName="ScrollArea";let o=s.forwardRef((e,t)=>{let{className:r,orientation:s="vertical",...l}=e;return(0,a.jsx)(i.VM,{ref:t,orientation:s,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===s&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===s&&"h-2.5 border-t border-t-transparent p-[1px]",r),...l,children:(0,a.jsx)(i.lr,{className:"relative flex-1 rounded-full bg-border"})})});o.displayName="ScrollBar"},29310:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>G});var a=r(95155),s=r(12115),i=r(35695),n=r(97168),l=r(19946);let o=(0,l.A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var c=r(88482),d=r(99474),u=r(46081),m=r(63655),x="Progress",[h,f]=(0,u.A)(x),[p,g]=h(x),y=s.forwardRef((e,t)=>{var r,s,i,n;let{__scopeProgress:l,value:o=null,max:c,getValueLabel:d=N,...u}=e;(c||0===c)&&!z(c)&&console.error((r="".concat(c),s="Progress","Invalid prop `max` of value `".concat(r,"` supplied to `").concat(s,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let x=z(c)?c:100;null===o||k(o,x)||console.error((i="".concat(o),n="Progress","Invalid prop `value` of value `".concat(i,"` supplied to `").concat(n,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let h=k(o,x)?o:null,f=w(h)?d(h,x):void 0;return(0,a.jsx)(p,{scope:l,value:h,max:x,children:(0,a.jsx)(m.sG.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":w(h)?h:void 0,"aria-valuetext":f,role:"progressbar","data-state":b(h,x),"data-value":null!=h?h:void 0,"data-max":x,...u,ref:t})})});y.displayName=x;var v="ProgressIndicator",j=s.forwardRef((e,t)=>{var r;let{__scopeProgress:s,...i}=e,n=g(v,s);return(0,a.jsx)(m.sG.div,{"data-state":b(n.value,n.max),"data-value":null!==(r=n.value)&&void 0!==r?r:void 0,"data-max":n.max,...i,ref:t})});function N(e,t){return"".concat(Math.round(e/t*100),"%")}function b(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function w(e){return"number"==typeof e}function z(e){return w(e)&&!isNaN(e)&&e>0}function k(e,t){return w(e)&&!isNaN(e)&&e<=t&&e>=0}j.displayName=v;var S=r(53999);let C=s.forwardRef((e,t)=>{let{className:r,value:s,...i}=e;return(0,a.jsx)(y,{ref:t,className:(0,S.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",r),...i,children:(0,a.jsx)(j,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})});C.displayName=y.displayName;var A=r(5040),P=r(16785);let R=(0,l.A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),T=(0,l.A)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]);var E=r(40133),q=r(14186),I=r(1978),D=r(60760),M=r(16891),O=r(31886),_=r(53580),Q=r(61139);function L(e){let{documentId:t,onQuizStart:r,onQuizEnd:i}=e,[l,o]=(0,s.useState)([]),[u,m]=(0,s.useState)(0),[x,h]=(0,s.useState)([]),[f,p]=(0,s.useState)(!1),[g,y]=(0,s.useState)(!1),[v,j]=(0,s.useState)(0),[N,b]=(0,s.useState)(0),[w,z]=(0,s.useState)(0),[k,S]=(0,s.useState)(!1),[L,F]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e;return f&&!g&&(e=setInterval(()=>{b(Date.now()-v)},1e3)),()=>clearInterval(e)},[f,g,v]),(0,s.useEffect)(()=>{t&&B()},[t]);let $=e=>{let t=Math.floor(e/1e3),r=Math.floor(t/60);return"".concat(r.toString().padStart(2,"0"),":").concat((t%60).toString().padStart(2,"0"))},B=async()=>{if(!t){(0,_.oR)({title:"Error",description:"No document selected for quiz",variant:"destructive"});return}F(!0);try{let e=await O.quizApi.getOrGenerateQuiz(t);if(e.quizzes&&e.quizzes.length>0)o(e.quizzes),h(Array(e.quizzes.length).fill("")),e.generated?(0,_.oR)({title:"Success",description:"Quiz generated successfully!"}):(0,_.oR)({title:"Quiz Loaded",description:"Existing quiz loaded successfully!"});else throw Error("No quiz data received")}catch(e){console.error("Error loading/generating quiz:",e),(0,_.oR)({title:"Error",description:"Failed to load or generate quiz. Please try again.",variant:"destructive"})}finally{F(!1)}},Z=async()=>{if(!t){(0,_.oR)({title:"Error",description:"No document selected for quiz generation",variant:"destructive"});return}F(!0);try{let e=await O.quizApi.generateQuiz(t,5);if(e.quizzes&&e.quizzes.length>0)o(e.quizzes),h(Array(e.quizzes.length).fill("")),(0,_.oR)({title:"Success",description:"New quiz generated successfully!"});else throw Error("No quiz data received")}catch(e){console.error("Error generating quiz:",e),(0,_.oR)({title:"Error",description:"Failed to generate quiz. Please try again.",variant:"destructive"})}finally{F(!1)}},G=async()=>{p(!0),j(Date.now()),b(0);try{null==r||r()}catch(e){console.error("Failed to pause time tracking for quiz:",e)}},H=e=>{let t=[...x];t[u]=e,h(t)},U=()=>(0,Q.WS)(l,x),W=async()=>{S(!0);let e=U(),r=Math.floor(N/1e3);z(e),y(!0);try{null==i||i()}catch(e){console.error("Failed to resume time tracking after quiz:",e)}try{let a=JSON.parse(localStorage.getItem("user")||"{}");a.id&&t&&(await O.performanceApi.createPerformance({student:a.id,document:t,quiz_score:e,time_taken:r,remarks:"Quiz completed with ".concat(Math.round(e),"% score in ").concat($(N))}),(0,_.oR)({title:"Quiz Completed!",description:"Your score: ".concat(Math.round(e),"% - Performance saved successfully")}))}catch(t){console.error("Error saving performance:",t),(0,_.oR)({title:"Quiz Completed!",description:"Your score: ".concat(Math.round(e),"% - Note: Performance could not be saved"),variant:"destructive"})}finally{S(!1)}};if(0===l.length)return(0,a.jsx)("div",{className:"flex flex-col h-full",children:(0,a.jsx)(M.F,{className:"flex-1",children:(0,a.jsx)("div",{className:"p-6 space-y-6",children:(0,a.jsx)(I.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,a.jsxs)(c.Zp,{className:"border border-border bg-card",children:[(0,a.jsxs)(c.aR,{className:"text-center",children:[(0,a.jsx)(I.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"mx-auto mb-4",children:(0,a.jsx)("div",{className:"w-16 h-16 bg-primary rounded-full flex items-center justify-center",children:(0,a.jsx)(A.A,{className:"h-8 w-8 text-primary-foreground"})})}),(0,a.jsx)(c.ZB,{className:"text-2xl text-foreground",children:"Quiz Center"}),(0,a.jsx)(c.BT,{className:"text-base",children:t?L?"Creating your personalized quiz...":"Ready to test your knowledge? Generate a quiz based on your document content.":"Select a document to view or generate a quiz"})]}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[L?(0,a.jsxs)(I.P.div,{initial:{opacity:0},animate:{opacity:1},className:"flex flex-col items-center justify-center py-8 space-y-4",children:[(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-4 border-muted border-t-primary"})}),(0,a.jsx)(I.P.p,{animate:{opacity:[.5,1,.5]},transition:{duration:2,repeat:1/0},className:"text-sm text-muted-foreground",children:"Analyzing your document and crafting questions..."})]}):(0,a.jsxs)(I.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},className:"grid gap-3",children:[(0,a.jsxs)(n.$,{onClick:B,disabled:!t,variant:"outline",className:"w-full h-12",size:"lg",children:[(0,a.jsx)(P.A,{className:"h-5 w-5 mr-2"}),"Load Existing Quiz"]}),(0,a.jsxs)(n.$,{onClick:Z,disabled:!t,className:"w-full h-12",size:"lg",children:[(0,a.jsx)(R,{className:"h-5 w-5 mr-2"}),"Generate New Quiz"]})]}),!t&&(0,a.jsx)(I.P.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"text-sm text-muted-foreground mt-4 text-center bg-muted/50 p-3 rounded-lg border",children:"⚠️ Please select a document to generate a quiz."})]})]})})})})});if(g){let e=(0,Q.Fd)(w);return(0,a.jsx)("div",{className:"flex flex-col h-full",children:(0,a.jsx)(M.F,{className:"flex-1",children:(0,a.jsx)("div",{className:"p-6 space-y-6",children:(0,a.jsx)(I.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5},children:(0,a.jsxs)(c.Zp,{className:"border border-border bg-card",children:[(0,a.jsxs)(c.aR,{className:"text-center",children:[(0,a.jsx)(I.P.div,{initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0},transition:{delay:.2,type:"spring",stiffness:200},className:"mx-auto mb-4",children:(0,a.jsx)("div",{className:"w-20 h-20 bg-primary rounded-full flex items-center justify-center",children:(0,a.jsx)(T,{className:"h-10 w-10 text-primary-foreground"})})}),(0,a.jsxs)(I.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:[(0,a.jsx)(c.ZB,{className:"text-3xl text-foreground",children:"\uD83C\uDF89 Quiz Completed!"}),(0,a.jsxs)(c.BT,{className:"text-lg font-medium ".concat(e.color),children:[e.level," - ",e.description]})]})]}),(0,a.jsxs)(c.Wu,{className:"space-y-6",children:[(0,a.jsxs)(I.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},className:"grid grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-muted/50 rounded-lg border",children:[(0,a.jsxs)(I.P.p,{initial:{scale:0},animate:{scale:1},transition:{delay:.5,type:"spring",stiffness:300},className:"text-4xl font-bold text-foreground mb-2",children:[Math.round(w),"%"]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground font-medium",children:"Your Score"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-muted/50 rounded-lg border",children:[(0,a.jsx)(I.P.p,{initial:{scale:0},animate:{scale:1},transition:{delay:.6,type:"spring",stiffness:300},className:"text-4xl font-bold text-foreground mb-2",children:$(N)}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground font-medium",children:"Time Taken"})]})]}),(0,a.jsxs)(I.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7},className:"flex gap-3",children:[(0,a.jsxs)(n.$,{onClick:()=>{m(0),h(Array(l.length).fill("")),p(!1),y(!1),j(0),b(0),z(0)},variant:"outline",className:"flex-1 h-12 border hover:bg-muted transition-all duration-200",size:"lg",children:[(0,a.jsx)(E.A,{className:"h-5 w-5 mr-2"}),"Retake Quiz"]}),(0,a.jsxs)(n.$,{onClick:Z,className:"flex-1 h-12",size:"lg",children:[(0,a.jsx)(R,{className:"h-5 w-5 mr-2"}),"New Quiz"]})]})]})]})})})})})}return(0,a.jsx)("div",{className:"flex flex-col h-full",children:(0,a.jsx)(M.F,{className:"flex-1",children:(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsxs)(I.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"bg-muted/50 p-4 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-primary rounded-full flex items-center justify-center",children:(0,a.jsx)(q.A,{className:"h-5 w-5 text-primary-foreground"})}),(0,a.jsx)("span",{className:"font-mono text-xl font-bold text-foreground",children:$(N)})]}),(0,a.jsxs)("div",{className:"text-sm font-medium bg-background px-3 py-1 rounded-full border",children:["Question ",u+1," of ",l.length]})]}),(0,a.jsx)(C,{value:(u+1)/l.length*100,className:"h-3"})]}),(0,a.jsx)(D.N,{mode:"wait",children:f?(0,a.jsx)(I.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:(0,a.jsxs)(c.Zp,{className:"border border-border bg-card",children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(I.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.1},children:(0,a.jsxs)(c.ZB,{className:"text-xl text-foreground",children:["Question ",u+1]})})}),(0,a.jsxs)(c.Wu,{className:"space-y-6",children:[(0,a.jsx)(I.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-muted/50 p-4 rounded-lg border",children:(0,a.jsx)("p",{className:"text-lg leading-relaxed",children:l[u].question})}),(0,a.jsx)(I.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:(0,a.jsx)(d.T,{placeholder:"Type your detailed answer here... \uD83D\uDCDD",value:x[u],onChange:e=>H(e.target.value),className:"min-h-[120px] text-base border focus:border-primary bg-background"})}),(0,a.jsxs)(I.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4},className:"flex justify-between gap-3",children:[(0,a.jsx)(n.$,{onClick:()=>{u>0&&m(u-1)},disabled:0===u,variant:"outline",className:"h-11 border hover:bg-muted disabled:opacity-50",size:"lg",children:"← Previous"}),u===l.length-1?(0,a.jsx)(n.$,{onClick:W,disabled:k||!x[u].trim(),className:"h-11",size:"lg",children:k?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-primary-foreground border-t-transparent mr-2"}),"Submitting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(T,{className:"h-5 w-5 mr-2"}),"Submit Quiz"]})}):(0,a.jsx)(n.$,{onClick:()=>{u<l.length-1&&m(u+1)},disabled:!x[u].trim(),className:"h-11",size:"lg",children:"Next →"})]})]})]})},"question-".concat(u)):(0,a.jsx)(I.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},transition:{duration:.3},children:(0,a.jsxs)(c.Zp,{className:"border border-border bg-card",children:[(0,a.jsxs)(c.aR,{className:"text-center",children:[(0,a.jsx)(I.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"mx-auto mb-4",children:(0,a.jsx)("div",{className:"w-16 h-16 bg-primary rounded-full flex items-center justify-center",children:(0,a.jsx)(P.A,{className:"h-8 w-8 text-primary-foreground"})})}),(0,a.jsx)(c.ZB,{className:"text-2xl text-foreground",children:"Ready to Start?"}),(0,a.jsxs)(c.BT,{className:"text-lg",children:["You have ",l.length," questions to answer. Take your time and good luck! \uD83C\uDF40"]})]}),(0,a.jsx)(c.Wu,{children:(0,a.jsxs)(n.$,{onClick:G,className:"w-full h-12",size:"lg",children:[(0,a.jsx)(R,{className:"h-5 w-5 mr-2"}),"Start Quiz"]})})]})},"start-screen")})]})})})}var F=r(35789),$=r(99304),B=r(79750);function Z(){let e=(0,i.useSearchParams)(),t=(0,i.useRouter)(),{theme:r}=(0,$.D)(),s=e.get("documentId")?parseInt(e.get("documentId")):void 0,{startQuiz:l,endQuiz:c,isActive:d,isPaused:u}=(0,B.U)({documentId:s,enabled:!!s,isProcessingComplete:!0,onQuizStart:()=>console.log("Quiz started - timer paused"),onQuizEnd:()=>console.log("Quiz ended - timer resumed")});return s?(0,a.jsx)(F.b,{children:(0,a.jsxs)("div",{className:"h-[calc(100vh-65px)] flex flex-col bg-background text-foreground",children:[(0,a.jsx)("div",{className:"border-b border-border p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(n.$,{variant:"ghost",size:"sm",onClick:()=>{s?t.push("/process?documentId=".concat(s)):t.push("/")},className:"gap-2",children:[(0,a.jsx)(o,{className:"h-4 w-4"}),"Back to Document"]}),(0,a.jsx)("h1",{className:"text-xl font-semibold",children:"Quiz"})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,a.jsx)(L,{documentId:s,onQuizStart:l,onQuizEnd:c})})]})}):(0,a.jsx)(F.b,{children:(0,a.jsx)("div",{className:"h-[calc(100vh-65px)] flex items-center justify-center",children:(0,a.jsxs)("div",{className:"rounded-lg p-8 text-center ".concat("light"===r?"bg-white border border-black":"bg-neutral-800"),children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"No Document Selected"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please select a document to take a quiz."}),(0,a.jsx)(n.$,{onClick:()=>t.push("/"),className:"bg-purple-600 hover:bg-purple-700",children:"Go to Home"})]})})})}function G(){return(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)(F.b,{children:(0,a.jsx)("div",{className:"h-[calc(100vh-65px)] flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Loading quiz..."})]})})}),children:(0,a.jsx)(Z,{})})}},35789:(e,t,r)=>{"use strict";r.d(t,{b:()=>p});var a=r(95155),s=r(12115),i=r(35695),n=r(9685),l=r(97168),o=r(74783),c=r(34835),d=r(94788),u=r(17951),m=r(62098),x=r(93509),h=r(67133),f=r(99304);function p(e){let{children:t,showQuizButton:r=!1,showUpgradeButton:p=!1,showUserEmail:g=!1,documentId:y=null}=e,[v,j]=(0,s.useState)(!1),{theme:N,setTheme:b}=(0,f.D)(),[w,z]=(0,s.useState)(!1),[k,S]=(0,s.useState)(""),C=(0,i.useRouter)();(0,s.useEffect)(()=>{let e="true"===localStorage.getItem("isLoggedIn"),t=localStorage.getItem("username");e&&t&&(z(!0),S(t))},[]);let A=()=>{localStorage.removeItem("isLoggedIn"),localStorage.removeItem("username"),z(!1),S(""),C.push("/")};return(0,a.jsxs)("div",{className:"flex h-screen bg-background text-foreground overflow-hidden",children:[(0,a.jsx)(n.B,{isOpen:v,setIsOpen:j,isLoggedIn:w,username:k,onLogout:A}),(0,a.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,a.jsxs)("div",{className:"p-4 flex justify-between items-center",children:[(0,a.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-10 w-10 rounded-full",onClick:()=>j(!0),children:(0,a.jsx)(o.A,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[g&&w&&(0,a.jsxs)(h.rI,{children:[(0,a.jsx)(h.ty,{asChild:!0,children:(0,a.jsx)(l.$,{className:"bg-purple-600 hover:bg-purple-700",children:k})}),(0,a.jsx)(h.SQ,{align:"end",children:(0,a.jsxs)(h._2,{onClick:A,children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Log out"})]})})]}),r&&y&&(0,a.jsxs)(l.$,{onClick:()=>C.push("/quiz?documentId=".concat(y)),variant:"outline",size:"sm",className:"gap-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),"Quiz"]}),p&&(0,a.jsxs)(l.$,{onClick:()=>C.push("/subscription"),className:"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0",size:"sm",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Upgrade"]}),(0,a.jsx)(l.$,{variant:"ghost",size:"icon",className:"h-10 w-10 rounded-full",onClick:()=>b("dark"===N?"light":"dark"),children:"dark"===N?(0,a.jsx)(m.A,{className:"h-5 w-5"}):(0,a.jsx)(x.A,{className:"h-5 w-5"})})]})]}),t]})]})}},53580:(e,t,r)=>{"use strict";r.d(t,{oR:()=>d}),r(12115);let a=0,s=new Map,i=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],o={toasts:[]};function c(e){o=n(o,e),l.forEach(e=>{e(o)})}function d(e){let{...t}=e,r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}},61139:(e,t,r)=>{"use strict";function a(e,t){if(0===e.length||0===t.length)return 0;let r=0;return e.forEach((e,a)=>{var s;let i=(null===(s=t[a])||void 0===s?void 0:s.toLowerCase().trim())||"",n=e.answer.toLowerCase().trim();i&&(i.includes(n)||n.includes(i)||function(e,t){let r=e.toLowerCase().split(/\s+/).filter(e=>e.length>2),a=t.toLowerCase().split(/\s+/).filter(e=>e.length>2);return 0===r.length||0===a.length?0:r.filter(e=>a.includes(e)).length/Math.max(r.length,a.length)}(i,n)>.7)&&r++}),r/e.length*100}function s(e){let t=Math.floor(e/60),r=e%60;return t>0?"".concat(t,"m ").concat(r,"s"):"".concat(r,"s")}function i(e){return e>=90?{level:"Excellent",color:"text-foreground",description:"Outstanding performance!"}:e>=80?{level:"Good",color:"text-foreground",description:"Well done!"}:e>=70?{level:"Average",color:"text-muted-foreground",description:"Room for improvement"}:e>=60?{level:"Below Average",color:"text-muted-foreground",description:"Needs more practice"}:{level:"Poor",color:"text-muted-foreground",description:"Requires significant improvement"}}r.d(t,{Fd:()=>i,Uj:()=>s,WS:()=>a})},79750:(e,t,r)=>{"use strict";r.d(t,{U:()=>n});var a=r(12115),s=r(35695),i=r(49509);function n(){let{documentId:e,enabled:t=!0,isProcessingComplete:r=!1,onQuizStart:n,onQuizEnd:l}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=(0,a.useRef)(null),c=(0,a.useRef)(!1),d=(0,a.useRef)(!1),u=(0,a.useRef)(e),m=(0,a.useRef)(null);(0,s.useRouter)();let x=i.env.NEXT_PUBLIC_API_URL||"http://localhost:8000/api",h=()=>{let e=localStorage.getItem("token");return{"Content-Type":"application/json",Authorization:"Token ".concat(e)}},f=(0,a.useCallback)(async()=>{if(t&&e&&r&&!c.current)try{if(!localStorage.getItem("token"))return;let t=await fetch("".concat(x,"/users/platform-time/start/"),{method:"POST",headers:h(),body:JSON.stringify({document_id:e})});if(t.ok){let r=await t.json();o.current=r.session_id,c.current=!0,d.current=!1,u.current=e,m.current=new Date,console.log("Document time session started:",r.session_id,r.message)}}catch(e){console.error("Error starting document time session:",e)}},[e,t,r]),p=(0,a.useCallback)(async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"manual";if(e&&c.current&&!d.current)try{if(!localStorage.getItem("token"))return;(await fetch("".concat(x,"/users/platform-time/pause/"),{method:"POST",headers:h(),body:JSON.stringify({document_id:e,reason:t})})).ok&&(d.current=!0,console.log("Document time session paused: ".concat(t)))}catch(e){console.error("Error pausing document time session:",e)}},[e]),g=(0,a.useCallback)(async()=>{if(e&&c.current&&d.current)try{if(!localStorage.getItem("token"))return;(await fetch("".concat(x,"/users/platform-time/resume/"),{method:"POST",headers:h(),body:JSON.stringify({document_id:e})})).ok&&(d.current=!1,console.log("Document time session resumed"))}catch(e){console.error("Error resuming document time session:",e)}},[e]),y=(0,a.useCallback)(async()=>{if(e&&c.current)try{if(!localStorage.getItem("token"))return;await fetch("".concat(x,"/users/platform-time/end/"),{method:"POST",headers:h(),body:JSON.stringify({document_id:e})}),o.current=null,c.current=!1,d.current=!1,m.current=null,console.log("Document time session ended")}catch(e){console.error("Error ending document time session:",e)}},[e]),v=(0,a.useCallback)(()=>{p("quiz"),null==n||n()},[p,n]),j=(0,a.useCallback)(()=>{g(),null==l||l()},[g,l]);return(0,a.useEffect)(()=>{if(!t||!e||!r)return;u.current!==e&&(c.current&&y(),u.current=e),f();let a=()=>{y()},s=()=>{"/process"!==window.location.pathname&&y()};return window.addEventListener("beforeunload",a),window.addEventListener("popstate",s),()=>{window.removeEventListener("beforeunload",a),window.removeEventListener("popstate",s),y()}},[e,t,r,f,y]),{sessionId:o.current,isActive:c.current,isPaused:d.current,sessionStartTime:m.current,startQuiz:v,endQuiz:j,pauseSession:p,resumeSession:g,endSession:y}}},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>l,wL:()=>u});var a=r(95155),s=r(12115),i=r(53999);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});n.displayName="Card";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...s})});l.displayName="CardHeader";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});o.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",r),...s})});d.displayName="CardContent";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",r),...s})});u.displayName="CardFooter"},94788:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},99474:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var a=r(95155),s=r(12115),i=r(53999);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...s})});n.displayName="Textarea"}},e=>{var t=t=>e(e.s=t);e.O(0,[9749,3464,2707,4838,3653,1455,7609,8441,1684,7358],()=>t(15603)),_N_E=e.O()}]);