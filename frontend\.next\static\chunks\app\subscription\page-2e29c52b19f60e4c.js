(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6626],{35659:(e,s,r)=>{Promise.resolve().then(r.bind(r,54703))},35789:(e,s,r)=>{"use strict";r.d(s,{b:()=>j});var l=r(95155),a=r(12115),t=r(35695),n=r(9685),c=r(97168),i=r(74783),d=r(34835),o=r(94788),x=r(17951),m=r(62098),u=r(93509),h=r(67133),p=r(99304);function j(e){let{children:s,showQuizButton:r=!1,showUpgradeButton:j=!1,showUserEmail:f=!1,documentId:g=null}=e,[N,v]=(0,a.useState)(!1),{theme:b,setTheme:w}=(0,p.D)(),[y,k]=(0,a.useState)(!1),[A,C]=(0,a.useState)(""),$=(0,t.useRouter)();(0,a.useEffect)(()=>{let e="true"===localStorage.getItem("isLoggedIn"),s=localStorage.getItem("username");e&&s&&(k(!0),C(s))},[]);let R=()=>{localStorage.removeItem("isLoggedIn"),localStorage.removeItem("username"),k(!1),C(""),$.push("/")};return(0,l.jsxs)("div",{className:"flex h-screen bg-background text-foreground overflow-hidden",children:[(0,l.jsx)(n.B,{isOpen:N,setIsOpen:v,isLoggedIn:y,username:A,onLogout:R}),(0,l.jsxs)("div",{className:"flex-1 overflow-auto",children:[(0,l.jsxs)("div",{className:"p-4 flex justify-between items-center",children:[(0,l.jsx)(c.$,{variant:"ghost",size:"icon",className:"h-10 w-10 rounded-full",onClick:()=>v(!0),children:(0,l.jsx)(i.A,{className:"h-5 w-5"})}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[f&&y&&(0,l.jsxs)(h.rI,{children:[(0,l.jsx)(h.ty,{asChild:!0,children:(0,l.jsx)(c.$,{className:"bg-purple-600 hover:bg-purple-700",children:A})}),(0,l.jsx)(h.SQ,{align:"end",children:(0,l.jsxs)(h._2,{onClick:R,children:[(0,l.jsx)(d.A,{className:"mr-2 h-4 w-4"}),(0,l.jsx)("span",{children:"Log out"})]})})]}),r&&g&&(0,l.jsxs)(c.$,{onClick:()=>$.push("/quiz?documentId=".concat(g)),variant:"outline",size:"sm",className:"gap-2",children:[(0,l.jsx)(o.A,{className:"h-4 w-4"}),"Quiz"]}),j&&(0,l.jsxs)(c.$,{onClick:()=>$.push("/subscription"),className:"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0",size:"sm",children:[(0,l.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Upgrade"]}),(0,l.jsx)(c.$,{variant:"ghost",size:"icon",className:"h-10 w-10 rounded-full",onClick:()=>w("dark"===b?"light":"dark"),children:"dark"===b?(0,l.jsx)(m.A,{className:"h-5 w-5"}):(0,l.jsx)(u.A,{className:"h-5 w-5"})})]})]}),s]})]})}},53999:(e,s,r)=>{"use strict";r.d(s,{cn:()=>t});var l=r(52596),a=r(39688);function t(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,l.$)(s))}},54703:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var l=r(95155),a=r(97168),t=r(88482),n=r(5196),c=r(35695),i=r(35789);function d(){let e=(0,c.useRouter)();return(0,l.jsx)(i.b,{children:(0,l.jsxs)("div",{className:"max-w-6xl mx-auto py-8 px-4",children:[(0,l.jsx)(a.$,{variant:"ghost",onClick:()=>e.back(),className:"mb-8",children:"← Back"}),(0,l.jsx)("h1",{className:"text-3xl font-bold text-center mb-4",children:"Choose Your Plan"}),(0,l.jsx)("p",{className:"text-center text-muted-foreground mb-12",children:"Select the plan that best fits your learning needs"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,l.jsxs)(t.Zp,{className:"border-border bg-card hover:border-purple-500 transition-all duration-300",children:[(0,l.jsxs)(t.aR,{children:[(0,l.jsx)(t.ZB,{className:"text-xl",children:"Daily Plan"}),(0,l.jsx)(t.BT,{children:"Perfect for quick learning sessions"}),(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("span",{className:"text-3xl font-bold",children:"$1.99"}),(0,l.jsx)("span",{className:"text-muted-foreground",children:" / day"})]})]}),(0,l.jsx)(t.Wu,{children:(0,l.jsxs)("ul",{className:"space-y-2",children:[(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,l.jsx)("span",{children:"Unlimited uploads"})]}),(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,l.jsx)("span",{children:"Basic AI features"})]}),(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,l.jsx)("span",{children:"24-hour access"})]})]})}),(0,l.jsx)(t.wL,{children:(0,l.jsx)(a.$,{className:"w-full bg-purple-600 hover:bg-purple-700",children:"Subscribe Now"})})]}),(0,l.jsxs)(t.Zp,{className:"border-purple-500 bg-card relative",children:[(0,l.jsx)("div",{className:"absolute top-0 right-0 bg-purple-500 text-white text-xs px-3 py-1 rounded-bl-lg rounded-tr-lg",children:"POPULAR"}),(0,l.jsxs)(t.aR,{children:[(0,l.jsx)(t.ZB,{className:"text-xl",children:"Monthly Plan"}),(0,l.jsx)(t.BT,{children:"Our most popular option"}),(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("span",{className:"text-3xl font-bold",children:"$19.99"}),(0,l.jsx)("span",{className:"text-muted-foreground",children:" / month"})]})]}),(0,l.jsx)(t.Wu,{children:(0,l.jsxs)("ul",{className:"space-y-2",children:[(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,l.jsx)("span",{children:"Unlimited uploads"})]}),(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,l.jsx)("span",{children:"Advanced AI features"})]}),(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,l.jsx)("span",{children:"5 custom spaces"})]}),(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,l.jsx)("span",{children:"Priority support"})]})]})}),(0,l.jsx)(t.wL,{children:(0,l.jsx)(a.$,{className:"w-full bg-purple-600 hover:bg-purple-700",children:"Subscribe Now"})})]}),(0,l.jsxs)(t.Zp,{className:"border-border bg-card hover:border-purple-500 transition-all duration-300",children:[(0,l.jsxs)(t.aR,{children:[(0,l.jsx)(t.ZB,{className:"text-xl",children:"Yearly Plan"}),(0,l.jsx)(t.BT,{children:"Best value for committed learners"}),(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("span",{className:"text-3xl font-bold",children:"$199.99"}),(0,l.jsx)("span",{className:"text-muted-foreground",children:" / year"}),(0,l.jsx)("div",{className:"text-sm text-purple-500 font-medium",children:"Save $40"})]})]}),(0,l.jsx)(t.Wu,{children:(0,l.jsxs)("ul",{className:"space-y-2",children:[(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,l.jsx)("span",{children:"Everything in Monthly"})]}),(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,l.jsx)("span",{children:"Unlimited spaces"})]}),(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,l.jsx)("span",{children:"Advanced analytics"})]}),(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-5 w-5 text-purple-500 mr-2"}),(0,l.jsx)("span",{children:"Dedicated support"})]})]})}),(0,l.jsx)(t.wL,{children:(0,l.jsx)(a.$,{className:"w-full bg-purple-600 hover:bg-purple-700",children:"Subscribe Now"})})]})]})]})})}},88482:(e,s,r)=>{"use strict";r.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>i,Zp:()=>n,aR:()=>c,wL:()=>x});var l=r(95155),a=r(12115),t=r(53999);let n=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,l.jsx)("div",{ref:s,className:(0,t.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});n.displayName="Card";let c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,l.jsx)("div",{ref:s,className:(0,t.cn)("flex flex-col space-y-1.5 p-6",r),...a})});c.displayName="CardHeader";let i=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,l.jsx)("div",{ref:s,className:(0,t.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});i.displayName="CardTitle";let d=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,l.jsx)("div",{ref:s,className:(0,t.cn)("text-sm text-muted-foreground",r),...a})});d.displayName="CardDescription";let o=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,l.jsx)("div",{ref:s,className:(0,t.cn)("p-6 pt-0",r),...a})});o.displayName="CardContent";let x=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,l.jsx)("div",{ref:s,className:(0,t.cn)("flex items-center p-6 pt-0",r),...a})});x.displayName="CardFooter"},94788:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});let l=(0,r(19946).A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},97168:(e,s,r)=>{"use strict";r.d(s,{$:()=>d});var l=r(95155),a=r(12115),t=r(99708),n=r(74466),c=r(53999);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,s)=>{let{className:r,variant:a,size:n,asChild:d=!1,...o}=e,x=d?t.DX:"button";return(0,l.jsx)(x,{className:(0,c.cn)(i({variant:a,size:n,className:r})),ref:s,...o})});d.displayName="Button"}},e=>{var s=s=>e(e.s=s);e.O(0,[9749,2707,4838,7609,8441,1684,7358],()=>s(35659)),_N_E=e.O()}]);