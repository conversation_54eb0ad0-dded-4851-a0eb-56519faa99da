"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7330],{22638:(e,t,r)=>{r.d(t,{default:()=>aA});class a{constructor(e,t,r){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=t,this.end=r}static range(e,t){return t?e&&e.loc&&t.loc&&e.loc.lexer===t.loc.lexer?new a(e.loc.lexer,e.loc.start,t.loc.end):null:e&&e.loc}}class n{constructor(e,t){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=e,this.loc=t}range(e,t){return new n(t,a.range(this,e))}}class i{constructor(e,t){this.name=void 0,this.position=void 0,this.length=void 0,this.rawMessage=void 0;var r="KaTeX parse error: "+e,a=t&&t.loc;if(a&&a.start<=a.end){var n,s,o,l=a.lexer.input;n=a.start,s=a.end,n===l.length?r+=" at end of input: ":r+=" at position "+(n+1)+": ";var h=l.slice(n,s).replace(/[^]/g,"$&̲");r+=(n>15?"…"+l.slice(n-15,n):l.slice(0,n))+h+(s+15<l.length?l.slice(s,s+15)+"…":l.slice(s))}var m=Error(r);return m.name="ParseError",m.__proto__=i.prototype,m.position=n,null!=n&&null!=s&&(m.length=s-n),m.rawMessage=e,m}}i.prototype.__proto__=Error.prototype;var s,o,l=/([A-Z])/g,h={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},m=/[&><"']/g,c=function e(t){return"ordgroup"===t.type||"color"===t.type?1===t.body.length?e(t.body[0]):t:"font"===t.type?e(t.body):t},p=function(e){if(!e)throw Error("Expected non-null, but got "+String(e));return e},u={contains:function(e,t){return -1!==e.indexOf(t)},deflt:function(e,t){return void 0===e?t:e},escape:function(e){return String(e).replace(m,e=>h[e])},hyphenate:function(e){return e.replace(l,"-$1").toLowerCase()},getBaseElem:c,isCharacterBox:function(e){var t=c(e);return"mathord"===t.type||"textord"===t.type||"atom"===t.type},protocolFromUrl:function(e){var t=/^[\x00-\x20]*([^\\/#?]*?)(:|&#0*58|&#x0*3a|&colon)/i.exec(e);return t?":"===t[2]&&/^[a-zA-Z][a-zA-Z0-9+\-.]*$/.test(t[1])?t[1].toLowerCase():null:"_relative"}},d={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:e=>"#"+e},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(e,t)=>(t.push(e),t)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:e=>Math.max(0,e),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:e=>Math.max(0,e),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:e=>Math.max(0,e),cli:"-e, --max-expand <n>",cliProcessor:e=>"Infinity"===e?1/0:parseInt(e)},globalGroup:{type:"boolean",cli:!1}};class g{constructor(e){for(var t in this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,e=e||{},d)if(d.hasOwnProperty(t)){var r=d[t];this[t]=void 0!==e[t]?r.processor?r.processor(e[t]):e[t]:function(e){if(e.default)return e.default;var t=e.type,r=Array.isArray(t)?t[0]:t;if("string"!=typeof r)return r.enum[0];switch(r){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}(r)}}reportNonstrict(e,t,r){var a=this.strict;if("function"==typeof a&&(a=a(e,t,r)),a&&"ignore"!==a){if(!0===a||"error"===a)throw new i("LaTeX-incompatible input and strict mode is set to 'error': "+(t+" [")+e+"]",r);"warn"===a?"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" [")+e+"]"):"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+a+"': "+t+" [")+e+"]")}}useStrictBehavior(e,t,r){var a=this.strict;if("function"==typeof a)try{a=a(e,t,r)}catch(e){a="error"}return!!a&&"ignore"!==a&&(!0===a||"error"===a||("warn"===a?("undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" [")+e+"]"),!1):("undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+a+"': "+t+" [")+e+"]"),!1)))}isTrusted(e){if(e.url&&!e.protocol){var t=u.protocolFromUrl(e.url);if(null==t)return!1;e.protocol=t}return!!("function"==typeof this.trust?this.trust(e):this.trust)}}class f{constructor(e,t,r){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=t,this.cramped=r}sup(){return v[b[this.id]]}sub(){return v[y[this.id]]}fracNum(){return v[x[this.id]]}fracDen(){return v[w[this.id]]}cramp(){return v[k[this.id]]}text(){return v[S[this.id]]}isTight(){return this.size>=2}}var v=[new f(0,0,!1),new f(1,0,!0),new f(2,1,!1),new f(3,1,!0),new f(4,2,!1),new f(5,2,!0),new f(6,3,!1),new f(7,3,!0)],b=[4,5,4,5,6,7,6,7],y=[5,5,5,5,7,7,7,7],x=[2,3,4,5,6,7,6,7],w=[3,3,5,5,7,7,7,7],k=[1,1,3,3,5,5,7,7],S=[0,1,2,3,2,3,2,3],M={DISPLAY:v[0],TEXT:v[2],SCRIPT:v[4],SCRIPTSCRIPT:v[6]},z=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}],A=[];function T(e){for(var t=0;t<A.length;t+=2)if(e>=A[t]&&e<=A[t+1])return!0;return!1}z.forEach(e=>e.blocks.forEach(e=>A.push(...e)));var B=function(e,t,r){t*=1e3;var a,n,i,s,o,l,h="";switch(e){case"sqrtMain":h="M95,"+(622+(a=t)+80)+"\nc-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14\nc0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54\nc44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10\ns173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429\nc69,-144,104.5,-217.7,106.5,-221\nl"+a/2.075+" -"+a+"\nc5.3,-9.3,12,-14,20,-14\nH400000v"+(40+a)+"H845.2724\ns-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7\nc-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z\nM"+(834+a)+" 80h400000v"+(40+a)+"h-400000z";break;case"sqrtSize1":h="M263,"+(601+(n=t)+80)+"c0.7,0,18,39.7,52,119\nc34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120\nc340,-704.7,510.7,-1060.3,512,-1067\nl"+n/2.084+" -"+n+"\nc4.7,-7.3,11,-11,19,-11\nH40000v"+(40+n)+"H1012.3\ns-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232\nc-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1\ns-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26\nc-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z\nM"+(1001+n)+" 80h400000v"+(40+n)+"h-400000z";break;case"sqrtSize2":h="M983 "+(10+(i=t)+80)+"\nl"+i/3.13+" -"+i+"\nc4,-6.7,10,-10,18,-10 H400000v"+(40+i)+"\nH1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7\ns-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744\nc-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30\nc26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722\nc56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5\nc53.7,-170.3,84.5,-266.8,92.5,-289.5z\nM"+(1001+i)+" 80h400000v"+(40+i)+"h-400000z";break;case"sqrtSize3":h="M424,"+(2398+(s=t)+80)+"\nc-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514\nc0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20\ns-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121\ns209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081\nl"+s/4.223+" -"+s+"c4,-6.7,10,-10,18,-10 H400000\nv"+(40+s)+"H1014.6\ns-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185\nc-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2z M"+(1001+s)+" 80\nh400000v"+(40+s)+"h-400000z";break;case"sqrtSize4":h="M473,"+(2713+(o=t)+80)+"\nc339.3,-1799.3,509.3,-2700,510,-2702 l"+o/5.298+" -"+o+"\nc3.3,-7.3,9.3,-11,18,-11 H400000v"+(40+o)+"H1017.7\ns-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200\nc0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26\ns76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,\n606zM"+(1001+o)+" 80h400000v"+(40+o)+"H1017.7z";break;case"sqrtTall":h="M702 "+((l=t)+80)+"H400000"+(40+l)+"\nH742v"+(r-54-80-l)+"l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1\nh-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170\nc-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667\n219 661 l218 661zM702 80H400000v"+(40+l)+"H742z"}return h},C=function(e,t){switch(e){case"⎜":return"M291 0 H417 V"+t+" H291z M291 0 H417 V"+t+" H291z";case"∣":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z";case"∥":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z"+("M367 0 H410 V"+t+" H367z M367 0 H410 V")+t+" H367z";case"⎟":return"M457 0 H583 V"+t+" H457z M457 0 H583 V"+t+" H457z";case"⎢":return"M319 0 H403 V"+t+" H319z M319 0 H403 V"+t+" H319z";case"⎥":return"M263 0 H347 V"+t+" H263z M263 0 H347 V"+t+" H263z";case"⎪":return"M384 0 H504 V"+t+" H384z M384 0 H504 V"+t+" H384z";case"⏐":return"M312 0 H355 V"+t+" H312z M312 0 H355 V"+t+" H312z";case"‖":return"M257 0 H300 V"+t+" H257z M257 0 H300 V"+t+" H257z"+("M478 0 H521 V"+t+" H478z M478 0 H521 V")+t+" H478z";default:return""}},q={doubleleftarrow:"M262 157\nl10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3\n 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28\n 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5\nc2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5\n 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87\n-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7\n-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z\nm8 0v40h399730v-40zm0 194v40h399730v-40z",doublerightarrow:"M399738 392l\n-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5\n 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88\n-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68\n-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18\n-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782\nc-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3\n-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z",leftarrow:"M400000 241H110l3-3c68.7-52.7 113.7-120\n 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8\n-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247\nc-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208\n 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3\n 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202\n l-3-3h399890zM100 241v40h399900v-40z",leftbrace:"M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117\n-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7\n 5-6 9-10 13-.7 1-7.3 1-20 1H6z",leftbraceunder:"M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13\n 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688\n 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7\n-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z",leftgroup:"M400000 80\nH435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0\n 435 0h399565z",leftgroupunder:"M400000 262\nH435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219\n 435 219h399565z",leftharpoon:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3\n-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5\n-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7\n-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z",leftharpoonplus:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5\n 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3\n-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7\n-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z\nm0 0v40h400000v-40z",leftharpoondown:"M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333\n 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5\n 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667\n-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z",leftharpoondownplus:"M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12\n 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7\n-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0\nv40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z",lefthook:"M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5\n-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3\n-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21\n 71.5 23h399859zM103 281v-40h399897v40z",leftlinesegment:"M40 281 V428 H0 V94 H40 V241 H400000 v40z\nM40 281 V428 H0 V94 H40 V241 H400000 v40z",leftmapsto:"M40 281 V448H0V74H40V241H400000v40z\nM40 281 V448H0V74H40V241H400000v40z",leftToFrom:"M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23\n-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8\nc28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3\n 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z",longequal:"M0 50 h400000 v40H0z m0 194h40000v40H0z\nM0 50 h400000 v40H0z m0 194h40000v40H0z",midbrace:"M200428 334\nc-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14\n-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7\n 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11\n 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z",midbraceunder:"M199572 214\nc100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14\n 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3\n 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0\n-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z",oiintSize1:"M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6\n-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z\nm368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8\n60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z",oiintSize2:"M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8\n-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z\nm502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2\nc0 110 84 276 504 276s502.4-166 502.4-276z",oiiintSize1:"M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6\n-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z\nm525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0\n85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z",oiiintSize2:"M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8\n-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z\nm770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1\nc0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z",rightarrow:"M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z",rightbrace:"M400000 542l\n-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5\ns-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1\nc124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z",rightbraceunder:"M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3\n 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237\n-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z",rightgroup:"M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0\n 3-1 3-3v-38c-76-158-257-219-435-219H0z",rightgroupunder:"M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18\n 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z",rightharpoon:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3\n-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2\n-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58\n 69.2 92 94.5zm0 0v40h399900v-40z",rightharpoonplus:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11\n-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7\n 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z\nm0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z",rightharpoondown:"M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8\n 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5\n-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95\n-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z",rightharpoondownplus:"M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8\n 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3\n 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3\n-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z\nm0-194v40h400000v-40zm0 0v40h400000v-40z",righthook:"M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3\n 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0\n-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21\n 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z",rightlinesegment:"M399960 241 V94 h40 V428 h-40 V281 H0 v-40z\nM399960 241 V94 h40 V428 h-40 V281 H0 v-40z",rightToFrom:"M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23\n 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32\n-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142\n-167z M100 147v40h399900v-40zM0 341v40h399900v-40z",twoheadleftarrow:"M0 167c68 40\n 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69\n-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3\n-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19\n-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101\n 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z",twoheadrightarrow:"M400000 167\nc-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3\n 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42\n 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333\n-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70\n 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z",tilde1:"M200 55.538c-77 0-168 73.953-177 73.953-3 0-7\n-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0\n 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0\n 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128\n-68.267.847-113-73.952-191-73.952z",tilde2:"M344 55.266c-142 0-300.638 81.316-311.5 86.418\n-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9\n 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114\nc1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751\n 181.476 676 181.476c-149 0-189-126.21-332-126.21z",tilde3:"M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457\n-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0\n 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697\n 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696\n -338 0-409-156.573-744-156.573z",tilde4:"M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345\n-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409\n 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9\n 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409\n -175.236-744-175.236z",vec:"M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5\n3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11\n10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63\n-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1\n-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59\nH213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359\nc-16-25.333-24-45-24-59z",widehat1:"M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22\nc-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z",widehat2:"M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat3:"M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat4:"M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widecheck1:"M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,\n-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z",widecheck2:"M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck3:"M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck4:"M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",baraboveleftarrow:"M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202\nc4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5\nc-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130\ns-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47\n121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6\ns2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11\nc0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z\nM100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z",rightarrowabovebar:"M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32\n-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0\n13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39\n-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5\n-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z",baraboveshortleftharpoon:"M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17\nc2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21\nc-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40\nc-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z\nM0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z",rightharpoonaboveshortbar:"M0,241 l0,40c399126,0,399993,0,399993,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z",shortbaraboveleftharpoon:"M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,\n1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,\n-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z\nM93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z",shortrightharpoonabovebar:"M53,241l0,40c398570,0,399437,0,399437,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z"},N=function(e,t){switch(e){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+" v1759 h347 v-84\nH403z M403 1759 V0 H319 V1759 v"+t+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+" v1759 H0 v84 H347z\nM347 1759 V0 H263 V1759 v"+t+" v1759 h84z";case"vert":return"M145 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M188 15 H145 v585 v"+t+" v585 h43z";case"doublevert":return"M145 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M188 15 H145 v585 v"+t+" v585 h43z\nM367 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M410 15 H367 v585 v"+t+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+t+" v1715 h263 v84 H319z\nMM319 602 V0 H403 V602 v"+t+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+t+" v1799 H0 v-84 H319z\nMM319 602 V0 H403 V602 v"+t+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+" v602 h84z\nM403 1759 V0 H319 V1759 v"+t+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+" v602 h84z\nM347 1759 V0 h-84 V1759 v"+t+" v602 h84z";case"lparen":return"M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1\nc-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,\n-36,557 l0,"+(t+84)+"c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,\n949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9\nc0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,\n-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189\nl0,-"+(t+92)+"c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,\n-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z";case"rparen":return"M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,\n63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5\nc11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,"+(t+9)+"\nc-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664\nc-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11\nc0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17\nc242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558\nl0,-"+(t+144)+"c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,\n-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z";default:throw Error("Unknown stretchy delimiter.")}};class I{constructor(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(e){return u.contains(this.classes,e)}toNode(){for(var e=document.createDocumentFragment(),t=0;t<this.children.length;t++)e.appendChild(this.children[t].toNode());return e}toMarkup(){for(var e="",t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e}toText(){return this.children.map(e=>e.toText()).join("")}}var H={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}},R={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},O={Å:"A",Ð:"D",Þ:"o",å:"a",ð:"d",þ:"o",А:"A",Б:"B",В:"B",Г:"F",Д:"A",Е:"E",Ж:"K",З:"3",И:"N",Й:"N",К:"K",Л:"N",М:"M",Н:"H",О:"O",П:"N",Р:"P",С:"C",Т:"T",У:"y",Ф:"O",Х:"X",Ц:"U",Ч:"h",Ш:"W",Щ:"W",Ъ:"B",Ы:"X",Ь:"B",Э:"3",Ю:"X",Я:"R",а:"a",б:"b",в:"a",г:"r",д:"y",е:"e",ж:"m",з:"e",и:"n",й:"n",к:"n",л:"n",м:"m",н:"n",о:"o",п:"n",р:"p",с:"c",т:"o",у:"y",ф:"b",х:"x",ц:"n",ч:"n",ш:"w",щ:"w",ъ:"a",ы:"m",ь:"a",э:"e",ю:"m",я:"r"};function E(e,t,r){if(!H[t])throw Error("Font metrics not found for font: "+t+".");var a=e.charCodeAt(0),n=H[t][a];if(!n&&e[0]in O&&(a=O[e[0]].charCodeAt(0),n=H[t][a]),!n&&"text"===r&&T(a)&&(n=H[t][77]),n)return{depth:n[0],height:n[1],italic:n[2],skew:n[3],width:n[4]}}var L={},D=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],V=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],P=function(e,t){return t.size<2?e:D[e-1][t.size-1]};class F{constructor(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||F.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=V[this.size-1],this.maxSize=e.maxSize,this.minRuleThickness=e.minRuleThickness,this._fontMetrics=void 0}extend(e){var t={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return new F(t)}havingStyle(e){return this.style===e?this:this.extend({style:e,size:P(this.textSize,e)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:V[e-1]})}havingBaseStyle(e){e=e||this.style.text();var t=P(F.BASESIZE,e);return this.size===t&&this.textSize===F.BASESIZE&&this.style===e?this:this.extend({style:e,size:t})}havingBaseSizing(){var e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})}withColor(e){return this.extend({color:e})}withPhantom(){return this.extend({phantom:!0})}withFont(e){return this.extend({font:e})}withTextFontFamily(e){return this.extend({fontFamily:e,font:""})}withTextFontWeight(e){return this.extend({fontWeight:e,font:""})}withTextFontShape(e){return this.extend({fontShape:e,font:""})}sizingClasses(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==F.BASESIZE?["sizing","reset-size"+this.size,"size"+F.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=function(e){var t;if(!L[t=e>=5?0:e>=3?1:2]){var r=L[t]={cssEmPerMu:R.quad[t]/18};for(var a in R)R.hasOwnProperty(a)&&(r[a]=R[a][t])}return L[t]}(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}}F.BASESIZE=6;var G={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:1.00375,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:1.00375},U={ex:!0,em:!0,mu:!0},Y=function(e){return"string"!=typeof e&&(e=e.unit),e in G||e in U||"ex"===e},X=function(e,t){var r,a;if(e.unit in G)r=G[e.unit]/t.fontMetrics().ptPerEm/t.sizeMultiplier;else if("mu"===e.unit)r=t.fontMetrics().cssEmPerMu;else{if(a=t.style.isTight()?t.havingStyle(t.style.text()):t,"ex"===e.unit)r=a.fontMetrics().xHeight;else if("em"===e.unit)r=a.fontMetrics().quad;else throw new i("Invalid unit: '"+e.unit+"'");a!==t&&(r*=a.sizeMultiplier/t.sizeMultiplier)}return Math.min(e.number*r,t.maxSize)},W=function(e){return+e.toFixed(4)+"em"},_=function(e){return e.filter(e=>e).join(" ")},j=function(e,t,r){if(this.classes=e||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=r||{},t){t.style.isTight()&&this.classes.push("mtight");var a=t.getColor();a&&(this.style.color=a)}},$=function(e){var t=document.createElement(e);for(var r in t.className=_(this.classes),this.style)this.style.hasOwnProperty(r)&&(t.style[r]=this.style[r]);for(var a in this.attributes)this.attributes.hasOwnProperty(a)&&t.setAttribute(a,this.attributes[a]);for(var n=0;n<this.children.length;n++)t.appendChild(this.children[n].toNode());return t},Z=/[\s"'>/=\x00-\x1f]/,K=function(e){var t="<"+e;this.classes.length&&(t+=' class="'+u.escape(_(this.classes))+'"');var r="";for(var a in this.style)this.style.hasOwnProperty(a)&&(r+=u.hyphenate(a)+":"+this.style[a]+";");for(var n in r&&(t+=' style="'+u.escape(r)+'"'),this.attributes)if(this.attributes.hasOwnProperty(n)){if(Z.test(n))throw new i("Invalid attribute name '"+n+"'");t+=" "+n+'="'+u.escape(this.attributes[n])+'"'}t+=">";for(var s=0;s<this.children.length;s++)t+=this.children[s].toMarkup();return t+("</"+e+">")};class J{constructor(e,t,r,a){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,j.call(this,e,r,a),this.children=t||[]}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return u.contains(this.classes,e)}toNode(){return $.call(this,"span")}toMarkup(){return K.call(this,"span")}}class Q{constructor(e,t,r,a){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,j.call(this,t,a),this.children=r||[],this.setAttribute("href",e)}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return u.contains(this.classes,e)}toNode(){return $.call(this,"a")}toMarkup(){return K.call(this,"a")}}class ee{constructor(e,t,r){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=t,this.src=e,this.classes=["mord"],this.style=r}hasClass(e){return u.contains(this.classes,e)}toNode(){var e=document.createElement("img");for(var t in e.src=this.src,e.alt=this.alt,e.className="mord",this.style)this.style.hasOwnProperty(t)&&(e.style[t]=this.style[t]);return e}toMarkup(){var e='<img src="'+u.escape(this.src)+'" alt="'+u.escape(this.alt)+'"',t="";for(var r in this.style)this.style.hasOwnProperty(r)&&(t+=u.hyphenate(r)+":"+this.style[r]+";");return t&&(e+=' style="'+u.escape(t)+'"'),e+="'/>"}}var et={î:"ı̂",ï:"ı̈",í:"ı́",ì:"ı̀"};class er{constructor(e,t,r,a,n,i,s,o){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=t||0,this.depth=r||0,this.italic=a||0,this.skew=n||0,this.width=i||0,this.classes=s||[],this.style=o||{},this.maxFontSize=0;var l=function(e){for(var t=0;t<z.length;t++)for(var r=z[t],a=0;a<r.blocks.length;a++){var n=r.blocks[a];if(e>=n[0]&&e<=n[1])return r.name}return null}(this.text.charCodeAt(0));l&&this.classes.push(l+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=et[this.text])}hasClass(e){return u.contains(this.classes,e)}toNode(){var e=document.createTextNode(this.text),t=null;for(var r in this.italic>0&&((t=document.createElement("span")).style.marginRight=W(this.italic)),this.classes.length>0&&((t=t||document.createElement("span")).className=_(this.classes)),this.style)this.style.hasOwnProperty(r)&&((t=t||document.createElement("span")).style[r]=this.style[r]);return t?(t.appendChild(e),t):e}toMarkup(){var e=!1,t="<span";this.classes.length&&(e=!0,t+=' class="',t+=u.escape(_(this.classes)),t+='"');var r="";for(var a in this.italic>0&&(r+="margin-right:"+this.italic+"em;"),this.style)this.style.hasOwnProperty(a)&&(r+=u.hyphenate(a)+":"+this.style[a]+";");r&&(e=!0,t+=' style="'+u.escape(r)+'"');var n=u.escape(this.text);return e?(t+=">",t+=n,t+="</span>"):n}}class ea{constructor(e,t){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=t||{}}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","svg");for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);for(var r=0;r<this.children.length;r++)e.appendChild(this.children[r].toNode());return e}toMarkup(){var e='<svg xmlns="http://www.w3.org/2000/svg"';for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+u.escape(this.attributes[t])+'"');e+=">";for(var r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+"</svg>"}}class en{constructor(e,t){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=t}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","path");return this.alternate?e.setAttribute("d",this.alternate):e.setAttribute("d",q[this.pathName]),e}toMarkup(){return this.alternate?'<path d="'+u.escape(this.alternate)+'"/>':'<path d="'+u.escape(q[this.pathName])+'"/>'}}class ei{constructor(e){this.attributes=void 0,this.attributes=e||{}}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","line");for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);return e}toMarkup(){var e="<line";for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+u.escape(this.attributes[t])+'"');return e+"/>"}}function es(e){if(e instanceof er)return e;throw Error("Expected symbolNode but got "+String(e)+".")}var eo={bin:1,close:1,inner:1,open:1,punct:1,rel:1},el={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},eh={math:{},text:{}};function em(e,t,r,a,n,i){eh[e][n]={font:t,group:r,replace:a},i&&a&&(eh[e][a]=eh[e][n])}var ec="math",ep="text",eu="main",ed="accent-token",eg="close",ef="inner",ev="mathord",eb="op-token",ey="open",ex="punct",ew="spacing",ek="textord";em(ec,eu,"rel","≡","\\equiv",!0),em(ec,eu,"rel","≺","\\prec",!0),em(ec,eu,"rel","≻","\\succ",!0),em(ec,eu,"rel","∼","\\sim",!0),em(ec,eu,"rel","⊥","\\perp"),em(ec,eu,"rel","⪯","\\preceq",!0),em(ec,eu,"rel","⪰","\\succeq",!0),em(ec,eu,"rel","≃","\\simeq",!0),em(ec,eu,"rel","∣","\\mid",!0),em(ec,eu,"rel","≪","\\ll",!0),em(ec,eu,"rel","≫","\\gg",!0),em(ec,eu,"rel","≍","\\asymp",!0),em(ec,eu,"rel","∥","\\parallel"),em(ec,eu,"rel","⋈","\\bowtie",!0),em(ec,eu,"rel","⌣","\\smile",!0),em(ec,eu,"rel","⊑","\\sqsubseteq",!0),em(ec,eu,"rel","⊒","\\sqsupseteq",!0),em(ec,eu,"rel","≐","\\doteq",!0),em(ec,eu,"rel","⌢","\\frown",!0),em(ec,eu,"rel","∋","\\ni",!0),em(ec,eu,"rel","∝","\\propto",!0),em(ec,eu,"rel","⊢","\\vdash",!0),em(ec,eu,"rel","⊣","\\dashv",!0),em(ec,eu,"rel","∋","\\owns"),em(ec,eu,ex,".","\\ldotp"),em(ec,eu,ex,"⋅","\\cdotp"),em(ec,eu,ek,"#","\\#"),em(ep,eu,ek,"#","\\#"),em(ec,eu,ek,"&","\\&"),em(ep,eu,ek,"&","\\&"),em(ec,eu,ek,"ℵ","\\aleph",!0),em(ec,eu,ek,"∀","\\forall",!0),em(ec,eu,ek,"ℏ","\\hbar",!0),em(ec,eu,ek,"∃","\\exists",!0),em(ec,eu,ek,"∇","\\nabla",!0),em(ec,eu,ek,"♭","\\flat",!0),em(ec,eu,ek,"ℓ","\\ell",!0),em(ec,eu,ek,"♮","\\natural",!0),em(ec,eu,ek,"♣","\\clubsuit",!0),em(ec,eu,ek,"℘","\\wp",!0),em(ec,eu,ek,"♯","\\sharp",!0),em(ec,eu,ek,"♢","\\diamondsuit",!0),em(ec,eu,ek,"ℜ","\\Re",!0),em(ec,eu,ek,"♡","\\heartsuit",!0),em(ec,eu,ek,"ℑ","\\Im",!0),em(ec,eu,ek,"♠","\\spadesuit",!0),em(ec,eu,ek,"\xa7","\\S",!0),em(ep,eu,ek,"\xa7","\\S"),em(ec,eu,ek,"\xb6","\\P",!0),em(ep,eu,ek,"\xb6","\\P"),em(ec,eu,ek,"†","\\dag"),em(ep,eu,ek,"†","\\dag"),em(ep,eu,ek,"†","\\textdagger"),em(ec,eu,ek,"‡","\\ddag"),em(ep,eu,ek,"‡","\\ddag"),em(ep,eu,ek,"‡","\\textdaggerdbl"),em(ec,eu,eg,"⎱","\\rmoustache",!0),em(ec,eu,ey,"⎰","\\lmoustache",!0),em(ec,eu,eg,"⟯","\\rgroup",!0),em(ec,eu,ey,"⟮","\\lgroup",!0),em(ec,eu,"bin","∓","\\mp",!0),em(ec,eu,"bin","⊖","\\ominus",!0),em(ec,eu,"bin","⊎","\\uplus",!0),em(ec,eu,"bin","⊓","\\sqcap",!0),em(ec,eu,"bin","∗","\\ast"),em(ec,eu,"bin","⊔","\\sqcup",!0),em(ec,eu,"bin","◯","\\bigcirc",!0),em(ec,eu,"bin","∙","\\bullet",!0),em(ec,eu,"bin","‡","\\ddagger"),em(ec,eu,"bin","≀","\\wr",!0),em(ec,eu,"bin","⨿","\\amalg"),em(ec,eu,"bin","&","\\And"),em(ec,eu,"rel","⟵","\\longleftarrow",!0),em(ec,eu,"rel","⇐","\\Leftarrow",!0),em(ec,eu,"rel","⟸","\\Longleftarrow",!0),em(ec,eu,"rel","⟶","\\longrightarrow",!0),em(ec,eu,"rel","⇒","\\Rightarrow",!0),em(ec,eu,"rel","⟹","\\Longrightarrow",!0),em(ec,eu,"rel","↔","\\leftrightarrow",!0),em(ec,eu,"rel","⟷","\\longleftrightarrow",!0),em(ec,eu,"rel","⇔","\\Leftrightarrow",!0),em(ec,eu,"rel","⟺","\\Longleftrightarrow",!0),em(ec,eu,"rel","↦","\\mapsto",!0),em(ec,eu,"rel","⟼","\\longmapsto",!0),em(ec,eu,"rel","↗","\\nearrow",!0),em(ec,eu,"rel","↩","\\hookleftarrow",!0),em(ec,eu,"rel","↪","\\hookrightarrow",!0),em(ec,eu,"rel","↘","\\searrow",!0),em(ec,eu,"rel","↼","\\leftharpoonup",!0),em(ec,eu,"rel","⇀","\\rightharpoonup",!0),em(ec,eu,"rel","↙","\\swarrow",!0),em(ec,eu,"rel","↽","\\leftharpoondown",!0),em(ec,eu,"rel","⇁","\\rightharpoondown",!0),em(ec,eu,"rel","↖","\\nwarrow",!0),em(ec,eu,"rel","⇌","\\rightleftharpoons",!0),em(ec,"ams","rel","≮","\\nless",!0),em(ec,"ams","rel","","\\@nleqslant"),em(ec,"ams","rel","","\\@nleqq"),em(ec,"ams","rel","⪇","\\lneq",!0),em(ec,"ams","rel","≨","\\lneqq",!0),em(ec,"ams","rel","","\\@lvertneqq"),em(ec,"ams","rel","⋦","\\lnsim",!0),em(ec,"ams","rel","⪉","\\lnapprox",!0),em(ec,"ams","rel","⊀","\\nprec",!0),em(ec,"ams","rel","⋠","\\npreceq",!0),em(ec,"ams","rel","⋨","\\precnsim",!0),em(ec,"ams","rel","⪹","\\precnapprox",!0),em(ec,"ams","rel","≁","\\nsim",!0),em(ec,"ams","rel","","\\@nshortmid"),em(ec,"ams","rel","∤","\\nmid",!0),em(ec,"ams","rel","⊬","\\nvdash",!0),em(ec,"ams","rel","⊭","\\nvDash",!0),em(ec,"ams","rel","⋪","\\ntriangleleft"),em(ec,"ams","rel","⋬","\\ntrianglelefteq",!0),em(ec,"ams","rel","⊊","\\subsetneq",!0),em(ec,"ams","rel","","\\@varsubsetneq"),em(ec,"ams","rel","⫋","\\subsetneqq",!0),em(ec,"ams","rel","","\\@varsubsetneqq"),em(ec,"ams","rel","≯","\\ngtr",!0),em(ec,"ams","rel","","\\@ngeqslant"),em(ec,"ams","rel","","\\@ngeqq"),em(ec,"ams","rel","⪈","\\gneq",!0),em(ec,"ams","rel","≩","\\gneqq",!0),em(ec,"ams","rel","","\\@gvertneqq"),em(ec,"ams","rel","⋧","\\gnsim",!0),em(ec,"ams","rel","⪊","\\gnapprox",!0),em(ec,"ams","rel","⊁","\\nsucc",!0),em(ec,"ams","rel","⋡","\\nsucceq",!0),em(ec,"ams","rel","⋩","\\succnsim",!0),em(ec,"ams","rel","⪺","\\succnapprox",!0),em(ec,"ams","rel","≆","\\ncong",!0),em(ec,"ams","rel","","\\@nshortparallel"),em(ec,"ams","rel","∦","\\nparallel",!0),em(ec,"ams","rel","⊯","\\nVDash",!0),em(ec,"ams","rel","⋫","\\ntriangleright"),em(ec,"ams","rel","⋭","\\ntrianglerighteq",!0),em(ec,"ams","rel","","\\@nsupseteqq"),em(ec,"ams","rel","⊋","\\supsetneq",!0),em(ec,"ams","rel","","\\@varsupsetneq"),em(ec,"ams","rel","⫌","\\supsetneqq",!0),em(ec,"ams","rel","","\\@varsupsetneqq"),em(ec,"ams","rel","⊮","\\nVdash",!0),em(ec,"ams","rel","⪵","\\precneqq",!0),em(ec,"ams","rel","⪶","\\succneqq",!0),em(ec,"ams","rel","","\\@nsubseteqq"),em(ec,"ams","bin","⊴","\\unlhd"),em(ec,"ams","bin","⊵","\\unrhd"),em(ec,"ams","rel","↚","\\nleftarrow",!0),em(ec,"ams","rel","↛","\\nrightarrow",!0),em(ec,"ams","rel","⇍","\\nLeftarrow",!0),em(ec,"ams","rel","⇏","\\nRightarrow",!0),em(ec,"ams","rel","↮","\\nleftrightarrow",!0),em(ec,"ams","rel","⇎","\\nLeftrightarrow",!0),em(ec,"ams","rel","△","\\vartriangle"),em(ec,"ams",ek,"ℏ","\\hslash"),em(ec,"ams",ek,"▽","\\triangledown"),em(ec,"ams",ek,"◊","\\lozenge"),em(ec,"ams",ek,"Ⓢ","\\circledS"),em(ec,"ams",ek,"\xae","\\circledR"),em(ep,"ams",ek,"\xae","\\circledR"),em(ec,"ams",ek,"∡","\\measuredangle",!0),em(ec,"ams",ek,"∄","\\nexists"),em(ec,"ams",ek,"℧","\\mho"),em(ec,"ams",ek,"Ⅎ","\\Finv",!0),em(ec,"ams",ek,"⅁","\\Game",!0),em(ec,"ams",ek,"‵","\\backprime"),em(ec,"ams",ek,"▲","\\blacktriangle"),em(ec,"ams",ek,"▼","\\blacktriangledown"),em(ec,"ams",ek,"■","\\blacksquare"),em(ec,"ams",ek,"⧫","\\blacklozenge"),em(ec,"ams",ek,"★","\\bigstar"),em(ec,"ams",ek,"∢","\\sphericalangle",!0),em(ec,"ams",ek,"∁","\\complement",!0),em(ec,"ams",ek,"\xf0","\\eth",!0),em(ep,eu,ek,"\xf0","\xf0"),em(ec,"ams",ek,"╱","\\diagup"),em(ec,"ams",ek,"╲","\\diagdown"),em(ec,"ams",ek,"□","\\square"),em(ec,"ams",ek,"□","\\Box"),em(ec,"ams",ek,"◊","\\Diamond"),em(ec,"ams",ek,"\xa5","\\yen",!0),em(ep,"ams",ek,"\xa5","\\yen",!0),em(ec,"ams",ek,"✓","\\checkmark",!0),em(ep,"ams",ek,"✓","\\checkmark"),em(ec,"ams",ek,"ℶ","\\beth",!0),em(ec,"ams",ek,"ℸ","\\daleth",!0),em(ec,"ams",ek,"ℷ","\\gimel",!0),em(ec,"ams",ek,"ϝ","\\digamma",!0),em(ec,"ams",ek,"ϰ","\\varkappa"),em(ec,"ams",ey,"┌","\\@ulcorner",!0),em(ec,"ams",eg,"┐","\\@urcorner",!0),em(ec,"ams",ey,"└","\\@llcorner",!0),em(ec,"ams",eg,"┘","\\@lrcorner",!0),em(ec,"ams","rel","≦","\\leqq",!0),em(ec,"ams","rel","⩽","\\leqslant",!0),em(ec,"ams","rel","⪕","\\eqslantless",!0),em(ec,"ams","rel","≲","\\lesssim",!0),em(ec,"ams","rel","⪅","\\lessapprox",!0),em(ec,"ams","rel","≊","\\approxeq",!0),em(ec,"ams","bin","⋖","\\lessdot"),em(ec,"ams","rel","⋘","\\lll",!0),em(ec,"ams","rel","≶","\\lessgtr",!0),em(ec,"ams","rel","⋚","\\lesseqgtr",!0),em(ec,"ams","rel","⪋","\\lesseqqgtr",!0),em(ec,"ams","rel","≑","\\doteqdot"),em(ec,"ams","rel","≓","\\risingdotseq",!0),em(ec,"ams","rel","≒","\\fallingdotseq",!0),em(ec,"ams","rel","∽","\\backsim",!0),em(ec,"ams","rel","⋍","\\backsimeq",!0),em(ec,"ams","rel","⫅","\\subseteqq",!0),em(ec,"ams","rel","⋐","\\Subset",!0),em(ec,"ams","rel","⊏","\\sqsubset",!0),em(ec,"ams","rel","≼","\\preccurlyeq",!0),em(ec,"ams","rel","⋞","\\curlyeqprec",!0),em(ec,"ams","rel","≾","\\precsim",!0),em(ec,"ams","rel","⪷","\\precapprox",!0),em(ec,"ams","rel","⊲","\\vartriangleleft"),em(ec,"ams","rel","⊴","\\trianglelefteq"),em(ec,"ams","rel","⊨","\\vDash",!0),em(ec,"ams","rel","⊪","\\Vvdash",!0),em(ec,"ams","rel","⌣","\\smallsmile"),em(ec,"ams","rel","⌢","\\smallfrown"),em(ec,"ams","rel","≏","\\bumpeq",!0),em(ec,"ams","rel","≎","\\Bumpeq",!0),em(ec,"ams","rel","≧","\\geqq",!0),em(ec,"ams","rel","⩾","\\geqslant",!0),em(ec,"ams","rel","⪖","\\eqslantgtr",!0),em(ec,"ams","rel","≳","\\gtrsim",!0),em(ec,"ams","rel","⪆","\\gtrapprox",!0),em(ec,"ams","bin","⋗","\\gtrdot"),em(ec,"ams","rel","⋙","\\ggg",!0),em(ec,"ams","rel","≷","\\gtrless",!0),em(ec,"ams","rel","⋛","\\gtreqless",!0),em(ec,"ams","rel","⪌","\\gtreqqless",!0),em(ec,"ams","rel","≖","\\eqcirc",!0),em(ec,"ams","rel","≗","\\circeq",!0),em(ec,"ams","rel","≜","\\triangleq",!0),em(ec,"ams","rel","∼","\\thicksim"),em(ec,"ams","rel","≈","\\thickapprox"),em(ec,"ams","rel","⫆","\\supseteqq",!0),em(ec,"ams","rel","⋑","\\Supset",!0),em(ec,"ams","rel","⊐","\\sqsupset",!0),em(ec,"ams","rel","≽","\\succcurlyeq",!0),em(ec,"ams","rel","⋟","\\curlyeqsucc",!0),em(ec,"ams","rel","≿","\\succsim",!0),em(ec,"ams","rel","⪸","\\succapprox",!0),em(ec,"ams","rel","⊳","\\vartriangleright"),em(ec,"ams","rel","⊵","\\trianglerighteq"),em(ec,"ams","rel","⊩","\\Vdash",!0),em(ec,"ams","rel","∣","\\shortmid"),em(ec,"ams","rel","∥","\\shortparallel"),em(ec,"ams","rel","≬","\\between",!0),em(ec,"ams","rel","⋔","\\pitchfork",!0),em(ec,"ams","rel","∝","\\varpropto"),em(ec,"ams","rel","◀","\\blacktriangleleft"),em(ec,"ams","rel","∴","\\therefore",!0),em(ec,"ams","rel","∍","\\backepsilon"),em(ec,"ams","rel","▶","\\blacktriangleright"),em(ec,"ams","rel","∵","\\because",!0),em(ec,"ams","rel","⋘","\\llless"),em(ec,"ams","rel","⋙","\\gggtr"),em(ec,"ams","bin","⊲","\\lhd"),em(ec,"ams","bin","⊳","\\rhd"),em(ec,"ams","rel","≂","\\eqsim",!0),em(ec,eu,"rel","⋈","\\Join"),em(ec,"ams","rel","≑","\\Doteq",!0),em(ec,"ams","bin","∔","\\dotplus",!0),em(ec,"ams","bin","∖","\\smallsetminus"),em(ec,"ams","bin","⋒","\\Cap",!0),em(ec,"ams","bin","⋓","\\Cup",!0),em(ec,"ams","bin","⩞","\\doublebarwedge",!0),em(ec,"ams","bin","⊟","\\boxminus",!0),em(ec,"ams","bin","⊞","\\boxplus",!0),em(ec,"ams","bin","⋇","\\divideontimes",!0),em(ec,"ams","bin","⋉","\\ltimes",!0),em(ec,"ams","bin","⋊","\\rtimes",!0),em(ec,"ams","bin","⋋","\\leftthreetimes",!0),em(ec,"ams","bin","⋌","\\rightthreetimes",!0),em(ec,"ams","bin","⋏","\\curlywedge",!0),em(ec,"ams","bin","⋎","\\curlyvee",!0),em(ec,"ams","bin","⊝","\\circleddash",!0),em(ec,"ams","bin","⊛","\\circledast",!0),em(ec,"ams","bin","⋅","\\centerdot"),em(ec,"ams","bin","⊺","\\intercal",!0),em(ec,"ams","bin","⋒","\\doublecap"),em(ec,"ams","bin","⋓","\\doublecup"),em(ec,"ams","bin","⊠","\\boxtimes",!0),em(ec,"ams","rel","⇢","\\dashrightarrow",!0),em(ec,"ams","rel","⇠","\\dashleftarrow",!0),em(ec,"ams","rel","⇇","\\leftleftarrows",!0),em(ec,"ams","rel","⇆","\\leftrightarrows",!0),em(ec,"ams","rel","⇚","\\Lleftarrow",!0),em(ec,"ams","rel","↞","\\twoheadleftarrow",!0),em(ec,"ams","rel","↢","\\leftarrowtail",!0),em(ec,"ams","rel","↫","\\looparrowleft",!0),em(ec,"ams","rel","⇋","\\leftrightharpoons",!0),em(ec,"ams","rel","↶","\\curvearrowleft",!0),em(ec,"ams","rel","↺","\\circlearrowleft",!0),em(ec,"ams","rel","↰","\\Lsh",!0),em(ec,"ams","rel","⇈","\\upuparrows",!0),em(ec,"ams","rel","↿","\\upharpoonleft",!0),em(ec,"ams","rel","⇃","\\downharpoonleft",!0),em(ec,eu,"rel","⊶","\\origof",!0),em(ec,eu,"rel","⊷","\\imageof",!0),em(ec,"ams","rel","⊸","\\multimap",!0),em(ec,"ams","rel","↭","\\leftrightsquigarrow",!0),em(ec,"ams","rel","⇉","\\rightrightarrows",!0),em(ec,"ams","rel","⇄","\\rightleftarrows",!0),em(ec,"ams","rel","↠","\\twoheadrightarrow",!0),em(ec,"ams","rel","↣","\\rightarrowtail",!0),em(ec,"ams","rel","↬","\\looparrowright",!0),em(ec,"ams","rel","↷","\\curvearrowright",!0),em(ec,"ams","rel","↻","\\circlearrowright",!0),em(ec,"ams","rel","↱","\\Rsh",!0),em(ec,"ams","rel","⇊","\\downdownarrows",!0),em(ec,"ams","rel","↾","\\upharpoonright",!0),em(ec,"ams","rel","⇂","\\downharpoonright",!0),em(ec,"ams","rel","⇝","\\rightsquigarrow",!0),em(ec,"ams","rel","⇝","\\leadsto"),em(ec,"ams","rel","⇛","\\Rrightarrow",!0),em(ec,"ams","rel","↾","\\restriction"),em(ec,eu,ek,"‘","`"),em(ec,eu,ek,"$","\\$"),em(ep,eu,ek,"$","\\$"),em(ep,eu,ek,"$","\\textdollar"),em(ec,eu,ek,"%","\\%"),em(ep,eu,ek,"%","\\%"),em(ec,eu,ek,"_","\\_"),em(ep,eu,ek,"_","\\_"),em(ep,eu,ek,"_","\\textunderscore"),em(ec,eu,ek,"∠","\\angle",!0),em(ec,eu,ek,"∞","\\infty",!0),em(ec,eu,ek,"′","\\prime"),em(ec,eu,ek,"△","\\triangle"),em(ec,eu,ek,"Γ","\\Gamma",!0),em(ec,eu,ek,"Δ","\\Delta",!0),em(ec,eu,ek,"Θ","\\Theta",!0),em(ec,eu,ek,"Λ","\\Lambda",!0),em(ec,eu,ek,"Ξ","\\Xi",!0),em(ec,eu,ek,"Π","\\Pi",!0),em(ec,eu,ek,"Σ","\\Sigma",!0),em(ec,eu,ek,"Υ","\\Upsilon",!0),em(ec,eu,ek,"Φ","\\Phi",!0),em(ec,eu,ek,"Ψ","\\Psi",!0),em(ec,eu,ek,"Ω","\\Omega",!0),em(ec,eu,ek,"A","Α"),em(ec,eu,ek,"B","Β"),em(ec,eu,ek,"E","Ε"),em(ec,eu,ek,"Z","Ζ"),em(ec,eu,ek,"H","Η"),em(ec,eu,ek,"I","Ι"),em(ec,eu,ek,"K","Κ"),em(ec,eu,ek,"M","Μ"),em(ec,eu,ek,"N","Ν"),em(ec,eu,ek,"O","Ο"),em(ec,eu,ek,"P","Ρ"),em(ec,eu,ek,"T","Τ"),em(ec,eu,ek,"X","Χ"),em(ec,eu,ek,"\xac","\\neg",!0),em(ec,eu,ek,"\xac","\\lnot"),em(ec,eu,ek,"⊤","\\top"),em(ec,eu,ek,"⊥","\\bot"),em(ec,eu,ek,"∅","\\emptyset"),em(ec,"ams",ek,"∅","\\varnothing"),em(ec,eu,ev,"α","\\alpha",!0),em(ec,eu,ev,"β","\\beta",!0),em(ec,eu,ev,"γ","\\gamma",!0),em(ec,eu,ev,"δ","\\delta",!0),em(ec,eu,ev,"ϵ","\\epsilon",!0),em(ec,eu,ev,"ζ","\\zeta",!0),em(ec,eu,ev,"η","\\eta",!0),em(ec,eu,ev,"θ","\\theta",!0),em(ec,eu,ev,"ι","\\iota",!0),em(ec,eu,ev,"κ","\\kappa",!0),em(ec,eu,ev,"λ","\\lambda",!0),em(ec,eu,ev,"μ","\\mu",!0),em(ec,eu,ev,"ν","\\nu",!0),em(ec,eu,ev,"ξ","\\xi",!0),em(ec,eu,ev,"ο","\\omicron",!0),em(ec,eu,ev,"π","\\pi",!0),em(ec,eu,ev,"ρ","\\rho",!0),em(ec,eu,ev,"σ","\\sigma",!0),em(ec,eu,ev,"τ","\\tau",!0),em(ec,eu,ev,"υ","\\upsilon",!0),em(ec,eu,ev,"ϕ","\\phi",!0),em(ec,eu,ev,"χ","\\chi",!0),em(ec,eu,ev,"ψ","\\psi",!0),em(ec,eu,ev,"ω","\\omega",!0),em(ec,eu,ev,"ε","\\varepsilon",!0),em(ec,eu,ev,"ϑ","\\vartheta",!0),em(ec,eu,ev,"ϖ","\\varpi",!0),em(ec,eu,ev,"ϱ","\\varrho",!0),em(ec,eu,ev,"ς","\\varsigma",!0),em(ec,eu,ev,"φ","\\varphi",!0),em(ec,eu,"bin","∗","*",!0),em(ec,eu,"bin","+","+"),em(ec,eu,"bin","−","-",!0),em(ec,eu,"bin","⋅","\\cdot",!0),em(ec,eu,"bin","∘","\\circ",!0),em(ec,eu,"bin","\xf7","\\div",!0),em(ec,eu,"bin","\xb1","\\pm",!0),em(ec,eu,"bin","\xd7","\\times",!0),em(ec,eu,"bin","∩","\\cap",!0),em(ec,eu,"bin","∪","\\cup",!0),em(ec,eu,"bin","∖","\\setminus",!0),em(ec,eu,"bin","∧","\\land"),em(ec,eu,"bin","∨","\\lor"),em(ec,eu,"bin","∧","\\wedge",!0),em(ec,eu,"bin","∨","\\vee",!0),em(ec,eu,ek,"√","\\surd"),em(ec,eu,ey,"⟨","\\langle",!0),em(ec,eu,ey,"∣","\\lvert"),em(ec,eu,ey,"∥","\\lVert"),em(ec,eu,eg,"?","?"),em(ec,eu,eg,"!","!"),em(ec,eu,eg,"⟩","\\rangle",!0),em(ec,eu,eg,"∣","\\rvert"),em(ec,eu,eg,"∥","\\rVert"),em(ec,eu,"rel","=","="),em(ec,eu,"rel",":",":"),em(ec,eu,"rel","≈","\\approx",!0),em(ec,eu,"rel","≅","\\cong",!0),em(ec,eu,"rel","≥","\\ge"),em(ec,eu,"rel","≥","\\geq",!0),em(ec,eu,"rel","←","\\gets"),em(ec,eu,"rel",">","\\gt",!0),em(ec,eu,"rel","∈","\\in",!0),em(ec,eu,"rel","","\\@not"),em(ec,eu,"rel","⊂","\\subset",!0),em(ec,eu,"rel","⊃","\\supset",!0),em(ec,eu,"rel","⊆","\\subseteq",!0),em(ec,eu,"rel","⊇","\\supseteq",!0),em(ec,"ams","rel","⊈","\\nsubseteq",!0),em(ec,"ams","rel","⊉","\\nsupseteq",!0),em(ec,eu,"rel","⊨","\\models"),em(ec,eu,"rel","←","\\leftarrow",!0),em(ec,eu,"rel","≤","\\le"),em(ec,eu,"rel","≤","\\leq",!0),em(ec,eu,"rel","<","\\lt",!0),em(ec,eu,"rel","→","\\rightarrow",!0),em(ec,eu,"rel","→","\\to"),em(ec,"ams","rel","≱","\\ngeq",!0),em(ec,"ams","rel","≰","\\nleq",!0),em(ec,eu,ew,"\xa0","\\ "),em(ec,eu,ew,"\xa0","\\space"),em(ec,eu,ew,"\xa0","\\nobreakspace"),em(ep,eu,ew,"\xa0","\\ "),em(ep,eu,ew,"\xa0"," "),em(ep,eu,ew,"\xa0","\\space"),em(ep,eu,ew,"\xa0","\\nobreakspace"),em(ec,eu,ew,null,"\\nobreak"),em(ec,eu,ew,null,"\\allowbreak"),em(ec,eu,ex,",",","),em(ec,eu,ex,";",";"),em(ec,"ams","bin","⊼","\\barwedge",!0),em(ec,"ams","bin","⊻","\\veebar",!0),em(ec,eu,"bin","⊙","\\odot",!0),em(ec,eu,"bin","⊕","\\oplus",!0),em(ec,eu,"bin","⊗","\\otimes",!0),em(ec,eu,ek,"∂","\\partial",!0),em(ec,eu,"bin","⊘","\\oslash",!0),em(ec,"ams","bin","⊚","\\circledcirc",!0),em(ec,"ams","bin","⊡","\\boxdot",!0),em(ec,eu,"bin","△","\\bigtriangleup"),em(ec,eu,"bin","▽","\\bigtriangledown"),em(ec,eu,"bin","†","\\dagger"),em(ec,eu,"bin","⋄","\\diamond"),em(ec,eu,"bin","⋆","\\star"),em(ec,eu,"bin","◃","\\triangleleft"),em(ec,eu,"bin","▹","\\triangleright"),em(ec,eu,ey,"{","\\{"),em(ep,eu,ek,"{","\\{"),em(ep,eu,ek,"{","\\textbraceleft"),em(ec,eu,eg,"}","\\}"),em(ep,eu,ek,"}","\\}"),em(ep,eu,ek,"}","\\textbraceright"),em(ec,eu,ey,"{","\\lbrace"),em(ec,eu,eg,"}","\\rbrace"),em(ec,eu,ey,"[","\\lbrack",!0),em(ep,eu,ek,"[","\\lbrack",!0),em(ec,eu,eg,"]","\\rbrack",!0),em(ep,eu,ek,"]","\\rbrack",!0),em(ec,eu,ey,"(","\\lparen",!0),em(ec,eu,eg,")","\\rparen",!0),em(ep,eu,ek,"<","\\textless",!0),em(ep,eu,ek,">","\\textgreater",!0),em(ec,eu,ey,"⌊","\\lfloor",!0),em(ec,eu,eg,"⌋","\\rfloor",!0),em(ec,eu,ey,"⌈","\\lceil",!0),em(ec,eu,eg,"⌉","\\rceil",!0),em(ec,eu,ek,"\\","\\backslash"),em(ec,eu,ek,"∣","|"),em(ec,eu,ek,"∣","\\vert"),em(ep,eu,ek,"|","\\textbar",!0),em(ec,eu,ek,"∥","\\|"),em(ec,eu,ek,"∥","\\Vert"),em(ep,eu,ek,"∥","\\textbardbl"),em(ep,eu,ek,"~","\\textasciitilde"),em(ep,eu,ek,"\\","\\textbackslash"),em(ep,eu,ek,"^","\\textasciicircum"),em(ec,eu,"rel","↑","\\uparrow",!0),em(ec,eu,"rel","⇑","\\Uparrow",!0),em(ec,eu,"rel","↓","\\downarrow",!0),em(ec,eu,"rel","⇓","\\Downarrow",!0),em(ec,eu,"rel","↕","\\updownarrow",!0),em(ec,eu,"rel","⇕","\\Updownarrow",!0),em(ec,eu,eb,"∐","\\coprod"),em(ec,eu,eb,"⋁","\\bigvee"),em(ec,eu,eb,"⋀","\\bigwedge"),em(ec,eu,eb,"⨄","\\biguplus"),em(ec,eu,eb,"⋂","\\bigcap"),em(ec,eu,eb,"⋃","\\bigcup"),em(ec,eu,eb,"∫","\\int"),em(ec,eu,eb,"∫","\\intop"),em(ec,eu,eb,"∬","\\iint"),em(ec,eu,eb,"∭","\\iiint"),em(ec,eu,eb,"∏","\\prod"),em(ec,eu,eb,"∑","\\sum"),em(ec,eu,eb,"⨂","\\bigotimes"),em(ec,eu,eb,"⨁","\\bigoplus"),em(ec,eu,eb,"⨀","\\bigodot"),em(ec,eu,eb,"∮","\\oint"),em(ec,eu,eb,"∯","\\oiint"),em(ec,eu,eb,"∰","\\oiiint"),em(ec,eu,eb,"⨆","\\bigsqcup"),em(ec,eu,eb,"∫","\\smallint"),em(ep,eu,ef,"…","\\textellipsis"),em(ec,eu,ef,"…","\\mathellipsis"),em(ep,eu,ef,"…","\\ldots",!0),em(ec,eu,ef,"…","\\ldots",!0),em(ec,eu,ef,"⋯","\\@cdots",!0),em(ec,eu,ef,"⋱","\\ddots",!0),em(ec,eu,ek,"⋮","\\varvdots"),em(ep,eu,ek,"⋮","\\varvdots"),em(ec,eu,ed,"ˊ","\\acute"),em(ec,eu,ed,"ˋ","\\grave"),em(ec,eu,ed,"\xa8","\\ddot"),em(ec,eu,ed,"~","\\tilde"),em(ec,eu,ed,"ˉ","\\bar"),em(ec,eu,ed,"˘","\\breve"),em(ec,eu,ed,"ˇ","\\check"),em(ec,eu,ed,"^","\\hat"),em(ec,eu,ed,"⃗","\\vec"),em(ec,eu,ed,"˙","\\dot"),em(ec,eu,ed,"˚","\\mathring"),em(ec,eu,ev,"","\\@imath"),em(ec,eu,ev,"","\\@jmath"),em(ec,eu,ek,"ı","ı"),em(ec,eu,ek,"ȷ","ȷ"),em(ep,eu,ek,"ı","\\i",!0),em(ep,eu,ek,"ȷ","\\j",!0),em(ep,eu,ek,"\xdf","\\ss",!0),em(ep,eu,ek,"\xe6","\\ae",!0),em(ep,eu,ek,"œ","\\oe",!0),em(ep,eu,ek,"\xf8","\\o",!0),em(ep,eu,ek,"\xc6","\\AE",!0),em(ep,eu,ek,"Œ","\\OE",!0),em(ep,eu,ek,"\xd8","\\O",!0),em(ep,eu,ed,"ˊ","\\'"),em(ep,eu,ed,"ˋ","\\`"),em(ep,eu,ed,"ˆ","\\^"),em(ep,eu,ed,"˜","\\~"),em(ep,eu,ed,"ˉ","\\="),em(ep,eu,ed,"˘","\\u"),em(ep,eu,ed,"˙","\\."),em(ep,eu,ed,"\xb8","\\c"),em(ep,eu,ed,"˚","\\r"),em(ep,eu,ed,"ˇ","\\v"),em(ep,eu,ed,"\xa8",'\\"'),em(ep,eu,ed,"˝","\\H"),em(ep,eu,ed,"◯","\\textcircled");var eS={"--":!0,"---":!0,"``":!0,"''":!0};em(ep,eu,ek,"–","--",!0),em(ep,eu,ek,"–","\\textendash"),em(ep,eu,ek,"—","---",!0),em(ep,eu,ek,"—","\\textemdash"),em(ep,eu,ek,"‘","`",!0),em(ep,eu,ek,"‘","\\textquoteleft"),em(ep,eu,ek,"’","'",!0),em(ep,eu,ek,"’","\\textquoteright"),em(ep,eu,ek,"“","``",!0),em(ep,eu,ek,"“","\\textquotedblleft"),em(ep,eu,ek,"”","''",!0),em(ep,eu,ek,"”","\\textquotedblright"),em(ec,eu,ek,"\xb0","\\degree",!0),em(ep,eu,ek,"\xb0","\\degree"),em(ep,eu,ek,"\xb0","\\textdegree",!0),em(ec,eu,ek,"\xa3","\\pounds"),em(ec,eu,ek,"\xa3","\\mathsterling",!0),em(ep,eu,ek,"\xa3","\\pounds"),em(ep,eu,ek,"\xa3","\\textsterling",!0),em(ec,"ams",ek,"✠","\\maltese"),em(ep,"ams",ek,"✠","\\maltese");for(var eM='0123456789/@."',ez=0;ez<eM.length;ez++){var eA=eM.charAt(ez);em(ec,eu,ek,eA,eA)}for(var eT='0123456789!@*()-=+";:?/.,',eB=0;eB<eT.length;eB++){var eC=eT.charAt(eB);em(ep,eu,ek,eC,eC)}for(var eq="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",eN=0;eN<eq.length;eN++){var eI=eq.charAt(eN);em(ec,eu,ev,eI,eI),em(ep,eu,ek,eI,eI)}em(ec,"ams",ek,"C","ℂ"),em(ep,"ams",ek,"C","ℂ"),em(ec,"ams",ek,"H","ℍ"),em(ep,"ams",ek,"H","ℍ"),em(ec,"ams",ek,"N","ℕ"),em(ep,"ams",ek,"N","ℕ"),em(ec,"ams",ek,"P","ℙ"),em(ep,"ams",ek,"P","ℙ"),em(ec,"ams",ek,"Q","ℚ"),em(ep,"ams",ek,"Q","ℚ"),em(ec,"ams",ek,"R","ℝ"),em(ep,"ams",ek,"R","ℝ"),em(ec,"ams",ek,"Z","ℤ"),em(ep,"ams",ek,"Z","ℤ"),em(ec,eu,ev,"h","ℎ"),em(ep,eu,ev,"h","ℎ");for(var eH="",eR=0;eR<eq.length;eR++){var eO=eq.charAt(eR);em(ec,eu,ev,eO,eH=String.fromCharCode(55349,56320+eR)),em(ep,eu,ek,eO,eH),em(ec,eu,ev,eO,eH=String.fromCharCode(55349,56372+eR)),em(ep,eu,ek,eO,eH),em(ec,eu,ev,eO,eH=String.fromCharCode(55349,56424+eR)),em(ep,eu,ek,eO,eH),em(ec,eu,ev,eO,eH=String.fromCharCode(55349,56580+eR)),em(ep,eu,ek,eO,eH),em(ec,eu,ev,eO,eH=String.fromCharCode(55349,56684+eR)),em(ep,eu,ek,eO,eH),em(ec,eu,ev,eO,eH=String.fromCharCode(55349,56736+eR)),em(ep,eu,ek,eO,eH),em(ec,eu,ev,eO,eH=String.fromCharCode(55349,56788+eR)),em(ep,eu,ek,eO,eH),em(ec,eu,ev,eO,eH=String.fromCharCode(55349,56840+eR)),em(ep,eu,ek,eO,eH),em(ec,eu,ev,eO,eH=String.fromCharCode(55349,56944+eR)),em(ep,eu,ek,eO,eH),eR<26&&(em(ec,eu,ev,eO,eH=String.fromCharCode(55349,56632+eR)),em(ep,eu,ek,eO,eH),em(ec,eu,ev,eO,eH=String.fromCharCode(55349,56476+eR)),em(ep,eu,ek,eO,eH))}em(ec,eu,ev,"k",eH=String.fromCharCode(55349,56668)),em(ep,eu,ek,"k",eH);for(var eE=0;eE<10;eE++){var eL=eE.toString();em(ec,eu,ev,eL,eH=String.fromCharCode(55349,57294+eE)),em(ep,eu,ek,eL,eH),em(ec,eu,ev,eL,eH=String.fromCharCode(55349,57314+eE)),em(ep,eu,ek,eL,eH),em(ec,eu,ev,eL,eH=String.fromCharCode(55349,57324+eE)),em(ep,eu,ek,eL,eH),em(ec,eu,ev,eL,eH=String.fromCharCode(55349,57334+eE)),em(ep,eu,ek,eL,eH)}for(var eD="\xd0\xde\xfe",eV=0;eV<eD.length;eV++){var eP=eD.charAt(eV);em(ec,eu,ev,eP,eP),em(ep,eu,ek,eP,eP)}var eF=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],eG=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],eU=function(e,t){var r=(e.charCodeAt(0)-55296)*1024+(e.charCodeAt(1)-56320)+65536,a=+("math"!==t);if(119808<=r&&r<120484){var n=Math.floor((r-119808)/26);return[eF[n][2],eF[n][a]]}if(120782<=r&&r<=120831){var s=Math.floor((r-120782)/10);return[eG[s][2],eG[s][a]]}if(120485===r||120486===r)return[eF[0][2],eF[0][a]];if(120486<r&&r<120782)return["",""];throw new i("Unsupported character: "+e)},eY=function(e,t,r){return eh[r][e]&&eh[r][e].replace&&(e=eh[r][e].replace),{value:e,metrics:E(e,t,r)}},eX=function(e,t,r,a,n){var i,s=eY(e,t,r),o=s.metrics;if(e=s.value,o){var l=o.italic;("text"===r||a&&"mathit"===a.font)&&(l=0),i=new er(e,o.height,o.depth,l,o.skew,o.width,n)}else"undefined"!=typeof console&&console.warn("No character metrics "+("for '"+e+"' in style '"+t+"' and mode '")+r+"'"),i=new er(e,0,0,0,0,0,n);if(a){i.maxFontSize=a.sizeMultiplier,a.style.isTight()&&i.classes.push("mtight");var h=a.getColor();h&&(i.style.color=h)}return i},eW=(e,t)=>{if(_(e.classes)!==_(t.classes)||e.skew!==t.skew||e.maxFontSize!==t.maxFontSize)return!1;if(1===e.classes.length){var r=e.classes[0];if("mbin"===r||"mord"===r)return!1}for(var a in e.style)if(e.style.hasOwnProperty(a)&&e.style[a]!==t.style[a])return!1;for(var n in t.style)if(t.style.hasOwnProperty(n)&&e.style[n]!==t.style[n])return!1;return!0},e_=function(e){for(var t=0,r=0,a=0,n=0;n<e.children.length;n++){var i=e.children[n];i.height>t&&(t=i.height),i.depth>r&&(r=i.depth),i.maxFontSize>a&&(a=i.maxFontSize)}e.height=t,e.depth=r,e.maxFontSize=a},ej=function(e,t,r,a){var n=new J(e,t,r,a);return e_(n),n},e$=(e,t,r,a)=>new J(e,t,r,a),eZ=function(e){var t=new I(e);return e_(t),t},eK=function(e){if("individualShift"===e.positionType){for(var t,r=e.children,a=[r[0]],n=-r[0].shift-r[0].elem.depth,i=n,s=1;s<r.length;s++){var o=-r[s].shift-i-r[s].elem.depth,l=o-(r[s-1].elem.height+r[s-1].elem.depth);i+=o,a.push({type:"kern",size:l}),a.push(r[s])}return{children:a,depth:n}}if("top"===e.positionType){for(var h=e.positionData,m=0;m<e.children.length;m++){var c=e.children[m];h-="kern"===c.type?c.size:c.elem.height+c.elem.depth}t=h}else if("bottom"===e.positionType)t=-e.positionData;else{var p=e.children[0];if("elem"!==p.type)throw Error('First child must have type "elem".');if("shift"===e.positionType)t=-p.elem.depth-e.positionData;else if("firstBaseline"===e.positionType)t=-p.elem.depth;else throw Error("Invalid positionType "+e.positionType+".")}return{children:e.children,depth:t}},eJ=function(e,t,r){var a="";switch(e){case"amsrm":a="AMS";break;case"textrm":a="Main";break;case"textsf":a="SansSerif";break;case"texttt":a="Typewriter";break;default:a=e}return a+"-"+("textbf"===t&&"textit"===r?"BoldItalic":"textbf"===t?"Bold":"textit"===t?"Italic":"Regular")},eQ={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathsfit:{variant:"sans-serif-italic",fontName:"SansSerif-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},e0={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]},e1={fontMap:eQ,makeSymbol:eX,mathsym:function(e,t,r,a){return(void 0===a&&(a=[]),"boldsymbol"===r.font&&eY(e,"Main-Bold",t).metrics)?eX(e,"Main-Bold",t,r,a.concat(["mathbf"])):"\\"===e||"main"===eh[t][e].font?eX(e,"Main-Regular",t,r,a):eX(e,"AMS-Regular",t,r,a.concat(["amsrm"]))},makeSpan:ej,makeSvgSpan:e$,makeLineSpan:function(e,t,r){var a=ej([e],[],t);return a.height=Math.max(r||t.fontMetrics().defaultRuleThickness,t.minRuleThickness),a.style.borderBottomWidth=W(a.height),a.maxFontSize=1,a},makeAnchor:function(e,t,r,a){var n=new Q(e,t,r,a);return e_(n),n},makeFragment:eZ,wrapFragment:function(e,t){return e instanceof I?ej([],[e],t):e},makeVList:function(e,t){for(var r,{children:a,depth:n}=eK(e),i=0,s=0;s<a.length;s++){var o=a[s];if("elem"===o.type){var l=o.elem;i=Math.max(i,l.maxFontSize,l.height)}}i+=2;var h=ej(["pstrut"],[]);h.style.height=W(i);for(var m=[],c=n,p=n,u=n,d=0;d<a.length;d++){var g=a[d];if("kern"===g.type)u+=g.size;else{var f=g.elem,v=ej(g.wrapperClasses||[],[h,f],void 0,g.wrapperStyle||{});v.style.top=W(-i-u-f.depth),g.marginLeft&&(v.style.marginLeft=g.marginLeft),g.marginRight&&(v.style.marginRight=g.marginRight),m.push(v),u+=f.height+f.depth}c=Math.min(c,u),p=Math.max(p,u)}var b=ej(["vlist"],m);if(b.style.height=W(p),c<0){var y=ej([],[]),x=ej(["vlist"],[y]);x.style.height=W(-c);var w=ej(["vlist-s"],[new er("​")]);r=[ej(["vlist-r"],[b,w]),ej(["vlist-r"],[x])]}else r=[ej(["vlist-r"],[b])];var k=ej(["vlist-t"],r);return 2===r.length&&k.classes.push("vlist-t2"),k.height=p,k.depth=-c,k},makeOrd:function(e,t,r){var a=e.mode,n=e.text,i=["mord"],s="math"===a||"text"===a&&t.font,o=s?t.font:t.fontFamily,l="",h="";if(55349===n.charCodeAt(0)&&([l,h]=eU(n,a)),l.length>0)return eX(n,l,a,t,i.concat(h));if(o){if("boldsymbol"===o){var m,c,p="textord"!==r&&eY(n,"Math-BoldItalic",a).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"};m=p.fontName,c=[p.fontClass]}else s?(m=eQ[o].fontName,c=[o]):(m=eJ(o,t.fontWeight,t.fontShape),c=[o,t.fontWeight,t.fontShape]);if(eY(n,m,a).metrics)return eX(n,m,a,t,i.concat(c));if(eS.hasOwnProperty(n)&&"Typewriter"===m.slice(0,10)){for(var u=[],d=0;d<n.length;d++)u.push(eX(n[d],m,a,t,i.concat(c)));return eZ(u)}}if("mathord"===r)return eX(n,"Math-Italic",a,t,i.concat(["mathnormal"]));if("textord"===r){var g=eh[a][n]&&eh[a][n].font;if("ams"===g)return eX(n,eJ("amsrm",t.fontWeight,t.fontShape),a,t,i.concat("amsrm",t.fontWeight,t.fontShape));if("main"===g||!g)return eX(n,eJ("textrm",t.fontWeight,t.fontShape),a,t,i.concat(t.fontWeight,t.fontShape));var f=eJ(g,t.fontWeight,t.fontShape);return eX(n,f,a,t,i.concat(f,t.fontWeight,t.fontShape))}throw Error("unexpected type: "+r+" in makeOrd")},makeGlue:(e,t)=>{var r=ej(["mspace"],[],t),a=X(e,t);return r.style.marginRight=W(a),r},staticSvg:function(e,t){var[r,a,n]=e0[e],i=e$(["overlay"],[new ea([new en(r)],{width:W(a),height:W(n),style:"width:"+W(a),viewBox:"0 0 "+1e3*a+" "+1e3*n,preserveAspectRatio:"xMinYMin"})],t);return i.height=n,i.style.height=W(n),i.style.width=W(a),i},svgData:e0,tryCombineChars:e=>{for(var t=0;t<e.length-1;t++){var r=e[t],a=e[t+1];r instanceof er&&a instanceof er&&eW(r,a)&&(r.text+=a.text,r.height=Math.max(r.height,a.height),r.depth=Math.max(r.depth,a.depth),r.italic=a.italic,e.splice(t+1,1),t--)}return e}},e4={number:3,unit:"mu"},e5={number:4,unit:"mu"},e6={number:5,unit:"mu"},e7={mord:{mop:e4,mbin:e5,mrel:e6,minner:e4},mop:{mord:e4,mop:e4,mrel:e6,minner:e4},mbin:{mord:e5,mop:e5,mopen:e5,minner:e5},mrel:{mord:e6,mop:e6,mopen:e6,minner:e6},mopen:{},mclose:{mop:e4,mbin:e5,mrel:e6,minner:e4},mpunct:{mord:e4,mop:e4,mrel:e6,mopen:e4,mclose:e4,mpunct:e4,minner:e4},minner:{mord:e4,mop:e4,mbin:e5,mrel:e6,mopen:e4,mpunct:e4,minner:e4}},e3={mord:{mop:e4},mop:{mord:e4,mop:e4},mbin:{},mrel:{},mopen:{},mclose:{mop:e4},mpunct:{},minner:{mop:e4}},e8={},e2={},e9={};function te(e){for(var{type:t,names:r,props:a,handler:n,htmlBuilder:i,mathmlBuilder:s}=e,o={type:t,numArgs:a.numArgs,argTypes:a.argTypes,allowedInArgument:!!a.allowedInArgument,allowedInText:!!a.allowedInText,allowedInMath:void 0===a.allowedInMath||a.allowedInMath,numOptionalArgs:a.numOptionalArgs||0,infix:!!a.infix,primitive:!!a.primitive,handler:n},l=0;l<r.length;++l)e8[r[l]]=o;t&&(i&&(e2[t]=i),s&&(e9[t]=s))}function tt(e){var{type:t,htmlBuilder:r,mathmlBuilder:a}=e;te({type:t,names:[],props:{numArgs:0},handler(){throw Error("Should never be called.")},htmlBuilder:r,mathmlBuilder:a})}var tr=function(e){return"ordgroup"===e.type&&1===e.body.length?e.body[0]:e},ta=function(e){return"ordgroup"===e.type?e.body:[e]},tn=e1.makeSpan,ti=["leftmost","mbin","mopen","mrel","mop","mpunct"],ts=["rightmost","mrel","mclose","mpunct"],to={display:M.DISPLAY,text:M.TEXT,script:M.SCRIPT,scriptscript:M.SCRIPTSCRIPT},tl={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},th=function(e,t,r,a){void 0===a&&(a=[null,null]);for(var n=[],i=0;i<e.length;i++){var s=tg(e[i],t);if(s instanceof I){var o=s.children;n.push(...o)}else n.push(s)}if(e1.tryCombineChars(n),!r)return n;var l=t;if(1===e.length){var h=e[0];"sizing"===h.type?l=t.havingSize(h.size):"styling"===h.type&&(l=t.havingStyle(to[h.style]))}var m=tn([a[0]||"leftmost"],[],t),c=tn([a[1]||"rightmost"],[],t),p="root"===r;return tm(n,(e,t)=>{var r=t.classes[0],a=e.classes[0];"mbin"===r&&u.contains(ts,a)?t.classes[0]="mord":"mbin"===a&&u.contains(ti,r)&&(e.classes[0]="mord")},{node:m},c,p),tm(n,(e,t)=>{var r=tu(t),a=tu(e),n=r&&a?e.hasClass("mtight")?e3[r][a]:e7[r][a]:null;if(n)return e1.makeGlue(n,l)},{node:m},c,p),n},tm=function e(t,r,a,n,i){n&&t.push(n);for(var s=0;s<t.length;s++){let n;var o=t[s],l=tc(o);if(l){e(l.children,r,a,null,i);continue}var h=!o.hasClass("mspace");if(h){var m=r(o,a.node);m&&(a.insertAfter?a.insertAfter(m):(t.unshift(m),s++))}h?a.node=o:i&&o.hasClass("newline")&&(a.node=tn(["leftmost"])),n=s,a.insertAfter=e=>{t.splice(n+1,0,e),s++}}n&&t.pop()},tc=function(e){return e instanceof I||e instanceof Q||e instanceof J&&e.hasClass("enclosing")?e:null},tp=function e(t,r){var a=tc(t);if(a){var n=a.children;if(n.length){if("right"===r)return e(n[n.length-1],"right");if("left"===r)return e(n[0],"left")}}return t},tu=function(e,t){return e?(t&&(e=tp(e,t)),tl[e.classes[0]]||null):null},td=function(e,t){var r=["nulldelimiter"].concat(e.baseSizingClasses());return tn(t.concat(r))},tg=function(e,t,r){if(!e)return tn();if(e2[e.type]){var a=e2[e.type](e,t);if(r&&t.size!==r.size){a=tn(t.sizingClasses(r),[a],t);var n=t.sizeMultiplier/r.sizeMultiplier;a.height*=n,a.depth*=n}return a}throw new i("Got group of unknown type: '"+e.type+"'")};function tf(e,t){var r=tn(["base"],e,t),a=tn(["strut"]);return a.style.height=W(r.height+r.depth),r.depth&&(a.style.verticalAlign=W(-r.depth)),r.children.unshift(a),r}function tv(e,t){var r,a,n=null;1===e.length&&"tag"===e[0].type&&(n=e[0].tag,e=e[0].body);var i=th(e,t,"root");2===i.length&&i[1].hasClass("tag")&&(r=i.pop());for(var s=[],o=[],l=0;l<i.length;l++)if(o.push(i[l]),i[l].hasClass("mbin")||i[l].hasClass("mrel")||i[l].hasClass("allowbreak")){for(var h=!1;l<i.length-1&&i[l+1].hasClass("mspace")&&!i[l+1].hasClass("newline");)l++,o.push(i[l]),i[l].hasClass("nobreak")&&(h=!0);h||(s.push(tf(o,t)),o=[])}else i[l].hasClass("newline")&&(o.pop(),o.length>0&&(s.push(tf(o,t)),o=[]),s.push(i[l]));o.length>0&&s.push(tf(o,t)),n?((a=tf(th(n,t,!0))).classes=["tag"],s.push(a)):r&&s.push(r);var m=tn(["katex-html"],s);if(m.setAttribute("aria-hidden","true"),a){var c=a.children[0];c.style.height=W(m.height+m.depth),m.depth&&(c.style.verticalAlign=W(-m.depth))}return m}function tb(e){return new I(e)}class ty{constructor(e,t,r){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=e,this.attributes={},this.children=t||[],this.classes=r||[]}setAttribute(e,t){this.attributes[e]=t}getAttribute(e){return this.attributes[e]}toNode(){var e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);this.classes.length>0&&(e.className=_(this.classes));for(var r=0;r<this.children.length;r++)if(this.children[r]instanceof tx&&this.children[r+1]instanceof tx){for(var a=this.children[r].toText()+this.children[++r].toText();this.children[r+1]instanceof tx;)a+=this.children[++r].toText();e.appendChild(new tx(a).toNode())}else e.appendChild(this.children[r].toNode());return e}toMarkup(){var e="<"+this.type;for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="',e+=u.escape(this.attributes[t]),e+='"');this.classes.length>0&&(e+=' class ="'+u.escape(_(this.classes))+'"'),e+=">";for(var r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+("</"+this.type+">")}toText(){return this.children.map(e=>e.toText()).join("")}}class tx{constructor(e){this.text=void 0,this.text=e}toNode(){return document.createTextNode(this.text)}toMarkup(){return u.escape(this.toText())}toText(){return this.text}}class tw{constructor(e){this.width=void 0,this.character=void 0,this.width=e,e>=.05555&&e<=.05556?this.character=" ":e>=.1666&&e<=.1667?this.character=" ":e>=.2222&&e<=.2223?this.character=" ":e>=.2777&&e<=.2778?this.character="  ":e>=-.05556&&e<=-.05555?this.character=" ⁣":e>=-.1667&&e<=-.1666?this.character=" ⁣":e>=-.2223&&e<=-.2222?this.character=" ⁣":e>=-.2778&&e<=-.2777?this.character=" ⁣":this.character=null}toNode(){if(this.character)return document.createTextNode(this.character);var e=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return e.setAttribute("width",W(this.width)),e}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+W(this.width)+'"/>'}toText(){return this.character?this.character:" "}}var tk={MathNode:ty,TextNode:tx,SpaceNode:tw,newDocumentFragment:tb},tS=function(e,t,r){return eh[t][e]&&eh[t][e].replace&&55349!==e.charCodeAt(0)&&!(eS.hasOwnProperty(e)&&r&&(r.fontFamily&&"tt"===r.fontFamily.slice(4,6)||r.font&&"tt"===r.font.slice(4,6)))&&(e=eh[t][e].replace),new tk.TextNode(e)},tM=function(e){return 1===e.length?e[0]:new tk.MathNode("mrow",e)},tz=function(e,t){if("texttt"===t.fontFamily)return"monospace";if("textsf"===t.fontFamily)return"textit"===t.fontShape&&"textbf"===t.fontWeight?"sans-serif-bold-italic":"textit"===t.fontShape?"sans-serif-italic":"textbf"===t.fontWeight?"bold-sans-serif":"sans-serif";if("textit"===t.fontShape&&"textbf"===t.fontWeight)return"bold-italic";if("textit"===t.fontShape)return"italic";if("textbf"===t.fontWeight)return"bold";var r=t.font;if(!r||"mathnormal"===r)return null;var a=e.mode;if("mathit"===r)return"italic";if("boldsymbol"===r)return"textord"===e.type?"bold":"bold-italic";if("mathbf"===r)return"bold";if("mathbb"===r)return"double-struck";if("mathsfit"===r)return"sans-serif-italic";else if("mathfrak"===r)return"fraktur";else if("mathscr"===r||"mathcal"===r)return"script";else if("mathsf"===r)return"sans-serif";else if("mathtt"===r)return"monospace";var n=e.text;return u.contains(["\\imath","\\jmath"],n)?null:(eh[a][n]&&eh[a][n].replace&&(n=eh[a][n].replace),E(n,e1.fontMap[r].fontName,a))?e1.fontMap[r].variant:null};function tA(e){if(!e)return!1;if("mi"===e.type&&1===e.children.length){var t=e.children[0];return t instanceof tx&&"."===t.text}if("mo"!==e.type||1!==e.children.length||"true"!==e.getAttribute("separator")||"0em"!==e.getAttribute("lspace")||"0em"!==e.getAttribute("rspace"))return!1;var r=e.children[0];return r instanceof tx&&","===r.text}var tT=function(e,t,r){if(1===e.length){var a,n=tC(e[0],t);return r&&n instanceof ty&&"mo"===n.type&&(n.setAttribute("lspace","0em"),n.setAttribute("rspace","0em")),[n]}for(var i=[],s=0;s<e.length;s++){var o=tC(e[s],t);if(o instanceof ty&&a instanceof ty){if("mtext"===o.type&&"mtext"===a.type&&o.getAttribute("mathvariant")===a.getAttribute("mathvariant")){a.children.push(...o.children);continue}if("mn"===o.type&&"mn"===a.type){a.children.push(...o.children);continue}if(tA(o)&&"mn"===a.type){a.children.push(...o.children);continue}else if("mn"===o.type&&tA(a))o.children=[...a.children,...o.children],i.pop();else if(("msup"===o.type||"msub"===o.type)&&o.children.length>=1&&("mn"===a.type||tA(a))){var l=o.children[0];l instanceof ty&&"mn"===l.type&&(l.children=[...a.children,...l.children],i.pop())}else if("mi"===a.type&&1===a.children.length){var h=a.children[0];if(h instanceof tx&&"̸"===h.text&&("mo"===o.type||"mi"===o.type||"mn"===o.type)){var m=o.children[0];m instanceof tx&&m.text.length>0&&(m.text=m.text.slice(0,1)+"̸"+m.text.slice(1),i.pop())}}}i.push(o),a=o}return i},tB=function(e,t,r){return tM(tT(e,t,r))},tC=function(e,t){if(!e)return new tk.MathNode("mrow");if(e9[e.type])return e9[e.type](e,t);throw new i("Got group of unknown type: '"+e.type+"'")};function tq(e,t,r,a,n){var i,s=tT(e,r);i=1===s.length&&s[0]instanceof ty&&u.contains(["mrow","mtable"],s[0].type)?s[0]:new tk.MathNode("mrow",s);var o=new tk.MathNode("annotation",[new tk.TextNode(t)]);o.setAttribute("encoding","application/x-tex");var l=new tk.MathNode("semantics",[i,o]),h=new tk.MathNode("math",[l]);return h.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),a&&h.setAttribute("display","block"),e1.makeSpan([n?"katex":"katex-mathml"],[h])}var tN=function(e){return new F({style:e.displayMode?M.DISPLAY:M.TEXT,maxSize:e.maxSize,minRuleThickness:e.minRuleThickness})},tI=function(e,t){if(t.displayMode){var r=["katex-display"];t.leqno&&r.push("leqno"),t.fleqn&&r.push("fleqn"),e=e1.makeSpan(r,[e])}return e},tH=function(e,t,r){var a,n=tN(r);if("mathml"===r.output)return tq(e,t,n,r.displayMode,!0);if("html"===r.output){var i=tv(e,n);a=e1.makeSpan(["katex"],[i])}else{var s=tq(e,t,n,r.displayMode,!1),o=tv(e,n);a=e1.makeSpan(["katex"],[s,o])}return tI(a,r)},tR=function(e,t,r){var a=tv(e,tN(r));return tI(e1.makeSpan(["katex"],[a]),r)},tO={widehat:"^",widecheck:"ˇ",widetilde:"~",utilde:"~",overleftarrow:"←",underleftarrow:"←",xleftarrow:"←",overrightarrow:"→",underrightarrow:"→",xrightarrow:"→",underbrace:"⏟",overbrace:"⏞",overgroup:"⏠",undergroup:"⏡",overleftrightarrow:"↔",underleftrightarrow:"↔",xleftrightarrow:"↔",Overrightarrow:"⇒",xRightarrow:"⇒",overleftharpoon:"↼",xleftharpoonup:"↼",overrightharpoon:"⇀",xrightharpoonup:"⇀",xLeftarrow:"⇐",xLeftrightarrow:"⇔",xhookleftarrow:"↩",xhookrightarrow:"↪",xmapsto:"↦",xrightharpoondown:"⇁",xleftharpoondown:"↽",xrightleftharpoons:"⇌",xleftrightharpoons:"⇋",xtwoheadleftarrow:"↞",xtwoheadrightarrow:"↠",xlongequal:"=",xtofrom:"⇄",xrightleftarrows:"⇄",xrightequilibrium:"⇌",xleftequilibrium:"⇋","\\cdrightarrow":"→","\\cdleftarrow":"←","\\cdlongequal":"="},tE={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},tL={encloseSpan:function(e,t,r,a,n){var i,s=e.height+e.depth+r+a;if(/fbox|color|angl/.test(t)){if(i=e1.makeSpan(["stretchy",t],[],n),"fbox"===t){var o=n.color&&n.getColor();o&&(i.style.borderColor=o)}}else{var l=[];/^[bx]cancel$/.test(t)&&l.push(new ei({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(t)&&l.push(new ei({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));var h=new ea(l,{width:"100%",height:W(s)});i=e1.makeSvgSpan([],[h],n)}return i.height=s,i.style.height=W(s),i},mathMLnode:function(e){var t=new tk.MathNode("mo",[new tk.TextNode(tO[e.replace(/^\\/,"")])]);return t.setAttribute("stretchy","true"),t},svgSpan:function(e,t){var{span:r,minWidth:a,height:n}=function(){var r=4e5,a=e.label.slice(1);if(u.contains(["widehat","widecheck","widetilde","utilde"],a)){var n,i,s,o,l="ordgroup"===(o=e.base).type?o.body.length:1;if(l>5)"widehat"===a||"widecheck"===a?(n=420,r=2364,s=.42,i=a+"4"):(n=312,r=2340,s=.34,i="tilde4");else{var h=[1,1,2,2,3,3][l];"widehat"===a||"widecheck"===a?(r=[0,1062,2364,2364,2364][h],n=[0,239,300,360,420][h],s=[0,.24,.3,.3,.36,.42][h],i=a+h):(r=[0,600,1033,2339,2340][h],n=[0,260,286,306,312][h],s=[0,.26,.286,.3,.306,.34][h],i="tilde"+h)}var m=new ea([new en(i)],{width:"100%",height:W(s),viewBox:"0 0 "+r+" "+n,preserveAspectRatio:"none"});return{span:e1.makeSvgSpan([],[m],t),minWidth:0,height:s}}var c,p,d=[],g=tE[a],[f,v,b]=g,y=b/1e3,x=f.length;if(1===x)c=["hide-tail"],p=[g[3]];else if(2===x)c=["halfarrow-left","halfarrow-right"],p=["xMinYMin","xMaxYMin"];else if(3===x)c=["brace-left","brace-center","brace-right"],p=["xMinYMin","xMidYMin","xMaxYMin"];else throw Error("Correct katexImagesData or update code here to support\n                    "+x+" children.");for(var w=0;w<x;w++){var k=new ea([new en(f[w])],{width:"400em",height:W(y),viewBox:"0 0 "+r+" "+b,preserveAspectRatio:p[w]+" slice"}),S=e1.makeSvgSpan([c[w]],[k],t);if(1===x)return{span:S,minWidth:v,height:y};S.style.height=W(y),d.push(S)}return{span:e1.makeSpan(["stretchy"],d,t),minWidth:v,height:y}}();return r.height=n,r.style.height=W(n),a>0&&(r.style.minWidth=W(a)),r}};function tD(e,t){if(!e||e.type!==t)throw Error("Expected node of type "+t+", but got "+(e?"node of type "+e.type:String(e)));return e}function tV(e){var t=tP(e);if(!t)throw Error("Expected node of symbol group type, but got "+(e?"node of type "+e.type:String(e)));return t}function tP(e){return e&&("atom"===e.type||el.hasOwnProperty(e.type))?e:null}var tF=(e,t)=>{e&&"supsub"===e.type?(o=(l=tD(e.base,"accent")).base,e.base=o,h=function(e){if(e instanceof J)return e;throw Error("Expected span<HtmlDomNode> but got "+String(e)+".")}(tg(e,t)),e.base=l):o=(l=tD(e,"accent")).base;var r=tg(o,t.havingCrampedStyle()),a=l.isShifty&&u.isCharacterBox(o),n=0;a&&(n=es(tg(u.getBaseElem(o),t.havingCrampedStyle())).skew);var i="\\c"===l.label,s=i?r.height+r.depth:Math.min(r.height,t.fontMetrics().xHeight);if(l.isStretchy)p=tL.svgSpan(l,t),p=e1.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"elem",elem:p,wrapperClasses:["svg-align"],wrapperStyle:n>0?{width:"calc(100% - "+W(2*n)+")",marginLeft:W(2*n)}:void 0}]},t);else{"\\vec"===l.label?(m=e1.staticSvg("vec",t),c=e1.svgData.vec[1]):((m=es(m=e1.makeOrd({mode:l.mode,text:l.label},t,"textord"))).italic=0,c=m.width,i&&(s+=m.depth));var o,l,h,m,c,p=e1.makeSpan(["accent-body"],[m]),d="\\textcircled"===l.label;d&&(p.classes.push("accent-full"),s=r.height);var g=n;d||(g-=c/2),p.style.left=W(g),"\\textcircled"===l.label&&(p.style.top=".2em"),p=e1.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"kern",size:-s},{type:"elem",elem:p}]},t)}var f=e1.makeSpan(["mord","accent"],[p],t);return h?(h.children[0]=f,h.height=Math.max(f.height,h.height),h.classes[0]="mord",h):f},tG=(e,t)=>{var r=e.isStretchy?tL.mathMLnode(e.label):new tk.MathNode("mo",[tS(e.label,e.mode)]),a=new tk.MathNode("mover",[tC(e.base,t),r]);return a.setAttribute("accent","true"),a},tU=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(e=>"\\"+e).join("|"));te({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(e,t)=>{var r=tr(t[0]),a=!tU.test(e.funcName),n=!a||"\\widehat"===e.funcName||"\\widetilde"===e.funcName||"\\widecheck"===e.funcName;return{type:"accent",mode:e.parser.mode,label:e.funcName,isStretchy:a,isShifty:n,base:r}},htmlBuilder:tF,mathmlBuilder:tG}),te({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(e,t)=>{var r=t[0],a=e.parser.mode;return"math"===a&&(e.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+e.funcName+" works only in text mode"),a="text"),{type:"accent",mode:a,label:e.funcName,isStretchy:!1,isShifty:!0,base:r}},htmlBuilder:tF,mathmlBuilder:tG}),te({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0];return{type:"accentUnder",mode:r.mode,label:a,base:n}},htmlBuilder:(e,t)=>{var r=tg(e.base,t),a=tL.svgSpan(e,t),n=.12*("\\utilde"===e.label),i=e1.makeVList({positionType:"top",positionData:r.height,children:[{type:"elem",elem:a,wrapperClasses:["svg-align"]},{type:"kern",size:n},{type:"elem",elem:r}]},t);return e1.makeSpan(["mord","accentunder"],[i],t)},mathmlBuilder:(e,t)=>{var r=tL.mathMLnode(e.label),a=new tk.MathNode("munder",[tC(e.base,t),r]);return a.setAttribute("accentunder","true"),a}});var tY=e=>{var t=new tk.MathNode("mpadded",e?[e]:[]);return t.setAttribute("width","+0.6em"),t.setAttribute("lspace","0.3em"),t};te({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(e,t,r){var{parser:a,funcName:n}=e;return{type:"xArrow",mode:a.mode,label:n,body:t[0],below:r[0]}},htmlBuilder(e,t){var r,a,n=t.style,i=t.havingStyle(n.sup()),s=e1.wrapFragment(tg(e.body,i,t),t),o="\\x"===e.label.slice(0,2)?"x":"cd";s.classes.push(o+"-arrow-pad"),e.below&&(i=t.havingStyle(n.sub()),(r=e1.wrapFragment(tg(e.below,i,t),t)).classes.push(o+"-arrow-pad"));var l=tL.svgSpan(e,t),h=-t.fontMetrics().axisHeight+.5*l.height,m=-t.fontMetrics().axisHeight-.5*l.height-.111;if((s.depth>.25||"\\xleftequilibrium"===e.label)&&(m-=s.depth),r){var c=-t.fontMetrics().axisHeight+r.height+.5*l.height+.111;a=e1.makeVList({positionType:"individualShift",children:[{type:"elem",elem:s,shift:m},{type:"elem",elem:l,shift:h},{type:"elem",elem:r,shift:c}]},t)}else a=e1.makeVList({positionType:"individualShift",children:[{type:"elem",elem:s,shift:m},{type:"elem",elem:l,shift:h}]},t);return a.children[0].children[0].children[1].classes.push("svg-align"),e1.makeSpan(["mrel","x-arrow"],[a],t)},mathmlBuilder(e,t){var r,a=tL.mathMLnode(e.label);if(a.setAttribute("minsize","x"===e.label.charAt(0)?"1.75em":"3.0em"),e.body){var n=tY(tC(e.body,t));if(e.below){var i=tY(tC(e.below,t));r=new tk.MathNode("munderover",[a,i,n])}else r=new tk.MathNode("mover",[a,n])}else if(e.below){var s=tY(tC(e.below,t));r=new tk.MathNode("munder",[a,s])}else r=tY(),r=new tk.MathNode("mover",[a,r]);return r}});var tX=e1.makeSpan;function tW(e,t){var r=th(e.body,t,!0);return tX([e.mclass],r,t)}function t_(e,t){var r,a=tT(e.body,t);return"minner"===e.mclass?r=new tk.MathNode("mpadded",a):"mord"===e.mclass?e.isCharacterBox?(r=a[0]).type="mi":r=new tk.MathNode("mi",a):(e.isCharacterBox?(r=a[0]).type="mo":r=new tk.MathNode("mo",a),"mbin"===e.mclass?(r.attributes.lspace="0.22em",r.attributes.rspace="0.22em"):"mpunct"===e.mclass?(r.attributes.lspace="0em",r.attributes.rspace="0.17em"):"mopen"===e.mclass||"mclose"===e.mclass?(r.attributes.lspace="0em",r.attributes.rspace="0em"):"minner"===e.mclass&&(r.attributes.lspace="0.0556em",r.attributes.width="+0.1111em")),r}te({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(e,t){var{parser:r,funcName:a}=e,n=t[0];return{type:"mclass",mode:r.mode,mclass:"m"+a.slice(5),body:ta(n),isCharacterBox:u.isCharacterBox(n)}},htmlBuilder:tW,mathmlBuilder:t_});var tj=e=>{var t="ordgroup"===e.type&&e.body.length?e.body[0]:e;return"atom"===t.type&&("bin"===t.family||"rel"===t.family)?"m"+t.family:"mord"};te({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(e,t){var{parser:r}=e;return{type:"mclass",mode:r.mode,mclass:tj(t[0]),body:ta(t[1]),isCharacterBox:u.isCharacterBox(t[1])}}}),te({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(e,t){var r,{parser:a,funcName:n}=e,i=t[1],s=t[0];r="\\stackrel"!==n?tj(i):"mrel";var o={type:"op",mode:i.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:"\\stackrel"!==n,body:ta(i)},l={type:"supsub",mode:s.mode,base:o,sup:"\\underset"===n?null:s,sub:"\\underset"===n?s:null};return{type:"mclass",mode:a.mode,mclass:r,body:[l],isCharacterBox:u.isCharacterBox(l)}},htmlBuilder:tW,mathmlBuilder:t_}),te({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(e,t){var{parser:r}=e;return{type:"pmb",mode:r.mode,mclass:tj(t[0]),body:ta(t[0])}},htmlBuilder(e,t){var r=th(e.body,t,!0),a=e1.makeSpan([e.mclass],r,t);return a.style.textShadow="0.02em 0.01em 0.04px",a},mathmlBuilder(e,t){var r=tT(e.body,t),a=new tk.MathNode("mstyle",r);return a.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),a}});var t$={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},tZ=()=>({type:"styling",body:[],mode:"math",style:"display"}),tK=e=>"textord"===e.type&&"@"===e.text,tJ=(e,t)=>("mathord"===e.type||"atom"===e.type)&&e.text===t;te({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(e,t){var{parser:r,funcName:a}=e;return{type:"cdlabel",mode:r.mode,side:a.slice(4),label:t[0]}},htmlBuilder(e,t){var r=t.havingStyle(t.style.sup()),a=e1.wrapFragment(tg(e.label,r,t),t);return a.classes.push("cd-label-"+e.side),a.style.bottom=W(.8-a.depth),a.height=0,a.depth=0,a},mathmlBuilder(e,t){var r=new tk.MathNode("mrow",[tC(e.label,t)]);return(r=new tk.MathNode("mpadded",[r])).setAttribute("width","0"),"left"===e.side&&r.setAttribute("lspace","-1width"),r.setAttribute("voffset","0.7em"),(r=new tk.MathNode("mstyle",[r])).setAttribute("displaystyle","false"),r.setAttribute("scriptlevel","1"),r}}),te({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(e,t){var{parser:r}=e;return{type:"cdlabelparent",mode:r.mode,fragment:t[0]}},htmlBuilder(e,t){var r=e1.wrapFragment(tg(e.fragment,t),t);return r.classes.push("cd-vert-arrow"),r},mathmlBuilder:(e,t)=>new tk.MathNode("mrow",[tC(e.fragment,t)])}),te({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(e,t){for(var r,{parser:a}=e,n=tD(t[0],"ordgroup").body,s="",o=0;o<n.length;o++)s+=tD(n[o],"textord").text;var l=parseInt(s);if(isNaN(l))throw new i("\\@char has non-numeric argument "+s);if(l<0||l>=1114111)throw new i("\\@char with invalid code point "+s);return l<=65535?r=String.fromCharCode(l):(l-=65536,r=String.fromCharCode((l>>10)+55296,(1023&l)+56320)),{type:"textord",mode:a.mode,text:r}}});var tQ=(e,t)=>{var r=th(e.body,t.withColor(e.color),!1);return e1.makeFragment(r)},t0=(e,t)=>{var r=tT(e.body,t.withColor(e.color)),a=new tk.MathNode("mstyle",r);return a.setAttribute("mathcolor",e.color),a};te({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(e,t){var{parser:r}=e,a=tD(t[0],"color-token").color,n=t[1];return{type:"color",mode:r.mode,color:a,body:ta(n)}},htmlBuilder:tQ,mathmlBuilder:t0}),te({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(e,t){var{parser:r,breakOnTokenText:a}=e,n=tD(t[0],"color-token").color;r.gullet.macros.set("\\current@color",n);var i=r.parseExpression(!0,a);return{type:"color",mode:r.mode,color:n,body:i}},htmlBuilder:tQ,mathmlBuilder:t0}),te({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler(e,t,r){var{parser:a}=e,n="["===a.gullet.future().text?a.parseSizeGroup(!0):null,i=!a.settings.displayMode||!a.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:a.mode,newLine:i,size:n&&tD(n,"size").value}},htmlBuilder(e,t){var r=e1.makeSpan(["mspace"],[],t);return e.newLine&&(r.classes.push("newline"),e.size&&(r.style.marginTop=W(X(e.size,t)))),r},mathmlBuilder(e,t){var r=new tk.MathNode("mspace");return e.newLine&&(r.setAttribute("linebreak","newline"),e.size&&r.setAttribute("height",W(X(e.size,t)))),r}});var t1={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},t4=e=>{var t=e.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(t))throw new i("Expected a control sequence",e);return t},t5=e=>{var t=e.gullet.popToken();return"="===t.text&&" "===(t=e.gullet.popToken()).text&&(t=e.gullet.popToken()),t},t6=(e,t,r,a)=>{var n=e.gullet.macros.get(r.text);null==n&&(r.noexpand=!0,n={tokens:[r],numArgs:0,unexpandable:!e.gullet.isExpandable(r.text)}),e.gullet.macros.set(t,n,a)};te({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(e){var{parser:t,funcName:r}=e;t.consumeSpaces();var a=t.fetch();if(t1[a.text])return("\\global"===r||"\\\\globallong"===r)&&(a.text=t1[a.text]),tD(t.parseFunction(),"internal");throw new i("Invalid token after macro prefix",a)}}),te({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){var t,{parser:r,funcName:a}=e,n=r.gullet.popToken(),s=n.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(s))throw new i("Expected a control sequence",n);for(var o=0,l=[[]];"{"!==r.gullet.future().text;)if("#"===(n=r.gullet.popToken()).text){if("{"===r.gullet.future().text){t=r.gullet.future(),l[o].push("{");break}if(n=r.gullet.popToken(),!/^[1-9]$/.test(n.text))throw new i('Invalid argument number "'+n.text+'"');if(parseInt(n.text)!==o+1)throw new i('Argument number "'+n.text+'" out of order');o++,l.push([])}else if("EOF"===n.text)throw new i("Expected a macro definition");else l[o].push(n.text);var{tokens:h}=r.gullet.consumeArg();return t&&h.unshift(t),("\\edef"===a||"\\xdef"===a)&&(h=r.gullet.expandTokens(h)).reverse(),r.gullet.macros.set(s,{tokens:h,numArgs:o,delimiters:l},a===t1[a]),{type:"internal",mode:r.mode}}}),te({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){var{parser:t,funcName:r}=e,a=t4(t.gullet.popToken());t.gullet.consumeSpaces();var n=t5(t);return t6(t,a,n,"\\\\globallet"===r),{type:"internal",mode:t.mode}}}),te({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){var{parser:t,funcName:r}=e,a=t4(t.gullet.popToken()),n=t.gullet.popToken(),i=t.gullet.popToken();return t6(t,a,i,"\\\\globalfuture"===r),t.gullet.pushToken(i),t.gullet.pushToken(n),{type:"internal",mode:t.mode}}});var t7=function(e,t,r){var a=E(eh.math[e]&&eh.math[e].replace||e,t,r);if(!a)throw Error("Unsupported symbol "+e+" and font size "+t+".");return a},t3=function(e,t,r,a){var n=r.havingBaseStyle(t),i=e1.makeSpan(a.concat(n.sizingClasses(r)),[e],r),s=n.sizeMultiplier/r.sizeMultiplier;return i.height*=s,i.depth*=s,i.maxFontSize=n.sizeMultiplier,i},t8=function(e,t,r){var a=t.havingBaseStyle(r),n=(1-t.sizeMultiplier/a.sizeMultiplier)*t.fontMetrics().axisHeight;e.classes.push("delimcenter"),e.style.top=W(n),e.height-=n,e.depth+=n},t2=function(e,t,r,a,n,i){var s=t3(e1.makeSymbol(e,"Main-Regular",n,a),t,a,i);return r&&t8(s,a,t),s},t9=function(e,t,r,a,n,i){var s=e1.makeSymbol(e,"Size"+t+"-Regular",n,a),o=t3(e1.makeSpan(["delimsizing","size"+t],[s],a),M.TEXT,a,i);return r&&t8(o,a,M.TEXT),o},re=function(e,t,r){return{type:"elem",elem:e1.makeSpan(["delimsizinginner","Size1-Regular"===t?"delim-size1":"delim-size4"],[e1.makeSpan([],[e1.makeSymbol(e,t,r)])])}},rt=function(e,t,r){var a=H["Size4-Regular"][e.charCodeAt(0)]?H["Size4-Regular"][e.charCodeAt(0)][4]:H["Size1-Regular"][e.charCodeAt(0)][4],n=new ea([new en("inner",C(e,Math.round(1e3*t)))],{width:W(a),height:W(t),style:"width:"+W(a),viewBox:"0 0 "+1e3*a+" "+Math.round(1e3*t),preserveAspectRatio:"xMinYMin"}),i=e1.makeSvgSpan([],[n],r);return i.height=t,i.style.height=W(t),i.style.width=W(a),{type:"elem",elem:i}},rr={type:"kern",size:-.008},ra=["|","\\lvert","\\rvert","\\vert"],rn=["\\|","\\lVert","\\rVert","\\Vert"],ri=function(e,t,r,a,n,i){var s,o,l,h,m="",c=0;s=l=h=e,o=null;var p="Size1-Regular";"\\uparrow"===e?l=h="⏐":"\\Uparrow"===e?l=h="‖":"\\downarrow"===e?s=l="⏐":"\\Downarrow"===e?s=l="‖":"\\updownarrow"===e?(s="\\uparrow",l="⏐",h="\\downarrow"):"\\Updownarrow"===e?(s="\\Uparrow",l="‖",h="\\Downarrow"):u.contains(ra,e)?(l="∣",m="vert",c=333):u.contains(rn,e)?(l="∥",m="doublevert",c=556):"["===e||"\\lbrack"===e?(s="⎡",l="⎢",h="⎣",p="Size4-Regular",m="lbrack",c=667):"]"===e||"\\rbrack"===e?(s="⎤",l="⎥",h="⎦",p="Size4-Regular",m="rbrack",c=667):"\\lfloor"===e||"⌊"===e?(l=s="⎢",h="⎣",p="Size4-Regular",m="lfloor",c=667):"\\lceil"===e||"⌈"===e?(s="⎡",l=h="⎢",p="Size4-Regular",m="lceil",c=667):"\\rfloor"===e||"⌋"===e?(l=s="⎥",h="⎦",p="Size4-Regular",m="rfloor",c=667):"\\rceil"===e||"⌉"===e?(s="⎤",l=h="⎥",p="Size4-Regular",m="rceil",c=667):"("===e||"\\lparen"===e?(s="⎛",l="⎜",h="⎝",p="Size4-Regular",m="lparen",c=875):")"===e||"\\rparen"===e?(s="⎞",l="⎟",h="⎠",p="Size4-Regular",m="rparen",c=875):"\\{"===e||"\\lbrace"===e?(s="⎧",o="⎨",h="⎩",l="⎪",p="Size4-Regular"):"\\}"===e||"\\rbrace"===e?(s="⎫",o="⎬",h="⎭",l="⎪",p="Size4-Regular"):"\\lgroup"===e||"⟮"===e?(s="⎧",h="⎩",l="⎪",p="Size4-Regular"):"\\rgroup"===e||"⟯"===e?(s="⎫",h="⎭",l="⎪",p="Size4-Regular"):"\\lmoustache"===e||"⎰"===e?(s="⎧",h="⎭",l="⎪",p="Size4-Regular"):("\\rmoustache"===e||"⎱"===e)&&(s="⎫",h="⎩",l="⎪",p="Size4-Regular");var d=t7(s,p,n),g=d.height+d.depth,f=t7(l,p,n),v=f.height+f.depth,b=t7(h,p,n),y=b.height+b.depth,x=0,w=1;if(null!==o){var k=t7(o,p,n);x=k.height+k.depth,w=2}var S=g+y+x,z=Math.max(0,Math.ceil((t-S)/(w*v))),A=S+z*w*v,T=a.fontMetrics().axisHeight;r&&(T*=a.sizeMultiplier);var B=A/2-T,C=[];if(m.length>0){var q=Math.round(1e3*A),I=N(m,Math.round(1e3*(A-g-y))),H=new en(m,I),R=(c/1e3).toFixed(3)+"em",O=(q/1e3).toFixed(3)+"em",E=new ea([H],{width:R,height:O,viewBox:"0 0 "+c+" "+q}),L=e1.makeSvgSpan([],[E],a);L.height=q/1e3,L.style.width=R,L.style.height=O,C.push({type:"elem",elem:L})}else{if(C.push(re(h,p,n)),C.push(rr),null===o)C.push(rt(l,A-g-y+.016,a));else{var D=(A-g-y-x)/2+.016;C.push(rt(l,D,a)),C.push(rr),C.push(re(o,p,n)),C.push(rr),C.push(rt(l,D,a))}C.push(rr),C.push(re(s,p,n))}var V=a.havingBaseStyle(M.TEXT),P=e1.makeVList({positionType:"bottom",positionData:B,children:C},V);return t3(e1.makeSpan(["delimsizing","mult"],[P],V),M.TEXT,a,i)},rs=function(e,t,r,a,n){var i=B(e,a,r),s=new ea([new en(e,i)],{width:"400em",height:W(t),viewBox:"0 0 400000 "+r,preserveAspectRatio:"xMinYMin slice"});return e1.makeSvgSpan(["hide-tail"],[s],n)},ro=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","\\surd"],rl=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱"],rh=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],rm=[0,1.2,1.8,2.4,3],rc=[{type:"small",style:M.SCRIPTSCRIPT},{type:"small",style:M.SCRIPT},{type:"small",style:M.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],rp=[{type:"small",style:M.SCRIPTSCRIPT},{type:"small",style:M.SCRIPT},{type:"small",style:M.TEXT},{type:"stack"}],ru=[{type:"small",style:M.SCRIPTSCRIPT},{type:"small",style:M.SCRIPT},{type:"small",style:M.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],rd=function(e){if("small"===e.type)return"Main-Regular";if("large"===e.type)return"Size"+e.size+"-Regular";if("stack"===e.type)return"Size4-Regular";throw Error("Add support for delim type '"+e.type+"' here.")},rg=function(e,t,r,a){for(var n=Math.min(2,3-a.style.size),i=n;i<r.length&&"stack"!==r[i].type;i++){var s=t7(e,rd(r[i]),"math"),o=s.height+s.depth;if("small"===r[i].type&&(o*=a.havingBaseStyle(r[i].style).sizeMultiplier),o>t)return r[i]}return r[r.length-1]},rf=function(e,t,r,a,n,i){"<"===e||"\\lt"===e||"⟨"===e?e="\\langle":(">"===e||"\\gt"===e||"⟩"===e)&&(e="\\rangle");var s=u.contains(rh,e)?rc:u.contains(ro,e)?ru:rp,o=rg(e,t,s,a);return"small"===o.type?t2(e,o.style,r,a,n,i):"large"===o.type?t9(e,o.size,r,a,n,i):ri(e,t,r,a,n,i)},rv={sqrtImage:function(e,t){var r,a,n=t.havingBaseSizing(),i=rg("\\surd",e*n.sizeMultiplier,ru,n),s=n.sizeMultiplier,o=Math.max(0,t.minRuleThickness-t.fontMetrics().sqrtRuleThickness),l=0,h=0,m=0;return"small"===i.type?(m=1e3+1e3*o+80,e<1?s=1:e<1.4&&(s=.7),l=(1+o+.08)/s,h=(1+o)/s,(r=rs("sqrtMain",l,m,o,t)).style.minWidth="0.853em",a=.833/s):"large"===i.type?(m=1080*rm[i.size],h=(rm[i.size]+o)/s,l=(rm[i.size]+o+.08)/s,(r=rs("sqrtSize"+i.size,l,m,o,t)).style.minWidth="1.02em",a=1/s):(l=e+o+.08,h=e+o,(r=rs("sqrtTall",l,m=Math.floor(1e3*e+o)+80,o,t)).style.minWidth="0.742em",a=1.056),r.height=h,r.style.height=W(l),{span:r,advanceWidth:a,ruleWidth:(t.fontMetrics().sqrtRuleThickness+o)*s}},sizedDelim:function(e,t,r,a,n){if("<"===e||"\\lt"===e||"⟨"===e?e="\\langle":(">"===e||"\\gt"===e||"⟩"===e)&&(e="\\rangle"),u.contains(ro,e)||u.contains(rh,e))return t9(e,t,!1,r,a,n);if(u.contains(rl,e))return ri(e,rm[t],!1,r,a,n);throw new i("Illegal delimiter: '"+e+"'")},sizeToMaxHeight:rm,customSizedDelim:rf,leftRightDelim:function(e,t,r,a,n,i){var s=a.fontMetrics().axisHeight*a.sizeMultiplier,o=5/a.fontMetrics().ptPerEm,l=Math.max(t-s,r+s);return rf(e,Math.max(l/500*901,2*l-o),!0,a,n,i)}},rb={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},ry=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","<",">","\\langle","⟨","\\rangle","⟩","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function rx(e,t){var r=tP(e);if(r&&u.contains(ry,r.text))return r;if(r)throw new i("Invalid delimiter '"+r.text+"' after '"+t.funcName+"'",e);throw new i("Invalid delimiter type '"+e.type+"'",e)}function rw(e){if(!e.body)throw Error("Bug: The leftright ParseNode wasn't fully parsed.")}te({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(e,t)=>{var r=rx(t[0],e);return{type:"delimsizing",mode:e.parser.mode,size:rb[e.funcName].size,mclass:rb[e.funcName].mclass,delim:r.text}},htmlBuilder:(e,t)=>"."===e.delim?e1.makeSpan([e.mclass]):rv.sizedDelim(e.delim,e.size,t,e.mode,[e.mclass]),mathmlBuilder:e=>{var t=[];"."!==e.delim&&t.push(tS(e.delim,e.mode));var r=new tk.MathNode("mo",t);"mopen"===e.mclass||"mclose"===e.mclass?r.setAttribute("fence","true"):r.setAttribute("fence","false"),r.setAttribute("stretchy","true");var a=W(rv.sizeToMaxHeight[e.size]);return r.setAttribute("minsize",a),r.setAttribute("maxsize",a),r}}),te({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var r=e.parser.gullet.macros.get("\\current@color");if(r&&"string"!=typeof r)throw new i("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:e.parser.mode,delim:rx(t[0],e).text,color:r}}}),te({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var r=rx(t[0],e),a=e.parser;++a.leftrightDepth;var n=a.parseExpression(!1);--a.leftrightDepth,a.expect("\\right",!1);var i=tD(a.parseFunction(),"leftright-right");return{type:"leftright",mode:a.mode,body:n,left:r.text,right:i.delim,rightColor:i.color}},htmlBuilder:(e,t)=>{rw(e);for(var r,a,n=th(e.body,t,!0,["mopen","mclose"]),i=0,s=0,o=!1,l=0;l<n.length;l++)n[l].isMiddle?o=!0:(i=Math.max(n[l].height,i),s=Math.max(n[l].depth,s));if(i*=t.sizeMultiplier,s*=t.sizeMultiplier,r="."===e.left?td(t,["mopen"]):rv.leftRightDelim(e.left,i,s,t,e.mode,["mopen"]),n.unshift(r),o)for(var h=1;h<n.length;h++){var m=n[h].isMiddle;m&&(n[h]=rv.leftRightDelim(m.delim,i,s,m.options,e.mode,[]))}if("."===e.right)a=td(t,["mclose"]);else{var c=e.rightColor?t.withColor(e.rightColor):t;a=rv.leftRightDelim(e.right,i,s,c,e.mode,["mclose"])}return n.push(a),e1.makeSpan(["minner"],n,t)},mathmlBuilder:(e,t)=>{rw(e);var r=tT(e.body,t);if("."!==e.left){var a=new tk.MathNode("mo",[tS(e.left,e.mode)]);a.setAttribute("fence","true"),r.unshift(a)}if("."!==e.right){var n=new tk.MathNode("mo",[tS(e.right,e.mode)]);n.setAttribute("fence","true"),e.rightColor&&n.setAttribute("mathcolor",e.rightColor),r.push(n)}return tM(r)}}),te({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var r=rx(t[0],e);if(!e.parser.leftrightDepth)throw new i("\\middle without preceding \\left",r);return{type:"middle",mode:e.parser.mode,delim:r.text}},htmlBuilder:(e,t)=>{var r;if("."===e.delim)r=td(t,[]);else{r=rv.sizedDelim(e.delim,1,t,e.mode,[]);var a={delim:e.delim,options:t};r.isMiddle=a}return r},mathmlBuilder:(e,t)=>{var r="\\vert"===e.delim||"|"===e.delim?tS("|","text"):tS(e.delim,e.mode),a=new tk.MathNode("mo",[r]);return a.setAttribute("fence","true"),a.setAttribute("lspace","0.05em"),a.setAttribute("rspace","0.05em"),a}});var rk=(e,t)=>{var r,a,n=e1.wrapFragment(tg(e.body,t),t),i=e.label.slice(1),s=t.sizeMultiplier,o=0,l=u.isCharacterBox(e.body);if("sout"===i)(r=e1.makeSpan(["stretchy","sout"])).height=t.fontMetrics().defaultRuleThickness/s,o=-.5*t.fontMetrics().xHeight;else if("phase"===i){var h=X({number:.6,unit:"pt"},t),m=X({number:.35,unit:"ex"},t);s/=t.havingBaseSizing().sizeMultiplier;var c=n.height+n.depth+h+m;n.style.paddingLeft=W(c/2+h);var p=Math.floor(1e3*c*s),d=new ea([new en("phase","M400000 "+p+" H0 L"+p/2+" 0 l65 45 L145 "+(p-80)+" H400000z")],{width:"400em",height:W(p/1e3),viewBox:"0 0 400000 "+p,preserveAspectRatio:"xMinYMin slice"});(r=e1.makeSvgSpan(["hide-tail"],[d],t)).style.height=W(c),o=n.depth+h+m}else{/cancel/.test(i)?l||n.classes.push("cancel-pad"):"angl"===i?n.classes.push("anglpad"):n.classes.push("boxpad");var g=0,f=0,v=0;/box/.test(i)?(v=Math.max(t.fontMetrics().fboxrule,t.minRuleThickness),f=g=t.fontMetrics().fboxsep+("colorbox"===i?0:v)):"angl"===i?(g=4*(v=Math.max(t.fontMetrics().defaultRuleThickness,t.minRuleThickness)),f=Math.max(0,.25-n.depth)):f=g=.2*!!l,r=tL.encloseSpan(n,i,g,f,t),/fbox|boxed|fcolorbox/.test(i)?(r.style.borderStyle="solid",r.style.borderWidth=W(v)):"angl"===i&&.049!==v&&(r.style.borderTopWidth=W(v),r.style.borderRightWidth=W(v)),o=n.depth+f,e.backgroundColor&&(r.style.backgroundColor=e.backgroundColor,e.borderColor&&(r.style.borderColor=e.borderColor))}if(e.backgroundColor)a=e1.makeVList({positionType:"individualShift",children:[{type:"elem",elem:r,shift:o},{type:"elem",elem:n,shift:0}]},t);else{var b=/cancel|phase/.test(i)?["svg-align"]:[];a=e1.makeVList({positionType:"individualShift",children:[{type:"elem",elem:n,shift:0},{type:"elem",elem:r,shift:o,wrapperClasses:b}]},t)}return(/cancel/.test(i)&&(a.height=n.height,a.depth=n.depth),/cancel/.test(i)&&!l)?e1.makeSpan(["mord","cancel-lap"],[a],t):e1.makeSpan(["mord"],[a],t)},rS=(e,t)=>{var r=0,a=new tk.MathNode(e.label.indexOf("colorbox")>-1?"mpadded":"menclose",[tC(e.body,t)]);switch(e.label){case"\\cancel":a.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":a.setAttribute("notation","downdiagonalstrike");break;case"\\phase":a.setAttribute("notation","phasorangle");break;case"\\sout":a.setAttribute("notation","horizontalstrike");break;case"\\fbox":a.setAttribute("notation","box");break;case"\\angl":a.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(r=t.fontMetrics().fboxsep*t.fontMetrics().ptPerEm,a.setAttribute("width","+"+2*r+"pt"),a.setAttribute("height","+"+2*r+"pt"),a.setAttribute("lspace",r+"pt"),a.setAttribute("voffset",r+"pt"),"\\fcolorbox"===e.label){var n=Math.max(t.fontMetrics().fboxrule,t.minRuleThickness);a.setAttribute("style","border: "+n+"em solid "+String(e.borderColor))}break;case"\\xcancel":a.setAttribute("notation","updiagonalstrike downdiagonalstrike")}return e.backgroundColor&&a.setAttribute("mathbackground",e.backgroundColor),a};te({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(e,t,r){var{parser:a,funcName:n}=e,i=tD(t[0],"color-token").color,s=t[1];return{type:"enclose",mode:a.mode,label:n,backgroundColor:i,body:s}},htmlBuilder:rk,mathmlBuilder:rS}),te({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(e,t,r){var{parser:a,funcName:n}=e,i=tD(t[0],"color-token").color,s=tD(t[1],"color-token").color,o=t[2];return{type:"enclose",mode:a.mode,label:n,backgroundColor:s,borderColor:i,body:o}},htmlBuilder:rk,mathmlBuilder:rS}),te({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(e,t){var{parser:r}=e;return{type:"enclose",mode:r.mode,label:"\\fbox",body:t[0]}}}),te({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(e,t){var{parser:r,funcName:a}=e,n=t[0];return{type:"enclose",mode:r.mode,label:a,body:n}},htmlBuilder:rk,mathmlBuilder:rS}),te({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(e,t){var{parser:r}=e;return{type:"enclose",mode:r.mode,label:"\\angl",body:t[0]}}});var rM={};function rz(e){for(var{type:t,names:r,props:a,handler:n,htmlBuilder:i,mathmlBuilder:s}=e,o={type:t,numArgs:a.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:n},l=0;l<r.length;++l)rM[r[l]]=o;i&&(e2[t]=i),s&&(e9[t]=s)}var rA={};function rT(e){var t=[];e.consumeSpaces();var r=e.fetch().text;for("\\relax"===r&&(e.consume(),e.consumeSpaces(),r=e.fetch().text);"\\hline"===r||"\\hdashline"===r;)e.consume(),t.push("\\hdashline"===r),e.consumeSpaces(),r=e.fetch().text;return t}var rB=e=>{if(!e.parser.settings.displayMode)throw new i("{"+e.envName+"} can be used only in display mode.")};function rC(e){if(-1===e.indexOf("ed"))return -1===e.indexOf("*")}function rq(e,t,r){var{hskipBeforeAndAfter:a,addJot:s,cols:o,arraystretch:l,colSeparationType:h,autoTag:m,singleRow:c,emptySingleRow:p,maxNumCols:u,leqno:d}=t;if(e.gullet.beginGroup(),c||e.gullet.macros.set("\\cr","\\\\\\relax"),!l){var g=e.gullet.expandMacroAsText("\\arraystretch");if(null==g)l=1;else if(!(l=parseFloat(g))||l<0)throw new i("Invalid \\arraystretch: "+g)}e.gullet.beginGroup();var f=[],v=[f],b=[],y=[],x=null!=m?[]:void 0;function w(){m&&e.gullet.macros.set("\\@eqnsw","1",!0)}function k(){x&&(e.gullet.macros.get("\\df@tag")?(x.push(e.subparse([new n("\\df@tag")])),e.gullet.macros.set("\\df@tag",void 0,!0)):x.push(!!m&&"1"===e.gullet.macros.get("\\@eqnsw")))}for(w(),y.push(rT(e));;){var S=e.parseExpression(!1,c?"\\end":"\\\\");e.gullet.endGroup(),e.gullet.beginGroup(),S={type:"ordgroup",mode:e.mode,body:S},r&&(S={type:"styling",mode:e.mode,style:r,body:[S]}),f.push(S);var M=e.fetch().text;if("&"===M){if(u&&f.length===u){if(c||h)throw new i("Too many tab characters: &",e.nextToken);e.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}e.consume()}else if("\\end"===M){k(),1===f.length&&"styling"===S.type&&0===S.body[0].body.length&&(v.length>1||!p)&&v.pop(),y.length<v.length+1&&y.push([]);break}else if("\\\\"===M){e.consume();var z=void 0;" "!==e.gullet.future().text&&(z=e.parseSizeGroup(!0)),b.push(z?z.value:null),k(),y.push(rT(e)),f=[],v.push(f),w()}else throw new i("Expected & or \\\\ or \\cr or \\end",e.nextToken)}return e.gullet.endGroup(),e.gullet.endGroup(),{type:"array",mode:e.mode,addJot:s,arraystretch:l,body:v,cols:o,rowGaps:b,hskipBeforeAndAfter:a,hLinesBeforeRow:y,colSeparationType:h,tags:x,leqno:d}}function rN(e){return"d"===e.slice(0,1)?"display":"text"}var rI=function(e,t){var r,a,n,s,o=e.body.length,l=e.hLinesBeforeRow,h=0,m=Array(o),c=[],p=Math.max(t.fontMetrics().arrayRuleWidth,t.minRuleThickness),d=1/t.fontMetrics().ptPerEm,g=5*d;e.colSeparationType&&"small"===e.colSeparationType&&(g=.2778*(t.havingStyle(M.SCRIPT).sizeMultiplier/t.sizeMultiplier));var f="CD"===e.colSeparationType?X({number:3,unit:"ex"},t):12*d,v=3*d,b=e.arraystretch*f,y=.7*b,x=.3*b,w=0;function k(e){for(var t=0;t<e.length;++t)t>0&&(w+=.25),c.push({pos:w,isDashed:e[t]})}for(k(l[0]),r=0;r<e.body.length;++r){var S=e.body[r],z=y,A=x;h<S.length&&(h=S.length);var T=Array(S.length);for(a=0;a<S.length;++a){var B=tg(S[a],t);A<B.depth&&(A=B.depth),z<B.height&&(z=B.height),T[a]=B}var C=e.rowGaps[r],q=0;C&&(q=X(C,t))>0&&(A<(q+=x)&&(A=q),q=0),e.addJot&&(A+=v),T.height=z,T.depth=A,T.pos=w+=z,w+=A+q,m[r]=T,k(l[r+1])}var N=w/2+t.fontMetrics().axisHeight,I=e.cols||[],H=[],R=[];if(e.tags&&e.tags.some(e=>e))for(r=0;r<o;++r){var O=m[r],E=O.pos-N,L=e.tags[r],D=void 0;(D=!0===L?e1.makeSpan(["eqn-num"],[],t):!1===L?e1.makeSpan([],[],t):e1.makeSpan([],th(L,t,!0),t)).depth=O.depth,D.height=O.height,R.push({type:"elem",elem:D,shift:E})}for(a=0,s=0;a<h||s<I.length;++a,++s){for(var V=I[s]||{},P=!0;"separator"===V.type;){if(P||((n=e1.makeSpan(["arraycolsep"],[])).style.width=W(t.fontMetrics().doubleRuleSep),H.push(n)),"|"===V.separator||":"===V.separator){var F="|"===V.separator?"solid":"dashed",G=e1.makeSpan(["vertical-separator"],[],t);G.style.height=W(w),G.style.borderRightWidth=W(p),G.style.borderRightStyle=F,G.style.margin="0 "+W(-p/2);var U=w-N;U&&(G.style.verticalAlign=W(-U)),H.push(G)}else throw new i("Invalid separator type: "+V.separator);V=I[++s]||{},P=!1}if(!(a>=h)){var Y=void 0;(a>0||e.hskipBeforeAndAfter)&&0!==(Y=u.deflt(V.pregap,g))&&((n=e1.makeSpan(["arraycolsep"],[])).style.width=W(Y),H.push(n));var _=[];for(r=0;r<o;++r){var j=m[r],$=j[a];if($){var Z=j.pos-N;$.depth=j.depth,$.height=j.height,_.push({type:"elem",elem:$,shift:Z})}}_=e1.makeVList({positionType:"individualShift",children:_},t),_=e1.makeSpan(["col-align-"+(V.align||"c")],[_]),H.push(_),(a<h-1||e.hskipBeforeAndAfter)&&0!==(Y=u.deflt(V.postgap,g))&&((n=e1.makeSpan(["arraycolsep"],[])).style.width=W(Y),H.push(n))}}if(m=e1.makeSpan(["mtable"],H),c.length>0){for(var K=e1.makeLineSpan("hline",t,p),J=e1.makeLineSpan("hdashline",t,p),Q=[{type:"elem",elem:m,shift:0}];c.length>0;){var ee=c.pop(),et=ee.pos-N;ee.isDashed?Q.push({type:"elem",elem:J,shift:et}):Q.push({type:"elem",elem:K,shift:et})}m=e1.makeVList({positionType:"individualShift",children:Q},t)}if(0===R.length)return e1.makeSpan(["mord"],[m],t);var er=e1.makeVList({positionType:"individualShift",children:R},t);return er=e1.makeSpan(["tag"],[er],t),e1.makeFragment([m,er])},rH={c:"center ",l:"left ",r:"right "},rR=function(e,t){for(var r=[],a=new tk.MathNode("mtd",[],["mtr-glue"]),n=new tk.MathNode("mtd",[],["mml-eqn-num"]),i=0;i<e.body.length;i++){for(var s=e.body[i],o=[],l=0;l<s.length;l++)o.push(new tk.MathNode("mtd",[tC(s[l],t)]));e.tags&&e.tags[i]&&(o.unshift(a),o.push(a),e.leqno?o.unshift(n):o.push(n)),r.push(new tk.MathNode("mtr",o))}var h=new tk.MathNode("mtable",r),m=.5===e.arraystretch?.1:.16+e.arraystretch-1+.09*!!e.addJot;h.setAttribute("rowspacing",W(m));var c="",p="";if(e.cols&&e.cols.length>0){var u=e.cols,d="",g=!1,f=0,v=u.length;"separator"===u[0].type&&(c+="top ",f=1),"separator"===u[u.length-1].type&&(c+="bottom ",v-=1);for(var b=f;b<v;b++)"align"===u[b].type?(p+=rH[u[b].align],g&&(d+="none "),g=!0):"separator"===u[b].type&&g&&(d+="|"===u[b].separator?"solid ":"dashed ",g=!1);h.setAttribute("columnalign",p.trim()),/[sd]/.test(d)&&h.setAttribute("columnlines",d.trim())}if("align"===e.colSeparationType){for(var y=e.cols||[],x="",w=1;w<y.length;w++)x+=w%2?"0em ":"1em ";h.setAttribute("columnspacing",x.trim())}else"alignat"===e.colSeparationType||"gather"===e.colSeparationType?h.setAttribute("columnspacing","0em"):"small"===e.colSeparationType?h.setAttribute("columnspacing","0.2778em"):"CD"===e.colSeparationType?h.setAttribute("columnspacing","0.5em"):h.setAttribute("columnspacing","1em");var k="",S=e.hLinesBeforeRow;c+=(S[0].length>0?"left ":"")+(S[S.length-1].length>0?"right ":"");for(var M=1;M<S.length-1;M++)k+=0===S[M].length?"none ":S[M][0]?"dashed ":"solid ";return/[sd]/.test(k)&&h.setAttribute("rowlines",k.trim()),""!==c&&(h=new tk.MathNode("menclose",[h])).setAttribute("notation",c.trim()),e.arraystretch&&e.arraystretch<1&&(h=new tk.MathNode("mstyle",[h])).setAttribute("scriptlevel","1"),h},rO=function(e,t){-1===e.envName.indexOf("ed")&&rB(e);var r,a=[],n=e.envName.indexOf("at")>-1?"alignat":"align",s="split"===e.envName,o=rq(e.parser,{cols:a,addJot:!0,autoTag:s?void 0:rC(e.envName),emptySingleRow:!0,colSeparationType:n,maxNumCols:s?2:void 0,leqno:e.parser.settings.leqno},"display"),l=0,h={type:"ordgroup",mode:e.mode,body:[]};if(t[0]&&"ordgroup"===t[0].type){for(var m="",c=0;c<t[0].body.length;c++)m+=tD(t[0].body[c],"textord").text;l=2*(r=Number(m))}var p=!l;o.body.forEach(function(e){for(var t=1;t<e.length;t+=2){var a=tD(e[t],"styling");tD(a.body[0],"ordgroup").body.unshift(h)}if(p)l<e.length&&(l=e.length);else{var n=e.length/2;if(r<n)throw new i("Too many math in a row: expected "+r+", but got "+n,e[0])}});for(var u=0;u<l;++u){var d="r",g=0;u%2==1?d="l":u>0&&p&&(g=1),a[u]={type:"align",align:d,pregap:g,postgap:0}}return o.colSeparationType=p?"align":"alignat",o};rz({type:"array",names:["array","darray"],props:{numArgs:1},handler(e,t){var r=(tP(t[0])?[t[0]]:tD(t[0],"ordgroup").body).map(function(e){var t=tV(e).text;if(-1!=="lcr".indexOf(t))return{type:"align",align:t};if("|"===t)return{type:"separator",separator:"|"};if(":"===t)return{type:"separator",separator:":"};throw new i("Unknown column alignment: "+t,e)}),a={cols:r,hskipBeforeAndAfter:!0,maxNumCols:r.length};return rq(e.parser,a,rN(e.envName))},htmlBuilder:rI,mathmlBuilder:rR}),rz({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(e){var t={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[e.envName.replace("*","")],r="c",a={hskipBeforeAndAfter:!1,cols:[{type:"align",align:r}]};if("*"===e.envName.charAt(e.envName.length-1)){var n=e.parser;if(n.consumeSpaces(),"["===n.fetch().text){if(n.consume(),n.consumeSpaces(),r=n.fetch().text,-1==="lcr".indexOf(r))throw new i("Expected l or c or r",n.nextToken);n.consume(),n.consumeSpaces(),n.expect("]"),n.consume(),a.cols=[{type:"align",align:r}]}}var s=rq(e.parser,a,rN(e.envName)),o=Math.max(0,...s.body.map(e=>e.length));return s.cols=Array(o).fill({type:"align",align:r}),t?{type:"leftright",mode:e.mode,body:[s],left:t[0],right:t[1],rightColor:void 0}:s},htmlBuilder:rI,mathmlBuilder:rR}),rz({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(e){var t=rq(e.parser,{arraystretch:.5},"script");return t.colSeparationType="small",t},htmlBuilder:rI,mathmlBuilder:rR}),rz({type:"array",names:["subarray"],props:{numArgs:1},handler(e,t){var r=(tP(t[0])?[t[0]]:tD(t[0],"ordgroup").body).map(function(e){var t=tV(e).text;if(-1!=="lc".indexOf(t))return{type:"align",align:t};throw new i("Unknown column alignment: "+t,e)});if(r.length>1)throw new i("{subarray} can contain only one column");var a={cols:r,hskipBeforeAndAfter:!1,arraystretch:.5};if((a=rq(e.parser,a,"script")).body.length>0&&a.body[0].length>1)throw new i("{subarray} can contain only one column");return a},htmlBuilder:rI,mathmlBuilder:rR}),rz({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(e){var t=rq(e.parser,{arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},rN(e.envName));return{type:"leftright",mode:e.mode,body:[t],left:e.envName.indexOf("r")>-1?".":"\\{",right:e.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:rI,mathmlBuilder:rR}),rz({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:rO,htmlBuilder:rI,mathmlBuilder:rR}),rz({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(e){u.contains(["gather","gather*"],e.envName)&&rB(e);var t={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:rC(e.envName),emptySingleRow:!0,leqno:e.parser.settings.leqno};return rq(e.parser,t,"display")},htmlBuilder:rI,mathmlBuilder:rR}),rz({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:rO,htmlBuilder:rI,mathmlBuilder:rR}),rz({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(e){rB(e);var t={autoTag:rC(e.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:e.parser.settings.leqno};return rq(e.parser,t,"display")},htmlBuilder:rI,mathmlBuilder:rR}),rz({type:"array",names:["CD"],props:{numArgs:0},handler:e=>(rB(e),function(e){var t=[];for(e.gullet.beginGroup(),e.gullet.macros.set("\\cr","\\\\\\relax"),e.gullet.beginGroup();;){t.push(e.parseExpression(!1,"\\\\")),e.gullet.endGroup(),e.gullet.beginGroup();var r=e.fetch().text;if("&"===r||"\\\\"===r)e.consume();else if("\\end"===r){0===t[t.length-1].length&&t.pop();break}else throw new i("Expected \\\\ or \\cr or \\end",e.nextToken)}for(var a=[],n=[a],s=0;s<t.length;s++){for(var o=t[s],l=tZ(),h=0;h<o.length;h++)if(tK(o[h])){a.push(l);var m=tV(o[h+=1]).text,c=[,,];if(c[0]={type:"ordgroup",mode:"math",body:[]},c[1]={type:"ordgroup",mode:"math",body:[]},"=|.".indexOf(m)>-1);else if("<>AV".indexOf(m)>-1)for(var p=0;p<2;p++){for(var u=!0,d=h+1;d<o.length;d++){if(tJ(o[d],m)){u=!1,h=d;break}if(tK(o[d]))throw new i("Missing a "+m+" character to complete a CD arrow.",o[d]);c[p].body.push(o[d])}if(u)throw new i("Missing a "+m+" character to complete a CD arrow.",o[h])}else throw new i('Expected one of "<>AV=|." after @',o[h]);var g={type:"styling",body:[function(e,t,r){var a=t$[e];switch(a){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return r.callFunction(a,[t[0]],[t[1]]);case"\\uparrow":case"\\downarrow":var n=r.callFunction("\\\\cdleft",[t[0]],[]),i=r.callFunction("\\Big",[{type:"atom",text:a,mode:"math",family:"rel"}],[]),s=r.callFunction("\\\\cdright",[t[1]],[]);return r.callFunction("\\\\cdparent",[{type:"ordgroup",mode:"math",body:[n,i,s]}],[]);case"\\\\cdlongequal":return r.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":return r.callFunction("\\Big",[{type:"textord",text:"\\Vert",mode:"math"}],[]);default:return{type:"textord",text:" ",mode:"math"}}}(m,c,e)],mode:"math",style:"display"};a.push(g),l=tZ()}else l.body.push(o[h]);s%2==0?a.push(l):a.shift(),a=[],n.push(a)}e.gullet.endGroup(),e.gullet.endGroup();var f=Array(n[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25});return{type:"array",mode:"math",body:n,arraystretch:1,addJot:!0,rowGaps:[null],cols:f,colSeparationType:"CD",hLinesBeforeRow:Array(n.length+1).fill([])}}(e.parser)),htmlBuilder:rI,mathmlBuilder:rR}),rA["\\nonumber"]="\\gdef\\@eqnsw{0}",rA["\\notag"]="\\nonumber",te({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(e,t){throw new i(e.funcName+" valid only within array environment")}}),te({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(e,t){var{parser:r,funcName:a}=e,n=t[0];if("ordgroup"!==n.type)throw new i("Invalid environment name",n);for(var s="",o=0;o<n.body.length;++o)s+=tD(n.body[o],"textord").text;if("\\begin"===a){if(!rM.hasOwnProperty(s))throw new i("No such environment: "+s,n);var l=rM[s],{args:h,optArgs:m}=r.parseArguments("\\begin{"+s+"}",l),c={mode:r.mode,envName:s,parser:r},p=l.handler(c,h,m);r.expect("\\end",!1);var u=r.nextToken,d=tD(r.parseFunction(),"environment");if(d.name!==s)throw new i("Mismatch: \\begin{"+s+"} matched by \\end{"+d.name+"}",u);return p}return{type:"environment",mode:r.mode,name:s,nameGroup:n}}});var rE=(e,t)=>{var r=e.font,a=t.withFont(r);return tg(e.body,a)},rL=(e,t)=>{var r=e.font,a=t.withFont(r);return tC(e.body,a)},rD={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};te({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathsfit","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=tr(t[0]),i=a;return i in rD&&(i=rD[i]),{type:"font",mode:r.mode,font:i.slice(1),body:n}},htmlBuilder:rE,mathmlBuilder:rL}),te({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(e,t)=>{var{parser:r}=e,a=t[0],n=u.isCharacterBox(a);return{type:"mclass",mode:r.mode,mclass:tj(a),body:[{type:"font",mode:r.mode,font:"boldsymbol",body:a}],isCharacterBox:n}}}),te({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(e,t)=>{var{parser:r,funcName:a,breakOnTokenText:n}=e,{mode:i}=r,s=r.parseExpression(!0,n);return{type:"font",mode:i,font:"math"+a.slice(1),body:{type:"ordgroup",mode:r.mode,body:s}}},htmlBuilder:rE,mathmlBuilder:rL});var rV=(e,t)=>{var r=t;return"display"===e?r=r.id>=M.SCRIPT.id?r.text():M.DISPLAY:"text"===e&&r.size===M.DISPLAY.size?r=M.TEXT:"script"===e?r=M.SCRIPT:"scriptscript"===e&&(r=M.SCRIPTSCRIPT),r},rP=(e,t)=>{var r,a,n,i,s,o,l,h,m,c,p,u=rV(e.size,t.style),d=u.fracNum(),g=u.fracDen();r=t.havingStyle(d);var f=tg(e.numer,r,t);if(e.continued){var v=8.5/t.fontMetrics().ptPerEm,b=3.5/t.fontMetrics().ptPerEm;f.height=f.height<v?v:f.height,f.depth=f.depth<b?b:f.depth}r=t.havingStyle(g);var y=tg(e.denom,r,t);if(e.hasBarLine?(e.barSize?(n=X(e.barSize,t),a=e1.makeLineSpan("frac-line",t,n)):a=e1.makeLineSpan("frac-line",t),n=a.height,i=a.height):(a=null,n=0,i=t.fontMetrics().defaultRuleThickness),u.size===M.DISPLAY.size||"display"===e.size?(s=t.fontMetrics().num1,o=n>0?3*i:7*i,l=t.fontMetrics().denom1):(n>0?(s=t.fontMetrics().num2,o=i):(s=t.fontMetrics().num3,o=3*i),l=t.fontMetrics().denom2),a){var x=t.fontMetrics().axisHeight;s-f.depth-(x+.5*n)<o&&(s+=o-(s-f.depth-(x+.5*n))),x-.5*n-(y.height-l)<o&&(l+=o-(x-.5*n-(y.height-l)));var w=-(x-.5*n);h=e1.makeVList({positionType:"individualShift",children:[{type:"elem",elem:y,shift:l},{type:"elem",elem:a,shift:w},{type:"elem",elem:f,shift:-s}]},t)}else{var k=s-f.depth-(y.height-l);k<o&&(s+=.5*(o-k),l+=.5*(o-k)),h=e1.makeVList({positionType:"individualShift",children:[{type:"elem",elem:y,shift:l},{type:"elem",elem:f,shift:-s}]},t)}return r=t.havingStyle(u),h.height*=r.sizeMultiplier/t.sizeMultiplier,h.depth*=r.sizeMultiplier/t.sizeMultiplier,m=u.size===M.DISPLAY.size?t.fontMetrics().delim1:u.size===M.SCRIPTSCRIPT.size?t.havingStyle(M.SCRIPT).fontMetrics().delim2:t.fontMetrics().delim2,c=null==e.leftDelim?td(t,["mopen"]):rv.customSizedDelim(e.leftDelim,m,!0,t.havingStyle(u),e.mode,["mopen"]),p=e.continued?e1.makeSpan([]):null==e.rightDelim?td(t,["mclose"]):rv.customSizedDelim(e.rightDelim,m,!0,t.havingStyle(u),e.mode,["mclose"]),e1.makeSpan(["mord"].concat(r.sizingClasses(t)),[c,e1.makeSpan(["mfrac"],[h]),p],t)},rF=(e,t)=>{var r=new tk.MathNode("mfrac",[tC(e.numer,t),tC(e.denom,t)]);if(e.hasBarLine){if(e.barSize){var a=X(e.barSize,t);r.setAttribute("linethickness",W(a))}}else r.setAttribute("linethickness","0px");var n=rV(e.size,t.style);if(n.size!==t.style.size){r=new tk.MathNode("mstyle",[r]);var i=n.size===M.DISPLAY.size?"true":"false";r.setAttribute("displaystyle",i),r.setAttribute("scriptlevel","0")}if(null!=e.leftDelim||null!=e.rightDelim){var s=[];if(null!=e.leftDelim){var o=new tk.MathNode("mo",[new tk.TextNode(e.leftDelim.replace("\\",""))]);o.setAttribute("fence","true"),s.push(o)}if(s.push(r),null!=e.rightDelim){var l=new tk.MathNode("mo",[new tk.TextNode(e.rightDelim.replace("\\",""))]);l.setAttribute("fence","true"),s.push(l)}return tM(s)}return r};te({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(e,t)=>{var r,{parser:a,funcName:n}=e,i=t[0],s=t[1],o=null,l=null,h="auto";switch(n){case"\\dfrac":case"\\frac":case"\\tfrac":r=!0;break;case"\\\\atopfrac":r=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":r=!1,o="(",l=")";break;case"\\\\bracefrac":r=!1,o="\\{",l="\\}";break;case"\\\\brackfrac":r=!1,o="[",l="]";break;default:throw Error("Unrecognized genfrac command")}switch(n){case"\\dfrac":case"\\dbinom":h="display";break;case"\\tfrac":case"\\tbinom":h="text"}return{type:"genfrac",mode:a.mode,continued:!1,numer:i,denom:s,hasBarLine:r,leftDelim:o,rightDelim:l,size:h,barSize:null}},htmlBuilder:rP,mathmlBuilder:rF}),te({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0],i=t[1];return{type:"genfrac",mode:r.mode,continued:!0,numer:n,denom:i,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}}),te({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(e){var t,{parser:r,funcName:a,token:n}=e;switch(a){case"\\over":t="\\frac";break;case"\\choose":t="\\binom";break;case"\\atop":t="\\\\atopfrac";break;case"\\brace":t="\\\\bracefrac";break;case"\\brack":t="\\\\brackfrac";break;default:throw Error("Unrecognized infix genfrac command")}return{type:"infix",mode:r.mode,replaceWith:t,token:n}}});var rG=["display","text","script","scriptscript"],rU=function(e){var t=null;return e.length>0&&(t="."===(t=e)?null:t),t};te({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(e,t){var r,{parser:a}=e,n=t[4],i=t[5],s=tr(t[0]),o="atom"===s.type&&"open"===s.family?rU(s.text):null,l=tr(t[1]),h="atom"===l.type&&"close"===l.family?rU(l.text):null,m=tD(t[2],"size"),c=null;r=!!m.isBlank||(c=m.value).number>0;var p="auto",u=t[3];return"ordgroup"===u.type?u.body.length>0&&(p=rG[Number(tD(u.body[0],"textord").text)]):p=rG[Number((u=tD(u,"textord")).text)],{type:"genfrac",mode:a.mode,numer:n,denom:i,continued:!1,hasBarLine:r,barSize:c,leftDelim:o,rightDelim:h,size:p}},htmlBuilder:rP,mathmlBuilder:rF}),te({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(e,t){var{parser:r,funcName:a,token:n}=e;return{type:"infix",mode:r.mode,replaceWith:"\\\\abovefrac",size:tD(t[0],"size").value,token:n}}}),te({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0],i=p(tD(t[1],"infix").size),s=t[2],o=i.number>0;return{type:"genfrac",mode:r.mode,numer:n,denom:s,continued:!1,hasBarLine:o,barSize:i,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:rP,mathmlBuilder:rF});var rY=(e,t)=>{var r,a,n,i=t.style;"supsub"===e.type?(r=e.sup?tg(e.sup,t.havingStyle(i.sup()),t):tg(e.sub,t.havingStyle(i.sub()),t),a=tD(e.base,"horizBrace")):a=tD(e,"horizBrace");var s=tg(a.base,t.havingBaseStyle(M.DISPLAY)),o=tL.svgSpan(a,t);if(a.isOver?(n=e1.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:s},{type:"kern",size:.1},{type:"elem",elem:o}]},t)).children[0].children[0].children[1].classes.push("svg-align"):(n=e1.makeVList({positionType:"bottom",positionData:s.depth+.1+o.height,children:[{type:"elem",elem:o},{type:"kern",size:.1},{type:"elem",elem:s}]},t)).children[0].children[0].children[0].classes.push("svg-align"),r){var l=e1.makeSpan(["mord",a.isOver?"mover":"munder"],[n],t);n=a.isOver?e1.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:l},{type:"kern",size:.2},{type:"elem",elem:r}]},t):e1.makeVList({positionType:"bottom",positionData:l.depth+.2+r.height+r.depth,children:[{type:"elem",elem:r},{type:"kern",size:.2},{type:"elem",elem:l}]},t)}return e1.makeSpan(["mord",a.isOver?"mover":"munder"],[n],t)};te({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(e,t){var{parser:r,funcName:a}=e;return{type:"horizBrace",mode:r.mode,label:a,isOver:/^\\over/.test(a),base:t[0]}},htmlBuilder:rY,mathmlBuilder:(e,t)=>{var r=tL.mathMLnode(e.label);return new tk.MathNode(e.isOver?"mover":"munder",[tC(e.base,t),r])}}),te({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=t[1],n=tD(t[0],"url").url;return r.settings.isTrusted({command:"\\href",url:n})?{type:"href",mode:r.mode,href:n,body:ta(a)}:r.formatUnsupportedCmd("\\href")},htmlBuilder:(e,t)=>{var r=th(e.body,t,!1);return e1.makeAnchor(e.href,[],r,t)},mathmlBuilder:(e,t)=>{var r=tB(e.body,t);return r instanceof ty||(r=new ty("mrow",[r])),r.setAttribute("href",e.href),r}}),te({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=tD(t[0],"url").url;if(!r.settings.isTrusted({command:"\\url",url:a}))return r.formatUnsupportedCmd("\\url");for(var n=[],i=0;i<a.length;i++){var s=a[i];"~"===s&&(s="\\textasciitilde"),n.push({type:"textord",mode:"text",text:s})}var o={type:"text",mode:r.mode,font:"\\texttt",body:n};return{type:"href",mode:r.mode,href:a,body:ta(o)}}}),te({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(e,t){var{parser:r}=e;return{type:"hbox",mode:r.mode,body:ta(t[0])}},htmlBuilder(e,t){var r=th(e.body,t,!1);return e1.makeFragment(r)},mathmlBuilder:(e,t)=>new tk.MathNode("mrow",tT(e.body,t))}),te({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(e,t)=>{var r,{parser:a,funcName:n,token:s}=e,o=tD(t[0],"raw").string,l=t[1];a.settings.strict&&a.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");var h={};switch(n){case"\\htmlClass":h.class=o,r={command:"\\htmlClass",class:o};break;case"\\htmlId":h.id=o,r={command:"\\htmlId",id:o};break;case"\\htmlStyle":h.style=o,r={command:"\\htmlStyle",style:o};break;case"\\htmlData":for(var m=o.split(","),c=0;c<m.length;c++){var p=m[c].split("=");if(2!==p.length)throw new i("Error parsing key-value for \\htmlData");h["data-"+p[0].trim()]=p[1].trim()}r={command:"\\htmlData",attributes:h};break;default:throw Error("Unrecognized html command")}return a.settings.isTrusted(r)?{type:"html",mode:a.mode,attributes:h,body:ta(l)}:a.formatUnsupportedCmd(n)},htmlBuilder:(e,t)=>{var r=th(e.body,t,!1),a=["enclosing"];e.attributes.class&&a.push(...e.attributes.class.trim().split(/\s+/));var n=e1.makeSpan(a,r,t);for(var i in e.attributes)"class"!==i&&e.attributes.hasOwnProperty(i)&&n.setAttribute(i,e.attributes[i]);return n},mathmlBuilder:(e,t)=>tB(e.body,t)}),te({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e;return{type:"htmlmathml",mode:r.mode,html:ta(t[0]),mathml:ta(t[1])}},htmlBuilder:(e,t)=>{var r=th(e.html,t,!1);return e1.makeFragment(r)},mathmlBuilder:(e,t)=>tB(e.mathml,t)});var rX=function(e){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(e))return{number:+e,unit:"bp"};var t=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(e);if(!t)throw new i("Invalid size: '"+e+"' in \\includegraphics");var r={number:+(t[1]+t[2]),unit:t[3]};if(!Y(r))throw new i("Invalid unit: '"+r.unit+"' in \\includegraphics.");return r};te({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(e,t,r)=>{var{parser:a}=e,n={number:0,unit:"em"},s={number:.9,unit:"em"},o={number:0,unit:"em"},l="";if(r[0])for(var h=tD(r[0],"raw").string.split(","),m=0;m<h.length;m++){var c=h[m].split("=");if(2===c.length){var p=c[1].trim();switch(c[0].trim()){case"alt":l=p;break;case"width":n=rX(p);break;case"height":s=rX(p);break;case"totalheight":o=rX(p);break;default:throw new i("Invalid key: '"+c[0]+"' in \\includegraphics.")}}}var u=tD(t[0],"url").url;return(""===l&&(l=(l=(l=u).replace(/^.*[\\/]/,"")).substring(0,l.lastIndexOf("."))),a.settings.isTrusted({command:"\\includegraphics",url:u}))?{type:"includegraphics",mode:a.mode,alt:l,width:n,height:s,totalheight:o,src:u}:a.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(e,t)=>{var r=X(e.height,t),a=0;e.totalheight.number>0&&(a=X(e.totalheight,t)-r);var n=0;e.width.number>0&&(n=X(e.width,t));var i={height:W(r+a)};n>0&&(i.width=W(n)),a>0&&(i.verticalAlign=W(-a));var s=new ee(e.src,e.alt,i);return s.height=r,s.depth=a,s},mathmlBuilder:(e,t)=>{var r=new tk.MathNode("mglyph",[]);r.setAttribute("alt",e.alt);var a=X(e.height,t),n=0;if(e.totalheight.number>0&&(n=X(e.totalheight,t)-a,r.setAttribute("valign",W(-n))),r.setAttribute("height",W(a+n)),e.width.number>0){var i=X(e.width,t);r.setAttribute("width",W(i))}return r.setAttribute("src",e.src),r}}),te({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(e,t){var{parser:r,funcName:a}=e,n=tD(t[0],"size");if(r.settings.strict){var i="m"===a[1],s="mu"===n.value.unit;i?(s||r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" supports only mu units, not "+n.value.unit+" units"),"math"!==r.mode&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" works only in math mode")):s&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+a+" doesn't support mu units")}return{type:"kern",mode:r.mode,dimension:n.value}},htmlBuilder:(e,t)=>e1.makeGlue(e.dimension,t),mathmlBuilder(e,t){var r=X(e.dimension,t);return new tk.SpaceNode(r)}}),te({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0];return{type:"lap",mode:r.mode,alignment:a.slice(5),body:n}},htmlBuilder:(e,t)=>{"clap"===e.alignment?(r=e1.makeSpan([],[tg(e.body,t)]),r=e1.makeSpan(["inner"],[r],t)):r=e1.makeSpan(["inner"],[tg(e.body,t)]);var r,a=e1.makeSpan(["fix"],[]),n=e1.makeSpan([e.alignment],[r,a],t),i=e1.makeSpan(["strut"]);return i.style.height=W(n.height+n.depth),n.depth&&(i.style.verticalAlign=W(-n.depth)),n.children.unshift(i),n=e1.makeSpan(["thinbox"],[n],t),e1.makeSpan(["mord","vbox"],[n],t)},mathmlBuilder:(e,t)=>{var r=new tk.MathNode("mpadded",[tC(e.body,t)]);if("rlap"!==e.alignment){var a="llap"===e.alignment?"-1":"-0.5";r.setAttribute("lspace",a+"width")}return r.setAttribute("width","0px"),r}}),te({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(e,t){var{funcName:r,parser:a}=e,n=a.mode;a.switchMode("math");var i="\\("===r?"\\)":"$",s=a.parseExpression(!1,i);return a.expect(i),a.switchMode(n),{type:"styling",mode:a.mode,style:"text",body:s}}}),te({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(e,t){throw new i("Mismatched "+e.funcName)}});var rW=(e,t)=>{switch(t.style.size){case M.DISPLAY.size:return e.display;case M.TEXT.size:return e.text;case M.SCRIPT.size:return e.script;case M.SCRIPTSCRIPT.size:return e.scriptscript;default:return e.text}};te({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(e,t)=>{var{parser:r}=e;return{type:"mathchoice",mode:r.mode,display:ta(t[0]),text:ta(t[1]),script:ta(t[2]),scriptscript:ta(t[3])}},htmlBuilder:(e,t)=>{var r=th(rW(e,t),t,!1);return e1.makeFragment(r)},mathmlBuilder:(e,t)=>tB(rW(e,t),t)});var r_=(e,t,r,a,n,i,s)=>{e=e1.makeSpan([],[e]);var o,l,h,m=r&&u.isCharacterBox(r);if(t){var c=tg(t,a.havingStyle(n.sup()),a);l={elem:c,kern:Math.max(a.fontMetrics().bigOpSpacing1,a.fontMetrics().bigOpSpacing3-c.depth)}}if(r){var p=tg(r,a.havingStyle(n.sub()),a);o={elem:p,kern:Math.max(a.fontMetrics().bigOpSpacing2,a.fontMetrics().bigOpSpacing4-p.height)}}if(l&&o){var d=a.fontMetrics().bigOpSpacing5+o.elem.height+o.elem.depth+o.kern+e.depth+s;h=e1.makeVList({positionType:"bottom",positionData:d,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:o.elem,marginLeft:W(-i)},{type:"kern",size:o.kern},{type:"elem",elem:e},{type:"kern",size:l.kern},{type:"elem",elem:l.elem,marginLeft:W(i)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}else if(o){var g=e.height-s;h=e1.makeVList({positionType:"top",positionData:g,children:[{type:"kern",size:a.fontMetrics().bigOpSpacing5},{type:"elem",elem:o.elem,marginLeft:W(-i)},{type:"kern",size:o.kern},{type:"elem",elem:e}]},a)}else{if(!l)return e;var f=e.depth+s;h=e1.makeVList({positionType:"bottom",positionData:f,children:[{type:"elem",elem:e},{type:"kern",size:l.kern},{type:"elem",elem:l.elem,marginLeft:W(i)},{type:"kern",size:a.fontMetrics().bigOpSpacing5}]},a)}var v=[h];if(o&&0!==i&&!m){var b=e1.makeSpan(["mspace"],[],a);b.style.marginRight=W(i),v.unshift(b)}return e1.makeSpan(["mop","op-limits"],v,a)},rj=["\\smallint"],r$=(e,t)=>{var r,a,n,i,s=!1;"supsub"===e.type?(r=e.sup,a=e.sub,n=tD(e.base,"op"),s=!0):n=tD(e,"op");var o=t.style,l=!1;if(o.size===M.DISPLAY.size&&n.symbol&&!u.contains(rj,n.name)&&(l=!0),n.symbol){var h=l?"Size2-Regular":"Size1-Regular",m="";if(("\\oiint"===n.name||"\\oiiint"===n.name)&&(m=n.name.slice(1),n.name="oiint"===m?"\\iint":"\\iiint"),i=e1.makeSymbol(n.name,h,"math",t,["mop","op-symbol",l?"large-op":"small-op"]),m.length>0){var c=i.italic,p=e1.staticSvg(m+"Size"+(l?"2":"1"),t);i=e1.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:0},{type:"elem",elem:p,shift:.08*!!l}]},t),n.name="\\"+m,i.classes.unshift("mop"),i.italic=c}}else if(n.body){var d=th(n.body,t,!0);1===d.length&&d[0]instanceof er?(i=d[0]).classes[0]="mop":i=e1.makeSpan(["mop"],d,t)}else{for(var g=[],f=1;f<n.name.length;f++)g.push(e1.mathsym(n.name[f],n.mode,t));i=e1.makeSpan(["mop"],g,t)}var v=0,b=0;return((i instanceof er||"\\oiint"===n.name||"\\oiiint"===n.name)&&!n.suppressBaseShift&&(v=(i.height-i.depth)/2-t.fontMetrics().axisHeight,b=i.italic),s)?r_(i,r,a,t,o,b,v):(v&&(i.style.position="relative",i.style.top=W(v)),i)},rZ=(e,t)=>{var r;if(e.symbol)r=new ty("mo",[tS(e.name,e.mode)]),u.contains(rj,e.name)&&r.setAttribute("largeop","false");else if(e.body)r=new ty("mo",tT(e.body,t));else{r=new ty("mi",[new tx(e.name.slice(1))]);var a=new ty("mo",[tS("⁡","text")]);r=e.parentIsSupSub?new ty("mrow",[r,a]):tb([r,a])}return r},rK={"∏":"\\prod","∐":"\\coprod","∑":"\\sum","⋀":"\\bigwedge","⋁":"\\bigvee","⋂":"\\bigcap","⋃":"\\bigcup","⨀":"\\bigodot","⨁":"\\bigoplus","⨂":"\\bigotimes","⨄":"\\biguplus","⨆":"\\bigsqcup"};te({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","∏","∐","∑","⋀","⋁","⋂","⋃","⨀","⨁","⨂","⨄","⨆"],props:{numArgs:0},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=a;return 1===n.length&&(n=rK[n]),{type:"op",mode:r.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:n}},htmlBuilder:r$,mathmlBuilder:rZ}),te({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var{parser:r}=e,a=t[0];return{type:"op",mode:r.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:ta(a)}},htmlBuilder:r$,mathmlBuilder:rZ});var rJ={"∫":"\\int","∬":"\\iint","∭":"\\iiint","∮":"\\oint","∯":"\\oiint","∰":"\\oiiint"};te({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(e){var{parser:t,funcName:r}=e;return{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:r$,mathmlBuilder:rZ}),te({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(e){var{parser:t,funcName:r}=e;return{type:"op",mode:t.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:r$,mathmlBuilder:rZ}),te({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","∫","∬","∭","∮","∯","∰"],props:{numArgs:0},handler(e){var{parser:t,funcName:r}=e,a=r;return 1===a.length&&(a=rJ[a]),{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:a}},htmlBuilder:r$,mathmlBuilder:rZ});var rQ=(e,t)=>{var r,a,n,i,s=!1;if("supsub"===e.type?(r=e.sup,a=e.sub,n=tD(e.base,"operatorname"),s=!0):n=tD(e,"operatorname"),n.body.length>0){for(var o=th(n.body.map(e=>{var t=e.text;return"string"==typeof t?{type:"textord",mode:e.mode,text:t}:e}),t.withFont("mathrm"),!0),l=0;l<o.length;l++){var h=o[l];h instanceof er&&(h.text=h.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}i=e1.makeSpan(["mop"],o,t)}else i=e1.makeSpan(["mop"],[],t);return s?r_(i,r,a,t,t.style,0,0):i};function r0(e,t,r){for(var a=th(e,t,!1),n=t.sizeMultiplier/r.sizeMultiplier,i=0;i<a.length;i++){var s=a[i].classes.indexOf("sizing");s<0?Array.prototype.push.apply(a[i].classes,t.sizingClasses(r)):a[i].classes[s+1]==="reset-size"+t.size&&(a[i].classes[s+1]="reset-size"+r.size),a[i].height*=n,a[i].depth*=n}return e1.makeFragment(a)}te({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(e,t)=>{var{parser:r,funcName:a}=e,n=t[0];return{type:"operatorname",mode:r.mode,body:ta(n),alwaysHandleSupSub:"\\operatornamewithlimits"===a,limits:!1,parentIsSupSub:!1}},htmlBuilder:rQ,mathmlBuilder:(e,t)=>{for(var r=tT(e.body,t.withFont("mathrm")),a=!0,n=0;n<r.length;n++){var i=r[n];if(i instanceof tk.SpaceNode);else if(i instanceof tk.MathNode)switch(i.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":var s=i.children[0];1===i.children.length&&s instanceof tk.TextNode?s.text=s.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):a=!1;break;default:a=!1}else a=!1}if(a){var o=r.map(e=>e.toText()).join("");r=[new tk.TextNode(o)]}var l=new tk.MathNode("mi",r);l.setAttribute("mathvariant","normal");var h=new tk.MathNode("mo",[tS("⁡","text")]);return e.parentIsSupSub?new tk.MathNode("mrow",[l,h]):tk.newDocumentFragment([l,h])}}),rA["\\operatorname"]="\\@ifstar\\operatornamewithlimits\\operatorname@",tt({type:"ordgroup",htmlBuilder:(e,t)=>e.semisimple?e1.makeFragment(th(e.body,t,!1)):e1.makeSpan(["mord"],th(e.body,t,!0),t),mathmlBuilder:(e,t)=>tB(e.body,t,!0)}),te({type:"overline",names:["\\overline"],props:{numArgs:1},handler(e,t){var{parser:r}=e,a=t[0];return{type:"overline",mode:r.mode,body:a}},htmlBuilder(e,t){var r=tg(e.body,t.havingCrampedStyle()),a=e1.makeLineSpan("overline-line",t),n=t.fontMetrics().defaultRuleThickness,i=e1.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"kern",size:3*n},{type:"elem",elem:a},{type:"kern",size:n}]},t);return e1.makeSpan(["mord","overline"],[i],t)},mathmlBuilder(e,t){var r=new tk.MathNode("mo",[new tk.TextNode("‾")]);r.setAttribute("stretchy","true");var a=new tk.MathNode("mover",[tC(e.body,t),r]);return a.setAttribute("accent","true"),a}}),te({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=t[0];return{type:"phantom",mode:r.mode,body:ta(a)}},htmlBuilder:(e,t)=>{var r=th(e.body,t.withPhantom(),!1);return e1.makeFragment(r)},mathmlBuilder:(e,t)=>{var r=tT(e.body,t);return new tk.MathNode("mphantom",r)}}),te({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=t[0];return{type:"hphantom",mode:r.mode,body:a}},htmlBuilder:(e,t)=>{var r=e1.makeSpan([],[tg(e.body,t.withPhantom())]);if(r.height=0,r.depth=0,r.children)for(var a=0;a<r.children.length;a++)r.children[a].height=0,r.children[a].depth=0;return r=e1.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t),e1.makeSpan(["mord"],[r],t)},mathmlBuilder:(e,t)=>{var r=tT(ta(e.body),t),a=new tk.MathNode("mphantom",r),n=new tk.MathNode("mpadded",[a]);return n.setAttribute("height","0px"),n.setAttribute("depth","0px"),n}}),te({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,a=t[0];return{type:"vphantom",mode:r.mode,body:a}},htmlBuilder:(e,t)=>{var r=e1.makeSpan(["inner"],[tg(e.body,t.withPhantom())]),a=e1.makeSpan(["fix"],[]);return e1.makeSpan(["mord","rlap"],[r,a],t)},mathmlBuilder:(e,t)=>{var r=tT(ta(e.body),t),a=new tk.MathNode("mphantom",r),n=new tk.MathNode("mpadded",[a]);return n.setAttribute("width","0px"),n}}),te({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(e,t){var{parser:r}=e,a=tD(t[0],"size").value,n=t[1];return{type:"raisebox",mode:r.mode,dy:a,body:n}},htmlBuilder(e,t){var r=tg(e.body,t),a=X(e.dy,t);return e1.makeVList({positionType:"shift",positionData:-a,children:[{type:"elem",elem:r}]},t)},mathmlBuilder(e,t){var r=new tk.MathNode("mpadded",[tC(e.body,t)]),a=e.dy.number+e.dy.unit;return r.setAttribute("voffset",a),r}}),te({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0,allowedInArgument:!0},handler(e){var{parser:t}=e;return{type:"internal",mode:t.mode}}}),te({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["size","size","size"]},handler(e,t,r){var{parser:a}=e,n=r[0],i=tD(t[0],"size"),s=tD(t[1],"size");return{type:"rule",mode:a.mode,shift:n&&tD(n,"size").value,width:i.value,height:s.value}},htmlBuilder(e,t){var r=e1.makeSpan(["mord","rule"],[],t),a=X(e.width,t),n=X(e.height,t),i=e.shift?X(e.shift,t):0;return r.style.borderRightWidth=W(a),r.style.borderTopWidth=W(n),r.style.bottom=W(i),r.width=a,r.height=n+i,r.depth=-i,r.maxFontSize=1.125*n*t.sizeMultiplier,r},mathmlBuilder(e,t){var r=X(e.width,t),a=X(e.height,t),n=e.shift?X(e.shift,t):0,i=t.color&&t.getColor()||"black",s=new tk.MathNode("mspace");s.setAttribute("mathbackground",i),s.setAttribute("width",W(r)),s.setAttribute("height",W(a));var o=new tk.MathNode("mpadded",[s]);return n>=0?o.setAttribute("height",W(n)):(o.setAttribute("height",W(n)),o.setAttribute("depth",W(-n))),o.setAttribute("voffset",W(n)),o}});var r1=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"];te({type:"sizing",names:r1,props:{numArgs:0,allowedInText:!0},handler:(e,t)=>{var{breakOnTokenText:r,funcName:a,parser:n}=e,i=n.parseExpression(!1,r);return{type:"sizing",mode:n.mode,size:r1.indexOf(a)+1,body:i}},htmlBuilder:(e,t)=>{var r=t.havingSize(e.size);return r0(e.body,r,t)},mathmlBuilder:(e,t)=>{var r=t.havingSize(e.size),a=tT(e.body,r),n=new tk.MathNode("mstyle",a);return n.setAttribute("mathsize",W(r.sizeMultiplier)),n}}),te({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(e,t,r)=>{var{parser:a}=e,n=!1,i=!1,s=r[0]&&tD(r[0],"ordgroup");if(s)for(var o="",l=0;l<s.body.length;++l)if("t"===(o=s.body[l].text))n=!0;else if("b"===o)i=!0;else{n=!1,i=!1;break}else n=!0,i=!0;var h=t[0];return{type:"smash",mode:a.mode,body:h,smashHeight:n,smashDepth:i}},htmlBuilder:(e,t)=>{var r=e1.makeSpan([],[tg(e.body,t)]);if(!e.smashHeight&&!e.smashDepth)return r;if(e.smashHeight&&(r.height=0,r.children))for(var a=0;a<r.children.length;a++)r.children[a].height=0;if(e.smashDepth&&(r.depth=0,r.children))for(var n=0;n<r.children.length;n++)r.children[n].depth=0;var i=e1.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t);return e1.makeSpan(["mord"],[i],t)},mathmlBuilder:(e,t)=>{var r=new tk.MathNode("mpadded",[tC(e.body,t)]);return e.smashHeight&&r.setAttribute("height","0px"),e.smashDepth&&r.setAttribute("depth","0px"),r}}),te({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(e,t,r){var{parser:a}=e,n=r[0],i=t[0];return{type:"sqrt",mode:a.mode,body:i,index:n}},htmlBuilder(e,t){var r=tg(e.body,t.havingCrampedStyle());0===r.height&&(r.height=t.fontMetrics().xHeight),r=e1.wrapFragment(r,t);var a=t.fontMetrics().defaultRuleThickness,n=a;t.style.id<M.TEXT.id&&(n=t.fontMetrics().xHeight);var i=a+n/4,s=r.height+r.depth+i+a,{span:o,ruleWidth:l,advanceWidth:h}=rv.sqrtImage(s,t),m=o.height-l;m>r.height+r.depth+i&&(i=(i+m-r.height-r.depth)/2);var c=o.height-r.height-i-l;r.style.paddingLeft=W(h);var p=e1.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r,wrapperClasses:["svg-align"]},{type:"kern",size:-(r.height+c)},{type:"elem",elem:o},{type:"kern",size:l}]},t);if(!e.index)return e1.makeSpan(["mord","sqrt"],[p],t);var u=t.havingStyle(M.SCRIPTSCRIPT),d=tg(e.index,u,t),g=.6*(p.height-p.depth),f=e1.makeVList({positionType:"shift",positionData:-g,children:[{type:"elem",elem:d}]},t),v=e1.makeSpan(["root"],[f]);return e1.makeSpan(["mord","sqrt"],[v,p],t)},mathmlBuilder(e,t){var{body:r,index:a}=e;return a?new tk.MathNode("mroot",[tC(r,t),tC(a,t)]):new tk.MathNode("msqrt",[tC(r,t)])}});var r4={display:M.DISPLAY,text:M.TEXT,script:M.SCRIPT,scriptscript:M.SCRIPTSCRIPT};te({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e,t){var{breakOnTokenText:r,funcName:a,parser:n}=e,i=n.parseExpression(!0,r),s=a.slice(1,a.length-5);return{type:"styling",mode:n.mode,style:s,body:i}},htmlBuilder(e,t){var r=r4[e.style],a=t.havingStyle(r).withFont("");return r0(e.body,a,t)},mathmlBuilder(e,t){var r=r4[e.style],a=t.havingStyle(r),n=tT(e.body,a),i=new tk.MathNode("mstyle",n),s={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]}[e.style];return i.setAttribute("scriptlevel",s[0]),i.setAttribute("displaystyle",s[1]),i}});var r5=function(e,t){var r=e.base;if(!r)return null;if("op"===r.type)return r.limits&&(t.style.size===M.DISPLAY.size||r.alwaysHandleSupSub)?r$:null;if("operatorname"===r.type)return r.alwaysHandleSupSub&&(t.style.size===M.DISPLAY.size||r.limits)?rQ:null;if("accent"===r.type)return u.isCharacterBox(r.base)?tF:null;if("horizBrace"===r.type)return!e.sub===r.isOver?rY:null;else return null};tt({type:"supsub",htmlBuilder(e,t){var r,a,n,i,s=r5(e,t);if(s)return s(e,t);var{base:o,sup:l,sub:h}=e,m=tg(o,t),c=t.fontMetrics(),p=0,d=0,g=o&&u.isCharacterBox(o);if(l){var f=t.havingStyle(t.style.sup());r=tg(l,f,t),g||(p=m.height-f.fontMetrics().supDrop*f.sizeMultiplier/t.sizeMultiplier)}if(h){var v=t.havingStyle(t.style.sub());a=tg(h,v,t),g||(d=m.depth+v.fontMetrics().subDrop*v.sizeMultiplier/t.sizeMultiplier)}n=t.style===M.DISPLAY?c.sup1:t.style.cramped?c.sup3:c.sup2;var b=t.sizeMultiplier,y=W(.5/c.ptPerEm/b),x=null;if(a){var w=e.base&&"op"===e.base.type&&e.base.name&&("\\oiint"===e.base.name||"\\oiiint"===e.base.name);(m instanceof er||w)&&(x=W(-m.italic))}if(r&&a){p=Math.max(p,n,r.depth+.25*c.xHeight),d=Math.max(d,c.sub2);var k=4*c.defaultRuleThickness;if(p-r.depth-(a.height-d)<k){d=k-(p-r.depth)+a.height;var S=.8*c.xHeight-(p-r.depth);S>0&&(p+=S,d-=S)}var z=[{type:"elem",elem:a,shift:d,marginRight:y,marginLeft:x},{type:"elem",elem:r,shift:-p,marginRight:y}];i=e1.makeVList({positionType:"individualShift",children:z},t)}else if(a){d=Math.max(d,c.sub1,a.height-.8*c.xHeight);var A=[{type:"elem",elem:a,marginLeft:x,marginRight:y}];i=e1.makeVList({positionType:"shift",positionData:d,children:A},t)}else if(r)p=Math.max(p,n,r.depth+.25*c.xHeight),i=e1.makeVList({positionType:"shift",positionData:-p,children:[{type:"elem",elem:r,marginRight:y}]},t);else throw Error("supsub must have either sup or sub.");var T=tu(m,"right")||"mord";return e1.makeSpan([T],[m,e1.makeSpan(["msupsub"],[i])],t)},mathmlBuilder(e,t){var r,a,n=!1;e.base&&"horizBrace"===e.base.type&&!!e.sup===e.base.isOver&&(n=!0,r=e.base.isOver),e.base&&("op"===e.base.type||"operatorname"===e.base.type)&&(e.base.parentIsSupSub=!0);var i=[tC(e.base,t)];if(e.sub&&i.push(tC(e.sub,t)),e.sup&&i.push(tC(e.sup,t)),n)a=r?"mover":"munder";else if(e.sub){if(e.sup){var s=e.base;a=s&&"op"===s.type&&s.limits&&t.style===M.DISPLAY?"munderover":s&&"operatorname"===s.type&&s.alwaysHandleSupSub&&(t.style===M.DISPLAY||s.limits)?"munderover":"msubsup"}else{var o=e.base;a=o&&"op"===o.type&&o.limits&&(t.style===M.DISPLAY||o.alwaysHandleSupSub)?"munder":o&&"operatorname"===o.type&&o.alwaysHandleSupSub&&(o.limits||t.style===M.DISPLAY)?"munder":"msub"}}else{var l=e.base;a=l&&"op"===l.type&&l.limits&&(t.style===M.DISPLAY||l.alwaysHandleSupSub)?"mover":l&&"operatorname"===l.type&&l.alwaysHandleSupSub&&(l.limits||t.style===M.DISPLAY)?"mover":"msup"}return new tk.MathNode(a,i)}}),tt({type:"atom",htmlBuilder:(e,t)=>e1.mathsym(e.text,e.mode,t,["m"+e.family]),mathmlBuilder(e,t){var r=new tk.MathNode("mo",[tS(e.text,e.mode)]);if("bin"===e.family){var a=tz(e,t);"bold-italic"===a&&r.setAttribute("mathvariant",a)}else"punct"===e.family?r.setAttribute("separator","true"):("open"===e.family||"close"===e.family)&&r.setAttribute("stretchy","false");return r}});var r6={mi:"italic",mn:"normal",mtext:"normal"};tt({type:"mathord",htmlBuilder:(e,t)=>e1.makeOrd(e,t,"mathord"),mathmlBuilder(e,t){var r=new tk.MathNode("mi",[tS(e.text,e.mode,t)]),a=tz(e,t)||"italic";return a!==r6[r.type]&&r.setAttribute("mathvariant",a),r}}),tt({type:"textord",htmlBuilder:(e,t)=>e1.makeOrd(e,t,"textord"),mathmlBuilder(e,t){var r,a=tS(e.text,e.mode,t),n=tz(e,t)||"normal";return n!==r6[(r="text"===e.mode?new tk.MathNode("mtext",[a]):/[0-9]/.test(e.text)?new tk.MathNode("mn",[a]):"\\prime"===e.text?new tk.MathNode("mo",[a]):new tk.MathNode("mi",[a])).type]&&r.setAttribute("mathvariant",n),r}});var r7={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},r3={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};tt({type:"spacing",htmlBuilder(e,t){if(r3.hasOwnProperty(e.text)){var r=r3[e.text].className||"";if("text"!==e.mode)return e1.makeSpan(["mspace",r],[e1.mathsym(e.text,e.mode,t)],t);var a=e1.makeOrd(e,t,"textord");return a.classes.push(r),a}if(r7.hasOwnProperty(e.text))return e1.makeSpan(["mspace",r7[e.text]],[],t);throw new i('Unknown type of space "'+e.text+'"')},mathmlBuilder(e,t){var r;if(r3.hasOwnProperty(e.text))r=new tk.MathNode("mtext",[new tk.TextNode("\xa0")]);else if(r7.hasOwnProperty(e.text))return new tk.MathNode("mspace");else throw new i('Unknown type of space "'+e.text+'"');return r}});var r8=()=>{var e=new tk.MathNode("mtd",[]);return e.setAttribute("width","50%"),e};tt({type:"tag",mathmlBuilder(e,t){var r=new tk.MathNode("mtable",[new tk.MathNode("mtr",[r8(),new tk.MathNode("mtd",[tB(e.body,t)]),r8(),new tk.MathNode("mtd",[tB(e.tag,t)])])]);return r.setAttribute("width","100%"),r}});var r2={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},r9={"\\textbf":"textbf","\\textmd":"textmd"},ae={"\\textit":"textit","\\textup":"textup"},at=(e,t)=>{var r=e.font;return r?r2[r]?t.withTextFontFamily(r2[r]):r9[r]?t.withTextFontWeight(r9[r]):"\\emph"===r?"textit"===t.fontShape?t.withTextFontShape("textup"):t.withTextFontShape("textit"):t.withTextFontShape(ae[r]):t};te({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup","\\emph"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(e,t){var{parser:r,funcName:a}=e,n=t[0];return{type:"text",mode:r.mode,body:ta(n),font:a}},htmlBuilder(e,t){var r=at(e,t),a=th(e.body,r,!0);return e1.makeSpan(["mord","text"],a,r)},mathmlBuilder(e,t){var r=at(e,t);return tB(e.body,r)}}),te({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(e,t){var{parser:r}=e;return{type:"underline",mode:r.mode,body:t[0]}},htmlBuilder(e,t){var r=tg(e.body,t),a=e1.makeLineSpan("underline-line",t),n=t.fontMetrics().defaultRuleThickness,i=e1.makeVList({positionType:"top",positionData:r.height,children:[{type:"kern",size:n},{type:"elem",elem:a},{type:"kern",size:3*n},{type:"elem",elem:r}]},t);return e1.makeSpan(["mord","underline"],[i],t)},mathmlBuilder(e,t){var r=new tk.MathNode("mo",[new tk.TextNode("‾")]);r.setAttribute("stretchy","true");var a=new tk.MathNode("munder",[tC(e.body,t),r]);return a.setAttribute("accentunder","true"),a}}),te({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(e,t){var{parser:r}=e;return{type:"vcenter",mode:r.mode,body:t[0]}},htmlBuilder(e,t){var r=tg(e.body,t),a=t.fontMetrics().axisHeight,n=.5*(r.height-a-(r.depth+a));return e1.makeVList({positionType:"shift",positionData:n,children:[{type:"elem",elem:r}]},t)},mathmlBuilder:(e,t)=>new tk.MathNode("mpadded",[tC(e.body,t)],["vcenter"])}),te({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(e,t,r){throw new i("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(e,t){for(var r=ar(e),a=[],n=t.havingStyle(t.style.text()),i=0;i<r.length;i++){var s=r[i];"~"===s&&(s="\\textasciitilde"),a.push(e1.makeSymbol(s,"Typewriter-Regular",e.mode,n,["mord","texttt"]))}return e1.makeSpan(["mord","text"].concat(n.sizingClasses(t)),e1.tryCombineChars(a),n)},mathmlBuilder(e,t){var r=new tk.TextNode(ar(e)),a=new tk.MathNode("mtext",[r]);return a.setAttribute("mathvariant","monospace"),a}});var ar=e=>e.body.replace(/ /g,e.star?"␣":"\xa0"),aa="[ \r\n	]",an="[̀-ͯ]",ai=RegExp(an+"+$"),as="("+aa+"+)|\\\\(\n|[ \r	]+\n?)[ \r	]*|([!-\\[\\]-‧‪-퟿豈-￿]"+an+"*|[\uD800-\uDBFF][\uDC00-\uDFFF]"+an+"*|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5|(\\\\[a-zA-Z@]+)"+aa+"*|\\\\[^\uD800-\uDFFF])";class ao{constructor(e,t){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=e,this.settings=t,this.tokenRegex=RegExp(as,"g"),this.catcodes={"%":14,"~":13}}setCatcode(e,t){this.catcodes[e]=t}lex(){var e=this.input,t=this.tokenRegex.lastIndex;if(t===e.length)return new n("EOF",new a(this,t,t));var r=this.tokenRegex.exec(e);if(null===r||r.index!==t)throw new i("Unexpected character: '"+e[t]+"'",new n(e[t],new a(this,t,t+1)));var s=r[6]||r[3]||(r[2]?"\\ ":" ");if(14===this.catcodes[s]){var o=e.indexOf("\n",this.tokenRegex.lastIndex);return -1===o?(this.tokenRegex.lastIndex=e.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=o+1,this.lex()}return new n(s,new a(this,t,this.tokenRegex.lastIndex))}}class al{constructor(e,t){void 0===e&&(e={}),void 0===t&&(t={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=t,this.builtins=e,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(0===this.undefStack.length)throw new i("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");var e=this.undefStack.pop();for(var t in e)e.hasOwnProperty(t)&&(null==e[t]?delete this.current[t]:this.current[t]=e[t])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)}get(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]}set(e,t,r){if(void 0===r&&(r=!1),r){for(var a=0;a<this.undefStack.length;a++)delete this.undefStack[a][e];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][e]=t)}else{var n=this.undefStack[this.undefStack.length-1];n&&!n.hasOwnProperty(e)&&(n[e]=this.current[e])}null==t?delete this.current[e]:this.current[e]=t}}rA["\\noexpand"]=function(e){var t=e.popToken();return e.isExpandable(t.text)&&(t.noexpand=!0,t.treatAsRelax=!0),{tokens:[t],numArgs:0}},rA["\\expandafter"]=function(e){var t=e.popToken();return e.expandOnce(!0),{tokens:[t],numArgs:0}},rA["\\@firstoftwo"]=function(e){return{tokens:e.consumeArgs(2)[0],numArgs:0}},rA["\\@secondoftwo"]=function(e){return{tokens:e.consumeArgs(2)[1],numArgs:0}},rA["\\@ifnextchar"]=function(e){var t=e.consumeArgs(3);e.consumeSpaces();var r=e.future();return 1===t[0].length&&t[0][0].text===r.text?{tokens:t[1],numArgs:0}:{tokens:t[2],numArgs:0}},rA["\\@ifstar"]="\\@ifnextchar *{\\@firstoftwo{#1}}",rA["\\TextOrMath"]=function(e){var t=e.consumeArgs(2);return"text"===e.mode?{tokens:t[0],numArgs:0}:{tokens:t[1],numArgs:0}};var ah={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};rA["\\char"]=function(e){var t,r,a=e.popToken(),n="";if("'"===a.text)t=8,a=e.popToken();else if('"'===a.text)t=16,a=e.popToken();else if("`"===a.text){if("\\"===(a=e.popToken()).text[0])n=a.text.charCodeAt(1);else if("EOF"===a.text)throw new i("\\char` missing argument");else n=a.text.charCodeAt(0)}else t=10;if(t){if(null==(n=ah[a.text])||n>=t)throw new i("Invalid base-"+t+" digit "+a.text);for(;null!=(r=ah[e.future().text])&&r<t;)n*=t,n+=r,e.popToken()}return"\\@char{"+n+"}"};var am=(e,t,r,a)=>{var n=e.consumeArg().tokens;if(1!==n.length)throw new i("\\newcommand's first argument must be a macro name");var s=n[0].text,o=e.isDefined(s);if(o&&!t)throw new i("\\newcommand{"+s+"} attempting to redefine "+s+"; use \\renewcommand");if(!o&&!r)throw new i("\\renewcommand{"+s+"} when command "+s+" does not yet exist; use \\newcommand");var l=0;if(1===(n=e.consumeArg().tokens).length&&"["===n[0].text){for(var h="",m=e.expandNextToken();"]"!==m.text&&"EOF"!==m.text;)h+=m.text,m=e.expandNextToken();if(!h.match(/^\s*[0-9]+\s*$/))throw new i("Invalid number of arguments: "+h);l=parseInt(h),n=e.consumeArg().tokens}return o&&a||e.macros.set(s,{tokens:n,numArgs:l}),""};rA["\\newcommand"]=e=>am(e,!1,!0,!1),rA["\\renewcommand"]=e=>am(e,!0,!1,!1),rA["\\providecommand"]=e=>am(e,!0,!0,!0),rA["\\message"]=e=>(console.log(e.consumeArgs(1)[0].reverse().map(e=>e.text).join("")),""),rA["\\errmessage"]=e=>(console.error(e.consumeArgs(1)[0].reverse().map(e=>e.text).join("")),""),rA["\\show"]=e=>{var t=e.popToken(),r=t.text;return console.log(t,e.macros.get(r),e8[r],eh.math[r],eh.text[r]),""},rA["\\bgroup"]="{",rA["\\egroup"]="}",rA["~"]="\\nobreakspace",rA["\\lq"]="`",rA["\\rq"]="'",rA["\\aa"]="\\r a",rA["\\AA"]="\\r A",rA["\\textcopyright"]="\\html@mathml{\\textcircled{c}}{\\char`\xa9}",rA["\\copyright"]="\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}",rA["\\textregistered"]="\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`\xae}",rA["ℬ"]="\\mathscr{B}",rA["ℰ"]="\\mathscr{E}",rA["ℱ"]="\\mathscr{F}",rA["ℋ"]="\\mathscr{H}",rA["ℐ"]="\\mathscr{I}",rA["ℒ"]="\\mathscr{L}",rA["ℳ"]="\\mathscr{M}",rA["ℛ"]="\\mathscr{R}",rA["ℭ"]="\\mathfrak{C}",rA["ℌ"]="\\mathfrak{H}",rA["ℨ"]="\\mathfrak{Z}",rA["\\Bbbk"]="\\Bbb{k}",rA["\xb7"]="\\cdotp",rA["\\llap"]="\\mathllap{\\textrm{#1}}",rA["\\rlap"]="\\mathrlap{\\textrm{#1}}",rA["\\clap"]="\\mathclap{\\textrm{#1}}",rA["\\mathstrut"]="\\vphantom{(}",rA["\\underbar"]="\\underline{\\text{#1}}",rA["\\not"]='\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}',rA["\\neq"]="\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`≠}}",rA["\\ne"]="\\neq",rA["≠"]="\\neq",rA["\\notin"]="\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`∉}}",rA["∉"]="\\notin",rA["≘"]="\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`≘}}",rA["≙"]="\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`≘}}",rA["≚"]="\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`≚}}",rA["≛"]="\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`≛}}",rA["≝"]="\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`≝}}",rA["≞"]="\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`≞}}",rA["≟"]="\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`≟}}",rA["⟂"]="\\perp",rA["‼"]="\\mathclose{!\\mkern-0.8mu!}",rA["∌"]="\\notni",rA["⌜"]="\\ulcorner",rA["⌝"]="\\urcorner",rA["⌞"]="\\llcorner",rA["⌟"]="\\lrcorner",rA["\xa9"]="\\copyright",rA["\xae"]="\\textregistered",rA["️"]="\\textregistered",rA["\\ulcorner"]='\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}',rA["\\urcorner"]='\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}',rA["\\llcorner"]='\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}',rA["\\lrcorner"]='\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}',rA["\\vdots"]="{\\varvdots\\rule{0pt}{15pt}}",rA["⋮"]="\\vdots",rA["\\varGamma"]="\\mathit{\\Gamma}",rA["\\varDelta"]="\\mathit{\\Delta}",rA["\\varTheta"]="\\mathit{\\Theta}",rA["\\varLambda"]="\\mathit{\\Lambda}",rA["\\varXi"]="\\mathit{\\Xi}",rA["\\varPi"]="\\mathit{\\Pi}",rA["\\varSigma"]="\\mathit{\\Sigma}",rA["\\varUpsilon"]="\\mathit{\\Upsilon}",rA["\\varPhi"]="\\mathit{\\Phi}",rA["\\varPsi"]="\\mathit{\\Psi}",rA["\\varOmega"]="\\mathit{\\Omega}",rA["\\substack"]="\\begin{subarray}{c}#1\\end{subarray}",rA["\\colon"]="\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax",rA["\\boxed"]="\\fbox{$\\displaystyle{#1}$}",rA["\\iff"]="\\DOTSB\\;\\Longleftrightarrow\\;",rA["\\implies"]="\\DOTSB\\;\\Longrightarrow\\;",rA["\\impliedby"]="\\DOTSB\\;\\Longleftarrow\\;",rA["\\dddot"]="{\\overset{\\raisebox{-0.1ex}{\\normalsize ...}}{#1}}",rA["\\ddddot"]="{\\overset{\\raisebox{-0.1ex}{\\normalsize ....}}{#1}}";var ac={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};rA["\\dots"]=function(e){var t="\\dotso",r=e.expandAfterFuture().text;return r in ac?t=ac[r]:"\\not"===r.slice(0,4)?t="\\dotsb":r in eh.math&&u.contains(["bin","rel"],eh.math[r].group)&&(t="\\dotsb"),t};var ap={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};rA["\\dotso"]=function(e){return e.future().text in ap?"\\ldots\\,":"\\ldots"},rA["\\dotsc"]=function(e){var t=e.future().text;return t in ap&&","!==t?"\\ldots\\,":"\\ldots"},rA["\\cdots"]=function(e){return e.future().text in ap?"\\@cdots\\,":"\\@cdots"},rA["\\dotsb"]="\\cdots",rA["\\dotsm"]="\\cdots",rA["\\dotsi"]="\\!\\cdots",rA["\\dotsx"]="\\ldots\\,",rA["\\DOTSI"]="\\relax",rA["\\DOTSB"]="\\relax",rA["\\DOTSX"]="\\relax",rA["\\tmspace"]="\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax",rA["\\,"]="\\tmspace+{3mu}{.1667em}",rA["\\thinspace"]="\\,",rA["\\>"]="\\mskip{4mu}",rA["\\:"]="\\tmspace+{4mu}{.2222em}",rA["\\medspace"]="\\:",rA["\\;"]="\\tmspace+{5mu}{.2777em}",rA["\\thickspace"]="\\;",rA["\\!"]="\\tmspace-{3mu}{.1667em}",rA["\\negthinspace"]="\\!",rA["\\negmedspace"]="\\tmspace-{4mu}{.2222em}",rA["\\negthickspace"]="\\tmspace-{5mu}{.277em}",rA["\\enspace"]="\\kern.5em ",rA["\\enskip"]="\\hskip.5em\\relax",rA["\\quad"]="\\hskip1em\\relax",rA["\\qquad"]="\\hskip2em\\relax",rA["\\tag"]="\\@ifstar\\tag@literal\\tag@paren",rA["\\tag@paren"]="\\tag@literal{({#1})}",rA["\\tag@literal"]=e=>{if(e.macros.get("\\df@tag"))throw new i("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"},rA["\\bmod"]="\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}",rA["\\pod"]="\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)",rA["\\pmod"]="\\pod{{\\rm mod}\\mkern6mu#1}",rA["\\mod"]="\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1",rA["\\newline"]="\\\\\\relax",rA["\\TeX"]="\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}";var au=W(H["Main-Regular"][84][1]-.7*H["Main-Regular"][65][1]);rA["\\LaTeX"]="\\textrm{\\html@mathml{L\\kern-.36em\\raisebox{"+au+"}{\\scriptstyle A}\\kern-.15em\\TeX}{LaTeX}}",rA["\\KaTeX"]="\\textrm{\\html@mathml{K\\kern-.17em\\raisebox{"+au+"}{\\scriptstyle A}\\kern-.15em\\TeX}{KaTeX}}",rA["\\hspace"]="\\@ifstar\\@hspacer\\@hspace",rA["\\@hspace"]="\\hskip #1\\relax",rA["\\@hspacer"]="\\rule{0pt}{0pt}\\hskip #1\\relax",rA["\\ordinarycolon"]=":",rA["\\vcentcolon"]="\\mathrel{\\mathop\\ordinarycolon}",rA["\\dblcolon"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}',rA["\\coloneqq"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}',rA["\\Coloneqq"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}',rA["\\coloneq"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}',rA["\\Coloneq"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}',rA["\\eqqcolon"]='\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}',rA["\\Eqqcolon"]='\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}',rA["\\eqcolon"]='\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}',rA["\\Eqcolon"]='\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}',rA["\\colonapprox"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}',rA["\\Colonapprox"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}',rA["\\colonsim"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}',rA["\\Colonsim"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}',rA["∷"]="\\dblcolon",rA["∹"]="\\eqcolon",rA["≔"]="\\coloneqq",rA["≕"]="\\eqqcolon",rA["⩴"]="\\Coloneqq",rA["\\ratio"]="\\vcentcolon",rA["\\coloncolon"]="\\dblcolon",rA["\\colonequals"]="\\coloneqq",rA["\\coloncolonequals"]="\\Coloneqq",rA["\\equalscolon"]="\\eqqcolon",rA["\\equalscoloncolon"]="\\Eqqcolon",rA["\\colonminus"]="\\coloneq",rA["\\coloncolonminus"]="\\Coloneq",rA["\\minuscolon"]="\\eqcolon",rA["\\minuscoloncolon"]="\\Eqcolon",rA["\\coloncolonapprox"]="\\Colonapprox",rA["\\coloncolonsim"]="\\Colonsim",rA["\\simcolon"]="\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}",rA["\\simcoloncolon"]="\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}",rA["\\approxcolon"]="\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}",rA["\\approxcoloncolon"]="\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}",rA["\\notni"]="\\html@mathml{\\not\\ni}{\\mathrel{\\char`∌}}",rA["\\limsup"]="\\DOTSB\\operatorname*{lim\\,sup}",rA["\\liminf"]="\\DOTSB\\operatorname*{lim\\,inf}",rA["\\injlim"]="\\DOTSB\\operatorname*{inj\\,lim}",rA["\\projlim"]="\\DOTSB\\operatorname*{proj\\,lim}",rA["\\varlimsup"]="\\DOTSB\\operatorname*{\\overline{lim}}",rA["\\varliminf"]="\\DOTSB\\operatorname*{\\underline{lim}}",rA["\\varinjlim"]="\\DOTSB\\operatorname*{\\underrightarrow{lim}}",rA["\\varprojlim"]="\\DOTSB\\operatorname*{\\underleftarrow{lim}}",rA["\\gvertneqq"]="\\html@mathml{\\@gvertneqq}{≩}",rA["\\lvertneqq"]="\\html@mathml{\\@lvertneqq}{≨}",rA["\\ngeqq"]="\\html@mathml{\\@ngeqq}{≱}",rA["\\ngeqslant"]="\\html@mathml{\\@ngeqslant}{≱}",rA["\\nleqq"]="\\html@mathml{\\@nleqq}{≰}",rA["\\nleqslant"]="\\html@mathml{\\@nleqslant}{≰}",rA["\\nshortmid"]="\\html@mathml{\\@nshortmid}{∤}",rA["\\nshortparallel"]="\\html@mathml{\\@nshortparallel}{∦}",rA["\\nsubseteqq"]="\\html@mathml{\\@nsubseteqq}{⊈}",rA["\\nsupseteqq"]="\\html@mathml{\\@nsupseteqq}{⊉}",rA["\\varsubsetneq"]="\\html@mathml{\\@varsubsetneq}{⊊}",rA["\\varsubsetneqq"]="\\html@mathml{\\@varsubsetneqq}{⫋}",rA["\\varsupsetneq"]="\\html@mathml{\\@varsupsetneq}{⊋}",rA["\\varsupsetneqq"]="\\html@mathml{\\@varsupsetneqq}{⫌}",rA["\\imath"]="\\html@mathml{\\@imath}{ı}",rA["\\jmath"]="\\html@mathml{\\@jmath}{ȷ}",rA["\\llbracket"]="\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`⟦}}",rA["\\rrbracket"]="\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`⟧}}",rA["⟦"]="\\llbracket",rA["⟧"]="\\rrbracket",rA["\\lBrace"]="\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`⦃}}",rA["\\rBrace"]="\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`⦄}}",rA["⦃"]="\\lBrace",rA["⦄"]="\\rBrace",rA["\\minuso"]="\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`⦵}}",rA["⦵"]="\\minuso",rA["\\darr"]="\\downarrow",rA["\\dArr"]="\\Downarrow",rA["\\Darr"]="\\Downarrow",rA["\\lang"]="\\langle",rA["\\rang"]="\\rangle",rA["\\uarr"]="\\uparrow",rA["\\uArr"]="\\Uparrow",rA["\\Uarr"]="\\Uparrow",rA["\\N"]="\\mathbb{N}",rA["\\R"]="\\mathbb{R}",rA["\\Z"]="\\mathbb{Z}",rA["\\alef"]="\\aleph",rA["\\alefsym"]="\\aleph",rA["\\Alpha"]="\\mathrm{A}",rA["\\Beta"]="\\mathrm{B}",rA["\\bull"]="\\bullet",rA["\\Chi"]="\\mathrm{X}",rA["\\clubs"]="\\clubsuit",rA["\\cnums"]="\\mathbb{C}",rA["\\Complex"]="\\mathbb{C}",rA["\\Dagger"]="\\ddagger",rA["\\diamonds"]="\\diamondsuit",rA["\\empty"]="\\emptyset",rA["\\Epsilon"]="\\mathrm{E}",rA["\\Eta"]="\\mathrm{H}",rA["\\exist"]="\\exists",rA["\\harr"]="\\leftrightarrow",rA["\\hArr"]="\\Leftrightarrow",rA["\\Harr"]="\\Leftrightarrow",rA["\\hearts"]="\\heartsuit",rA["\\image"]="\\Im",rA["\\infin"]="\\infty",rA["\\Iota"]="\\mathrm{I}",rA["\\isin"]="\\in",rA["\\Kappa"]="\\mathrm{K}",rA["\\larr"]="\\leftarrow",rA["\\lArr"]="\\Leftarrow",rA["\\Larr"]="\\Leftarrow",rA["\\lrarr"]="\\leftrightarrow",rA["\\lrArr"]="\\Leftrightarrow",rA["\\Lrarr"]="\\Leftrightarrow",rA["\\Mu"]="\\mathrm{M}",rA["\\natnums"]="\\mathbb{N}",rA["\\Nu"]="\\mathrm{N}",rA["\\Omicron"]="\\mathrm{O}",rA["\\plusmn"]="\\pm",rA["\\rarr"]="\\rightarrow",rA["\\rArr"]="\\Rightarrow",rA["\\Rarr"]="\\Rightarrow",rA["\\real"]="\\Re",rA["\\reals"]="\\mathbb{R}",rA["\\Reals"]="\\mathbb{R}",rA["\\Rho"]="\\mathrm{P}",rA["\\sdot"]="\\cdot",rA["\\sect"]="\\S",rA["\\spades"]="\\spadesuit",rA["\\sub"]="\\subset",rA["\\sube"]="\\subseteq",rA["\\supe"]="\\supseteq",rA["\\Tau"]="\\mathrm{T}",rA["\\thetasym"]="\\vartheta",rA["\\weierp"]="\\wp",rA["\\Zeta"]="\\mathrm{Z}",rA["\\argmin"]="\\DOTSB\\operatorname*{arg\\,min}",rA["\\argmax"]="\\DOTSB\\operatorname*{arg\\,max}",rA["\\plim"]="\\DOTSB\\mathop{\\operatorname{plim}}\\limits",rA["\\bra"]="\\mathinner{\\langle{#1}|}",rA["\\ket"]="\\mathinner{|{#1}\\rangle}",rA["\\braket"]="\\mathinner{\\langle{#1}\\rangle}",rA["\\Bra"]="\\left\\langle#1\\right|",rA["\\Ket"]="\\left|#1\\right\\rangle";var ad=e=>t=>{var r=t.consumeArg().tokens,a=t.consumeArg().tokens,n=t.consumeArg().tokens,i=t.consumeArg().tokens,s=t.macros.get("|"),o=t.macros.get("\\|");t.macros.beginGroup();var l=t=>r=>{e&&(r.macros.set("|",s),n.length&&r.macros.set("\\|",o));var i=t;return!t&&n.length&&"|"===r.future().text&&(r.popToken(),i=!0),{tokens:i?n:a,numArgs:0}};t.macros.set("|",l(!1)),n.length&&t.macros.set("\\|",l(!0));var h=t.consumeArg().tokens,m=t.expandTokens([...i,...h,...r]);return t.macros.endGroup(),{tokens:m.reverse(),numArgs:0}};s=ad(!1),rA["\\bra@ket"]=s,o=ad(!0),rA["\\bra@set"]=o,rA["\\Braket"]="\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}",rA["\\Set"]="\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}",rA["\\set"]="\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}",rA["\\angln"]="{\\angl n}",rA["\\blue"]="\\textcolor{##6495ed}{#1}",rA["\\orange"]="\\textcolor{##ffa500}{#1}",rA["\\pink"]="\\textcolor{##ff00af}{#1}",rA["\\red"]="\\textcolor{##df0030}{#1}",rA["\\green"]="\\textcolor{##28ae7b}{#1}",rA["\\gray"]="\\textcolor{gray}{#1}",rA["\\purple"]="\\textcolor{##9d38bd}{#1}",rA["\\blueA"]="\\textcolor{##ccfaff}{#1}",rA["\\blueB"]="\\textcolor{##80f6ff}{#1}",rA["\\blueC"]="\\textcolor{##63d9ea}{#1}",rA["\\blueD"]="\\textcolor{##11accd}{#1}",rA["\\blueE"]="\\textcolor{##0c7f99}{#1}",rA["\\tealA"]="\\textcolor{##94fff5}{#1}",rA["\\tealB"]="\\textcolor{##26edd5}{#1}",rA["\\tealC"]="\\textcolor{##01d1c1}{#1}",rA["\\tealD"]="\\textcolor{##01a995}{#1}",rA["\\tealE"]="\\textcolor{##208170}{#1}",rA["\\greenA"]="\\textcolor{##b6ffb0}{#1}",rA["\\greenB"]="\\textcolor{##8af281}{#1}",rA["\\greenC"]="\\textcolor{##74cf70}{#1}",rA["\\greenD"]="\\textcolor{##1fab54}{#1}",rA["\\greenE"]="\\textcolor{##0d923f}{#1}",rA["\\goldA"]="\\textcolor{##ffd0a9}{#1}",rA["\\goldB"]="\\textcolor{##ffbb71}{#1}",rA["\\goldC"]="\\textcolor{##ff9c39}{#1}",rA["\\goldD"]="\\textcolor{##e07d10}{#1}",rA["\\goldE"]="\\textcolor{##a75a05}{#1}",rA["\\redA"]="\\textcolor{##fca9a9}{#1}",rA["\\redB"]="\\textcolor{##ff8482}{#1}",rA["\\redC"]="\\textcolor{##f9685d}{#1}",rA["\\redD"]="\\textcolor{##e84d39}{#1}",rA["\\redE"]="\\textcolor{##bc2612}{#1}",rA["\\maroonA"]="\\textcolor{##ffbde0}{#1}",rA["\\maroonB"]="\\textcolor{##ff92c6}{#1}",rA["\\maroonC"]="\\textcolor{##ed5fa6}{#1}",rA["\\maroonD"]="\\textcolor{##ca337c}{#1}",rA["\\maroonE"]="\\textcolor{##9e034e}{#1}",rA["\\purpleA"]="\\textcolor{##ddd7ff}{#1}",rA["\\purpleB"]="\\textcolor{##c6b9fc}{#1}",rA["\\purpleC"]="\\textcolor{##aa87ff}{#1}",rA["\\purpleD"]="\\textcolor{##7854ab}{#1}",rA["\\purpleE"]="\\textcolor{##543b78}{#1}",rA["\\mintA"]="\\textcolor{##f5f9e8}{#1}",rA["\\mintB"]="\\textcolor{##edf2df}{#1}",rA["\\mintC"]="\\textcolor{##e0e5cc}{#1}",rA["\\grayA"]="\\textcolor{##f6f7f7}{#1}",rA["\\grayB"]="\\textcolor{##f0f1f2}{#1}",rA["\\grayC"]="\\textcolor{##e3e5e6}{#1}",rA["\\grayD"]="\\textcolor{##d6d8da}{#1}",rA["\\grayE"]="\\textcolor{##babec2}{#1}",rA["\\grayF"]="\\textcolor{##888d93}{#1}",rA["\\grayG"]="\\textcolor{##626569}{#1}",rA["\\grayH"]="\\textcolor{##3b3e40}{#1}",rA["\\grayI"]="\\textcolor{##21242c}{#1}",rA["\\kaBlue"]="\\textcolor{##314453}{#1}",rA["\\kaGreen"]="\\textcolor{##71B307}{#1}";var ag={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};class af{constructor(e,t,r){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=t,this.expansionCount=0,this.feed(e),this.macros=new al(rA,t.macros),this.mode=r,this.stack=[]}feed(e){this.lexer=new ao(e,this.settings)}switchMode(e){this.mode=e}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return 0===this.stack.length&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(e){this.stack.push(e)}pushTokens(e){this.stack.push(...e)}scanArgument(e){var t,r,a;if(e){if(this.consumeSpaces(),"["!==this.future().text)return null;t=this.popToken(),{tokens:a,end:r}=this.consumeArg(["]"])}else({tokens:a,start:t,end:r}=this.consumeArg());return this.pushToken(new n("EOF",r.loc)),this.pushTokens(a),t.range(r,"")}consumeSpaces(){for(;;)if(" "===this.future().text)this.stack.pop();else break}consumeArg(e){var t,r=[],a=e&&e.length>0;a||this.consumeSpaces();var n=this.future(),s=0,o=0;do{if(t=this.popToken(),r.push(t),"{"===t.text)++s;else if("}"===t.text){if(-1==--s)throw new i("Extra }",t)}else if("EOF"===t.text)throw new i("Unexpected end of input in a macro argument, expected '"+(e&&a?e[o]:"}")+"'",t);if(e&&a){if((0===s||1===s&&"{"===e[o])&&t.text===e[o]){if(++o===e.length){r.splice(-o,o);break}}else o=0}}while(0!==s||a);return"{"===n.text&&"}"===r[r.length-1].text&&(r.pop(),r.shift()),r.reverse(),{tokens:r,start:n,end:t}}consumeArgs(e,t){if(t){if(t.length!==e+1)throw new i("The length of delimiters doesn't match the number of args!");for(var r=t[0],a=0;a<r.length;a++){var n=this.popToken();if(r[a]!==n.text)throw new i("Use of the macro doesn't match its definition",n)}}for(var s=[],o=0;o<e;o++)s.push(this.consumeArg(t&&t[o+1]).tokens);return s}countExpansion(e){if(this.expansionCount+=e,this.expansionCount>this.settings.maxExpand)throw new i("Too many expansions: infinite loop or need to increase maxExpand setting")}expandOnce(e){var t=this.popToken(),r=t.text,a=t.noexpand?null:this._getExpansion(r);if(null==a||e&&a.unexpandable){if(e&&null==a&&"\\"===r[0]&&!this.isDefined(r))throw new i("Undefined control sequence: "+r);return this.pushToken(t),!1}this.countExpansion(1);var n=a.tokens,s=this.consumeArgs(a.numArgs,a.delimiters);if(a.numArgs){n=n.slice();for(var o=n.length-1;o>=0;--o){var l=n[o];if("#"===l.text){if(0===o)throw new i("Incomplete placeholder at end of macro body",l);if("#"===(l=n[--o]).text)n.splice(o+1,1);else if(/^[1-9]$/.test(l.text))n.splice(o,2,...s[+l.text-1]);else throw new i("Not a valid argument number",l)}}}return this.pushTokens(n),n.length}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;)if(!1===this.expandOnce()){var e=this.stack.pop();return e.treatAsRelax&&(e.text="\\relax"),e}throw Error()}expandMacro(e){return this.macros.has(e)?this.expandTokens([new n(e)]):void 0}expandTokens(e){var t=[],r=this.stack.length;for(this.pushTokens(e);this.stack.length>r;)if(!1===this.expandOnce(!0)){var a=this.stack.pop();a.treatAsRelax&&(a.noexpand=!1,a.treatAsRelax=!1),t.push(a)}return this.countExpansion(t.length),t}expandMacroAsText(e){var t=this.expandMacro(e);return t?t.map(e=>e.text).join(""):t}_getExpansion(e){var t=this.macros.get(e);if(null==t)return t;if(1===e.length){var r=this.lexer.catcodes[e];if(null!=r&&13!==r)return}var a="function"==typeof t?t(this):t;if("string"==typeof a){var n=0;if(-1!==a.indexOf("#"))for(var i=a.replace(/##/g,"");-1!==i.indexOf("#"+(n+1));)++n;for(var s=new ao(a,this.settings),o=[],l=s.lex();"EOF"!==l.text;)o.push(l),l=s.lex();return o.reverse(),{tokens:o,numArgs:n}}return a}isDefined(e){return this.macros.has(e)||e8.hasOwnProperty(e)||eh.math.hasOwnProperty(e)||eh.text.hasOwnProperty(e)||ag.hasOwnProperty(e)}isExpandable(e){var t=this.macros.get(e);return null!=t?"string"==typeof t||"function"==typeof t||!t.unexpandable:e8.hasOwnProperty(e)&&!e8[e].primitive}}var av=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,ab=Object.freeze({"₊":"+","₋":"-","₌":"=","₍":"(","₎":")","₀":"0","₁":"1","₂":"2","₃":"3","₄":"4","₅":"5","₆":"6","₇":"7","₈":"8","₉":"9",ₐ:"a",ₑ:"e",ₕ:"h",ᵢ:"i",ⱼ:"j",ₖ:"k",ₗ:"l",ₘ:"m",ₙ:"n",ₒ:"o",ₚ:"p",ᵣ:"r",ₛ:"s",ₜ:"t",ᵤ:"u",ᵥ:"v",ₓ:"x",ᵦ:"β",ᵧ:"γ",ᵨ:"ρ",ᵩ:"ϕ",ᵪ:"χ","⁺":"+","⁻":"-","⁼":"=","⁽":"(","⁾":")","⁰":"0","\xb9":"1","\xb2":"2","\xb3":"3","⁴":"4","⁵":"5","⁶":"6","⁷":"7","⁸":"8","⁹":"9",ᴬ:"A",ᴮ:"B",ᴰ:"D",ᴱ:"E",ᴳ:"G",ᴴ:"H",ᴵ:"I",ᴶ:"J",ᴷ:"K",ᴸ:"L",ᴹ:"M",ᴺ:"N",ᴼ:"O",ᴾ:"P",ᴿ:"R",ᵀ:"T",ᵁ:"U",ⱽ:"V",ᵂ:"W",ᵃ:"a",ᵇ:"b",ᶜ:"c",ᵈ:"d",ᵉ:"e",ᶠ:"f",ᵍ:"g",ʰ:"h",ⁱ:"i",ʲ:"j",ᵏ:"k",ˡ:"l",ᵐ:"m",ⁿ:"n",ᵒ:"o",ᵖ:"p",ʳ:"r",ˢ:"s",ᵗ:"t",ᵘ:"u",ᵛ:"v",ʷ:"w",ˣ:"x",ʸ:"y",ᶻ:"z",ᵝ:"β",ᵞ:"γ",ᵟ:"δ",ᵠ:"ϕ",ᵡ:"χ",ᶿ:"θ"}),ay={"́":{text:"\\'",math:"\\acute"},"̀":{text:"\\`",math:"\\grave"},"̈":{text:'\\"',math:"\\ddot"},"̃":{text:"\\~",math:"\\tilde"},"̄":{text:"\\=",math:"\\bar"},"̆":{text:"\\u",math:"\\breve"},"̌":{text:"\\v",math:"\\check"},"̂":{text:"\\^",math:"\\hat"},"̇":{text:"\\.",math:"\\dot"},"̊":{text:"\\r",math:"\\mathring"},"̋":{text:"\\H"},"̧":{text:"\\c"}},ax={á:"á",à:"à",ä:"ä",ǟ:"ǟ",ã:"ã",ā:"ā",ă:"ă",ắ:"ắ",ằ:"ằ",ẵ:"ẵ",ǎ:"ǎ",â:"â",ấ:"ấ",ầ:"ầ",ẫ:"ẫ",ȧ:"ȧ",ǡ:"ǡ",å:"å",ǻ:"ǻ",ḃ:"ḃ",ć:"ć",ḉ:"ḉ",č:"č",ĉ:"ĉ",ċ:"ċ",ç:"ç",ď:"ď",ḋ:"ḋ",ḑ:"ḑ",é:"é",è:"è",ë:"ë",ẽ:"ẽ",ē:"ē",ḗ:"ḗ",ḕ:"ḕ",ĕ:"ĕ",ḝ:"ḝ",ě:"ě",ê:"ê",ế:"ế",ề:"ề",ễ:"ễ",ė:"ė",ȩ:"ȩ",ḟ:"ḟ",ǵ:"ǵ",ḡ:"ḡ",ğ:"ğ",ǧ:"ǧ",ĝ:"ĝ",ġ:"ġ",ģ:"ģ",ḧ:"ḧ",ȟ:"ȟ",ĥ:"ĥ",ḣ:"ḣ",ḩ:"ḩ",í:"í",ì:"ì",ï:"ï",ḯ:"ḯ",ĩ:"ĩ",ī:"ī",ĭ:"ĭ",ǐ:"ǐ",î:"î",ǰ:"ǰ",ĵ:"ĵ",ḱ:"ḱ",ǩ:"ǩ",ķ:"ķ",ĺ:"ĺ",ľ:"ľ",ļ:"ļ",ḿ:"ḿ",ṁ:"ṁ",ń:"ń",ǹ:"ǹ",ñ:"ñ",ň:"ň",ṅ:"ṅ",ņ:"ņ",ó:"ó",ò:"ò",ö:"ö",ȫ:"ȫ",õ:"õ",ṍ:"ṍ",ṏ:"ṏ",ȭ:"ȭ",ō:"ō",ṓ:"ṓ",ṑ:"ṑ",ŏ:"ŏ",ǒ:"ǒ",ô:"ô",ố:"ố",ồ:"ồ",ỗ:"ỗ",ȯ:"ȯ",ȱ:"ȱ",ő:"ő",ṕ:"ṕ",ṗ:"ṗ",ŕ:"ŕ",ř:"ř",ṙ:"ṙ",ŗ:"ŗ",ś:"ś",ṥ:"ṥ",š:"š",ṧ:"ṧ",ŝ:"ŝ",ṡ:"ṡ",ş:"ş",ẗ:"ẗ",ť:"ť",ṫ:"ṫ",ţ:"ţ",ú:"ú",ù:"ù",ü:"ü",ǘ:"ǘ",ǜ:"ǜ",ǖ:"ǖ",ǚ:"ǚ",ũ:"ũ",ṹ:"ṹ",ū:"ū",ṻ:"ṻ",ŭ:"ŭ",ǔ:"ǔ",û:"û",ů:"ů",ű:"ű",ṽ:"ṽ",ẃ:"ẃ",ẁ:"ẁ",ẅ:"ẅ",ŵ:"ŵ",ẇ:"ẇ",ẘ:"ẘ",ẍ:"ẍ",ẋ:"ẋ",ý:"ý",ỳ:"ỳ",ÿ:"ÿ",ỹ:"ỹ",ȳ:"ȳ",ŷ:"ŷ",ẏ:"ẏ",ẙ:"ẙ",ź:"ź",ž:"ž",ẑ:"ẑ",ż:"ż",Á:"Á",À:"À",Ä:"Ä",Ǟ:"Ǟ",Ã:"Ã",Ā:"Ā",Ă:"Ă",Ắ:"Ắ",Ằ:"Ằ",Ẵ:"Ẵ",Ǎ:"Ǎ",Â:"Â",Ấ:"Ấ",Ầ:"Ầ",Ẫ:"Ẫ",Ȧ:"Ȧ",Ǡ:"Ǡ",Å:"Å",Ǻ:"Ǻ",Ḃ:"Ḃ",Ć:"Ć",Ḉ:"Ḉ",Č:"Č",Ĉ:"Ĉ",Ċ:"Ċ",Ç:"Ç",Ď:"Ď",Ḋ:"Ḋ",Ḑ:"Ḑ",É:"É",È:"È",Ë:"Ë",Ẽ:"Ẽ",Ē:"Ē",Ḗ:"Ḗ",Ḕ:"Ḕ",Ĕ:"Ĕ",Ḝ:"Ḝ",Ě:"Ě",Ê:"Ê",Ế:"Ế",Ề:"Ề",Ễ:"Ễ",Ė:"Ė",Ȩ:"Ȩ",Ḟ:"Ḟ",Ǵ:"Ǵ",Ḡ:"Ḡ",Ğ:"Ğ",Ǧ:"Ǧ",Ĝ:"Ĝ",Ġ:"Ġ",Ģ:"Ģ",Ḧ:"Ḧ",Ȟ:"Ȟ",Ĥ:"Ĥ",Ḣ:"Ḣ",Ḩ:"Ḩ",Í:"Í",Ì:"Ì",Ï:"Ï",Ḯ:"Ḯ",Ĩ:"Ĩ",Ī:"Ī",Ĭ:"Ĭ",Ǐ:"Ǐ",Î:"Î",İ:"İ",Ĵ:"Ĵ",Ḱ:"Ḱ",Ǩ:"Ǩ",Ķ:"Ķ",Ĺ:"Ĺ",Ľ:"Ľ",Ļ:"Ļ",Ḿ:"Ḿ",Ṁ:"Ṁ",Ń:"Ń",Ǹ:"Ǹ",Ñ:"Ñ",Ň:"Ň",Ṅ:"Ṅ",Ņ:"Ņ",Ó:"Ó",Ò:"Ò",Ö:"Ö",Ȫ:"Ȫ",Õ:"Õ",Ṍ:"Ṍ",Ṏ:"Ṏ",Ȭ:"Ȭ",Ō:"Ō",Ṓ:"Ṓ",Ṑ:"Ṑ",Ŏ:"Ŏ",Ǒ:"Ǒ",Ô:"Ô",Ố:"Ố",Ồ:"Ồ",Ỗ:"Ỗ",Ȯ:"Ȯ",Ȱ:"Ȱ",Ő:"Ő",Ṕ:"Ṕ",Ṗ:"Ṗ",Ŕ:"Ŕ",Ř:"Ř",Ṙ:"Ṙ",Ŗ:"Ŗ",Ś:"Ś",Ṥ:"Ṥ",Š:"Š",Ṧ:"Ṧ",Ŝ:"Ŝ",Ṡ:"Ṡ",Ş:"Ş",Ť:"Ť",Ṫ:"Ṫ",Ţ:"Ţ",Ú:"Ú",Ù:"Ù",Ü:"Ü",Ǘ:"Ǘ",Ǜ:"Ǜ",Ǖ:"Ǖ",Ǚ:"Ǚ",Ũ:"Ũ",Ṹ:"Ṹ",Ū:"Ū",Ṻ:"Ṻ",Ŭ:"Ŭ",Ǔ:"Ǔ",Û:"Û",Ů:"Ů",Ű:"Ű",Ṽ:"Ṽ",Ẃ:"Ẃ",Ẁ:"Ẁ",Ẅ:"Ẅ",Ŵ:"Ŵ",Ẇ:"Ẇ",Ẍ:"Ẍ",Ẋ:"Ẋ",Ý:"Ý",Ỳ:"Ỳ",Ÿ:"Ÿ",Ỹ:"Ỹ",Ȳ:"Ȳ",Ŷ:"Ŷ",Ẏ:"Ẏ",Ź:"Ź",Ž:"Ž",Ẑ:"Ẑ",Ż:"Ż",ά:"ά",ὰ:"ὰ",ᾱ:"ᾱ",ᾰ:"ᾰ",έ:"έ",ὲ:"ὲ",ή:"ή",ὴ:"ὴ",ί:"ί",ὶ:"ὶ",ϊ:"ϊ",ΐ:"ΐ",ῒ:"ῒ",ῑ:"ῑ",ῐ:"ῐ",ό:"ό",ὸ:"ὸ",ύ:"ύ",ὺ:"ὺ",ϋ:"ϋ",ΰ:"ΰ",ῢ:"ῢ",ῡ:"ῡ",ῠ:"ῠ",ώ:"ώ",ὼ:"ὼ",Ύ:"Ύ",Ὺ:"Ὺ",Ϋ:"Ϋ",Ῡ:"Ῡ",Ῠ:"Ῠ",Ώ:"Ώ",Ὼ:"Ὼ"};class aw{constructor(e,t){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new af(e,t,this.mode),this.settings=t,this.leftrightDepth=0}expect(e,t){if(void 0===t&&(t=!0),this.fetch().text!==e)throw new i("Expected '"+e+"', got '"+this.fetch().text+"'",this.fetch());t&&this.consume()}consume(){this.nextToken=null}fetch(){return null==this.nextToken&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(e){this.mode=e,this.gullet.switchMode(e)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{var e=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),e}finally{this.gullet.endGroups()}}subparse(e){var t=this.nextToken;this.consume(),this.gullet.pushToken(new n("}")),this.gullet.pushTokens(e);var r=this.parseExpression(!1);return this.expect("}"),this.nextToken=t,r}parseExpression(e,t){for(var r=[];;){"math"===this.mode&&this.consumeSpaces();var a=this.fetch();if(-1!==aw.endOfExpression.indexOf(a.text)||t&&a.text===t||e&&e8[a.text]&&e8[a.text].infix)break;var n=this.parseAtom(t);if(n){if("internal"===n.type)continue}else break;r.push(n)}return"text"===this.mode&&this.formLigatures(r),this.handleInfixNodes(r)}handleInfixNodes(e){for(var t=-1,r=0;r<e.length;r++)if("infix"===e[r].type){if(-1!==t)throw new i("only one infix operator per group",e[r].token);t=r,a=e[r].replaceWith}if(-1===t||!a)return e;var a,n,s,o,l=e.slice(0,t),h=e.slice(t+1);return n=1===l.length&&"ordgroup"===l[0].type?l[0]:{type:"ordgroup",mode:this.mode,body:l},s=1===h.length&&"ordgroup"===h[0].type?h[0]:{type:"ordgroup",mode:this.mode,body:h},["\\\\abovefrac"===a?this.callFunction(a,[n,e[t],s],[]):this.callFunction(a,[n,s],[])]}handleSupSubscript(e){var t,r=this.fetch(),a=r.text;this.consume(),this.consumeSpaces();do t=this.parseGroup(e);while((null==t?void 0:t.type)==="internal");if(!t)throw new i("Expected group after '"+a+"'",r);return t}formatUnsupportedCmd(e){for(var t=[],r=0;r<e.length;r++)t.push({type:"textord",mode:"text",text:e[r]});var a={type:"text",mode:this.mode,body:t};return{type:"color",mode:this.mode,color:this.settings.errorColor,body:[a]}}parseAtom(e){var t,r,a=this.parseGroup("atom",e);if((null==a?void 0:a.type)==="internal"||"text"===this.mode)return a;for(;;){this.consumeSpaces();var s=this.fetch();if("\\limits"===s.text||"\\nolimits"===s.text){if(a&&"op"===a.type)a.limits="\\limits"===s.text,a.alwaysHandleSupSub=!0;else if(a&&"operatorname"===a.type)a.alwaysHandleSupSub&&(a.limits="\\limits"===s.text);else throw new i("Limit controls must follow a math operator",s);this.consume()}else if("^"===s.text){if(t)throw new i("Double superscript",s);t=this.handleSupSubscript("superscript")}else if("_"===s.text){if(r)throw new i("Double subscript",s);r=this.handleSupSubscript("subscript")}else if("'"===s.text){if(t)throw new i("Double superscript",s);var o={type:"textord",mode:this.mode,text:"\\prime"},l=[o];for(this.consume();"'"===this.fetch().text;)l.push(o),this.consume();"^"===this.fetch().text&&l.push(this.handleSupSubscript("superscript")),t={type:"ordgroup",mode:this.mode,body:l}}else if(ab[s.text]){var h=av.test(s.text),m=[];for(m.push(new n(ab[s.text])),this.consume();;){var c=this.fetch().text;if(!ab[c]||av.test(c)!==h)break;m.unshift(new n(ab[c])),this.consume()}var p=this.subparse(m);h?r={type:"ordgroup",mode:"math",body:p}:t={type:"ordgroup",mode:"math",body:p}}else break}return t||r?{type:"supsub",mode:this.mode,base:a,sup:t,sub:r}:a}parseFunction(e,t){var r=this.fetch(),a=r.text,n=e8[a];if(!n)return null;if(this.consume(),t&&"atom"!==t&&!n.allowedInArgument)throw new i("Got function '"+a+"' with no arguments"+(t?" as "+t:""),r);if("text"!==this.mode||n.allowedInText){if("math"===this.mode&&!1===n.allowedInMath)throw new i("Can't use function '"+a+"' in math mode",r)}else throw new i("Can't use function '"+a+"' in text mode",r);var{args:s,optArgs:o}=this.parseArguments(a,n);return this.callFunction(a,s,o,r,e)}callFunction(e,t,r,a,n){var s=e8[e];if(s&&s.handler)return s.handler({funcName:e,parser:this,token:a,breakOnTokenText:n},t,r);throw new i("No function handler for "+e)}parseArguments(e,t){var r=t.numArgs+t.numOptionalArgs;if(0===r)return{args:[],optArgs:[]};for(var a=[],n=[],s=0;s<r;s++){var o=t.argTypes&&t.argTypes[s],l=s<t.numOptionalArgs;(t.primitive&&null==o||"sqrt"===t.type&&1===s&&null==n[0])&&(o="primitive");var h=this.parseGroupOfType("argument to '"+e+"'",o,l);if(l)n.push(h);else if(null!=h)a.push(h);else throw new i("Null argument, please report this as a bug")}return{args:a,optArgs:n}}parseGroupOfType(e,t,r){switch(t){case"color":return this.parseColorGroup(r);case"size":return this.parseSizeGroup(r);case"url":return this.parseUrlGroup(r);case"math":case"text":return this.parseArgumentGroup(r,t);case"hbox":var a=this.parseArgumentGroup(r,"text");return null!=a?{type:"styling",mode:a.mode,body:[a],style:"text"}:null;case"raw":var n=this.parseStringGroup("raw",r);return null!=n?{type:"raw",mode:"text",string:n.text}:null;case"primitive":if(r)throw new i("A primitive argument cannot be optional");var s=this.parseGroup(e);if(null==s)throw new i("Expected group as "+e,this.fetch());return s;case"original":case null:case void 0:return this.parseArgumentGroup(r);default:throw new i("Unknown group type as "+e,this.fetch())}}consumeSpaces(){for(;" "===this.fetch().text;)this.consume()}parseStringGroup(e,t){var r,a=this.gullet.scanArgument(t);if(null==a)return null;for(var n="";"EOF"!==(r=this.fetch()).text;)n+=r.text,this.consume();return this.consume(),a.text=n,a}parseRegexGroup(e,t){for(var r,a=this.fetch(),n=a,s="";"EOF"!==(r=this.fetch()).text&&e.test(s+r.text);)s+=(n=r).text,this.consume();if(""===s)throw new i("Invalid "+t+": '"+a.text+"'",a);return a.range(n,s)}parseColorGroup(e){var t=this.parseStringGroup("color",e);if(null==t)return null;var r=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(t.text);if(!r)throw new i("Invalid color: '"+t.text+"'",t);var a=r[0];return/^[0-9a-f]{6}$/i.test(a)&&(a="#"+a),{type:"color-token",mode:this.mode,color:a}}parseSizeGroup(e){var t,r=!1;if(this.gullet.consumeSpaces(),!(t=e||"{"===this.gullet.future().text?this.parseStringGroup("size",e):this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size")))return null;e||0!==t.text.length||(t.text="0pt",r=!0);var a=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t.text);if(!a)throw new i("Invalid size: '"+t.text+"'",t);var n={number:+(a[1]+a[2]),unit:a[3]};if(!Y(n))throw new i("Invalid unit: '"+n.unit+"'",t);return{type:"size",mode:this.mode,value:n,isBlank:r}}parseUrlGroup(e){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);var t=this.parseStringGroup("url",e);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),null==t)return null;var r=t.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:r}}parseArgumentGroup(e,t){var r=this.gullet.scanArgument(e);if(null==r)return null;var a=this.mode;t&&this.switchMode(t),this.gullet.beginGroup();var n=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();var i={type:"ordgroup",mode:this.mode,loc:r.loc,body:n};return t&&this.switchMode(a),i}parseGroup(e,t){var r,n=this.fetch(),s=n.text;if("{"===s||"\\begingroup"===s){this.consume();var o="{"===s?"}":"\\endgroup";this.gullet.beginGroup();var l=this.parseExpression(!1,o),h=this.fetch();this.expect(o),this.gullet.endGroup(),r={type:"ordgroup",mode:this.mode,loc:a.range(n,h),body:l,semisimple:"\\begingroup"===s||void 0}}else if(null==(r=this.parseFunction(t,e)||this.parseSymbol())&&"\\"===s[0]&&!ag.hasOwnProperty(s)){if(this.settings.throwOnError)throw new i("Undefined control sequence: "+s,n);r=this.formatUnsupportedCmd(s),this.consume()}return r}formLigatures(e){for(var t=e.length-1,r=0;r<t;++r){var n=e[r],i=n.text;"-"===i&&"-"===e[r+1].text&&(r+1<t&&"-"===e[r+2].text?(e.splice(r,3,{type:"textord",mode:"text",loc:a.range(n,e[r+2]),text:"---"}),t-=2):(e.splice(r,2,{type:"textord",mode:"text",loc:a.range(n,e[r+1]),text:"--"}),t-=1)),("'"===i||"`"===i)&&e[r+1].text===i&&(e.splice(r,2,{type:"textord",mode:"text",loc:a.range(n,e[r+1]),text:i+i}),t-=1)}}parseSymbol(){var e=this.fetch(),t=e.text;if(/^\\verb[^a-zA-Z]/.test(t)){this.consume();var r=t.slice(5),n="*"===r.charAt(0);if(n&&(r=r.slice(1)),r.length<2||r.charAt(0)!==r.slice(-1))throw new i("\\verb assertion failed --\n                    please report what input caused this bug");return{type:"verb",mode:"text",body:r=r.slice(1,-1),star:n}}ax.hasOwnProperty(t[0])&&!eh[this.mode][t[0]]&&(this.settings.strict&&"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+t[0]+'" used in math mode',e),t=ax[t[0]]+t.slice(1));var s=ai.exec(t);if(s&&("i"===(t=t.substring(0,s.index))?t="ı":"j"===t&&(t="ȷ")),eh[this.mode][t]){this.settings.strict&&"math"===this.mode&&eD.indexOf(t)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+t[0]+'" used in math mode',e);var o,l,h=eh[this.mode][t].group,m=a.range(e);o=eo.hasOwnProperty(h)?{type:"atom",mode:this.mode,family:h,loc:m,text:t}:{type:h,mode:this.mode,loc:m,text:t}}else{if(!(t.charCodeAt(0)>=128))return null;this.settings.strict&&(T(t.charCodeAt(0))?"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+t[0]+'" used in math mode',e):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+t[0]+'" ('+t.charCodeAt(0)+")",e)),o={type:"textord",mode:"text",loc:a.range(e),text:t}}if(this.consume(),s)for(var c=0;c<s[0].length;c++){var p=s[0][c];if(!ay[p])throw new i("Unknown accent ' "+p+"'",e);var u=ay[p][this.mode]||ay[p].text;if(!u)throw new i("Accent "+p+" unsupported in "+this.mode+" mode",e);o={type:"accent",mode:this.mode,loc:a.range(e),label:u,isStretchy:!1,isShifty:!0,base:o}}return o}}aw.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var ak=function(e,t){if(!("string"==typeof e||e instanceof String))throw TypeError("KaTeX can only parse string typed expression");var r=new aw(e,t);delete r.gullet.macros.current["\\df@tag"];var a=r.parse();if(delete r.gullet.macros.current["\\current@color"],delete r.gullet.macros.current["\\color"],r.gullet.macros.get("\\df@tag")){if(!t.displayMode)throw new i("\\tag works only in display equations");a=[{type:"tag",mode:"text",body:a,tag:r.subparse([new n("\\df@tag")])}]}return a},aS=function(e,t,r){t.textContent="";var a=az(e,r).toNode();t.appendChild(a)};"undefined"!=typeof document&&"CSS1Compat"!==document.compatMode&&("undefined"!=typeof console&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),aS=function(){throw new i("KaTeX doesn't work in quirks mode.")});var aM=function(e,t,r){if(r.throwOnError||!(e instanceof i))throw e;var a=e1.makeSpan(["katex-error"],[new er(t)]);return a.setAttribute("title",e.toString()),a.setAttribute("style","color:"+r.errorColor),a},az=function(e,t){var r=new g(t);try{var a=ak(e,r);return tH(a,e,r)}catch(t){return aM(t,e,r)}},aA={version:"0.16.22",render:aS,renderToString:function(e,t){return az(e,t).toMarkup()},ParseError:i,SETTINGS_SCHEMA:d,__parse:function(e,t){return ak(e,new g(t))},__renderToDomTree:az,__renderToHTMLTree:function(e,t){var r=new g(t);try{var a=ak(e,r);return tR(a,e,r)}catch(t){return aM(t,e,r)}},__setFontMetrics:function(e,t){H[e]=t},__defineSymbol:em,__defineFunction:te,__defineMacro:function(e,t){rA[e]=t},__domTree:{Span:J,Anchor:Q,SymbolNode:er,SvgNode:ea,PathNode:en,LineNode:ei}}}}]);