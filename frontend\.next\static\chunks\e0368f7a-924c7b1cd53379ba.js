"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5661],{15685:(t,e,a)=>{a.d(e,{DA:()=>b,IU:()=>I,KX:()=>D,U:()=>P,U7:()=>eg,U_:()=>ef,Zk:()=>d,aP:()=>eo,gh:()=>ey,lC:()=>g,on:()=>ec});var l=a(53781),r=a(55704),i=a(88574),n=a(11901),s=a(47802),h=a(30685),o=a(58363),d=(0,s.K2)(async(t,e,a)=>{let l;let r=e.useHtmlLabels||(0,s._3)(s.D7()?.htmlLabels),o=t.insert("g").attr("class",a||"node default").attr("id",e.domId||e.id),d=o.insert("g").attr("class","label").attr("style",(0,n.KL)(e.labelStyle));l=void 0===e.label?"":"string"==typeof e.label?e.label:e.label[0];let c=await (0,i.GZ)(d,(0,s.jZ)((0,n.Sm)(l),(0,s.D7)()),{useHtmlLabels:r,width:e.width||s.D7().flowchart?.wrappingWidth,cssClasses:"markdown-node-label",style:e.labelStyle,addSvgBackground:!!e.icon||!!e.img}),g=c.getBBox(),y=(e?.padding??0)/2;if(r){let t=c.children[0],e=(0,h.Ltv)(c),a=t.getElementsByTagName("img");if(a){let t=""===l.replace(/<img[^>]*>/g,"").trim();await Promise.all([...a].map(e=>new Promise(a=>{function l(){if(e.style.display="flex",e.style.flexDirection="column",t){let t=(0,s.D7)().fontSize?(0,s.D7)().fontSize:window.getComputedStyle(document.body).fontSize,[a=s.UI.fontSize]=(0,n.I5)(t),l=5*a+"px";e.style.minWidth=l,e.style.maxWidth=l}else e.style.width="100%";a(e)}(0,s.K2)(l,"setupImage"),setTimeout(()=>{e.complete&&l()}),e.addEventListener("error",l),e.addEventListener("load",l)})))}g=t.getBoundingClientRect(),e.attr("width",g.width),e.attr("height",g.height)}return r?d.attr("transform","translate("+-g.width/2+", "+-g.height/2+")"):d.attr("transform","translate(0, "+-g.height/2+")"),e.centerLabel&&d.attr("transform","translate("+-g.width/2+", "+-g.height/2+")"),d.insert("rect",":first-child"),{shapeSvg:o,bbox:g,halfPadding:y,label:d}},"labelHelper"),c=(0,s.K2)(async(t,e,a)=>{let l=a.useHtmlLabels||(0,s._3)(s.D7()?.flowchart?.htmlLabels),r=t.insert("g").attr("class","label").attr("style",a.labelStyle||""),o=await (0,i.GZ)(r,(0,s.jZ)((0,n.Sm)(e),(0,s.D7)()),{useHtmlLabels:l,width:a.width||s.D7()?.flowchart?.wrappingWidth,style:a.labelStyle,addSvgBackground:!!a.icon||!!a.img}),d=o.getBBox(),c=a.padding/2;if((0,s._3)(s.D7()?.flowchart?.htmlLabels)){let t=o.children[0],e=(0,h.Ltv)(o);d=t.getBoundingClientRect(),e.attr("width",d.width),e.attr("height",d.height)}return l?r.attr("transform","translate("+-d.width/2+", "+-d.height/2+")"):r.attr("transform","translate(0, "+-d.height/2+")"),a.centerLabel&&r.attr("transform","translate("+-d.width/2+", "+-d.height/2+")"),r.insert("rect",":first-child"),{shapeSvg:t,bbox:d,halfPadding:c,label:r}},"insertLabel"),g=(0,s.K2)((t,e)=>{let a=e.node().getBBox();t.width=a.width,t.height=a.height},"updateNodeBounds"),y=(0,s.K2)((t,e)=>("handDrawn"===t.look?"rough-node":"node")+" "+t.cssClasses+" "+(e||""),"getNodeClasses");function f(t){let e=t.map((t,e)=>`${0===e?"M":"L"}${t.x},${t.y}`);return e.push("Z"),e.join(" ")}function p(t,e,a,l,r,i){let n=[],s=a-t,h=2*Math.PI/(s/i),o=e+(l-e)/2;for(let e=0;e<=50;e++){let a=t+e/50*s,l=o+r*Math.sin(h*(a-t));n.push({x:a,y:l})}return n}function m(t,e,a,l,r,i){let n=[],s=r*Math.PI/180,h=(i*Math.PI/180-s)/(l-1);for(let r=0;r<l;r++){let l=s+r*h,i=t+a*Math.cos(l),o=e+a*Math.sin(l);n.push({x:-i,y:-o})}return n}(0,s.K2)(f,"createPathFromPoints"),(0,s.K2)(p,"generateFullSineWavePoints"),(0,s.K2)(m,"generateCirclePoints");var u=(0,s.K2)((t,e)=>{var a,l,r=t.x,i=t.y,n=e.x-r,s=e.y-i,h=t.width/2,o=t.height/2;return Math.abs(s)*h>Math.abs(n)*o?(s<0&&(o=-o),a=0===s?0:o*n/s,l=o):(n<0&&(h=-h),a=h,l=0===n?0:h*s/n),{x:r+a,y:i+l}},"intersectRect");function x(t,e){e&&t.attr("style",e)}async function w(t){let e=(0,h.Ltv)(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),a=e.append("xhtml:div"),l=t.label;t.label&&(0,s.Wi)(t.label)&&(l=await (0,s.VJ)(t.label.replace(s.Y2.lineBreakRegex,"\n"),(0,s.D7)()));let r=t.isNode?"nodeLabel":"edgeLabel";return a.html('<span class="'+r+'" '+(t.labelStyle?'style="'+t.labelStyle+'"':"")+">"+l+"</span>"),x(a,t.labelStyle),a.style("display","inline-block"),a.style("padding-right","1px"),a.style("white-space","nowrap"),a.attr("xmlns","http://www.w3.org/1999/xhtml"),e.node()}(0,s.K2)(x,"applyStyle"),(0,s.K2)(w,"addHtmlLabel");var b=(0,s.K2)(async(t,e,a,l)=>{let r=t||"";if("object"==typeof r&&(r=r[0]),(0,s._3)((0,s.D7)().flowchart.htmlLabels)){r=r.replace(/\\n|\n/g,"<br />"),s.Rm.info("vertexText"+r);let t={isNode:l,label:(0,n.Sm)(r).replace(/fa[blrs]?:fa-[\w-]+/g,t=>`<i class='${t.replace(":"," ")}'></i>`),labelStyle:e?e.replace("fill:","color:"):e};return await w(t)}{let t=document.createElementNS("http://www.w3.org/2000/svg","text");t.setAttribute("style",e.replace("color:","fill:"));let l=[];for(let e of"string"==typeof r?r.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(r)?r:[]){let l=document.createElementNS("http://www.w3.org/2000/svg","tspan");l.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),l.setAttribute("dy","1em"),l.setAttribute("x","0"),a?l.setAttribute("class","title-row"):l.setAttribute("class","row"),l.textContent=e.trim(),t.appendChild(l)}return t}},"createLabel"),$=(0,s.K2)((t,e,a,l,r)=>["M",t+r,e,"H",t+a-r,"A",r,r,0,0,1,t+a,e+r,"V",e+l-r,"A",r,r,0,0,1,t+a-r,e+l,"H",t+r,"A",r,r,0,0,1,t,e+l-r,"V",e+r,"A",r,r,0,0,1,t+r,e,"Z"].join(" "),"createRoundedRectPathD"),k=(0,s.K2)(t=>{let{handDrawnSeed:e}=(0,s.D7)();return{fill:t,hachureAngle:120,hachureGap:4,fillWeight:2,roughness:.7,stroke:t,seed:e}},"solidStateFill"),S=(0,s.K2)(t=>{let e=M([...t.cssCompiledStyles||[],...t.cssStyles||[]]);return{stylesMap:e,stylesArray:[...e]}},"compileStyles"),M=(0,s.K2)(t=>{let e=new Map;return t.forEach(t=>{let[a,l]=t.split(":");e.set(a.trim(),l?.trim())}),e},"styles2Map"),D=(0,s.K2)(t=>"color"===t||"font-size"===t||"font-family"===t||"font-weight"===t||"font-style"===t||"text-decoration"===t||"text-align"===t||"text-transform"===t||"line-height"===t||"letter-spacing"===t||"word-spacing"===t||"text-shadow"===t||"text-overflow"===t||"white-space"===t||"word-wrap"===t||"word-break"===t||"overflow-wrap"===t||"hyphens"===t,"isLabelStyle"),A=(0,s.K2)(t=>{let{stylesArray:e}=S(t),a=[],l=[],r=[],i=[];return e.forEach(t=>{let e=t[0];D(e)?a.push(t.join(":")+" !important"):(l.push(t.join(":")+" !important"),e.includes("stroke")&&r.push(t.join(":")+" !important"),"fill"===e&&i.push(t.join(":")+" !important"))}),{labelStyles:a.join(";"),nodeStyles:l.join(";"),stylesArray:e,borderStyles:r,backgroundStyles:i}},"styles2String"),v=(0,s.K2)((t,e)=>{let{themeVariables:a,handDrawnSeed:l}=(0,s.D7)(),{nodeBorder:r,mainBkg:i}=a,{stylesMap:n}=S(t);return Object.assign({roughness:.7,fill:n.get("fill")||i,fillStyle:"hachure",fillWeight:4,hachureGap:5.2,stroke:n.get("stroke")||r,seed:l,strokeWidth:n.get("stroke-width")?.replace("px","")||1.3,fillLineDash:[0,0]},e)},"userNodeOverrides"),K=(0,s.K2)(async(t,e)=>{let a;s.Rm.info("Creating subgraph rect for ",e.id,e);let r=(0,s.D7)(),{themeVariables:n,handDrawnSeed:d}=r,{clusterBkg:c,clusterBorder:g}=n,{labelStyles:y,nodeStyles:f,borderStyles:p,backgroundStyles:m}=A(e),x=t.insert("g").attr("class","cluster "+e.cssClasses).attr("id",e.id).attr("data-look",e.look),w=(0,s._3)(r.flowchart.htmlLabels),b=x.insert("g").attr("class","cluster-label "),k=await (0,i.GZ)(b,e.label,{style:e.labelStyle,useHtmlLabels:w,isNode:!0}),S=k.getBBox();if((0,s._3)(r.flowchart.htmlLabels)){let t=k.children[0],e=(0,h.Ltv)(k);S=t.getBoundingClientRect(),e.attr("width",S.width),e.attr("height",S.height)}let M=e.width<=S.width+e.padding?S.width+e.padding:e.width;e.width<=S.width+e.padding?e.diff=(M-e.width)/2-e.padding:e.diff=-e.padding;let D=e.height,K=e.x-M/2,C=e.y-D/2;if(s.Rm.trace("Data ",e,JSON.stringify(e)),"handDrawn"===e.look){let t=o.A.svg(x),l=v(e,{roughness:.7,fill:c,stroke:g,fillWeight:3,seed:d}),r=t.path($(K,C,M,D,0),l);(a=x.insert(()=>(s.Rm.debug("Rough node insert CXC",r),r),":first-child")).select("path:nth-child(2)").attr("style",p.join(";")),a.select("path").attr("style",m.join(";").replace("fill","stroke"))}else(a=x.insert("rect",":first-child")).attr("style",f).attr("rx",e.rx).attr("ry",e.ry).attr("x",K).attr("y",C).attr("width",M).attr("height",D);let{subGraphTitleTopMargin:B}=(0,l.O)(r);if(b.attr("transform",`translate(${e.x-S.width/2}, ${e.y-e.height/2+B})`),y){let t=b.select("span");t&&t.attr("style",y)}let N=a.node().getBBox();return e.offsetX=0,e.width=N.width,e.height=N.height,e.offsetY=S.height-e.padding/2,e.intersect=function(t){return u(e,t)},{cluster:x,labelBBox:S}},"rect"),C=(0,s.K2)((t,e)=>{let a=t.insert("g").attr("class","note-cluster").attr("id",e.id),l=a.insert("rect",":first-child"),r=0*e.padding,i=r/2;l.attr("rx",e.rx).attr("ry",e.ry).attr("x",e.x-e.width/2-i).attr("y",e.y-e.height/2-i).attr("width",e.width+r).attr("height",e.height+r).attr("fill","none");let n=l.node().getBBox();return e.width=n.width,e.height=n.height,e.intersect=function(t){return u(e,t)},{cluster:a,labelBBox:{width:0,height:0}}},"noteGroup"),B=(0,s.K2)(async(t,e)=>{let a;let l=(0,s.D7)(),{themeVariables:r,handDrawnSeed:i}=l,{altBackground:n,compositeBackground:d,compositeTitleBackground:c,nodeBorder:g}=r,y=t.insert("g").attr("class",e.cssClasses).attr("id",e.id).attr("data-id",e.id).attr("data-look",e.look),f=y.insert("g",":first-child"),p=y.insert("g").attr("class","cluster-label"),m=y.append("rect"),x=p.node().appendChild(await b(e.label,e.labelStyle,void 0,!0)),w=x.getBBox();if((0,s._3)(l.flowchart.htmlLabels)){let t=x.children[0],e=(0,h.Ltv)(x);w=t.getBoundingClientRect(),e.attr("width",w.width),e.attr("height",w.height)}let k=0*e.padding,S=(e.width<=w.width+e.padding?w.width+e.padding:e.width)+k;e.width<=w.width+e.padding?e.diff=(S-e.width)/2-e.padding:e.diff=-e.padding;let M=e.height+k,D=e.height+k-w.height-6,A=e.x-S/2,v=e.y-M/2;e.width=S;let K=e.y-e.height/2-k/2+w.height+2;if("handDrawn"===e.look){let t=e.cssClasses.includes("statediagram-cluster-alt"),l=o.A.svg(y),r=e.rx||e.ry?l.path($(A,v,S,M,10),{roughness:.7,fill:c,fillStyle:"solid",stroke:g,seed:i}):l.rectangle(A,v,S,M,{seed:i});a=y.insert(()=>r,":first-child");let s=l.rectangle(A,K,S,D,{fill:t?n:d,fillStyle:t?"hachure":"solid",stroke:g,seed:i});a=y.insert(()=>r,":first-child"),m=y.insert(()=>s)}else(a=f.insert("rect",":first-child")).attr("class","outer").attr("x",A).attr("y",v).attr("width",S).attr("height",M).attr("data-look",e.look),m.attr("class","inner").attr("x",A).attr("y",K).attr("width",S).attr("height",D);return p.attr("transform",`translate(${e.x-w.width/2}, ${v+1-3*!(0,s._3)(l.flowchart.htmlLabels)})`),e.height=a.node().getBBox().height,e.offsetX=0,e.offsetY=w.height-e.padding/2,e.labelBBox=w,e.intersect=function(t){return u(e,t)},{cluster:y,labelBBox:w}},"roundedWithTitle"),N=(0,s.K2)(async(t,e)=>{let a;s.Rm.info("Creating subgraph rect for ",e.id,e);let r=(0,s.D7)(),{themeVariables:n,handDrawnSeed:d}=r,{clusterBkg:c,clusterBorder:g}=n,{labelStyles:y,nodeStyles:f,borderStyles:p,backgroundStyles:m}=A(e),x=t.insert("g").attr("class","cluster "+e.cssClasses).attr("id",e.id).attr("data-look",e.look),w=(0,s._3)(r.flowchart.htmlLabels),b=x.insert("g").attr("class","cluster-label "),k=await (0,i.GZ)(b,e.label,{style:e.labelStyle,useHtmlLabels:w,isNode:!0,width:e.width}),S=k.getBBox();if((0,s._3)(r.flowchart.htmlLabels)){let t=k.children[0],e=(0,h.Ltv)(k);S=t.getBoundingClientRect(),e.attr("width",S.width),e.attr("height",S.height)}let M=e.width<=S.width+e.padding?S.width+e.padding:e.width;e.width<=S.width+e.padding?e.diff=(M-e.width)/2-e.padding:e.diff=-e.padding;let D=e.height,K=e.x-M/2,C=e.y-D/2;if(s.Rm.trace("Data ",e,JSON.stringify(e)),"handDrawn"===e.look){let t=o.A.svg(x),l=v(e,{roughness:.7,fill:c,stroke:g,fillWeight:4,seed:d}),r=t.path($(K,C,M,D,e.rx),l);(a=x.insert(()=>(s.Rm.debug("Rough node insert CXC",r),r),":first-child")).select("path:nth-child(2)").attr("style",p.join(";")),a.select("path").attr("style",m.join(";").replace("fill","stroke"))}else(a=x.insert("rect",":first-child")).attr("style",f).attr("rx",e.rx).attr("ry",e.ry).attr("x",K).attr("y",C).attr("width",M).attr("height",D);let{subGraphTitleTopMargin:B}=(0,l.O)(r);if(b.attr("transform",`translate(${e.x-S.width/2}, ${e.y-e.height/2+B})`),y){let t=b.select("span");t&&t.attr("style",y)}let N=a.node().getBBox();return e.offsetX=0,e.width=N.width,e.height=N.height,e.offsetY=S.height-e.padding/2,e.intersect=function(t){return u(e,t)},{cluster:x,labelBBox:S}},"kanbanSection"),L={rect:K,squareRect:K,roundedWithTitle:B,noteGroup:C,divider:(0,s.K2)((t,e)=>{let a;let{themeVariables:l,handDrawnSeed:r}=(0,s.D7)(),{nodeBorder:i}=l,n=t.insert("g").attr("class",e.cssClasses).attr("id",e.id).attr("data-look",e.look),h=n.insert("g",":first-child"),d=0*e.padding,c=e.width+d;e.diff=-e.padding;let g=e.height+d,y=e.x-c/2,f=e.y-g/2;if(e.width=c,"handDrawn"===e.look){let t=o.A.svg(n).rectangle(y,f,c,g,{fill:"lightgrey",roughness:.5,strokeLineDash:[5],stroke:i,seed:r});a=n.insert(()=>t,":first-child")}else(a=h.insert("rect",":first-child")).attr("class","divider").attr("x",y).attr("y",f).attr("width",c).attr("height",g).attr("data-look",e.look);return e.height=a.node().getBBox().height,e.offsetX=0,e.offsetY=0,e.intersect=function(t){return u(e,t)},{cluster:n,labelBBox:{}}},"divider"),kanbanSection:N},R=new Map,P=(0,s.K2)(async(t,e)=>{let a=e.shape||"rect",l=await L[a](t,e);return R.set(e.id,l),l},"insertCluster"),I=(0,s.K2)(()=>{R=new Map},"clear");function T(t,e,a,l){var r=t.x,i=t.y,n=r-l.x,s=i-l.y,h=Math.sqrt(e*e*s*s+a*a*n*n),o=Math.abs(e*a*n/h);l.x<r&&(o=-o);var d=Math.abs(e*a*s/h);return l.y<i&&(d=-d),{x:r+o,y:i+d}}function W(t,e,a){return T(t,e,e,a)}function _(t,e,a,l){var r,i,n,s,h,o,d,c,g,y,f,p,m;if(r=e.y-t.y,n=t.x-e.x,h=e.x*t.y-t.x*e.y,g=r*a.x+n*a.y+h,y=r*l.x+n*l.y+h,!(0!==g&&0!==y&&E(g,y))){if(i=l.y-a.y,s=a.x-l.x,o=l.x*a.y-a.x*l.y,d=i*t.x+s*t.y+o,c=i*e.x+s*e.y+o,!(0!==d&&0!==c&&E(d,c))&&0!=(f=r*s-i*n))return p=Math.abs(f/2),{x:(m=n*o-s*h)<0?(m-p)/f:(m+p)/f,y:(m=i*h-r*o)<0?(m-p)/f:(m+p)/f}}}function E(t,e){return t*e>0}function j(t,e,a){let l=t.x,r=t.y,i=[],n=Number.POSITIVE_INFINITY,s=Number.POSITIVE_INFINITY;"function"==typeof e.forEach?e.forEach(function(t){n=Math.min(n,t.x),s=Math.min(s,t.y)}):(n=Math.min(n,e.x),s=Math.min(s,e.y));let h=l-t.width/2-n,o=r-t.height/2-s;for(let l=0;l<e.length;l++){let r=e[l],n=e[l<e.length-1?l+1:0],s=_(t,a,{x:h+r.x,y:o+r.y},{x:h+n.x,y:o+n.y});s&&i.push(s)}return i.length?(i.length>1&&i.sort(function(t,e){let l=t.x-a.x,r=t.y-a.y,i=Math.sqrt(l*l+r*r),n=e.x-a.x,s=e.y-a.y,h=Math.sqrt(n*n+s*s);return i<h?-1:+(i!==h)}),i[0]):t}(0,s.K2)(function(t,e){return t.intersect(e)},"intersectNode"),(0,s.K2)(T,"intersectEllipse"),(0,s.K2)(W,"intersectCircle"),(0,s.K2)(_,"intersectLine"),(0,s.K2)(E,"sameSign"),(0,s.K2)(j,"intersectPolygon");var H={circle:W,polygon:j,rect:u};function z(t,e){let{labelStyles:a}=A(e);e.labelStyle=a;let l=y(e),r=l;l||(r="anchor");let i=t.insert("g").attr("class",r).attr("id",e.domId||e.id),{cssStyles:h}=e,d=o.A.svg(i),c=v(e,{fill:"black",stroke:"none",fillStyle:"solid"});"handDrawn"!==e.look&&(c.roughness=0);let f=d.circle(0,0,2,c),p=i.insert(()=>f,":first-child");return p.attr("class","anchor").attr("style",(0,n.KL)(h)),g(e,p),e.intersect=function(t){return s.Rm.info("Circle intersect",e,1,t),H.circle(e,1,t)},i}function q(t,e,a,l,r,i,n){let s=Math.atan2(l-e,a-t),h=Math.sqrt(((a-t)/2/r)**2+((l-e)/2/i)**2);if(h>1)throw Error("The given radii are too small to create an arc between the points.");let o=Math.sqrt(1-h**2),d=(t+a)/2+o*i*Math.sin(s)*(n?-1:1),c=(e+l)/2-o*r*Math.cos(s)*(n?-1:1),g=Math.atan2((e-c)/i,(t-d)/r),y=Math.atan2((l-c)/i,(a-d)/r)-g;n&&y<0&&(y+=2*Math.PI),!n&&y>0&&(y-=2*Math.PI);let f=[];for(let t=0;t<20;t++){let e=g+t/19*y,a=d+r*Math.cos(e),l=c+i*Math.sin(e);f.push({x:a,y:l})}return f}async function O(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),n=i.width+e.padding+20,s=i.height+e.padding,h=s/2,c=h/(2.5+s/50),{cssStyles:p}=e,m=[{x:n/2,y:-s/2},{x:-n/2,y:-s/2},...q(-n/2,-s/2,-n/2,s/2,c,h,!1),{x:n/2,y:s/2},...q(n/2,s/2,n/2,-s/2,c,h,!0)],u=o.A.svg(r),x=v(e,{});"handDrawn"!==e.look&&(x.roughness=0,x.fillStyle="solid");let w=f(m),b=u.path(w,x),$=r.insert(()=>b,":first-child");return $.attr("class","basic label-container"),p&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",p),l&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",l),$.attr("transform",`translate(${c/2}, 0)`),g(e,$),e.intersect=function(t){return H.polygon(e,m,t)},r}function F(t,e,a,l){return t.insert("polygon",":first-child").attr("points",l.map(function(t){return t.x+","+t.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-e/2+","+a/2+")")}async function Z(t,e){let a;let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),s=n.height+e.padding,h=n.width+e.padding+12,c=-s,p=[{x:12,y:c},{x:h,y:c},{x:h,y:0},{x:0,y:0},{x:0,y:c+12},{x:12,y:c}],{cssStyles:m}=e;if("handDrawn"===e.look){let t=o.A.svg(i),l=v(e,{}),r=f(p),n=t.path(r,l);a=i.insert(()=>n,":first-child").attr("transform",`translate(${-h/2}, ${s/2})`),m&&a.attr("style",m)}else a=F(i,h,s,p);return r&&a.attr("style",r),g(e,a),e.intersect=function(t){return H.polygon(e,p,t)},i}function X(t,e){let{nodeStyles:a}=A(e);e.label="";let l=t.insert("g").attr("class",y(e)).attr("id",e.domId??e.id),{cssStyles:r}=e,i=Math.max(28,e.width??0),n=[{x:0,y:i/2},{x:i/2,y:0},{x:0,y:-i/2},{x:-i/2,y:0}],s=o.A.svg(l),h=v(e,{});"handDrawn"!==e.look&&(h.roughness=0,h.fillStyle="solid");let d=f(n),c=s.path(d,h),g=l.insert(()=>c,":first-child");return r&&"handDrawn"!==e.look&&g.selectAll("path").attr("style",r),a&&"handDrawn"!==e.look&&g.selectAll("path").attr("style",a),e.width=28,e.height=28,e.intersect=function(t){return H.polygon(e,n,t)},l}async function Y(t,e){let a;let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let{shapeSvg:i,bbox:h,halfPadding:c}=await d(t,e,y(e)),f=h.width/2+c,{cssStyles:p}=e;if("handDrawn"===e.look){let t=o.A.svg(i),l=v(e,{}),r=t.circle(0,0,2*f,l);(a=i.insert(()=>r,":first-child")).attr("class","basic label-container").attr("style",(0,n.KL)(p))}else a=i.insert("circle",":first-child").attr("class","basic label-container").attr("style",r).attr("r",f).attr("cx",0).attr("cy",0);return g(e,a),e.intersect=function(t){return s.Rm.info("Circle intersect",e,f,t),H.circle(e,f,t)},i}function U(t){let e=Math.cos(Math.PI/4),a=Math.sin(Math.PI/4),l=2*t,r={x:l/2*e,y:l/2*a},i={x:-(l/2)*e,y:l/2*a},n={x:-(l/2)*e,y:-(l/2)*a},s={x:l/2*e,y:-(l/2)*a};return`M ${i.x},${i.y} L ${s.x},${s.y}
                   M ${r.x},${r.y} L ${n.x},${n.y}`}function G(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a,e.label="";let r=t.insert("g").attr("class",y(e)).attr("id",e.domId??e.id),i=Math.max(30,e?.width??0),{cssStyles:n}=e,h=o.A.svg(r),d=v(e,{});"handDrawn"!==e.look&&(d.roughness=0,d.fillStyle="solid");let c=h.circle(0,0,2*i,d),f=U(i),p=h.path(f,d),m=r.insert(()=>c,":first-child");return m.insert(()=>p),n&&"handDrawn"!==e.look&&m.selectAll("path").attr("style",n),l&&"handDrawn"!==e.look&&m.selectAll("path").attr("style",l),g(e,m),e.intersect=function(t){return s.Rm.info("crossedCircle intersect",e,{radius:i,point:t}),H.circle(e,i,t)},r}function V(t,e,a,l=100,r=0,i=180){let n=[],s=r*Math.PI/180,h=(i*Math.PI/180-s)/(l-1);for(let r=0;r<l;r++){let l=s+r*h,i=t+a*Math.cos(l),o=e+a*Math.sin(l);n.push({x:-i,y:-o})}return n}async function J(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=i.width+(e.padding??0),h=i.height+(e.padding??0),c=Math.max(5,.1*h),{cssStyles:p}=e,m=[...V(s/2,-h/2,c,30,-90,0),{x:-s/2-c,y:c},...V(s/2+2*c,-c,c,20,-180,-270),...V(s/2+2*c,c,c,20,-90,-180),{x:-s/2-c,y:-h/2},...V(s/2,h/2,c,20,0,90)],u=[{x:s/2,y:-h/2-c},{x:-s/2,y:-h/2-c},...V(s/2,-h/2,c,20,-90,0),{x:-s/2-c,y:-c},...V(s/2+.1*s,-c,c,20,-180,-270),...V(s/2+.1*s,c,c,20,-90,-180),{x:-s/2-c,y:h/2},...V(s/2,h/2,c,20,0,90),{x:-s/2,y:h/2+c},{x:s/2,y:h/2+c}],x=o.A.svg(r),w=v(e,{fill:"none"});"handDrawn"!==e.look&&(w.roughness=0,w.fillStyle="solid");let b=f(m).replace("Z",""),$=x.path(b,w),k=f(u),S=x.path(k,{...w}),M=r.insert("g",":first-child");return M.insert(()=>S,":first-child").attr("stroke-opacity",0),M.insert(()=>$,":first-child"),M.attr("class","text"),p&&"handDrawn"!==e.look&&M.selectAll("path").attr("style",p),l&&"handDrawn"!==e.look&&M.selectAll("path").attr("style",l),M.attr("transform",`translate(${c}, 0)`),n.attr("transform",`translate(${-s/2+c-(i.x-(i.left??0))},${-h/2+(e.padding??0)/2-(i.y-(i.top??0))})`),g(e,M),e.intersect=function(t){return H.polygon(e,u,t)},r}function Q(t,e,a,l=100,r=0,i=180){let n=[],s=r*Math.PI/180,h=(i*Math.PI/180-s)/(l-1);for(let r=0;r<l;r++){let l=s+r*h,i=t+a*Math.cos(l),o=e+a*Math.sin(l);n.push({x:i,y:o})}return n}async function tt(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=i.width+(e.padding??0),h=i.height+(e.padding??0),c=Math.max(5,.1*h),{cssStyles:p}=e,m=[...Q(s/2,-h/2,c,20,-90,0),{x:s/2+c,y:-c},...Q(s/2+2*c,-c,c,20,-180,-270),...Q(s/2+2*c,c,c,20,-90,-180),{x:s/2+c,y:h/2},...Q(s/2,h/2,c,20,0,90)],u=[{x:-s/2,y:-h/2-c},{x:s/2,y:-h/2-c},...Q(s/2,-h/2,c,20,-90,0),{x:s/2+c,y:-c},...Q(s/2+2*c,-c,c,20,-180,-270),...Q(s/2+2*c,c,c,20,-90,-180),{x:s/2+c,y:h/2},...Q(s/2,h/2,c,20,0,90),{x:s/2,y:h/2+c},{x:-s/2,y:h/2+c}],x=o.A.svg(r),w=v(e,{fill:"none"});"handDrawn"!==e.look&&(w.roughness=0,w.fillStyle="solid");let b=f(m).replace("Z",""),$=x.path(b,w),k=f(u),S=x.path(k,{...w}),M=r.insert("g",":first-child");return M.insert(()=>S,":first-child").attr("stroke-opacity",0),M.insert(()=>$,":first-child"),M.attr("class","text"),p&&"handDrawn"!==e.look&&M.selectAll("path").attr("style",p),l&&"handDrawn"!==e.look&&M.selectAll("path").attr("style",l),M.attr("transform",`translate(${-c}, 0)`),n.attr("transform",`translate(${-s/2+(e.padding??0)/2-(i.x-(i.left??0))},${-h/2+(e.padding??0)/2-(i.y-(i.top??0))})`),g(e,M),e.intersect=function(t){return H.polygon(e,u,t)},r}function te(t,e,a,l=100,r=0,i=180){let n=[],s=r*Math.PI/180,h=(i*Math.PI/180-s)/(l-1);for(let r=0;r<l;r++){let l=s+r*h,i=t+a*Math.cos(l),o=e+a*Math.sin(l);n.push({x:-i,y:-o})}return n}async function ta(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=i.width+(e.padding??0),h=i.height+(e.padding??0),c=Math.max(5,.1*h),{cssStyles:p}=e,m=[...te(s/2,-h/2,c,30,-90,0),{x:-s/2-c,y:c},...te(s/2+2*c,-c,c,20,-180,-270),...te(s/2+2*c,c,c,20,-90,-180),{x:-s/2-c,y:-h/2},...te(s/2,h/2,c,20,0,90)],u=[...te(-s/2+c+c/2,-h/2,c,20,-90,-180),{x:s/2-c/2,y:c},...te(-s/2-c/2,-c,c,20,0,90),...te(-s/2-c/2,c,c,20,-90,0),{x:s/2-c/2,y:-c},...te(-s/2+c+c/2,h/2,c,30,-180,-270)],x=[{x:s/2,y:-h/2-c},{x:-s/2,y:-h/2-c},...te(s/2,-h/2,c,20,-90,0),{x:-s/2-c,y:-c},...te(s/2+2*c,-c,c,20,-180,-270),...te(s/2+2*c,c,c,20,-90,-180),{x:-s/2-c,y:h/2},...te(s/2,h/2,c,20,0,90),{x:-s/2,y:h/2+c},{x:s/2-c-c/2,y:h/2+c},...te(-s/2+c+c/2,-h/2,c,20,-90,-180),{x:s/2-c/2,y:c},...te(-s/2-c/2,-c,c,20,0,90),...te(-s/2-c/2,c,c,20,-90,0),{x:s/2-c/2,y:-c},...te(-s/2+c+c/2,h/2,c,30,-180,-270)],w=o.A.svg(r),b=v(e,{fill:"none"});"handDrawn"!==e.look&&(b.roughness=0,b.fillStyle="solid");let $=f(m).replace("Z",""),k=w.path($,b),S=f(u).replace("Z",""),M=w.path(S,b),D=f(x),K=w.path(D,{...b}),C=r.insert("g",":first-child");return C.insert(()=>K,":first-child").attr("stroke-opacity",0),C.insert(()=>k,":first-child"),C.insert(()=>M,":first-child"),C.attr("class","text"),p&&"handDrawn"!==e.look&&C.selectAll("path").attr("style",p),l&&"handDrawn"!==e.look&&C.selectAll("path").attr("style",l),C.attr("transform",`translate(${c-c/4}, 0)`),n.attr("transform",`translate(${-s/2+(e.padding??0)/2-(i.x-(i.left??0))},${-h/2+(e.padding??0)/2-(i.y-(i.top??0))})`),g(e,C),e.intersect=function(t){return H.polygon(e,x,t)},r}async function tl(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),n=Math.max(80,(i.width+(e.padding??0)*2)*1.25,e?.width??0),s=Math.max(20,i.height+(e.padding??0)*2,e?.height??0),h=s/2,{cssStyles:c}=e,p=o.A.svg(r),u=v(e,{});"handDrawn"!==e.look&&(u.roughness=0,u.fillStyle="solid");let x=n-h,w=s/4,b=[{x:x,y:0},{x:w,y:0},{x:0,y:s/2},{x:w,y:s},{x:x,y:s},...m(-x,-s/2,h,50,270,90)],$=f(b),k=p.path($,u),S=r.insert(()=>k,":first-child");return S.attr("class","basic label-container"),c&&"handDrawn"!==e.look&&S.selectChildren("path").attr("style",c),l&&"handDrawn"!==e.look&&S.selectChildren("path").attr("style",l),S.attr("transform",`translate(${-n/2}, ${-s/2})`),g(e,S),e.intersect=function(t){return H.polygon(e,b,t)},r}(0,s.K2)(z,"anchor"),(0,s.K2)(q,"generateArcPoints"),(0,s.K2)(O,"bowTieRect"),(0,s.K2)(F,"insertPolygonShape"),(0,s.K2)(Z,"card"),(0,s.K2)(X,"choice"),(0,s.K2)(Y,"circle"),(0,s.K2)(U,"createLine"),(0,s.K2)(G,"crossedCircle"),(0,s.K2)(V,"generateCirclePoints"),(0,s.K2)(J,"curlyBraceLeft"),(0,s.K2)(Q,"generateCirclePoints"),(0,s.K2)(tt,"curlyBraceRight"),(0,s.K2)(te,"generateCirclePoints"),(0,s.K2)(ta,"curlyBraces"),(0,s.K2)(tl,"curvedTrapezoid");var tr=(0,s.K2)((t,e,a,l,r,i)=>`M${t},${e+i} a${r},${i} 0,0,0 ${a},0 a${r},${i} 0,0,0 ${-a},0 l0,${l} a${r},${i} 0,0,0 ${a},0 l0,${-l}`,"createCylinderPathD"),ti=(0,s.K2)((t,e,a,l,r,i)=>`M${t},${e+i} M${t+a},${e+i} a${r},${i} 0,0,0 ${-a},0 l0,${l} a${r},${i} 0,0,0 ${a},0 l0,${-l}`,"createOuterCylinderPathD"),tn=(0,s.K2)((t,e,a,l,r,i)=>`M${t-a/2},${-l/2} a${r},${i} 0,0,0 ${a},0`,"createInnerCylinderPathD");async function ts(t,e){let a;let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let{shapeSvg:i,bbox:s,label:h}=await d(t,e,y(e)),c=Math.max(s.width+e.padding,e.width??0),f=c/2,p=f/(2.5+c/50),m=Math.max(s.height+p+e.padding,e.height??0),{cssStyles:u}=e;if("handDrawn"===e.look){let t=o.A.svg(i),l=ti(0,0,c,m,f,p),r=tn(0,p,c,m,f,p),n=t.path(l,v(e,{})),s=t.path(r,v(e,{fill:"none"}));a=i.insert(()=>s,":first-child"),(a=i.insert(()=>n,":first-child")).attr("class","basic label-container"),u&&a.attr("style",u)}else{let t=tr(0,0,c,m,f,p);a=i.insert("path",":first-child").attr("d",t).attr("class","basic label-container").attr("style",(0,n.KL)(u)).attr("style",r)}return a.attr("label-offset-y",p),a.attr("transform",`translate(${-c/2}, ${-(m/2+p)})`),g(e,a),h.attr("transform",`translate(${-(s.width/2)-(s.x-(s.left??0))}, ${-(s.height/2)+(e.padding??0)/1.5-(s.y-(s.top??0))})`),e.intersect=function(t){let a=H.rect(e,t),l=a.x-(e.x??0);if(0!=f&&(Math.abs(l)<(e.width??0)/2||Math.abs(l)==(e.width??0)/2&&Math.abs(a.y-(e.y??0))>(e.height??0)/2-p)){let r=p*p*(1-l*l/(f*f));r>0&&(r=Math.sqrt(r)),r=p-r,t.y-(e.y??0)>0&&(r=-r),a.y+=r}return a},i}async function th(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=i.width+e.padding,h=i.height+e.padding,c=.2*h,f=-s/2,p=-h/2-c/2,{cssStyles:m}=e,u=o.A.svg(r),x=v(e,{});"handDrawn"!==e.look&&(x.roughness=0,x.fillStyle="solid");let w=u.polygon([{x:f,y:p+c},{x:-f,y:p+c},{x:-f,y:-p},{x:f,y:-p},{x:f,y:p},{x:-f,y:p},{x:-f,y:p+c}].map(t=>[t.x,t.y]),x),b=r.insert(()=>w,":first-child");return b.attr("class","basic label-container"),m&&"handDrawn"!==e.look&&b.selectAll("path").attr("style",m),l&&"handDrawn"!==e.look&&b.selectAll("path").attr("style",l),n.attr("transform",`translate(${f+(e.padding??0)/2-(i.x-(i.left??0))}, ${p+c+(e.padding??0)/2-(i.y-(i.top??0))})`),g(e,b),e.intersect=function(t){return H.rect(e,t)},r}async function to(t,e){let a;let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let{shapeSvg:i,bbox:h,halfPadding:c}=await d(t,e,y(e)),f=h.width/2+c+5,p=h.width/2+c,{cssStyles:m}=e;if("handDrawn"===e.look){let t=o.A.svg(i),l=v(e,{roughness:.2,strokeWidth:2.5}),r=v(e,{roughness:.2,strokeWidth:1.5}),s=t.circle(0,0,2*f,l),h=t.circle(0,0,2*p,r);(a=i.insert("g",":first-child")).attr("class",(0,n.KL)(e.cssClasses)).attr("style",(0,n.KL)(m)),a.node()?.appendChild(s),a.node()?.appendChild(h)}else{let t=(a=i.insert("g",":first-child")).insert("circle",":first-child"),e=a.insert("circle");a.attr("class","basic label-container").attr("style",r),t.attr("class","outer-circle").attr("style",r).attr("r",f).attr("cx",0).attr("cy",0),e.attr("class","inner-circle").attr("style",r).attr("r",p).attr("cx",0).attr("cy",0)}return g(e,a),e.intersect=function(t){return s.Rm.info("DoubleCircle intersect",e,f,t),H.circle(e,f,t)},i}function td(t,e,{config:{themeVariables:a}}){let{labelStyles:l,nodeStyles:r}=A(e);e.label="",e.labelStyle=l;let i=t.insert("g").attr("class",y(e)).attr("id",e.domId??e.id),{cssStyles:n}=e,h=o.A.svg(i),{nodeBorder:d}=a,c=v(e,{fillStyle:"solid"});"handDrawn"!==e.look&&(c.roughness=0);let f=h.circle(0,0,14,c),p=i.insert(()=>f,":first-child");return p.selectAll("path").attr("style",`fill: ${d} !important;`),n&&n.length>0&&"handDrawn"!==e.look&&p.selectAll("path").attr("style",n),r&&"handDrawn"!==e.look&&p.selectAll("path").attr("style",r),g(e,p),e.intersect=function(t){return s.Rm.info("filledCircle intersect",e,{radius:7,point:t}),H.circle(e,7,t)},i}async function tc(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),h=i.width+(e.padding??0),c=h+i.height,p=h+i.height,m=[{x:0,y:-c},{x:p,y:-c},{x:p/2,y:0}],{cssStyles:u}=e,x=o.A.svg(r),w=v(e,{});"handDrawn"!==e.look&&(w.roughness=0,w.fillStyle="solid");let b=f(m),$=x.path(b,w),k=r.insert(()=>$,":first-child").attr("transform",`translate(${-c/2}, ${c/2})`);return u&&"handDrawn"!==e.look&&k.selectChildren("path").attr("style",u),l&&"handDrawn"!==e.look&&k.selectChildren("path").attr("style",l),e.width=h,e.height=c,g(e,k),n.attr("transform",`translate(${-i.width/2-(i.x-(i.left??0))}, ${-c/2+(e.padding??0)/2+(i.y-(i.top??0))})`),e.intersect=function(t){return s.Rm.info("Triangle intersect",e,m,t),H.polygon(e,m,t)},r}function tg(t,e,{dir:a,config:{state:l,themeVariables:r}}){let{nodeStyles:i}=A(e);e.label="";let n=t.insert("g").attr("class",y(e)).attr("id",e.domId??e.id),{cssStyles:s}=e,h=Math.max(70,e?.width??0),d=Math.max(10,e?.height??0);"LR"===a&&(h=Math.max(10,e?.width??0),d=Math.max(70,e?.height??0));let c=-1*h/2,f=-1*d/2,p=o.A.svg(n),m=v(e,{stroke:r.lineColor,fill:r.lineColor});"handDrawn"!==e.look&&(m.roughness=0,m.fillStyle="solid");let u=p.rectangle(c,f,h,d,m),x=n.insert(()=>u,":first-child");s&&"handDrawn"!==e.look&&x.selectAll("path").attr("style",s),i&&"handDrawn"!==e.look&&x.selectAll("path").attr("style",i),g(e,x);let w=l?.padding??0;return e.width&&e.height&&(e.width+=w/2||0,e.height+=w/2||0),e.intersect=function(t){return H.rect(e,t)},n}async function ty(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),n=Math.max(80,i.width+(e.padding??0)*2,e?.width??0),h=Math.max(50,i.height+(e.padding??0)*2,e?.height??0),c=h/2,{cssStyles:p}=e,u=o.A.svg(r),x=v(e,{});"handDrawn"!==e.look&&(x.roughness=0,x.fillStyle="solid");let w=[{x:-n/2,y:-h/2},{x:n/2-c,y:-h/2},...m(-n/2+c,0,c,50,90,270),{x:n/2-c,y:h/2},{x:-n/2,y:h/2}],b=f(w),$=u.path(b,x),k=r.insert(()=>$,":first-child");return k.attr("class","basic label-container"),p&&"handDrawn"!==e.look&&k.selectChildren("path").attr("style",p),l&&"handDrawn"!==e.look&&k.selectChildren("path").attr("style",l),g(e,k),e.intersect=function(t){return s.Rm.info("Pill intersect",e,{radius:c,point:t}),H.polygon(e,w,t)},r}(0,s.K2)(ts,"cylinder"),(0,s.K2)(th,"dividedRectangle"),(0,s.K2)(to,"doublecircle"),(0,s.K2)(td,"filledCircle"),(0,s.K2)(tc,"flippedTriangle"),(0,s.K2)(tg,"forkJoin"),(0,s.K2)(ty,"halfRoundedRectangle");var tf=(0,s.K2)((t,e,a,l,r)=>`M${t+r},${e} L${t+a-r},${e} L${t+a},${e-l/2} L${t+a-r},${e-l} L${t+r},${e-l} L${t},${e-l/2} Z`,"createHexagonPathD");async function tp(t,e){let a;let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),s=n.height+e.padding,h=s/4,c=n.width+2*h+e.padding,f=[{x:h,y:0},{x:c-h,y:0},{x:c,y:-s/2},{x:c-h,y:-s},{x:h,y:-s},{x:0,y:-s/2}],{cssStyles:p}=e;if("handDrawn"===e.look){let t=o.A.svg(i),l=v(e,{}),r=tf(0,0,c,s,h),n=t.path(r,l);a=i.insert(()=>n,":first-child").attr("transform",`translate(${-c/2}, ${s/2})`),p&&a.attr("style",p)}else a=F(i,c,s,f);return r&&a.attr("style",r),e.width=c,e.height=s,g(e,a),e.intersect=function(t){return H.polygon(e,f,t)},i}async function tm(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.label="",e.labelStyle=a;let{shapeSvg:r}=await d(t,e,y(e)),i=Math.max(30,e?.width??0),n=Math.max(30,e?.height??0),{cssStyles:h}=e,c=o.A.svg(r),p=v(e,{});"handDrawn"!==e.look&&(p.roughness=0,p.fillStyle="solid");let m=[{x:0,y:0},{x:i,y:0},{x:0,y:n},{x:i,y:n}],u=f(m),x=c.path(u,p),w=r.insert(()=>x,":first-child");return w.attr("class","basic label-container"),h&&"handDrawn"!==e.look&&w.selectChildren("path").attr("style",h),l&&"handDrawn"!==e.look&&w.selectChildren("path").attr("style",l),w.attr("transform",`translate(${-i/2}, ${-n/2})`),g(e,w),e.intersect=function(t){return s.Rm.info("Pill intersect",e,{points:m}),H.polygon(e,m,t)},r}async function tu(t,e,{config:{themeVariables:a,flowchart:l}}){let{labelStyles:i}=A(e);e.labelStyle=i;let n=Math.max(e.assetHeight??48,e.assetWidth??48);e.width=Math.max(n,l?.wrappingWidth??0);let{shapeSvg:h,bbox:c,label:y}=await d(t,e,"icon-shape default"),f="t"===e.pos,{nodeBorder:p}=a,{stylesMap:m}=S(e),u=-n/2,x=-n/2,w=8*!!e.label,b=o.A.svg(h),$=v(e,{stroke:"none",fill:"none"});"handDrawn"!==e.look&&($.roughness=0,$.fillStyle="solid");let k=b.rectangle(u,x,n,n,$),M=Math.max(n,c.width),D=n+c.height+w,K=b.rectangle(-M/2,-D/2,M,D,{...$,fill:"transparent",stroke:"none"}),C=h.insert(()=>k,":first-child"),B=h.insert(()=>K);if(e.icon){let t=h.append("g");t.html(`<g>${await (0,r.WY)(e.icon,{height:n,width:n,fallbackPrefix:""})}</g>`);let a=t.node().getBBox(),l=a.width,i=a.height,s=a.x,o=a.y;t.attr("transform",`translate(${-l/2-s},${f?c.height/2+w/2-i/2-o:-c.height/2-w/2-i/2-o})`),t.attr("style",`color: ${m.get("stroke")??p};`)}return y.attr("transform",`translate(${-c.width/2-(c.x-(c.left??0))},${f?-D/2:D/2-c.height})`),C.attr("transform",`translate(0,${f?c.height/2+w/2:-c.height/2-w/2})`),g(e,B),e.intersect=function(t){if(s.Rm.info("iconSquare intersect",e,t),!e.label)return H.rect(e,t);let a=e.x??0,l=e.y??0,r=e.height??0,i=[];return i=f?[{x:a-c.width/2,y:l-r/2},{x:a+c.width/2,y:l-r/2},{x:a+c.width/2,y:l-r/2+c.height+w},{x:a+n/2,y:l-r/2+c.height+w},{x:a+n/2,y:l+r/2},{x:a-n/2,y:l+r/2},{x:a-n/2,y:l-r/2+c.height+w},{x:a-c.width/2,y:l-r/2+c.height+w}]:[{x:a-n/2,y:l-r/2},{x:a+n/2,y:l-r/2},{x:a+n/2,y:l-r/2+n},{x:a+c.width/2,y:l-r/2+n},{x:a+c.width/2/2,y:l+r/2},{x:a-c.width/2,y:l+r/2},{x:a-c.width/2,y:l-r/2+n},{x:a-n/2,y:l-r/2+n}],H.polygon(e,i,t)},h}async function tx(t,e,{config:{themeVariables:a,flowchart:l}}){let{labelStyles:i}=A(e);e.labelStyle=i;let n=Math.max(e.assetHeight??48,e.assetWidth??48);e.width=Math.max(n,l?.wrappingWidth??0);let{shapeSvg:h,bbox:c,label:y}=await d(t,e,"icon-shape default"),f=8*!!e.label,p="t"===e.pos,{nodeBorder:m,mainBkg:u}=a,{stylesMap:x}=S(e),w=o.A.svg(h),b=v(e,{});"handDrawn"!==e.look&&(b.roughness=0,b.fillStyle="solid"),b.stroke=x.get("fill")??u;let $=h.append("g");e.icon&&$.html(`<g>${await (0,r.WY)(e.icon,{height:n,width:n,fallbackPrefix:""})}</g>`);let k=$.node().getBBox(),M=k.width,D=k.height,K=k.x,C=k.y,B=Math.max(M,D)*Math.SQRT2+40,N=w.circle(0,0,B,b),L=Math.max(B,c.width),R=B+c.height+f,P=w.rectangle(-L/2,-R/2,L,R,{...b,fill:"transparent",stroke:"none"}),I=h.insert(()=>N,":first-child"),T=h.insert(()=>P);return $.attr("transform",`translate(${-M/2-K},${p?c.height/2+f/2-D/2-C:-c.height/2-f/2-D/2-C})`),$.attr("style",`color: ${x.get("stroke")??m};`),y.attr("transform",`translate(${-c.width/2-(c.x-(c.left??0))},${p?-R/2:R/2-c.height})`),I.attr("transform",`translate(0,${p?c.height/2+f/2:-c.height/2-f/2})`),g(e,T),e.intersect=function(t){return s.Rm.info("iconSquare intersect",e,t),H.rect(e,t)},h}async function tw(t,e,{config:{themeVariables:a,flowchart:l}}){let{labelStyles:i}=A(e);e.labelStyle=i;let n=Math.max(e.assetHeight??48,e.assetWidth??48);e.width=Math.max(n,l?.wrappingWidth??0);let{shapeSvg:h,bbox:c,halfPadding:y,label:f}=await d(t,e,"icon-shape default"),p="t"===e.pos,m=n+2*y,u=n+2*y,{nodeBorder:x,mainBkg:w}=a,{stylesMap:b}=S(e),k=8*!!e.label,M=o.A.svg(h),D=v(e,{});"handDrawn"!==e.look&&(D.roughness=0,D.fillStyle="solid"),D.stroke=b.get("fill")??w;let K=M.path($(-u/2,-m/2,u,m,5),D),C=Math.max(u,c.width),B=m+c.height+k,N=M.rectangle(-C/2,-B/2,C,B,{...D,fill:"transparent",stroke:"none"}),L=h.insert(()=>K,":first-child").attr("class","icon-shape2"),R=h.insert(()=>N);if(e.icon){let t=h.append("g");t.html(`<g>${await (0,r.WY)(e.icon,{height:n,width:n,fallbackPrefix:""})}</g>`);let a=t.node().getBBox(),l=a.width,i=a.height,s=a.x,o=a.y;t.attr("transform",`translate(${-l/2-s},${p?c.height/2+k/2-i/2-o:-c.height/2-k/2-i/2-o})`),t.attr("style",`color: ${b.get("stroke")??x};`)}return f.attr("transform",`translate(${-c.width/2-(c.x-(c.left??0))},${p?-B/2:B/2-c.height})`),L.attr("transform",`translate(0,${p?c.height/2+k/2:-c.height/2-k/2})`),g(e,R),e.intersect=function(t){if(s.Rm.info("iconSquare intersect",e,t),!e.label)return H.rect(e,t);let a=e.x??0,l=e.y??0,r=e.height??0,i=[];return i=p?[{x:a-c.width/2,y:l-r/2},{x:a+c.width/2,y:l-r/2},{x:a+c.width/2,y:l-r/2+c.height+k},{x:a+u/2,y:l-r/2+c.height+k},{x:a+u/2,y:l+r/2},{x:a-u/2,y:l+r/2},{x:a-u/2,y:l-r/2+c.height+k},{x:a-c.width/2,y:l-r/2+c.height+k}]:[{x:a-u/2,y:l-r/2},{x:a+u/2,y:l-r/2},{x:a+u/2,y:l-r/2+m},{x:a+c.width/2,y:l-r/2+m},{x:a+c.width/2/2,y:l+r/2},{x:a-c.width/2,y:l+r/2},{x:a-c.width/2,y:l-r/2+m},{x:a-u/2,y:l-r/2+m}],H.polygon(e,i,t)},h}async function tb(t,e,{config:{themeVariables:a,flowchart:l}}){let{labelStyles:i}=A(e);e.labelStyle=i;let n=Math.max(e.assetHeight??48,e.assetWidth??48);e.width=Math.max(n,l?.wrappingWidth??0);let{shapeSvg:h,bbox:c,halfPadding:y,label:f}=await d(t,e,"icon-shape default"),p="t"===e.pos,m=n+2*y,u=n+2*y,{nodeBorder:x,mainBkg:w}=a,{stylesMap:b}=S(e),k=8*!!e.label,M=o.A.svg(h),D=v(e,{});"handDrawn"!==e.look&&(D.roughness=0,D.fillStyle="solid"),D.stroke=b.get("fill")??w;let K=M.path($(-u/2,-m/2,u,m,.1),D),C=Math.max(u,c.width),B=m+c.height+k,N=M.rectangle(-C/2,-B/2,C,B,{...D,fill:"transparent",stroke:"none"}),L=h.insert(()=>K,":first-child"),R=h.insert(()=>N);if(e.icon){let t=h.append("g");t.html(`<g>${await (0,r.WY)(e.icon,{height:n,width:n,fallbackPrefix:""})}</g>`);let a=t.node().getBBox(),l=a.width,i=a.height,s=a.x,o=a.y;t.attr("transform",`translate(${-l/2-s},${p?c.height/2+k/2-i/2-o:-c.height/2-k/2-i/2-o})`),t.attr("style",`color: ${b.get("stroke")??x};`)}return f.attr("transform",`translate(${-c.width/2-(c.x-(c.left??0))},${p?-B/2:B/2-c.height})`),L.attr("transform",`translate(0,${p?c.height/2+k/2:-c.height/2-k/2})`),g(e,R),e.intersect=function(t){if(s.Rm.info("iconSquare intersect",e,t),!e.label)return H.rect(e,t);let a=e.x??0,l=e.y??0,r=e.height??0,i=[];return i=p?[{x:a-c.width/2,y:l-r/2},{x:a+c.width/2,y:l-r/2},{x:a+c.width/2,y:l-r/2+c.height+k},{x:a+u/2,y:l-r/2+c.height+k},{x:a+u/2,y:l+r/2},{x:a-u/2,y:l+r/2},{x:a-u/2,y:l-r/2+c.height+k},{x:a-c.width/2,y:l-r/2+c.height+k}]:[{x:a-u/2,y:l-r/2},{x:a+u/2,y:l-r/2},{x:a+u/2,y:l-r/2+m},{x:a+c.width/2,y:l-r/2+m},{x:a+c.width/2/2,y:l+r/2},{x:a-c.width/2,y:l+r/2},{x:a-c.width/2,y:l-r/2+m},{x:a-u/2,y:l-r/2+m}],H.polygon(e,i,t)},h}async function t$(t,e,{config:{flowchart:a}}){let l=new Image;l.src=e?.img??"",await l.decode();let r=Number(l.naturalWidth.toString().replace("px","")),i=Number(l.naturalHeight.toString().replace("px",""));e.imageAspectRatio=r/i;let{labelStyles:n}=A(e);e.labelStyle=n;let h=a?.wrappingWidth;e.defaultWidth=a?.wrappingWidth;let c=Math.max(e.label?h??0:0,e?.assetWidth??r),y="on"===e.constraint&&e?.assetHeight?e.assetHeight*e.imageAspectRatio:c,f="on"===e.constraint?y/e.imageAspectRatio:e?.assetHeight??i;e.width=Math.max(y,h??0);let{shapeSvg:p,bbox:m,label:u}=await d(t,e,"image-shape default"),x="t"===e.pos,w=-y/2,b=8*!!e.label,$=o.A.svg(p),k=v(e,{});"handDrawn"!==e.look&&(k.roughness=0,k.fillStyle="solid");let S=$.rectangle(w,-f/2,y,f,k),M=Math.max(y,m.width),D=f+m.height+b,K=$.rectangle(-M/2,-D/2,M,D,{...k,fill:"none",stroke:"none"}),C=p.insert(()=>S,":first-child"),B=p.insert(()=>K);if(e.img){let t=p.append("image");t.attr("href",e.img),t.attr("width",y),t.attr("height",f),t.attr("preserveAspectRatio","none"),t.attr("transform",`translate(${-y/2},${x?D/2-f:-D/2})`)}return u.attr("transform",`translate(${-m.width/2-(m.x-(m.left??0))},${x?-f/2-m.height/2-b/2:f/2-m.height/2+b/2})`),C.attr("transform",`translate(0,${x?m.height/2+b/2:-m.height/2-b/2})`),g(e,B),e.intersect=function(t){if(s.Rm.info("iconSquare intersect",e,t),!e.label)return H.rect(e,t);let a=e.x??0,l=e.y??0,r=e.height??0,i=[];return i=x?[{x:a-m.width/2,y:l-r/2},{x:a+m.width/2,y:l-r/2},{x:a+m.width/2,y:l-r/2+m.height+b},{x:a+y/2,y:l-r/2+m.height+b},{x:a+y/2,y:l+r/2},{x:a-y/2,y:l+r/2},{x:a-y/2,y:l-r/2+m.height+b},{x:a-m.width/2,y:l-r/2+m.height+b}]:[{x:a-y/2,y:l-r/2},{x:a+y/2,y:l-r/2},{x:a+y/2,y:l-r/2+f},{x:a+m.width/2,y:l-r/2+f},{x:a+m.width/2/2,y:l+r/2},{x:a-m.width/2,y:l+r/2},{x:a-m.width/2,y:l-r/2+f},{x:a-y/2,y:l-r/2+f}],H.polygon(e,i,t)},p}async function tk(t,e){let a;let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),s=Math.max(n.width+(e.padding??0)*2,e?.width??0),h=Math.max(n.height+(e.padding??0)*2,e?.height??0),c=[{x:0,y:0},{x:s,y:0},{x:s+3*h/6,y:-h},{x:-3*h/6,y:-h}],{cssStyles:p}=e;if("handDrawn"===e.look){let t=o.A.svg(i),l=v(e,{}),r=f(c),n=t.path(r,l);a=i.insert(()=>n,":first-child").attr("transform",`translate(${-s/2}, ${h/2})`),p&&a.attr("style",p)}else a=F(i,s,h,c);return r&&a.attr("style",r),e.width=s,e.height=h,g(e,a),e.intersect=function(t){return H.polygon(e,c,t)},i}async function tS(t,e,a){let l;let{labelStyles:r,nodeStyles:i}=A(e);e.labelStyle=r;let{shapeSvg:s,bbox:h}=await d(t,e,y(e)),c=Math.max(h.width+2*a.labelPaddingX,e?.width||0),f=Math.max(h.height+2*a.labelPaddingY,e?.height||0),p=-c/2,m=-f/2,{rx:u,ry:x}=e,{cssStyles:w}=e;if(a?.rx&&a.ry&&(u=a.rx,x=a.ry),"handDrawn"===e.look){let t=o.A.svg(s),a=v(e,{}),r=u||x?t.path($(p,m,c,f,u||0),a):t.rectangle(p,m,c,f,a);(l=s.insert(()=>r,":first-child")).attr("class","basic label-container").attr("style",(0,n.KL)(w))}else(l=s.insert("rect",":first-child")).attr("class","basic label-container").attr("style",i).attr("rx",(0,n.KL)(u)).attr("ry",(0,n.KL)(x)).attr("x",p).attr("y",m).attr("width",c).attr("height",f);return g(e,l),e.intersect=function(t){return H.rect(e,t)},s}async function tM(t,e){let{shapeSvg:a,bbox:l,label:r}=await d(t,e,"label"),i=a.insert("rect",":first-child");return i.attr("width",.1).attr("height",.1),a.attr("class","label edgeLabel"),r.attr("transform",`translate(${-(l.width/2)-(l.x-(l.left??0))}, ${-(l.height/2)-(l.y-(l.top??0))})`),g(e,i),e.intersect=function(t){return H.rect(e,t)},a}async function tD(t,e){let a;let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),s=Math.max(n.width+(e.padding??0),e?.width??0),h=Math.max(n.height+(e.padding??0),e?.height??0),c=[{x:0,y:0},{x:s+3*h/6,y:0},{x:s,y:-h},{x:-(3*h)/6,y:-h}],{cssStyles:p}=e;if("handDrawn"===e.look){let t=o.A.svg(i),l=v(e,{}),r=f(c),n=t.path(r,l);a=i.insert(()=>n,":first-child").attr("transform",`translate(${-s/2}, ${h/2})`),p&&a.attr("style",p)}else a=F(i,s,h,c);return r&&a.attr("style",r),e.width=s,e.height=h,g(e,a),e.intersect=function(t){return H.polygon(e,c,t)},i}async function tA(t,e){let a;let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),s=Math.max(n.width+(e.padding??0),e?.width??0),h=Math.max(n.height+(e.padding??0),e?.height??0),c=[{x:-3*h/6,y:0},{x:s,y:0},{x:s+3*h/6,y:-h},{x:0,y:-h}],{cssStyles:p}=e;if("handDrawn"===e.look){let t=o.A.svg(i),l=v(e,{}),r=f(c),n=t.path(r,l);a=i.insert(()=>n,":first-child").attr("transform",`translate(${-s/2}, ${h/2})`),p&&a.attr("style",p)}else a=F(i,s,h,c);return r&&a.attr("style",r),e.width=s,e.height=h,g(e,a),e.intersect=function(t){return H.polygon(e,c,t)},i}function tv(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.label="",e.labelStyle=a;let r=t.insert("g").attr("class",y(e)).attr("id",e.domId??e.id),{cssStyles:i}=e,n=Math.max(35,e?.width??0),h=Math.max(35,e?.height??0),d=[{x:n,y:0},{x:0,y:h+3.5},{x:n-14,y:h+3.5},{x:0,y:2*h},{x:n,y:h-3.5},{x:14,y:h-3.5}],c=o.A.svg(r),p=v(e,{});"handDrawn"!==e.look&&(p.roughness=0,p.fillStyle="solid");let m=f(d),u=c.path(m,p),x=r.insert(()=>u,":first-child");return i&&"handDrawn"!==e.look&&x.selectAll("path").attr("style",i),l&&"handDrawn"!==e.look&&x.selectAll("path").attr("style",l),x.attr("transform",`translate(-${n/2},${-h})`),g(e,x),e.intersect=function(t){return s.Rm.info("lightningBolt intersect",e,t),H.polygon(e,d,t)},r}(0,s.K2)(tp,"hexagon"),(0,s.K2)(tm,"hourglass"),(0,s.K2)(tu,"icon"),(0,s.K2)(tx,"iconCircle"),(0,s.K2)(tw,"iconRounded"),(0,s.K2)(tb,"iconSquare"),(0,s.K2)(t$,"imageSquare"),(0,s.K2)(tk,"inv_trapezoid"),(0,s.K2)(tS,"drawRect"),(0,s.K2)(tM,"labelRect"),(0,s.K2)(tD,"lean_left"),(0,s.K2)(tA,"lean_right"),(0,s.K2)(tv,"lightningBolt");var tK=(0,s.K2)((t,e,a,l,r,i,n)=>`M${t},${e+i} a${r},${i} 0,0,0 ${a},0 a${r},${i} 0,0,0 ${-a},0 l0,${l} a${r},${i} 0,0,0 ${a},0 l0,${-l} M${t},${e+i+n} a${r},${i} 0,0,0 ${a},0`,"createCylinderPathD"),tC=(0,s.K2)((t,e,a,l,r,i,n)=>`M${t},${e+i} M${t+a},${e+i} a${r},${i} 0,0,0 ${-a},0 l0,${l} a${r},${i} 0,0,0 ${a},0 l0,${-l} M${t},${e+i+n} a${r},${i} 0,0,0 ${a},0`,"createOuterCylinderPathD"),tB=(0,s.K2)((t,e,a,l,r,i)=>`M${t-a/2},${-l/2} a${r},${i} 0,0,0 ${a},0`,"createInnerCylinderPathD");async function tN(t,e){let a;let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let{shapeSvg:i,bbox:s,label:h}=await d(t,e,y(e)),c=Math.max(s.width+(e.padding??0),e.width??0),f=c/2,p=f/(2.5+c/50),m=Math.max(s.height+p+(e.padding??0),e.height??0),u=.1*m,{cssStyles:x}=e;if("handDrawn"===e.look){let t=o.A.svg(i),l=tC(0,0,c,m,f,p,u),r=tB(0,p,c,m,f,p),n=v(e,{}),s=t.path(l,n),h=t.path(r,n);i.insert(()=>h,":first-child").attr("class","line"),(a=i.insert(()=>s,":first-child")).attr("class","basic label-container"),x&&a.attr("style",x)}else{let t=tK(0,0,c,m,f,p,u);a=i.insert("path",":first-child").attr("d",t).attr("class","basic label-container").attr("style",(0,n.KL)(x)).attr("style",r)}return a.attr("label-offset-y",p),a.attr("transform",`translate(${-c/2}, ${-(m/2+p)})`),g(e,a),h.attr("transform",`translate(${-(s.width/2)-(s.x-(s.left??0))}, ${-(s.height/2)+p-(s.y-(s.top??0))})`),e.intersect=function(t){let a=H.rect(e,t),l=a.x-(e.x??0);if(0!=f&&(Math.abs(l)<(e.width??0)/2||Math.abs(l)==(e.width??0)/2&&Math.abs(a.y-(e.y??0))>(e.height??0)/2-p)){let r=p*p*(1-l*l/(f*f));r>0&&(r=Math.sqrt(r)),r=p-r,t.y-(e.y??0)>0&&(r=-r),a.y+=r}return a},i}async function tL(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0)*2,e?.width??0),h=Math.max(i.height+(e.padding??0)*2,e?.height??0),c=h/4,f=h+c,{cssStyles:m}=e,u=o.A.svg(r),x=v(e,{});"handDrawn"!==e.look&&(x.roughness=0,x.fillStyle="solid");let w=[{x:-s/2-s/2*.1,y:-f/2},{x:-s/2-s/2*.1,y:f/2},...p(-s/2-s/2*.1,f/2,s/2+s/2*.1,f/2,c,.8),{x:s/2+s/2*.1,y:-f/2},{x:-s/2-s/2*.1,y:-f/2},{x:-s/2,y:-f/2},{x:-s/2,y:f/2*1.1},{x:-s/2,y:-f/2}],b=u.polygon(w.map(t=>[t.x,t.y]),x),$=r.insert(()=>b,":first-child");return $.attr("class","basic label-container"),m&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",m),l&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",l),$.attr("transform",`translate(0,${-c/2})`),n.attr("transform",`translate(${-s/2+(e.padding??0)+s/2*.1/2-(i.x-(i.left??0))},${-h/2+(e.padding??0)-c/2-(i.y-(i.top??0))})`),g(e,$),e.intersect=function(t){return H.polygon(e,w,t)},r}async function tR(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0)*2,e?.width??0),h=Math.max(i.height+(e.padding??0)*2,e?.height??0),c=-s/2,p=-h/2,{cssStyles:m}=e,u=o.A.svg(r),x=v(e,{}),w=[{x:c-5,y:p+5},{x:c-5,y:p+h+5},{x:c+s-5,y:p+h+5},{x:c+s-5,y:p+h},{x:c+s,y:p+h},{x:c+s,y:p+h-5},{x:c+s+5,y:p+h-5},{x:c+s+5,y:p-5},{x:c+5,y:p-5},{x:c+5,y:p},{x:c,y:p},{x:c,y:p+5}];"handDrawn"!==e.look&&(x.roughness=0,x.fillStyle="solid");let b=f(w),$=u.path(b,x),k=f([{x:c,y:p+5},{x:c+s-5,y:p+5},{x:c+s-5,y:p+h},{x:c+s,y:p+h},{x:c+s,y:p},{x:c,y:p}]),S=u.path(k,{...x,fill:"none"}),M=r.insert(()=>S,":first-child");return M.insert(()=>$,":first-child"),M.attr("class","basic label-container"),m&&"handDrawn"!==e.look&&M.selectAll("path").attr("style",m),l&&"handDrawn"!==e.look&&M.selectAll("path").attr("style",l),n.attr("transform",`translate(${-(i.width/2)-5-(i.x-(i.left??0))}, ${-(i.height/2)+5-(i.y-(i.top??0))})`),g(e,M),e.intersect=function(t){return H.polygon(e,w,t)},r}async function tP(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0)*2,e?.width??0),h=Math.max(i.height+(e.padding??0)*2,e?.height??0),c=h/4,m=h+c,u=-s/2,x=-m/2,{cssStyles:w}=e,b=p(u-5,x+m+5,u+s-5,x+m+5,c,.8),$=b?.[b.length-1],k=[{x:u-5,y:x+5},{x:u-5,y:x+m+5},...b,{x:u+s-5,y:$.y-5},{x:u+s,y:$.y-5},{x:u+s,y:$.y-10},{x:u+s+5,y:$.y-10},{x:u+s+5,y:x-5},{x:u+5,y:x-5},{x:u+5,y:x},{x:u,y:x},{x:u,y:x+5}],S=[{x:u,y:x+5},{x:u+s-5,y:x+5},{x:u+s-5,y:$.y-5},{x:u+s,y:$.y-5},{x:u+s,y:x},{x:u,y:x}],M=o.A.svg(r),D=v(e,{});"handDrawn"!==e.look&&(D.roughness=0,D.fillStyle="solid");let K=f(k),C=M.path(K,D),B=f(S),N=M.path(B,D),L=r.insert(()=>C,":first-child");return L.insert(()=>N),L.attr("class","basic label-container"),w&&"handDrawn"!==e.look&&L.selectAll("path").attr("style",w),l&&"handDrawn"!==e.look&&L.selectAll("path").attr("style",l),L.attr("transform",`translate(0,${-c/2})`),n.attr("transform",`translate(${-(i.width/2)-5-(i.x-(i.left??0))}, ${-(i.height/2)+5-c/2-(i.y-(i.top??0))})`),g(e,L),e.intersect=function(t){return H.polygon(e,k,t)},r}async function tI(t,e,{config:{themeVariables:a}}){let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l,e.useHtmlLabels||s.zj().flowchart?.htmlLabels!==!1||(e.centerLabel=!0);let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),h=Math.max(n.width+(e.padding??0)*2,e?.width??0),c=Math.max(n.height+(e.padding??0)*2,e?.height??0),{cssStyles:f}=e,p=o.A.svg(i),m=v(e,{fill:a.noteBkgColor,stroke:a.noteBorderColor});"handDrawn"!==e.look&&(m.roughness=0,m.fillStyle="solid");let u=p.rectangle(-h/2,-c/2,h,c,m),x=i.insert(()=>u,":first-child");return x.attr("class","basic label-container"),f&&"handDrawn"!==e.look&&x.selectAll("path").attr("style",f),r&&"handDrawn"!==e.look&&x.selectAll("path").attr("style",r),g(e,x),e.intersect=function(t){return H.rect(e,t)},i}(0,s.K2)(tN,"linedCylinder"),(0,s.K2)(tL,"linedWaveEdgedRect"),(0,s.K2)(tR,"multiRect"),(0,s.K2)(tP,"multiWaveEdgedRectangle"),(0,s.K2)(tI,"note");var tT=(0,s.K2)((t,e,a)=>`M${t+a/2},${e} L${t+a},${e-a/2} L${t+a/2},${e-a} L${t},${e-a/2} Z`,"createDecisionBoxPathD");async function tW(t,e){let a;let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),h=n.width+e.padding+(n.height+e.padding),c=[{x:h/2,y:0},{x:h,y:-h/2},{x:h/2,y:-h},{x:0,y:-h/2}],{cssStyles:f}=e;if("handDrawn"===e.look){let t=o.A.svg(i),l=v(e,{}),r=tT(0,0,h),n=t.path(r,l);a=i.insert(()=>n,":first-child").attr("transform",`translate(${-h/2}, ${h/2})`),f&&a.attr("style",f)}else a=F(i,h,h,c);return r&&a.attr("style",r),g(e,a),e.intersect=function(t){return s.Rm.debug("APA12 Intersect called SPLIT\npoint:",t,"\nnode:\n",e,"\nres:",H.polygon(e,c,t)),H.polygon(e,c,t)},i}async function t_(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0),e?.width??0),h=Math.max(i.height+(e.padding??0),e?.height??0),c=-s/2,p=-h/2,m=p/2,u=[{x:c+m,y:p},{x:c,y:0},{x:c+m,y:-p},{x:-c,y:-p},{x:-c,y:p}],{cssStyles:x}=e,w=o.A.svg(r),b=v(e,{});"handDrawn"!==e.look&&(b.roughness=0,b.fillStyle="solid");let $=f(u),k=w.path($,b),S=r.insert(()=>k,":first-child");return S.attr("class","basic label-container"),x&&"handDrawn"!==e.look&&S.selectAll("path").attr("style",x),l&&"handDrawn"!==e.look&&S.selectAll("path").attr("style",l),S.attr("transform",`translate(${-m/2},0)`),n.attr("transform",`translate(${-m/2-i.width/2-(i.x-(i.left??0))}, ${-(i.height/2)-(i.y-(i.top??0))})`),g(e,S),e.intersect=function(t){return H.polygon(e,u,t)},r}async function tE(t,e){let a,l,r;let{labelStyles:i,nodeStyles:n}=A(e);e.labelStyle=i,a=e.cssClasses?"node "+e.cssClasses:"node default";let d=t.insert("g").attr("class",a).attr("id",e.domId||e.id),c=d.insert("g"),y=d.insert("g").attr("class","label").attr("style",n),f=e.description,p=e.label,m=y.node().appendChild(await b(p,e.labelStyle,!0,!0)),u={width:0,height:0};if((0,s._3)(s.D7()?.flowchart?.htmlLabels)){let t=m.children[0],e=(0,h.Ltv)(m);u=t.getBoundingClientRect(),e.attr("width",u.width),e.attr("height",u.height)}s.Rm.info("Text 2",f);let x=f||[],w=m.getBBox(),k=y.node().appendChild(await b(x.join?x.join("<br/>"):x,e.labelStyle,!0,!0)),S=k.children[0],M=(0,h.Ltv)(k);u=S.getBoundingClientRect(),M.attr("width",u.width),M.attr("height",u.height);let D=(e.padding||0)/2;(0,h.Ltv)(k).attr("transform","translate( "+(u.width>w.width?0:(w.width-u.width)/2)+", "+(w.height+D+5)+")"),(0,h.Ltv)(m).attr("transform","translate( "+(u.width<w.width?0:-(w.width-u.width)/2)+", 0)"),u=y.node().getBBox(),y.attr("transform","translate("+-u.width/2+", "+(-u.height/2-D+3)+")");let K=u.width+(e.padding||0),C=u.height+(e.padding||0),B=-u.width/2-D,N=-u.height/2-D;if("handDrawn"===e.look){let t=o.A.svg(d),a=v(e,{}),i=t.path($(B,N,K,C,e.rx||0),a),n=t.line(-u.width/2-D,-u.height/2-D+w.height+D,u.width/2+D,-u.height/2-D+w.height+D,a);r=d.insert(()=>(s.Rm.debug("Rough node insert CXC",i),n),":first-child"),l=d.insert(()=>(s.Rm.debug("Rough node insert CXC",i),i),":first-child")}else l=c.insert("rect",":first-child"),r=c.insert("line"),l.attr("class","outer title-state").attr("style",n).attr("x",-u.width/2-D).attr("y",-u.height/2-D).attr("width",u.width+(e.padding||0)).attr("height",u.height+(e.padding||0)),r.attr("class","divider").attr("x1",-u.width/2-D).attr("x2",u.width/2+D).attr("y1",-u.height/2-D+w.height+D).attr("y2",-u.height/2-D+w.height+D);return g(e,l),e.intersect=function(t){return H.rect(e,t)},d}async function tj(t,e){let a={rx:5,ry:5,classes:"",labelPaddingX:+(e?.padding||0),labelPaddingY:+(e?.padding||0)};return tS(t,e,a)}async function tH(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:s}=await d(t,e,y(e)),h=e?.padding??0,c=Math.max(i.width+(e.padding??0)*2,e?.width??0),f=Math.max(i.height+(e.padding??0)*2,e?.height??0),p=-i.width/2-h,m=-i.height/2-h,{cssStyles:u}=e,x=o.A.svg(r),w=v(e,{});"handDrawn"!==e.look&&(w.roughness=0,w.fillStyle="solid");let b=x.polygon([{x:p,y:m},{x:p+c+8,y:m},{x:p+c+8,y:m+f},{x:p-8,y:m+f},{x:p-8,y:m},{x:p,y:m},{x:p,y:m+f}].map(t=>[t.x,t.y]),w),$=r.insert(()=>b,":first-child");return $.attr("class","basic label-container").attr("style",(0,n.KL)(u)),l&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",l),u&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",l),s.attr("transform",`translate(${-c/2+4+(e.padding??0)-(i.x-(i.left??0))},${-f/2+(e.padding??0)-(i.y-(i.top??0))})`),g(e,$),e.intersect=function(t){return H.rect(e,t)},r}async function tz(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0)*2,e?.width??0),h=Math.max(i.height+(e.padding??0)*2,e?.height??0),c=-s/2,p=-h/2,{cssStyles:m}=e,u=o.A.svg(r),x=v(e,{});"handDrawn"!==e.look&&(x.roughness=0,x.fillStyle="solid");let w=[{x:c,y:p},{x:c,y:p+h},{x:c+s,y:p+h},{x:c+s,y:p-h/2}],b=f(w),$=u.path(b,x),k=r.insert(()=>$,":first-child");return k.attr("class","basic label-container"),m&&"handDrawn"!==e.look&&k.selectChildren("path").attr("style",m),l&&"handDrawn"!==e.look&&k.selectChildren("path").attr("style",l),k.attr("transform",`translate(0, ${h/4})`),n.attr("transform",`translate(${-s/2+(e.padding??0)-(i.x-(i.left??0))}, ${-h/4+(e.padding??0)-(i.y-(i.top??0))})`),g(e,k),e.intersect=function(t){return H.polygon(e,w,t)},r}async function tq(t,e){let a={rx:0,ry:0,classes:"",labelPaddingX:2*(e?.padding||0),labelPaddingY:+(e?.padding||0)};return tS(t,e,a)}async function tO(t,e){let a;let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let{shapeSvg:i,bbox:s}=await d(t,e,y(e)),h=s.height+e.padding,c=s.width+h/4+e.padding,{cssStyles:f}=e;if("handDrawn"===e.look){let t=o.A.svg(i),l=v(e,{}),r=$(-c/2,-h/2,c,h,h/2),s=t.path(r,l);(a=i.insert(()=>s,":first-child")).attr("class","basic label-container").attr("style",(0,n.KL)(f))}else(a=i.insert("rect",":first-child")).attr("class","basic label-container").attr("style",r).attr("rx",h/2).attr("ry",h/2).attr("x",-c/2).attr("y",-h/2).attr("width",c).attr("height",h);return g(e,a),e.intersect=function(t){return H.rect(e,t)},i}async function tF(t,e){return tS(t,e,{rx:5,ry:5,classes:"flowchart-node"})}function tZ(t,e,{config:{themeVariables:a}}){let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let{cssStyles:i}=e,{lineColor:n,stateBorder:s,nodeBorder:h}=a,d=t.insert("g").attr("class","node default").attr("id",e.domId||e.id),c=o.A.svg(d),y=v(e,{});"handDrawn"!==e.look&&(y.roughness=0,y.fillStyle="solid");let f=c.circle(0,0,14,{...y,stroke:n,strokeWidth:2}),p=s??h,m=c.circle(0,0,5,{...y,fill:p,stroke:p,strokeWidth:2,fillStyle:"solid"}),u=d.insert(()=>f,":first-child");return u.insert(()=>m),i&&u.selectAll("path").attr("style",i),r&&u.selectAll("path").attr("style",r),g(e,u),e.intersect=function(t){return H.circle(e,7,t)},d}function tX(t,e,{config:{themeVariables:a}}){let l;let{lineColor:r}=a,i=t.insert("g").attr("class","node default").attr("id",e.domId||e.id);if("handDrawn"===e.look){let t=o.A.svg(i).circle(0,0,14,k(r));(l=i.insert(()=>t)).attr("class","state-start").attr("r",7).attr("width",14).attr("height",14)}else(l=i.insert("circle",":first-child")).attr("class","state-start").attr("r",7).attr("width",14).attr("height",14);return g(e,l),e.intersect=function(t){return H.circle(e,7,t)},i}async function tY(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),s=(e?.padding||0)/2,h=i.width+e.padding,c=i.height+e.padding,f=-i.width/2-s,p=-i.height/2-s,m=[{x:0,y:0},{x:h,y:0},{x:h,y:-c},{x:0,y:-c},{x:0,y:0},{x:-8,y:0},{x:h+8,y:0},{x:h+8,y:-c},{x:-8,y:-c},{x:-8,y:0}];if("handDrawn"===e.look){let t=o.A.svg(r),a=v(e,{}),l=t.rectangle(f-8,p,h+16,c,a),i=t.line(f,p,f,p+c,a),s=t.line(f+h,p,f+h,p+c,a);r.insert(()=>i,":first-child"),r.insert(()=>s,":first-child");let d=r.insert(()=>l,":first-child"),{cssStyles:y}=e;d.attr("class","basic label-container").attr("style",(0,n.KL)(y)),g(e,d)}else{let t=F(r,h,c,m);l&&t.attr("style",l),g(e,t)}return e.intersect=function(t){return H.polygon(e,m,t)},r}async function tU(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),n=Math.max(i.width+(e.padding??0)*2,e?.width??0),s=Math.max(i.height+(e.padding??0)*2,e?.height??0),h=-n/2,c=-s/2,p=.2*s,{cssStyles:m}=e,u=o.A.svg(r),x=v(e,{}),w=[{x:h-p/2,y:c},{x:h+n+p/2,y:c},{x:h+n+p/2,y:c+s},{x:h-p/2,y:c+s}];"handDrawn"!==e.look&&(x.roughness=0,x.fillStyle="solid");let b=f(w),$=u.path(b,x),k=f([{x:h+n-p/2,y:c+s},{x:h+n+p/2,y:c+s},{x:h+n+p/2,y:c+s-.2*s}]),S=u.path(k,{...x,fillStyle:"solid"}),M=r.insert(()=>S,":first-child");return M.insert(()=>$,":first-child"),M.attr("class","basic label-container"),m&&"handDrawn"!==e.look&&M.selectAll("path").attr("style",m),l&&"handDrawn"!==e.look&&M.selectAll("path").attr("style",l),g(e,M),e.intersect=function(t){return H.polygon(e,w,t)},r}async function tG(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0)*2,e?.width??0),h=Math.max(i.height+(e.padding??0)*2,e?.height??0),c=h/4,m=.2*s,u=.2*h,x=h+c,{cssStyles:w}=e,b=o.A.svg(r),$=v(e,{});"handDrawn"!==e.look&&($.roughness=0,$.fillStyle="solid");let k=[{x:-s/2-s/2*.1,y:x/2},...p(-s/2-s/2*.1,x/2,s/2+s/2*.1,x/2,c,.8),{x:s/2+s/2*.1,y:-x/2},{x:-s/2-s/2*.1,y:-x/2}],S=-s/2+s/2*.1,M=-x/2-.4*u,D=[{x:S+s-m,y:(M+h)*1.4},{x:S+s,y:M+h-u},{x:S+s,y:(M+h)*.9},...p(S+s,(M+h)*1.3,S+s-m,(M+h)*1.5,-(.03*h),.5)],K=f(k),C=b.path(K,$),B=f(D),N=b.path(B,{...$,fillStyle:"solid"}),L=r.insert(()=>N,":first-child");return L.insert(()=>C,":first-child"),L.attr("class","basic label-container"),w&&"handDrawn"!==e.look&&L.selectAll("path").attr("style",w),l&&"handDrawn"!==e.look&&L.selectAll("path").attr("style",l),L.attr("transform",`translate(0,${-c/2})`),n.attr("transform",`translate(${-s/2+(e.padding??0)-(i.x-(i.left??0))},${-h/2+(e.padding??0)-c/2-(i.y-(i.top??0))})`),g(e,L),e.intersect=function(t){return H.polygon(e,k,t)},r}async function tV(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),n=Math.max(i.width+e.padding,e?.width||0),s=Math.max(i.height+e.padding,e?.height||0),h=r.insert("rect",":first-child");return h.attr("class","text").attr("style",l).attr("rx",0).attr("ry",0).attr("x",-n/2).attr("y",-s/2).attr("width",n).attr("height",s),g(e,h),e.intersect=function(t){return H.rect(e,t)},r}(0,s.K2)(tW,"question"),(0,s.K2)(t_,"rect_left_inv_arrow"),(0,s.K2)(tE,"rectWithTitle"),(0,s.K2)(tj,"roundedRect"),(0,s.K2)(tH,"shadedProcess"),(0,s.K2)(tz,"slopedRect"),(0,s.K2)(tq,"squareRect"),(0,s.K2)(tO,"stadium"),(0,s.K2)(tF,"state"),(0,s.K2)(tZ,"stateEnd"),(0,s.K2)(tX,"stateStart"),(0,s.K2)(tY,"subroutine"),(0,s.K2)(tU,"taggedRect"),(0,s.K2)(tG,"taggedWaveEdgedRectangle"),(0,s.K2)(tV,"text");var tJ=(0,s.K2)((t,e,a,l,r,i)=>`M${t},${e}
    a${r},${i} 0,0,1 0,${-l}
    l${a},0
    a${r},${i} 0,0,1 0,${l}
    M${a},${-l}
    a${r},${i} 0,0,0 0,${l}
    l${-a},0`,"createCylinderPathD"),tQ=(0,s.K2)((t,e,a,l,r,i)=>`M${t},${e} M${t+a},${e} a${r},${i} 0,0,0 0,${-l} l${-a},0 a${r},${i} 0,0,0 0,${l} l${a},0`,"createOuterCylinderPathD"),t0=(0,s.K2)((t,e,a,l,r,i)=>`M${t+a/2},${-l/2} a${r},${i} 0,0,0 0,${l}`,"createInnerCylinderPathD");async function t2(t,e){let a;let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let{shapeSvg:i,bbox:s,label:h,halfPadding:c}=await d(t,e,y(e)),f="neo"===e.look?2*c:c,p=s.height+f,m=p/2,u=m/(2.5+p/50),x=s.width+u+f,{cssStyles:w}=e;if("handDrawn"===e.look){let t=o.A.svg(i),l=tQ(0,0,x,p,u,m),r=t0(0,0,x,p,u,m),n=t.path(l,v(e,{})),s=t.path(r,v(e,{fill:"none"}));a=i.insert(()=>s,":first-child"),(a=i.insert(()=>n,":first-child")).attr("class","basic label-container"),w&&a.attr("style",w)}else{let t=tJ(0,0,x,p,u,m);(a=i.insert("path",":first-child").attr("d",t).attr("class","basic label-container").attr("style",(0,n.KL)(w)).attr("style",r)).attr("class","basic label-container"),w&&a.selectAll("path").attr("style",w),r&&a.selectAll("path").attr("style",r)}return a.attr("label-offset-x",u),a.attr("transform",`translate(${-x/2}, ${p/2} )`),h.attr("transform",`translate(${-(s.width/2)-u-(s.x-(s.left??0))}, ${-(s.height/2)-(s.y-(s.top??0))})`),g(e,a),e.intersect=function(t){let a=H.rect(e,t),l=a.y-(e.y??0);if(0!=m&&(Math.abs(l)<(e.height??0)/2||Math.abs(l)==(e.height??0)/2&&Math.abs(a.x-(e.x??0))>(e.width??0)/2-u)){let r=u*u*(1-l*l/(m*m));0!=r&&(r=Math.sqrt(Math.abs(r))),r=u-r,t.x-(e.x??0)>0&&(r=-r),a.x+=r}return a},i}async function t1(t,e){let a;let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let{shapeSvg:i,bbox:n}=await d(t,e,y(e)),s=n.width+e.padding,h=n.height+e.padding,c=[{x:-3*h/6,y:0},{x:s+3*h/6,y:0},{x:s,y:-h},{x:0,y:-h}],{cssStyles:p}=e;if("handDrawn"===e.look){let t=o.A.svg(i),l=v(e,{}),r=f(c),n=t.path(r,l);a=i.insert(()=>n,":first-child").attr("transform",`translate(${-s/2}, ${h/2})`),p&&a.attr("style",p)}else a=F(i,s,h,c);return r&&a.attr("style",r),e.width=s,e.height=h,g(e,a),e.intersect=function(t){return H.polygon(e,c,t)},i}async function t5(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),n=Math.max(60,i.width+(e.padding??0)*2,e?.width??0),s=Math.max(20,i.height+(e.padding??0)*2,e?.height??0),{cssStyles:h}=e,c=o.A.svg(r),p=v(e,{});"handDrawn"!==e.look&&(p.roughness=0,p.fillStyle="solid");let m=[{x:-n/2*.8,y:-s/2},{x:n/2*.8,y:-s/2},{x:n/2,y:-s/2*.6},{x:n/2,y:s/2},{x:-n/2,y:s/2},{x:-n/2,y:-s/2*.6}],u=f(m),x=c.path(u,p),w=r.insert(()=>x,":first-child");return w.attr("class","basic label-container"),h&&"handDrawn"!==e.look&&w.selectChildren("path").attr("style",h),l&&"handDrawn"!==e.look&&w.selectChildren("path").attr("style",l),g(e,w),e.intersect=function(t){return H.polygon(e,m,t)},r}async function t8(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),h=(0,s._3)(s.D7().flowchart?.htmlLabels),c=i.width+(e.padding??0),p=c+i.height,m=c+i.height,u=[{x:0,y:0},{x:m,y:0},{x:m/2,y:-p}],{cssStyles:x}=e,w=o.A.svg(r),b=v(e,{});"handDrawn"!==e.look&&(b.roughness=0,b.fillStyle="solid");let $=f(u),k=w.path($,b),S=r.insert(()=>k,":first-child").attr("transform",`translate(${-p/2}, ${p/2})`);return x&&"handDrawn"!==e.look&&S.selectChildren("path").attr("style",x),l&&"handDrawn"!==e.look&&S.selectChildren("path").attr("style",l),e.width=c,e.height=p,g(e,S),n.attr("transform",`translate(${-i.width/2-(i.x-(i.left??0))}, ${p/2-(i.height+(e.padding??0)/(h?2:1)-(i.y-(i.top??0)))})`),e.intersect=function(t){return s.Rm.info("Triangle intersect",e,u,t),H.polygon(e,u,t)},r}async function t3(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0)*2,e?.width??0),h=Math.max(i.height+(e.padding??0)*2,e?.height??0),c=h/8,m=h+c,{cssStyles:u}=e,x=70-s,w=x>0?x/2:0,b=o.A.svg(r),$=v(e,{});"handDrawn"!==e.look&&($.roughness=0,$.fillStyle="solid");let k=[{x:-s/2-w,y:m/2},...p(-s/2-w,m/2,s/2+w,m/2,c,.8),{x:s/2+w,y:-m/2},{x:-s/2-w,y:-m/2}],S=f(k),M=b.path(S,$),D=r.insert(()=>M,":first-child");return D.attr("class","basic label-container"),u&&"handDrawn"!==e.look&&D.selectAll("path").attr("style",u),l&&"handDrawn"!==e.look&&D.selectAll("path").attr("style",l),D.attr("transform",`translate(0,${-c/2})`),n.attr("transform",`translate(${-s/2+(e.padding??0)-(i.x-(i.left??0))},${-h/2+(e.padding??0)-c-(i.y-(i.top??0))})`),g(e,D),e.intersect=function(t){return H.polygon(e,k,t)},r}async function t7(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i}=await d(t,e,y(e)),n=Math.max(i.width+(e.padding??0)*2,e?.width??0),s=Math.max(i.height+(e.padding??0)*2,e?.height??0),h=n/s,c=n,m=s;c>m*h?m=c/h:c=m*h,c=Math.max(c,100);let u=Math.min(.2*(m=Math.max(m,50)),m/4),x=m+2*u,{cssStyles:w}=e,b=o.A.svg(r),$=v(e,{});"handDrawn"!==e.look&&($.roughness=0,$.fillStyle="solid");let k=[{x:-c/2,y:x/2},...p(-c/2,x/2,c/2,x/2,u,1),{x:c/2,y:-x/2},...p(c/2,-x/2,-c/2,-x/2,u,-1)],S=f(k),M=b.path(S,$),D=r.insert(()=>M,":first-child");return D.attr("class","basic label-container"),w&&"handDrawn"!==e.look&&D.selectAll("path").attr("style",w),l&&"handDrawn"!==e.look&&D.selectAll("path").attr("style",l),g(e,D),e.intersect=function(t){return H.polygon(e,k,t)},r}async function t4(t,e){let{labelStyles:a,nodeStyles:l}=A(e);e.labelStyle=a;let{shapeSvg:r,bbox:i,label:n}=await d(t,e,y(e)),s=Math.max(i.width+(e.padding??0)*2,e?.width??0),h=Math.max(i.height+(e.padding??0)*2,e?.height??0),c=-s/2,f=-h/2,{cssStyles:p}=e,m=o.A.svg(r),u=v(e,{}),x=[{x:c-5,y:f-5},{x:c-5,y:f+h},{x:c+s,y:f+h},{x:c+s,y:f-5}],w=`M${c-5},${f-5} L${c+s},${f-5} L${c+s},${f+h} L${c-5},${f+h} L${c-5},${f-5}
                M${c-5},${f} L${c+s},${f}
                M${c},${f-5} L${c},${f+h}`;"handDrawn"!==e.look&&(u.roughness=0,u.fillStyle="solid");let b=m.path(w,u),$=r.insert(()=>b,":first-child");return $.attr("transform","translate(2.5, 2.5)"),$.attr("class","basic label-container"),p&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",p),l&&"handDrawn"!==e.look&&$.selectAll("path").attr("style",l),n.attr("transform",`translate(${-(i.width/2)+2.5-(i.x-(i.left??0))}, ${-(i.height/2)+2.5-(i.y-(i.top??0))})`),g(e,$),e.intersect=function(t){return H.polygon(e,x,t)},r}async function t9(t,e){if(e.alias&&(e.label=e.alias),"handDrawn"===e.look){let{themeVariables:a}=(0,s.zj)(),{background:l}=a,r={...e,id:e.id+"-background",look:"default",cssStyles:["stroke: none",`fill: ${l}`]};await t9(t,r)}let a=(0,s.zj)();e.useHtmlLabels=a.htmlLabels;let l=a.er?.diagramPadding??10,r=a.er?.entityPadding??6,{cssStyles:i}=e,{labelStyles:d}=A(e);if(0===e.attributes.length&&e.label){let r={rx:0,ry:0,labelPaddingX:l,labelPaddingY:1.5*l,classes:""};(0,n.Un)(e.label,a)+2*r.labelPaddingX<a.er.minEntityWidth&&(e.width=a.er.minEntityWidth);let i=await tS(t,e,r);if(!(0,s._3)(a.htmlLabels)){let t=i.select("text"),e=t.node()?.getBBox();t.attr("transform",`translate(${-e.width/2}, 0)`)}return i}a.htmlLabels||(l*=1.25,r*=1.25);let c=y(e);c||(c="node default");let f=t.insert("g").attr("class",c).attr("id",e.domId||e.id),p=await t6(f,e.label??"",a,0,0,["name"],d);p.height+=r;let m=0,u=[],x=0,w=0,b=0,$=0,k=!0,S=!0;for(let t of e.attributes){let e=await t6(f,t.type,a,0,m,["attribute-type"],d);x=Math.max(x,e.width+l);let i=await t6(f,t.name,a,0,m,["attribute-name"],d);w=Math.max(w,i.width+l);let n=await t6(f,t.keys.join(),a,0,m,["attribute-keys"],d);b=Math.max(b,n.width+l);let s=await t6(f,t.comment,a,0,m,["attribute-comment"],d);$=Math.max($,s.width+l),m+=Math.max(e.height,i.height,n.height,s.height)+r,u.push(m)}u.pop();let M=4;b<=l&&(k=!1,b=0,M--),$<=l&&(S=!1,$=0,M--);let D=f.node().getBBox();if(p.width+2*l-(x+w+b+$)>0){let t=p.width+2*l-(x+w+b+$);x+=t/M,w+=t/M,b>0&&(b+=t/M),$>0&&($+=t/M)}let K=x+w+b+$,C=o.A.svg(f),B=v(e,{});"handDrawn"!==e.look&&(B.roughness=0,B.fillStyle="solid");let N=Math.max(D.width+2*l,e?.width||0,K),L=Math.max(D.height+(u[0]||m)+r,e?.height||0),R=-N/2,P=-L/2;f.selectAll("g:not(:first-child)").each((t,e,a)=>{let i=(0,h.Ltv)(a[e]),n=i.attr("transform"),s=0,o=0;if(n){let t=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(n);t&&(s=parseFloat(t[1]),o=parseFloat(t[2]),i.attr("class").includes("attribute-name")?s+=x:i.attr("class").includes("attribute-keys")?s+=x+w:i.attr("class").includes("attribute-comment")&&(s+=x+w+b))}i.attr("transform",`translate(${R+l/2+s}, ${o+P+p.height+r/2})`)}),f.select(".name").attr("transform","translate("+-p.width/2+", "+(P+r/2)+")");let I=C.rectangle(R,P,N,L,B),T=f.insert(()=>I,":first-child").attr("style",i.join("")),{themeVariables:W}=(0,s.zj)(),{rowEven:_,rowOdd:E,nodeBorder:j}=W;for(let[t,e]of(u.push(0),u.entries())){if(0===t&&u.length>1)continue;let a=t%2==0&&0!==e,l=C.rectangle(R,p.height+P+e,N,p.height,{...B,fill:a?_:E,stroke:j});f.insert(()=>l,"g.label").attr("style",i.join("")).attr("class",`row-rect-${t%2==0?"even":"odd"}`)}let z=C.line(R,p.height+P,N+R,p.height+P,B);for(let t of(f.insert(()=>z).attr("class","divider"),z=C.line(x+R,p.height+P,x+R,L+P,B),f.insert(()=>z).attr("class","divider"),k&&(z=C.line(x+w+R,p.height+P,x+w+R,L+P,B),f.insert(()=>z).attr("class","divider")),S&&(z=C.line(x+w+b+R,p.height+P,x+w+b+R,L+P,B),f.insert(()=>z).attr("class","divider")),u))z=C.line(R,p.height+P+t,N+R,p.height+P+t,B),f.insert(()=>z).attr("class","divider");return g(e,T),e.intersect=function(t){return H.rect(e,t)},f}async function t6(t,e,a,l=0,r=0,o=[],d=""){let c=t.insert("g").attr("class",`label ${o.join(" ")}`).attr("transform",`translate(${l}, ${r})`).attr("style",d);e!==(0,s.QO)(e)&&(e=(e=(0,s.QO)(e)).replaceAll("<","&lt;").replaceAll(">","&gt;"));let g=c.node().appendChild(await (0,i.GZ)(c,e,{width:(0,n.Un)(e,a)+100,style:d,useHtmlLabels:a.htmlLabels},a));if(e.includes("&lt;")||e.includes("&gt;")){let t=g.children[0];for(t.textContent=t.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">");t.childNodes[0];)(t=t.childNodes[0]).textContent=t.textContent.replaceAll("&lt;","<").replaceAll("&gt;",">")}let y=g.getBBox();if((0,s._3)(a.htmlLabels)){let t=g.children[0];t.style.textAlign="start";let e=(0,h.Ltv)(g);y=t.getBoundingClientRect(),e.attr("width",y.width),e.attr("height",y.height)}return y}async function et(t,e,a,l,r=a.class.padding??12){let i=3*!l,n=t.insert("g").attr("class",y(e)).attr("id",e.domId||e.id),s=null,h=null,o=null,d=null,c=0,g=0,f=0;if(s=n.insert("g").attr("class","annotation-group text"),e.annotations.length>0){let t=e.annotations[0];await ee(s,{text:`\xab${t}\xbb`},0),c=s.node().getBBox().height}h=n.insert("g").attr("class","label-group text"),await ee(h,e,0,["font-weight: bolder"]);let p=h.node().getBBox();g=p.height,o=n.insert("g").attr("class","members-group text");let m=0;for(let t of e.members){let e=await ee(o,t,m,[t.parseClassifier()]);m+=e+i}(f=o.node().getBBox().height)<=0&&(f=r/2),d=n.insert("g").attr("class","methods-group text");let u=0;for(let t of e.methods){let e=await ee(d,t,u,[t.parseClassifier()]);u+=e+i}let x=n.node().getBBox();if(null!==s){let t=s.node().getBBox();s.attr("transform",`translate(${-t.width/2})`)}return h.attr("transform",`translate(${-p.width/2}, ${c})`),x=n.node().getBBox(),o.attr("transform",`translate(0, ${c+g+2*r})`),x=n.node().getBBox(),d.attr("transform",`translate(0, ${c+g+(f?f+4*r:2*r)})`),x=n.node().getBBox(),{shapeSvg:n,bbox:x}}async function ee(t,e,a,l=[]){let r;let o=t.insert("g").attr("class","label").attr("style",l.join("; ")),d=(0,s.zj)(),c="useHtmlLabels"in e?e.useHtmlLabels:(0,s._3)(d.htmlLabels)??!0,g="";g="text"in e?e.text:e.label,!c&&g.startsWith("\\")&&(g=g.substring(1)),(0,s.Wi)(g)&&(c=!0);let y=await (0,i.GZ)(o,(0,s.oB)((0,n.Sm)(g)),{width:(0,n.Un)(g,d)+50,classes:"markdown-node-label",useHtmlLabels:c},d),f=1;if(c){let t=y.children[0],e=(0,h.Ltv)(y);f=t.innerHTML.split("<br>").length,t.innerHTML.includes("</math>")&&(f+=t.innerHTML.split("<mrow>").length-1);let a=t.getElementsByTagName("img");if(a){let t=""===g.replace(/<img[^>]*>/g,"").trim();await Promise.all([...a].map(e=>new Promise(a=>{function l(){if(e.style.display="flex",e.style.flexDirection="column",t){let t=5*parseInt(d.fontSize?.toString()??window.getComputedStyle(document.body).fontSize,10)+"px";e.style.minWidth=t,e.style.maxWidth=t}else e.style.width="100%";a(e)}(0,s.K2)(l,"setupImage"),setTimeout(()=>{e.complete&&l()}),e.addEventListener("error",l),e.addEventListener("load",l)})))}r=t.getBoundingClientRect(),e.attr("width",r.width),e.attr("height",r.height)}else{l.includes("font-weight: bolder")&&(0,h.Ltv)(y).selectAll("tspan").attr("font-weight",""),f=y.children.length;let t=y.children[0];(""===y.textContent||y.textContent.includes("&gt"))&&(t.textContent=g[0]+g.substring(1).replaceAll("&gt;",">").replaceAll("&lt;","<").trim()," "===g[1]&&(t.textContent=t.textContent[0]+" "+t.textContent.substring(1))),"undefined"===t.textContent&&(t.textContent=""),r=y.getBBox()}return o.attr("transform","translate(0,"+(-r.height/(2*f)+a)+")"),r.height}async function ea(t,e){let a=(0,s.D7)(),l=a.class.padding??12,r=e.useHtmlLabels??(0,s._3)(a.htmlLabels)??!0;e.annotations=e.annotations??[],e.members=e.members??[],e.methods=e.methods??[];let{shapeSvg:i,bbox:n}=await et(t,e,a,r,l),{labelStyles:d,nodeStyles:c}=A(e);e.labelStyle=d,e.cssStyles=e.styles||"";let y=e.styles?.join(";")||c||"";e.cssStyles||(e.cssStyles=y.replaceAll("!important","").split(";"));let f=0===e.members.length&&0===e.methods.length&&!a.class?.hideEmptyMembersBox,p=o.A.svg(i),m=v(e,{});"handDrawn"!==e.look&&(m.roughness=0,m.fillStyle="solid");let u=n.width,x=n.height;0===e.members.length&&0===e.methods.length?x+=l:e.members.length>0&&0===e.methods.length&&(x+=2*l);let w=-u/2,b=-x/2,$=p.rectangle(w-l,b-l-(f?l:0===e.members.length&&0===e.methods.length?-l/2:0),u+2*l,x+2*l+(f?2*l:0===e.members.length&&0===e.methods.length?-l:0),m),k=i.insert(()=>$,":first-child");k.attr("class","basic label-container");let S=k.node().getBBox();i.selectAll(".text").each((t,a,n)=>{let s=(0,h.Ltv)(n[a]),o=s.attr("transform"),d=0;if(o){let t=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(o);t&&(d=parseFloat(t[2]))}let c=d+b+l-(f?l:0===e.members.length&&0===e.methods.length?-l/2:0);r||(c-=4);let g=w;(s.attr("class").includes("label-group")||s.attr("class").includes("annotation-group"))&&(g=-s.node()?.getBBox().width/2||0,i.selectAll("text").each(function(t,e,a){"middle"===window.getComputedStyle(a[e]).textAnchor&&(g=0)})),s.attr("transform",`translate(${g}, ${c})`)});let M=i.select(".annotation-group").node().getBBox().height-(f?l/2:0)||0,D=i.select(".label-group").node().getBBox().height-(f?l/2:0)||0,K=i.select(".members-group").node().getBBox().height-(f?l/2:0)||0;if(e.members.length>0||e.methods.length>0||f){let t=p.line(S.x,M+D+b+l,S.x+S.width,M+D+b+l,m);i.insert(()=>t).attr("class","divider").attr("style",y)}if(f||e.members.length>0||e.methods.length>0){let t=p.line(S.x,M+D+K+b+2*l+l,S.x+S.width,M+D+K+b+l+2*l,m);i.insert(()=>t).attr("class","divider").attr("style",y)}if("handDrawn"!==e.look&&i.selectAll("path").attr("style",y),k.select(":nth-child(2)").attr("style",y),i.selectAll(".divider").select("path").attr("style",y),e.labelStyle?i.selectAll("span").attr("style",e.labelStyle):i.selectAll("span").attr("style",y),!r){let t=RegExp(/color\s*:\s*([^;]*)/),e=t.exec(y);if(e){let t=e[0].replace("color","fill");i.selectAll("tspan").attr("style",t)}else if(d){let e=t.exec(d);if(e){let t=e[0].replace("color","fill");i.selectAll("tspan").attr("style",t)}}}return g(e,k),e.intersect=function(t){return H.rect(e,t)},i}async function el(t,e){let a;let{labelStyles:l,nodeStyles:r}=A(e);e.labelStyle=l;let i="verifyMethod"in e,n=y(e),s=t.insert("g").attr("class",n).attr("id",e.domId??e.id),d=a=i?await er(s,`&lt;&lt;${e.type}&gt;&gt;`,0,e.labelStyle):await er(s,"&lt;&lt;Element&gt;&gt;",0,e.labelStyle),c=await er(s,e.name,d,e.labelStyle+"; font-weight: bold;");if(d+=c+20,i){let t=await er(s,`${e.requirementId?`Id: ${e.requirementId}`:""}`,d,e.labelStyle);d+=t;let a=await er(s,`${e.text?`Text: ${e.text}`:""}`,d,e.labelStyle);d+=a;let l=await er(s,`${e.risk?`Risk: ${e.risk}`:""}`,d,e.labelStyle);d+=l,await er(s,`${e.verifyMethod?`Verification: ${e.verifyMethod}`:""}`,d,e.labelStyle)}else{let t=await er(s,`${e.type?`Type: ${e.type}`:""}`,d,e.labelStyle);d+=t,await er(s,`${e.docRef?`Doc Ref: ${e.docRef}`:""}`,d,e.labelStyle)}let f=(s.node()?.getBBox().width??200)+20,p=(s.node()?.getBBox().height??200)+20,m=-f/2,u=-p/2,x=o.A.svg(s),w=v(e,{});"handDrawn"!==e.look&&(w.roughness=0,w.fillStyle="solid");let b=x.rectangle(m,u,f,p,w),$=s.insert(()=>b,":first-child");if($.attr("class","basic label-container").attr("style",r),s.selectAll(".label").each((t,e,a)=>{let l=(0,h.Ltv)(a[e]),r=l.attr("transform"),i=0,n=0;if(r){let t=RegExp(/translate\(([^,]+),([^)]+)\)/).exec(r);t&&(i=parseFloat(t[1]),n=parseFloat(t[2]))}let s=n-p/2,o=m+10;(0===e||1===e)&&(o=i),l.attr("transform",`translate(${o}, ${s+20})`)}),d>a+c+20){let t=x.line(m,u+a+c+20,m+f,u+a+c+20,w);s.insert(()=>t).attr("style",r)}return g(e,$),e.intersect=function(t){return H.rect(e,t)},s}async function er(t,e,a,l=""){let r;if(""===e)return 0;let o=t.insert("g").attr("class","label").attr("style",l),d=(0,s.D7)(),c=d.htmlLabels??!0,g=await (0,i.GZ)(o,(0,s.oB)((0,n.Sm)(e)),{width:(0,n.Un)(e,d)+50,classes:"markdown-node-label",useHtmlLabels:c,style:l},d);if(c){let t=g.children[0],e=(0,h.Ltv)(g);r=t.getBoundingClientRect(),e.attr("width",r.width),e.attr("height",r.height)}else{for(let t of g.children[0].children)t.textContent=t.textContent.replaceAll("&gt;",">").replaceAll("&lt;","<"),l&&t.setAttribute("style",l);r=g.getBBox(),r.height+=6}return o.attr("transform",`translate(${-r.width/2},${-r.height/2+a})`),r.height}(0,s.K2)(t2,"tiltedCylinder"),(0,s.K2)(t1,"trapezoid"),(0,s.K2)(t5,"trapezoidalPentagon"),(0,s.K2)(t8,"triangle"),(0,s.K2)(t3,"waveEdgedRectangle"),(0,s.K2)(t7,"waveRectangle"),(0,s.K2)(t4,"windowPane"),(0,s.K2)(t9,"erBox"),(0,s.K2)(t6,"addText"),(0,s.K2)(et,"textHelper"),(0,s.K2)(ee,"addText"),(0,s.K2)(ea,"classBox"),(0,s.K2)(el,"requirementBox"),(0,s.K2)(er,"addText");var ei=(0,s.K2)(t=>{switch(t){case"Very High":return"red";case"High":return"orange";case"Medium":return null;case"Low":return"blue";case"Very Low":return"lightblue"}},"colorFromPriority");async function en(t,e,{config:a}){let l,r,i,n;let{labelStyles:s,nodeStyles:h}=A(e);e.labelStyle=s||"";let f=e.width;e.width=(e.width??200)-10;let{shapeSvg:p,bbox:m,label:u}=await d(t,e,y(e)),x=e.padding||10,w="";"ticket"in e&&e.ticket&&a?.kanban?.ticketBaseUrl&&(w=a?.kanban?.ticketBaseUrl.replace("#TICKET#",e.ticket),l=p.insert("svg:a",":first-child").attr("class","kanban-ticket-link").attr("xlink:href",w).attr("target","_blank"));let b={useHtmlLabels:e.useHtmlLabels,labelStyle:e.labelStyle||"",width:e.width,img:e.img,padding:e.padding||8,centerLabel:!1};l?{label:r,bbox:i}=await c(l,"ticket"in e&&e.ticket||"",b):{label:r,bbox:i}=await c(p,"ticket"in e&&e.ticket||"",b);let{label:k,bbox:S}=await c(p,"assigned"in e&&e.assigned||"",b);e.width=f;let M=e?.width||0,D=Math.max(i.height,S.height)/2,K=Math.max(m.height+20,e?.height||0)+D,C=-M/2,B=-K/2;u.attr("transform","translate("+(x-M/2)+", "+(-D-m.height/2)+")"),r.attr("transform","translate("+(x-M/2)+", "+(-D+m.height/2)+")"),k.attr("transform","translate("+(x+M/2-S.width-20)+", "+(-D+m.height/2)+")");let{rx:N,ry:L}=e,{cssStyles:R}=e;if("handDrawn"===e.look){let t=o.A.svg(p),a=v(e,{}),l=N||L?t.path($(C,B,M,K,N||0),a):t.rectangle(C,B,M,K,a);(n=p.insert(()=>l,":first-child")).attr("class","basic label-container").attr("style",R||null)}else{(n=p.insert("rect",":first-child")).attr("class","basic label-container __APA__").attr("style",h).attr("rx",N??5).attr("ry",L??5).attr("x",C).attr("y",B).attr("width",M).attr("height",K);let t="priority"in e&&e.priority;if(t){let e=p.append("line"),a=C+2,l=B+Math.floor((N??0)/2),r=B+K-Math.floor((N??0)/2);e.attr("x1",a).attr("y1",l).attr("x2",a).attr("y2",r).attr("stroke-width","4").attr("stroke",ei(t))}}return g(e,n),e.height=K,e.intersect=function(t){return H.rect(e,t)},p}(0,s.K2)(en,"kanbanItem");var es=[{semanticName:"Process",name:"Rectangle",shortName:"rect",description:"Standard process shape",aliases:["proc","process","rectangle"],internalAliases:["squareRect"],handler:tq},{semanticName:"Event",name:"Rounded Rectangle",shortName:"rounded",description:"Represents an event",aliases:["event"],internalAliases:["roundedRect"],handler:tj},{semanticName:"Terminal Point",name:"Stadium",shortName:"stadium",description:"Terminal point",aliases:["terminal","pill"],handler:tO},{semanticName:"Subprocess",name:"Framed Rectangle",shortName:"fr-rect",description:"Subprocess",aliases:["subprocess","subproc","framed-rectangle","subroutine"],handler:tY},{semanticName:"Database",name:"Cylinder",shortName:"cyl",description:"Database storage",aliases:["db","database","cylinder"],handler:ts},{semanticName:"Start",name:"Circle",shortName:"circle",description:"Starting point",aliases:["circ"],handler:Y},{semanticName:"Decision",name:"Diamond",shortName:"diam",description:"Decision-making step",aliases:["decision","diamond","question"],handler:tW},{semanticName:"Prepare Conditional",name:"Hexagon",shortName:"hex",description:"Preparation or condition step",aliases:["hexagon","prepare"],handler:tp},{semanticName:"Data Input/Output",name:"Lean Right",shortName:"lean-r",description:"Represents input or output",aliases:["lean-right","in-out"],internalAliases:["lean_right"],handler:tA},{semanticName:"Data Input/Output",name:"Lean Left",shortName:"lean-l",description:"Represents output or input",aliases:["lean-left","out-in"],internalAliases:["lean_left"],handler:tD},{semanticName:"Priority Action",name:"Trapezoid Base Bottom",shortName:"trap-b",description:"Priority action",aliases:["priority","trapezoid-bottom","trapezoid"],handler:t1},{semanticName:"Manual Operation",name:"Trapezoid Base Top",shortName:"trap-t",description:"Represents a manual task",aliases:["manual","trapezoid-top","inv-trapezoid"],internalAliases:["inv_trapezoid"],handler:tk},{semanticName:"Stop",name:"Double Circle",shortName:"dbl-circ",description:"Represents a stop point",aliases:["double-circle"],internalAliases:["doublecircle"],handler:to},{semanticName:"Text Block",name:"Text Block",shortName:"text",description:"Text block",handler:tV},{semanticName:"Card",name:"Notched Rectangle",shortName:"notch-rect",description:"Represents a card",aliases:["card","notched-rectangle"],handler:Z},{semanticName:"Lined/Shaded Process",name:"Lined Rectangle",shortName:"lin-rect",description:"Lined process shape",aliases:["lined-rectangle","lined-process","lin-proc","shaded-process"],handler:tH},{semanticName:"Start",name:"Small Circle",shortName:"sm-circ",description:"Small starting point",aliases:["start","small-circle"],internalAliases:["stateStart"],handler:tX},{semanticName:"Stop",name:"Framed Circle",shortName:"fr-circ",description:"Stop point",aliases:["stop","framed-circle"],internalAliases:["stateEnd"],handler:tZ},{semanticName:"Fork/Join",name:"Filled Rectangle",shortName:"fork",description:"Fork or join in process flow",aliases:["join"],internalAliases:["forkJoin"],handler:tg},{semanticName:"Collate",name:"Hourglass",shortName:"hourglass",description:"Represents a collate operation",aliases:["hourglass","collate"],handler:tm},{semanticName:"Comment",name:"Curly Brace",shortName:"brace",description:"Adds a comment",aliases:["comment","brace-l"],handler:J},{semanticName:"Comment Right",name:"Curly Brace",shortName:"brace-r",description:"Adds a comment",handler:tt},{semanticName:"Comment with braces on both sides",name:"Curly Braces",shortName:"braces",description:"Adds a comment",handler:ta},{semanticName:"Com Link",name:"Lightning Bolt",shortName:"bolt",description:"Communication link",aliases:["com-link","lightning-bolt"],handler:tv},{semanticName:"Document",name:"Document",shortName:"doc",description:"Represents a document",aliases:["doc","document"],handler:t3},{semanticName:"Delay",name:"Half-Rounded Rectangle",shortName:"delay",description:"Represents a delay",aliases:["half-rounded-rectangle"],handler:ty},{semanticName:"Direct Access Storage",name:"Horizontal Cylinder",shortName:"h-cyl",description:"Direct access storage",aliases:["das","horizontal-cylinder"],handler:t2},{semanticName:"Disk Storage",name:"Lined Cylinder",shortName:"lin-cyl",description:"Disk storage",aliases:["disk","lined-cylinder"],handler:tN},{semanticName:"Display",name:"Curved Trapezoid",shortName:"curv-trap",description:"Represents a display",aliases:["curved-trapezoid","display"],handler:tl},{semanticName:"Divided Process",name:"Divided Rectangle",shortName:"div-rect",description:"Divided process shape",aliases:["div-proc","divided-rectangle","divided-process"],handler:th},{semanticName:"Extract",name:"Triangle",shortName:"tri",description:"Extraction process",aliases:["extract","triangle"],handler:t8},{semanticName:"Internal Storage",name:"Window Pane",shortName:"win-pane",description:"Internal storage",aliases:["internal-storage","window-pane"],handler:t4},{semanticName:"Junction",name:"Filled Circle",shortName:"f-circ",description:"Junction point",aliases:["junction","filled-circle"],handler:td},{semanticName:"Loop Limit",name:"Trapezoidal Pentagon",shortName:"notch-pent",description:"Loop limit step",aliases:["loop-limit","notched-pentagon"],handler:t5},{semanticName:"Manual File",name:"Flipped Triangle",shortName:"flip-tri",description:"Manual file operation",aliases:["manual-file","flipped-triangle"],handler:tc},{semanticName:"Manual Input",name:"Sloped Rectangle",shortName:"sl-rect",description:"Manual input step",aliases:["manual-input","sloped-rectangle"],handler:tz},{semanticName:"Multi-Document",name:"Stacked Document",shortName:"docs",description:"Multiple documents",aliases:["documents","st-doc","stacked-document"],handler:tP},{semanticName:"Multi-Process",name:"Stacked Rectangle",shortName:"st-rect",description:"Multiple processes",aliases:["procs","processes","stacked-rectangle"],handler:tR},{semanticName:"Stored Data",name:"Bow Tie Rectangle",shortName:"bow-rect",description:"Stored data",aliases:["stored-data","bow-tie-rectangle"],handler:O},{semanticName:"Summary",name:"Crossed Circle",shortName:"cross-circ",description:"Summary",aliases:["summary","crossed-circle"],handler:G},{semanticName:"Tagged Document",name:"Tagged Document",shortName:"tag-doc",description:"Tagged document",aliases:["tag-doc","tagged-document"],handler:tG},{semanticName:"Tagged Process",name:"Tagged Rectangle",shortName:"tag-rect",description:"Tagged process",aliases:["tagged-rectangle","tag-proc","tagged-process"],handler:tU},{semanticName:"Paper Tape",name:"Flag",shortName:"flag",description:"Paper tape",aliases:["paper-tape"],handler:t7},{semanticName:"Odd",name:"Odd",shortName:"odd",description:"Odd shape",internalAliases:["rect_left_inv_arrow"],handler:t_},{semanticName:"Lined Document",name:"Lined Document",shortName:"lin-doc",description:"Lined document",aliases:["lined-document"],handler:tL}],eh=(0,s.K2)(()=>Object.fromEntries([...Object.entries({state:tF,choice:X,note:tI,rectWithTitle:tE,labelRect:tM,iconSquare:tb,iconCircle:tx,icon:tu,iconRounded:tw,imageSquare:t$,anchor:z,kanbanItem:en,classBox:ea,erBox:t9,requirementBox:el}),...es.flatMap(t=>[t.shortName,..."aliases"in t?t.aliases:[],..."internalAliases"in t?t.internalAliases:[]].map(e=>[e,t.handler]))]),"generateShapeMap")();function eo(t){return t in eh}(0,s.K2)(eo,"isValidShape");var ed=new Map;async function ec(t,e,a){let l,r;"rect"===e.shape&&(e.rx&&e.ry?e.shape="roundedRect":e.shape="squareRect");let i=e.shape?eh[e.shape]:void 0;if(!i)throw Error(`No such shape: ${e.shape}. Please check your syntax.`);if(e.link){let n;"sandbox"===a.config.securityLevel?n="_top":e.linkTarget&&(n=e.linkTarget||"_blank"),l=t.insert("svg:a").attr("xlink:href",e.link).attr("target",n??null),r=await i(l,e,a)}else l=r=await i(t,e,a);return e.tooltip&&r.attr("title",e.tooltip),ed.set(e.id,l),e.haveCallback&&l.attr("class",l.attr("class")+" clickable"),l}(0,s.K2)(ec,"insertNode");var eg=(0,s.K2)((t,e)=>{ed.set(e.id,t)},"setNodeElem"),ey=(0,s.K2)(()=>{ed.clear()},"clear"),ef=(0,s.K2)(t=>{let e=ed.get(t.id);s.Rm.trace("Transforming node",t.diff,t,"translate("+(t.x-t.width/2-5)+", "+t.width/2+")");let a=t.diff||0;return t.clusterNode?e.attr("transform","translate("+(t.x+a-t.width/2)+", "+(t.y-t.height/2-8)+")"):e.attr("transform","translate("+t.x+", "+t.y+")"),a},"positionNode")}}]);