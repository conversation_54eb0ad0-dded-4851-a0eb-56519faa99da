"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { QuizInterface } from "@/components/quiz-interface"
import { LayoutWithSidebar } from "@/components/layout-with-sidebar"
import { useTheme } from "@/components/theme-provider"
import { useDocumentTime } from "@/hooks/use-document-time"

function QuizPageContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { theme } = useTheme()
  const documentId = searchParams.get("documentId") ? parseInt(searchParams.get("documentId")!) : undefined

  // Document time tracking with quiz pause/resume
  const { startQuiz, endQuiz, isActive, isPaused } = useDocumentTime({
    documentId: documentId,
    enabled: !!documentId,
    isProcessingComplete: true, // Quiz page assumes document is already processed
    onQuizStart: () => console.log('Quiz started - timer paused'),
    onQuizEnd: () => console.log('Quiz ended - timer resumed')
  });

  const handleBackToDocument = () => {
    if (documentId) {
      router.push(`/process?documentId=${documentId}`)
    } else {
      router.push("/")
    }
  }

  if (!documentId) {
    return (
      <LayoutWithSidebar>
        <div className="h-[calc(100vh-65px)] flex items-center justify-center">
          <div className={`rounded-lg p-8 text-center ${theme === "light" ? "bg-white border border-black" : "bg-neutral-800"}`}>
            <h2 className="text-xl font-semibold mb-4">No Document Selected</h2>
            <p className="text-muted-foreground mb-4">
              Please select a document to take a quiz.
            </p>
            <Button onClick={() => router.push("/")} className="bg-purple-600 hover:bg-purple-700">
              Go to Home
            </Button>
          </div>
        </div>
      </LayoutWithSidebar>
    )
  }

  return (
    <LayoutWithSidebar>
      <div className="h-[calc(100vh-65px)] flex flex-col bg-background text-foreground">
        {/* Header */}
        <div className="border-b border-border p-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToDocument}
              className="gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Document
            </Button>
            <h1 className="text-xl font-semibold">Quiz</h1>
          </div>
        </div>

        {/* Quiz Content */}
        <div className="flex-1 overflow-hidden">
          <QuizInterface
            documentId={documentId}
            onQuizStart={startQuiz}
            onQuizEnd={endQuiz}
          />
        </div>
      </div>
    </LayoutWithSidebar>
  )
}

export default function QuizPage() {
  return (
    <Suspense fallback={
      <LayoutWithSidebar>
        <div className="h-[calc(100vh-65px)] flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading quiz...</p>
          </div>
        </div>
      </LayoutWithSidebar>
    }>
      <QuizPageContent />
    </Suspense>
  )
}
