"use client"

import React, { useState, useEffect, useRef } from "react"
import { documentApi } from "@/lib/api"
import { useTheme } from "@/components/theme-provider"
import { FileText, Image, Video, Music, FileSpreadsheet, Presentation, Archive, File, Download, Eye, AlertCircle } from "lucide-react"
import mammoth from 'mammoth'

// Dynamically import PDF components to avoid SSR issues
let Document: any = null
let Page: any = null
let pdfjs: any = null

// Initialize PDF components only on client side
if (typeof window !== 'undefined') {
  import('react-pdf').then((mod) => {
    Document = mod.Document
    Page = mod.Page
    pdfjs = mod.pdfjs
    pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`
  })
}

interface AdvancedFilePreviewProps {
  documentId: number
  fileName: string
  fileType: string
  className?: string
}

export function AdvancedFilePreview({ documentId, fileName, fileType, className = "" }: AdvancedFilePreviewProps) {
  const [previewContent, setPreviewContent] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [numPages, setNumPages] = useState<number>(0)
  const [pageNumber, setPageNumber] = useState<number>(1)
  const [scale, setScale] = useState<number>(1.0)
  const { theme } = useTheme()
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    loadFilePreview()
  }, [documentId])

  const loadFilePreview = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const type = fileType.toLowerCase()
      
      // Get file blob for processing
      const blob = await documentApi.getDocumentFileBlob(documentId)
      
      if (type === 'pdf') {
        // PDF files will be handled by react-pdf component
        const url = URL.createObjectURL(blob)
        setPreviewContent(url)
      } else if (['doc', 'docx'].includes(type)) {
        // Convert Word documents to HTML
        const arrayBuffer = await blob.arrayBuffer()
        const result = await mammoth.convertToHtml({ arrayBuffer })
        setPreviewContent(result.value)
      } else if (['txt', 'csv', 'json', 'xml', 'html', 'css', 'js', 'py', 'java', 'cpp', 'c'].includes(type)) {
        // Text-based files
        const text = await blob.text()
        setPreviewContent(text)
      } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(type)) {
        // Image files
        const url = URL.createObjectURL(blob)
        setPreviewContent(url)
      } else {
        // Unsupported file type
        setError('Preview not available for this file type')
      }
    } catch (err) {
      console.error('Error loading file preview:', err)
      setError('Failed to load file preview. The file may be corrupted or too large.')
    } finally {
      setIsLoading(false)
    }
  }

  const getFileIcon = (fileType: string) => {
    const type = fileType.toLowerCase()
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(type)) {
      return <Image className="h-8 w-8 text-blue-500" />
    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(type)) {
      return <Video className="h-8 w-8 text-red-500" />
    } else if (['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(type)) {
      return <Music className="h-8 w-8 text-green-500" />
    } else if (['pdf', 'doc', 'docx', 'txt', 'rtf'].includes(type)) {
      return <FileText className="h-8 w-8 text-orange-500" />
    } else if (['xls', 'xlsx', 'csv'].includes(type)) {
      return <FileSpreadsheet className="h-8 w-8 text-green-600" />
    } else if (['ppt', 'pptx'].includes(type)) {
      return <Presentation className="h-8 w-8 text-red-600" />
    } else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(type)) {
      return <Archive className="h-8 w-8 text-purple-500" />
    } else {
      return <File className="h-8 w-8 text-gray-500" />
    }
  }

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages)
    setPageNumber(1)
  }

  const changePage = (offset: number) => {
    setPageNumber(prevPageNumber => prevPageNumber + offset)
  }

  const previousPage = () => changePage(-1)
  const nextPage = () => changePage(1)

  const zoomIn = () => setScale(prev => Math.min(prev + 0.2, 3.0))
  const zoomOut = () => setScale(prev => Math.max(prev - 0.2, 0.5))
  const resetZoom = () => setScale(1.0)

  const downloadFile = () => {
    const url = documentApi.getDocumentFileUrl(documentId)
    window.open(url, '_blank')
  }

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading preview...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`flex flex-col items-center justify-center h-full p-8 ${className}`}>
        <AlertCircle className="h-16 w-16 text-destructive mb-4" />
        <p className="text-lg font-medium mb-2">{fileName}</p>
        <p className="text-sm text-destructive mb-4">{error}</p>
        <div className="flex gap-3">
          <button
            onClick={loadFilePreview}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            Retry Preview
          </button>
          <button
            onClick={downloadFile}
            className="px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Download File
          </button>
        </div>
        <p className="text-xs text-muted-foreground mt-4">
          File type: {fileType.toUpperCase()}
        </p>
      </div>
    )
  }

  const renderPreview = () => {
    const type = fileType.toLowerCase()

    // PDF Preview
    if (type === 'pdf' && previewContent && Document && Page) {
      return (
        <div className="h-full flex flex-col">
          {/* PDF Controls */}
          <div className="flex items-center justify-between p-4 border-b bg-muted/50">
            <div className="flex items-center gap-4">
              <button
                onClick={previousPage}
                disabled={pageNumber <= 1}
                className="px-3 py-1 bg-primary text-primary-foreground rounded disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <span className="text-sm">
                Page {pageNumber} of {numPages}
              </span>
              <button
                onClick={nextPage}
                disabled={pageNumber >= numPages}
                className="px-3 py-1 bg-primary text-primary-foreground rounded disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
            <div className="flex items-center gap-2">
              <button onClick={zoomOut} className="px-2 py-1 border rounded">-</button>
              <span className="text-sm">{Math.round(scale * 100)}%</span>
              <button onClick={zoomIn} className="px-2 py-1 border rounded">+</button>
              <button onClick={resetZoom} className="px-2 py-1 border rounded text-xs">Reset</button>
            </div>
          </div>

          {/* PDF Document */}
          <div className="flex-1 overflow-auto flex justify-center p-4" ref={containerRef}>
            <Document
              file={previewContent}
              onLoadSuccess={onDocumentLoadSuccess}
              loading={<div className="text-center">Loading PDF...</div>}
              error={<div className="text-center text-destructive">Failed to load PDF</div>}
            >
              <Page
                pageNumber={pageNumber}
                scale={scale}
                renderTextLayer={false}
                renderAnnotationLayer={false}
              />
            </Document>
          </div>
        </div>
      )
    }

    // PDF loading fallback
    if (type === 'pdf' && previewContent && (!Document || !Page)) {
      return (
        <div className="h-full flex flex-col items-center justify-center p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground mb-4">Loading PDF viewer...</p>
            <button
              onClick={downloadFile}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download PDF
            </button>
          </div>
        </div>
      )
    }

    // Word Document Preview
    if (['doc', 'docx'].includes(type) && previewContent) {
      return (
        <div className="h-full overflow-auto p-6">
          <div 
            className="prose prose-sm max-w-none dark:prose-invert"
            dangerouslySetInnerHTML={{ __html: previewContent }}
          />
        </div>
      )
    }

    // Text File Preview
    if (['txt', 'csv', 'json', 'xml', 'html', 'css', 'js', 'py', 'java', 'cpp', 'c'].includes(type) && previewContent) {
      return (
        <div className="h-full overflow-auto p-4">
          <pre className="text-sm font-mono whitespace-pre-wrap break-words">
            {previewContent}
          </pre>
        </div>
      )
    }

    // Image Preview
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(type) && previewContent) {
      return (
        <div className="h-full flex items-center justify-center p-4">
          <img
            src={previewContent}
            alt={fileName}
            className="max-w-full max-h-full object-contain rounded-lg shadow-lg"
            style={{ transform: `scale(${scale})` }}
          />
        </div>
      )
    }

    // Fallback for unsupported types
    return (
      <div className="flex flex-col items-center justify-center h-full p-8">
        {getFileIcon(fileType)}
        <p className="text-lg font-medium mt-4">{fileName}</p>
        <p className="text-sm text-muted-foreground mt-2">
          File type: {fileType.toUpperCase()}
        </p>
        <p className="text-xs text-muted-foreground mt-4 text-center max-w-md">
          This file type is not supported for preview. You can download the file to view it in the appropriate application.
        </p>
        <button
          onClick={downloadFile}
          className="mt-4 px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2"
        >
          <Download className="h-4 w-4" />
          Download File
        </button>
      </div>
    )
  }

  return (
    <div className={`h-full w-full bg-background border rounded-lg ${className}`}>
      {renderPreview()}
    </div>
  )
}
