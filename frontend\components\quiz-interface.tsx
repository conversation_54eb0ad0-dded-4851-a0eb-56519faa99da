"use client"

import Re<PERSON>, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Clock, CheckCircle, XCircle, RotateCcw, Trophy, Target, Zap, BookOpen } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { ScrollArea } from "@/components/ui/scroll-area"
import { quizApi, performanceApi } from "@/lib/api"
import { toast } from "@/hooks/use-toast"
import { calculateQuizScore, formatDuration, getPerformanceLevel } from "@/lib/quiz-utils"
// import { useTimeTrackingContext } from "@/hooks/use-time-tracking-context"

interface QuizQuestion {
  question: string
  answer: string
}

interface QuizInterfaceProps {
  documentId?: number
  onQuizStart?: () => void
  onQuizEnd?: () => void
}

export function QuizInterface({ documentId, onQuizStart, onQuizEnd }: QuizInterfaceProps) {
  const [questions, setQuestions] = useState<QuizQuestion[]>([])
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [userAnswers, setUserAnswers] = useState<string[]>([])
  const [isQuizStarted, setIsQuizStarted] = useState(false)
  const [isQuizCompleted, setIsQuizCompleted] = useState(false)
  const [startTime, setStartTime] = useState<number>(0)
  const [timeElapsed, setTimeElapsed] = useState<number>(0)
  const [score, setScore] = useState<number>(0)
  const [loading, setLoading] = useState(false)
  const [generating, setGenerating] = useState(false)

  // Time tracking integration for pausing/resuming study time during quiz
  // Using props passed from parent component

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isQuizStarted && !isQuizCompleted) {
      interval = setInterval(() => {
        setTimeElapsed(Date.now() - startTime)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isQuizStarted, isQuizCompleted, startTime])

  useEffect(() => {
    if (documentId) {
      loadQuiz()
    }
  }, [documentId])

  const formatTime = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const loadQuiz = async () => {
    if (!documentId) {
      toast({
        title: "Error",
        description: "No document selected for quiz",
        variant: "destructive",
      })
      return
    }

    setGenerating(true)
    try {
      const response = await quizApi.getOrGenerateQuiz(documentId)
      if (response.quizzes && response.quizzes.length > 0) {
        setQuestions(response.quizzes)
        setUserAnswers(new Array(response.quizzes.length).fill(''))

        if (response.generated) {
          toast({
            title: "Success",
            description: "Quiz generated successfully!",
          })
        } else {
          toast({
            title: "Quiz Loaded",
            description: "Existing quiz loaded successfully!",
          })
        }
      } else {
        throw new Error('No quiz data received')
      }
    } catch (error) {
      console.error('Error loading/generating quiz:', error)
      toast({
        title: "Error",
        description: "Failed to load or generate quiz. Please try again.",
        variant: "destructive",
      })
    } finally {
      setGenerating(false)
    }
  }

  const generateQuiz = async () => {
    if (!documentId) {
      toast({
        title: "Error",
        description: "No document selected for quiz generation",
        variant: "destructive",
      })
      return
    }

    setGenerating(true)
    try {
      const response = await quizApi.generateQuiz(documentId, 5)
      if (response.quizzes && response.quizzes.length > 0) {
        setQuestions(response.quizzes)
        setUserAnswers(new Array(response.quizzes.length).fill(''))
        toast({
          title: "Success",
          description: "New quiz generated successfully!",
        })
      } else {
        throw new Error('No quiz data received')
      }
    } catch (error) {
      console.error('Error generating quiz:', error)
      toast({
        title: "Error",
        description: "Failed to generate quiz. Please try again.",
        variant: "destructive",
      })
    } finally {
      setGenerating(false)
    }
  }

  const startQuiz = async () => {
    setIsQuizStarted(true)
    setStartTime(Date.now())
    setTimeElapsed(0)

    // Pause time tracking when quiz starts
    try {
      onQuizStart?.()
    } catch (error) {
      console.error('Failed to pause time tracking for quiz:', error)
    }
  }

  const handleAnswerChange = (answer: string) => {
    const newAnswers = [...userAnswers]
    newAnswers[currentQuestionIndex] = answer
    setUserAnswers(newAnswers)
  }

  const nextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1)
    }
  }

  const previousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1)
    }
  }

  const calculateScore = () => {
    return calculateQuizScore(questions, userAnswers)
  }

  const submitQuiz = async () => {
    setLoading(true)
    const finalScore = calculateScore()
    const timeTakenSeconds = Math.floor(timeElapsed / 1000)

    setScore(finalScore)
    setIsQuizCompleted(true)

    // Resume time tracking when quiz ends
    try {
      onQuizEnd?.()
    } catch (error) {
      console.error('Failed to resume time tracking after quiz:', error)
    }

    // Save performance to backend
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}')
      if (user.id && documentId) {
        await performanceApi.createPerformance({
          student: user.id,
          document: documentId,
          quiz_score: finalScore,
          time_taken: timeTakenSeconds,
          remarks: `Quiz completed with ${Math.round(finalScore)}% score in ${formatTime(timeElapsed)}`
        })

        toast({
          title: "Quiz Completed!",
          description: `Your score: ${Math.round(finalScore)}% - Performance saved successfully`,
        })
      }
    } catch (error) {
      console.error('Error saving performance:', error)
      toast({
        title: "Quiz Completed!",
        description: `Your score: ${Math.round(finalScore)}% - Note: Performance could not be saved`,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const resetQuiz = () => {
    setCurrentQuestionIndex(0)
    setUserAnswers(new Array(questions.length).fill(''))
    setIsQuizStarted(false)
    setIsQuizCompleted(false)
    setStartTime(0)
    setTimeElapsed(0)
    setScore(0)
  }

  if (questions.length === 0) {
    return (
      <div className="flex flex-col h-full">
        <ScrollArea className="flex-1">
          <div className="p-6 space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="border-2 border-dashed border-purple-300 dark:border-purple-700 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20">
                <CardHeader className="text-center">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    className="mx-auto mb-4"
                  >
                    <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                      <BookOpen className="h-8 w-8 text-white" />
                    </div>
                  </motion.div>
                  <CardTitle className="text-2xl bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Quiz Center
                  </CardTitle>
                  <CardDescription className="text-lg">
                    {!documentId
                      ? "Select a document to view or generate a quiz"
                      : generating
                        ? "Creating your personalized quiz..."
                        : "Ready to test your knowledge? Generate a quiz based on your document content."
                    }
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {generating ? (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="flex flex-col items-center justify-center py-8 space-y-4"
                    >
                      <div className="relative">
                        <div className="animate-spin rounded-full h-12 w-12 border-4 border-purple-200 dark:border-purple-800"></div>
                        <div className="animate-spin rounded-full h-12 w-12 border-4 border-purple-500 border-t-transparent absolute top-0"></div>
                      </div>
                      <motion.p
                        animate={{ opacity: [0.5, 1, 0.5] }}
                        transition={{ duration: 2, repeat: Infinity }}
                        className="text-sm text-muted-foreground"
                      >
                        Analyzing your document and crafting questions...
                      </motion.p>
                    </motion.div>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="grid gap-3"
                    >
                      <Button
                        onClick={loadQuiz}
                        disabled={!documentId}
                        className="w-full h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
                        size="lg"
                      >
                        <Target className="h-5 w-5 mr-2" />
                        Load Existing Quiz
                      </Button>
                      <Button
                        onClick={generateQuiz}
                        disabled={!documentId}
                        className="w-full h-12 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
                        size="lg"
                      >
                        <Zap className="h-5 w-5 mr-2" />
                        Generate New Quiz
                      </Button>
                    </motion.div>
                  )}
                  {!documentId && (
                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.4 }}
                      className="text-sm text-muted-foreground mt-4 text-center bg-yellow-50 dark:bg-yellow-950/20 p-3 rounded-lg border border-yellow-200 dark:border-yellow-800"
                    >
                      ⚠️ Please select a document to generate a quiz.
                    </motion.p>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </ScrollArea>
      </div>
    )
  }

  if (isQuizCompleted) {
    const performanceLevel = getPerformanceLevel(score)

    return (
      <div className="flex flex-col h-full">
        <ScrollArea className="flex-1">
          <div className="p-6 space-y-6">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="border-2 border-green-200 dark:border-green-800 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20">
                <CardHeader className="text-center">
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    className="mx-auto mb-4"
                  >
                    <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg">
                      <Trophy className="h-10 w-10 text-white" />
                    </div>
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <CardTitle className="text-3xl bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                      🎉 Quiz Completed!
                    </CardTitle>
                    <CardDescription className={`text-lg font-medium ${performanceLevel.color}`}>
                      {performanceLevel.level} - {performanceLevel.description}
                    </CardDescription>
                  </motion.div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="grid grid-cols-2 gap-6"
                  >
                    <div className="text-center p-4 bg-white/50 dark:bg-black/20 rounded-xl border border-green-200 dark:border-green-800">
                      <motion.p
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.5, type: "spring", stiffness: 300 }}
                        className={`text-4xl font-bold ${performanceLevel.color} mb-2`}
                      >
                        {Math.round(score)}%
                      </motion.p>
                      <p className="text-sm text-muted-foreground font-medium">Your Score</p>
                    </div>
                    <div className="text-center p-4 bg-white/50 dark:bg-black/20 rounded-xl border border-green-200 dark:border-green-800">
                      <motion.p
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.6, type: "spring", stiffness: 300 }}
                        className="text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2"
                      >
                        {formatTime(timeElapsed)}
                      </motion.p>
                      <p className="text-sm text-muted-foreground font-medium">Time Taken</p>
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7 }}
                    className="flex gap-3"
                  >
                    <Button
                      onClick={resetQuiz}
                      variant="outline"
                      className="flex-1 h-12 border-2 border-purple-300 hover:border-purple-500 hover:bg-purple-50 dark:hover:bg-purple-950/20 transition-all duration-200"
                      size="lg"
                    >
                      <RotateCcw className="h-5 w-5 mr-2" />
                      Retake Quiz
                    </Button>
                    <Button
                      onClick={generateQuiz}
                      className="flex-1 h-12 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
                      size="lg"
                    >
                      <Zap className="h-5 w-5 mr-2" />
                      New Quiz
                    </Button>
                  </motion.div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </ScrollArea>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full">
      <ScrollArea className="flex-1">
        <div className="p-6 space-y-6">
          {/* Timer and Progress */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800"
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                  <Clock className="h-5 w-5 text-white" />
                </div>
                <span className="font-mono text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                  {formatTime(timeElapsed)}
                </span>
              </div>
              <div className="text-sm font-medium bg-white dark:bg-black/20 px-3 py-1 rounded-full border border-purple-200 dark:border-purple-800">
                Question {currentQuestionIndex + 1} of {questions.length}
              </div>
            </div>
            <Progress
              value={((currentQuestionIndex + 1) / questions.length) * 100}
              className="h-3 bg-purple-100 dark:bg-purple-900"
            />
          </motion.div>

          {/* Quiz Content */}
          <AnimatePresence mode="wait">
            {!isQuizStarted ? (
              <motion.div
                key="start-screen"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="border-2 border-purple-200 dark:border-purple-800 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-950/20 dark:to-pink-950/20">
                  <CardHeader className="text-center">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                      className="mx-auto mb-4"
                    >
                      <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <Target className="h-8 w-8 text-white" />
                      </div>
                    </motion.div>
                    <CardTitle className="text-2xl bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      Ready to Start?
                    </CardTitle>
                    <CardDescription className="text-lg">
                      You have {questions.length} questions to answer. Take your time and good luck! 🍀
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button
                      onClick={startQuiz}
                      className="w-full h-12 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
                      size="lg"
                    >
                      <Zap className="h-5 w-5 mr-2" />
                      Start Quiz
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ) : (
              <motion.div
                key={`question-${currentQuestionIndex}`}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="border-2 border-blue-200 dark:border-blue-800 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
                  <CardHeader>
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                    >
                      <CardTitle className="text-xl bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                        Question {currentQuestionIndex + 1}
                      </CardTitle>
                    </motion.div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="bg-white/70 dark:bg-black/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800"
                    >
                      <p className="text-lg leading-relaxed">{questions[currentQuestionIndex].question}</p>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      <Textarea
                        placeholder="Type your detailed answer here... 📝"
                        value={userAnswers[currentQuestionIndex]}
                        onChange={(e) => handleAnswerChange(e.target.value)}
                        className="min-h-[120px] text-base border-2 border-blue-200 dark:border-blue-800 focus:border-blue-500 dark:focus:border-blue-500 bg-white/70 dark:bg-black/20"
                      />
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="flex justify-between gap-3"
                    >
                      <Button
                        onClick={previousQuestion}
                        disabled={currentQuestionIndex === 0}
                        variant="outline"
                        className="h-11 border-2 border-gray-300 hover:border-gray-500 disabled:opacity-50"
                        size="lg"
                      >
                        ← Previous
                      </Button>

                      {currentQuestionIndex === questions.length - 1 ? (
                        <Button
                          onClick={submitQuiz}
                          disabled={loading || !userAnswers[currentQuestionIndex].trim()}
                          className="h-11 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
                          size="lg"
                        >
                          {loading ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                              Submitting...
                            </>
                          ) : (
                            <>
                              <Trophy className="h-5 w-5 mr-2" />
                              Submit Quiz
                            </>
                          )}
                        </Button>
                      ) : (
                        <Button
                          onClick={nextQuestion}
                          disabled={!userAnswers[currentQuestionIndex].trim()}
                          className="h-11 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
                          size="lg"
                        >
                          Next →
                        </Button>
                      )}
                    </motion.div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </ScrollArea>
    </div>
  )
}
