import axios from 'axios';

// Base URL for API requests
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  // Removed global Content-Type header to allow file uploads
});

// Add request interceptor to include auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Token ${token}`;
  }
  return config;
});

// Auth API endpoints
export const authApi = {
  signIn: async (credentials: { username: string; password: string }) => {
    try {
      const response = await api.post('/users/login/', credentials);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  register: async (userData: {
    username: string;
    email: string;
    password: string;
    confirm_password: string;
    first_name: string;
    last_name: string;
  }) => {
    try {
      const response = await api.post('/users/register/', userData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  verifyOTP: async (data: { email: string; otp: string }) => {
    try {
      const response = await api.post('/users/verify-otp/', data);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  logout: async () => {
    try {
      const response = await api.post('/users/logout/');
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Document API endpoints
export const documentApi = {
  // Existing document endpoints
  uploadDocument: async (file: File, groupId?: number) => {
    const formData = new FormData();
    formData.append('file', file);
    if (groupId) {
      formData.append('group', groupId.toString());
    }
    const response = await api.post('/documents/upload/', formData);
    return response.data;
  },

  getDocuments: async (groupId?: number) => {
    const params = groupId ? { group: groupId } : {};
    const response = await api.get('/documents/', { params });
    return response.data;
  },

  // Document group endpoints
  createGroup: async (data: { name: string; description?: string }) => {
    const response = await api.post('/documents/groups/', data);
    return response.data;
  },

  getGroups: async () => {
    const response = await api.get('/documents/groups/');
    return response.data;
  },

  updateGroup: async (groupId: number, data: { name: string; description?: string }) => {
    const response = await api.patch(`/documents/groups/${groupId}/`, data);
    return response.data;
  },

  deleteGroup: async (groupId: number) => {
    const response = await api.delete(`/documents/groups/${groupId}/`);
    return response.data;
  },

  // Document management within groups
  moveDocument: async (documentId: number, groupId: number | null) => {
    const response = await api.patch(`/documents/${documentId}/`, { group: groupId });
    return response.data;
  },

  getDocumentStatus: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/${documentId}/`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getDocumentDetails: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/${documentId}/`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getDocumentFileUrl: (documentId: number) => {
    // Construct the file URL for preview with proper authentication
    return `${API_BASE_URL}/documents/${documentId}/file/`;
  },

  getDocumentFileBlob: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/${documentId}/file/`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Chat API endpoints
export const chatApi = {
  sendMessage: async (message: string, documentId?: string, model: string = 'openai') => {
    try {
      const response = await api.post('/chat/message/', {
        message,
        document_id: documentId,
        model,
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getHistory: async () => {
    try {
      const response = await api.get('/chat/history/');
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};


// Performance API endpoints
export const performanceApi = {
  createPerformance: async (performanceData: {
    student: number;
    document: number;
    quiz_score: number;
    time_taken: number;
    remarks?: string;
  }) => {
    try {
      const response = await api.post('/users/performance/', performanceData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getPerformances: async (documentId?: number) => {
    try {
      const url = documentId
        ? `/users/performance/?document=${documentId}`
        : '/users/performance/';
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getStudentPerformances: async (studentId: number) => {
    try {
      const response = await api.get(`/users/performance/?student=${studentId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getPerformanceStats: async (documentId: number) => {
    try {
      const response = await api.get(`/users/performance/?document=${documentId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

}
// Blueprint API
export const blueprintApi = {
  // Get existing blueprint using DRF endpoint
  getBlueprint: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/${documentId}/blueprint/`);
      return response.data;
    } catch (error) {
      console.error('Error getting blueprint:', error);
      throw error;
    }
  },

  // Upload blueprint file or text using DRF endpoint
  uploadBlueprint: async (documentId: number, fileOrText: File | string) => {
    const formData = new FormData();
    if (fileOrText instanceof File) {
      formData.append('file', fileOrText);
    } else {
      const blob = new Blob([fileOrText], { type: 'text/plain' });
      formData.append('file', new File([blob], 'blueprint.txt', { type: 'text/plain' }));
    }
    try {
      const response = await api.post(`/documents/${documentId}/blueprint/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      let errorMsg = 'Failed to upload blueprint';
      // Fix: cast error to any for axios error shape
      const err = error as any;
      if (err.response && err.response.data && err.response.data.error) {
        errorMsg = err.response.data.error;
      }
      throw new Error(errorMsg);
    }
  },

  // Generate new blueprint using FastAPI
  generateBlueprint: async (
    documentId: number,
    focusAreas?: string,
    llmModel: string = 'gemini'
  ) => {
    try {
      const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';
      const token = localStorage.getItem('token');

      const response = await axios.post(
        `${fastApiUrl}/generate-blueprint/${documentId}`,
        {
          document_id: documentId,
          llm_model: llmModel,
          focus_areas: focusAreas,
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error generating blueprint:', error);
      throw error;
    }
  },

  // Get blueprint topics for a document
  getBlueprintTopics: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/topics/by_document/?document_id=${documentId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting blueprint topics:', error);
      throw error;
    }
  },

  // Get content for a specific blueprint topic by id
  getBlueprintTopicContent: async (topicId: number) => {
    try {
      // Use the correct backend endpoint for topic content details
      const response = await api.get(`/documents/topics/${topicId}/content_details/`)
      return response.data
    } catch (error) {
      console.error('Error getting topic content:', error)
      throw error
    }
  }
};

// Legacy blueprint function for backward compatibility
export const processBlueprint = async (
  documentId: number,
  blueprintText: string,
  llmModel: string = 'openai' // can be 'gemini', 'rag', 'openai', etc.
) => {
  try {
    const response = await api.post(`/process-blueprint/${documentId}/`, {
      document_id: documentId,
      blueprint_text: blueprintText,
      llm_model: llmModel,
    });
    return response.data;
  } catch (error) {
    console.error('Error processing blueprint:', error);
    throw error;
  }
};

// Flashcards API
export const flashcardsApi = {
  // Get flashcards for a document
  getFlashcards: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/flashcards/${documentId}/`);
      return response.data;
    } catch (error) {
      console.error('Error getting flashcards:', error);
      throw error;
    }
  },

  generateFlashcards: async (
    documentId: number,
    numFlashcards: number = 10,
    llmModel: string = 'gemini'
  ) => {
    try {
      const response = await api.post(`/api/documents/flashcards/generate/`, {
        document_id: documentId,
        num_flashcards: numFlashcards,
        llm_model: llmModel,
        force: true // Force regeneration of flashcards
      });
      return response.data;
    } catch (error) {
      console.error('Error generating flashcards:', error);
      throw error;
    }
  },

  // Generate flashcards using FastAPI (enhanced version)
  generateFlashcardsEnhanced: async (
    documentId: number,
    numFlashcards: number = 10,
    llmModel: string = 'gemini'
  ) => {
    try {
      const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';
      const token = localStorage.getItem('token');

      const response = await axios.post(
        `${fastApiUrl}/generate-flashcards/${documentId}`,
        {
          document_id: documentId,
          llm_model: llmModel,
          num_flashcards: numFlashcards,
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error generating enhanced flashcards:', error);
      throw error;
    }
  }
};

// Flowchart API
export const flowchartApi = {
  // Get flowchart for a document
  getFlowchart: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/flowcharts/${documentId}/`);
      return response.data;
    } catch (error) {
      console.error('Error getting flowchart:', error);
      throw error;
    }
  },

  generateFlowchart: async (
    documentId: number,
    llmModel: string = 'gemini'
  ) => {
    try {
      const response = await api.post(`/documents/flowcharts/generate/`, {
        document_id: documentId,
        llm_model: llmModel,
        force: true // Force regeneration of flowchart
      });
      return response.data;
    } catch (error) {
      console.error('Error generating flowchart:', error);
      throw error;
    }
  },

  // Generate flowchart using FastAPI (enhanced version)
  generateFlowchartEnhanced: async (
    documentId: number,
    llmModel: string = 'gemini'
  ) => {
    try {
      const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';
      const token = localStorage.getItem('token');

      const response = await axios.post(
        `${fastApiUrl}/generate-flowchart/${documentId}`,
        {
          document_id: documentId,
          llm_model: llmModel,
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error generating enhanced flowchart:', error);
      throw error;
    }
  }
};

// Summary API
export const summaryApi = {
  // Get existing summary
  getSummary: async (documentId: number) => {
    try {
      const response = await api.get(`/summaries/${documentId}/`);
      return response.data;
    } catch (error) {
      console.error('Error getting summary:', error);
      throw error;
    }
  },

  // Generate new summary using FastAPI
  generateSummary: async (
    documentId: number,
    summaryType: string = 'comprehensive',
    llmModel: string = 'gemini'
  ) => {
    try {
      const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';
      const token = localStorage.getItem('token');

      const response = await axios.post(
        `${fastApiUrl}/generate-summary/${documentId}`,
        {
          document_id: documentId,
          llm_model: llmModel,
          summary_type: summaryType,
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error generating summary:', error);
      throw error;
    }
  }
};

// Quiz API
export const quizApi = {
  // Combined GET/POST endpoint - preferred method
  getOrGenerateQuiz: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/${documentId}/quiz/`);
      return response.data;
    } catch (error) {
      console.error('Error getting/generating quiz:', error);
      throw error;
    }
  },

  generateQuiz: async (
    documentId: number,
    numQuestions: number = 5,
    llmModel: string = 'gemini'
  ) => {
    try {
      const response = await api.post(`/documents/${documentId}/quiz/`, {
        num_questions: numQuestions,
        llm_model: llmModel,
      });
      return response.data;
    } catch (error) {
      console.error('Error generating quiz:', error);
      throw error;
    }
  },

  // Generate quiz using FastAPI (enhanced version)
  generateQuizEnhanced: async (
    documentId: number,
    numQuestions: number = 5,
    llmModel: string = 'gemini'
  ) => {
    try {
      const fastApiUrl = process.env.NEXT_PUBLIC_FASTAPI_URL || 'http://localhost:8001';
      const token = localStorage.getItem('token');

      const response = await axios.post(
        `${fastApiUrl}/generate-quiz/${documentId}`,
        {
          document_id: documentId,
          llm_model: llmModel,
          num_questions: numQuestions,
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error generating enhanced quiz:', error);
      throw error;
    }
  },

  // Legacy method for backward compatibility
  getQuizzes: async (documentId: number) => {
    try {
      const response = await api.get(`/quizzes/?document=${documentId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching quizzes:', error);
      throw error;
    }
  },

  submitQuizAnswer: async (quizId: number, answers: any) => {
    try {
      const response = await api.post(`/quizzes/${quizId}/submit/`, { answers });
      return response.data;
    } catch (error) {
      console.error('Error submitting quiz:', error);
      throw error;
    }
  }
};

// Time Tracking API
export const timeTrackingApi = {
  // Start a new document session
  startSession: async (documentId: number) => {
    try {
      const response = await api.post('/users/time-tracking/start-session/', {
        document_id: documentId,
      });
      return response.data;
    } catch (error) {
      console.error('Error starting time tracking session:', error);
      throw error;
    }
  },

  // Update session activity (heartbeat)
  updateActivity: async (sessionId: number) => {
    try {
      const response = await api.post('/users/time-tracking/update-activity/', {
        session_id: sessionId,
      });
      return response.data;
    } catch (error) {
      console.error('Error updating session activity:', error);
      throw error;
    }
  },

  // Pause session (e.g., when starting quiz)
  pauseSession: async (sessionId: number, notes?: string) => {
    try {
      const response = await api.post('/users/time-tracking/pause-resume/', {
        session_id: sessionId,
        action: 'pause',
        notes: notes || 'Session paused',
      });
      return response.data;
    } catch (error) {
      console.error('Error pausing session:', error);
      throw error;
    }
  },

  // Resume session (e.g., after completing quiz)
  resumeSession: async (sessionId: number) => {
    try {
      const response = await api.post('/users/time-tracking/pause-resume/', {
        session_id: sessionId,
        action: 'resume',
      });
      return response.data;
    } catch (error) {
      console.error('Error resuming session:', error);
      throw error;
    }
  },

  // End session
  endSession: async (sessionId: number) => {
    try {
      const response = await api.post('/users/time-tracking/end-session/', {
        session_id: sessionId,
      });
      return response.data;
    } catch (error) {
      console.error('Error ending session:', error);
      throw error;
    }
  },

  // Get document time statistics
  getDocumentStats: async (documentId: number) => {
    try {
      const response = await api.get(`/users/time-tracking/document/${documentId}/stats/`);
      return response.data;
    } catch (error) {
      console.error('Error getting document stats:', error);
      throw error;
    }
  },

  // Get user time tracking overview
  getUserOverview: async () => {
    try {
      const response = await api.get('/users/time-tracking/overview/');
      return response.data;
    } catch (error) {
      console.error('Error getting user overview:', error);
      throw error;
    }
  },

  // Cleanup abandoned sessions (admin function)
  cleanupSessions: async () => {
    try {
      const response = await api.post('/users/time-tracking/cleanup/');
      return response.data;
    } catch (error) {
      console.error('Error cleaning up sessions:', error);
      throw error;
    }
  },
};

// Chapter API
export const chapterApi = {
  // Combined GET/POST endpoint - preferred method
  getOrGenerateChapters: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/${documentId}/chapters/`);
      return response.data;
    } catch (error) {
      console.error('Error getting/generating chapters:', error);
      throw error;
    }
  },

  generateChapters: async (
    documentId: number,
    numChapters: number = 5,
    llmModel: string = 'gemini'
  ) => {
    try {
      const response = await api.post(`/documents/${documentId}/chapters/`, {
        num_chapters: numChapters,
        llm_model: llmModel,
      });
      return response.data;
    } catch (error) {
      console.error('Error generating chapters:', error);
      throw error;
    }
  },
};

// Legacy exports for backward compatibility
export const generateQuiz = quizApi.generateQuiz;
export const generateFlowchart = flowchartApi.generateFlowchart;
export const generateFlashcards = flashcardsApi.generateFlashcards;
export const generateChapters = chapterApi.generateChapters;

export default api;
